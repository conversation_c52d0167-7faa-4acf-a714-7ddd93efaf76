{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(npm run verify:quick:*)", "<PERSON><PERSON>(open:*)", "Bash(grep:*)", "mcp__thinking__sequentialthinking", "Bash(npm run lint)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "Bash(cp:*)", "Bash(zip:*)"], "deny": []}}