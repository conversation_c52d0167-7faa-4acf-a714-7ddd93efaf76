# 设计文档

## 概述

Chrome垂直侧边栏插件是一个复杂的浏览器扩展，需要深度集成到网页中并提供类似原生浏览器功能的用户体验。本设计采用Chrome Extension Manifest V3架构，结合内容脚本注入、消息传递和现代Web技术来实现功能。

核心设计理念：
- **非侵入性集成**：侧边栏应该无缝嵌入到任何网页中，不破坏原有页面功能
- **性能优化**：最小化对页面性能的影响，使用高效的DOM操作和事件处理
- **用户体验一致性**：提供与Edge浏览器相似的交互体验和视觉效果
- **可扩展性**：模块化设计，便于后续功能扩展和维护

## 架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Background    │    │   Popup/Options │    │  Content Script │
│     Script      │◄──►│      Pages      │    │                 │
│                 │    │                 │    │                 │
│ - Tab管理       │    │ - 设置界面      │    │ - 侧边栏UI      │
│ - 数据存储      │    │ - 用户配置      │    │ - 页面注入      │
│ - 消息路由      │    │                 │    │ - 事件处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              Chrome APIs                     │
         │          ┌─────────────────┐                │
         └─────────►│   Chrome APIs   │◄───────────────┘
                    │                 │
                    │ - tabs API      │
                    │ - bookmarks API │
                    │ - storage API   │
                    │ - runtime API   │
                    └─────────────────┘
```

### 组件分层

1. **扩展核心层**
   - Background Script：后台服务，处理标签页管理、数据同步
   - Service Worker：处理扩展生命周期和跨页面通信

2. **用户界面层**
   - Content Script：注入到网页的侧边栏组件
   - Popup：扩展图标点击后的快速设置面板
   - Options Page：详细设置页面

3. **数据层**
   - Chrome Storage API：用户设置和状态持久化
   - Chrome Bookmarks API：收藏夹数据访问
   - Chrome Tabs API：标签页信息获取

## 组件和接口

### 1. Background Script (background.js)

**职责：**
- 管理全局状态和跨标签页数据同步
- 处理标签页生命周期事件
- 提供数据访问接口

**核心接口：**

```typescript
interface BackgroundService {
  // 标签页管理
  getAllTabs(): Promise<TabInfo[]>
  getTabGroups(): Promise<TabGroup[]>
  switchToTab(tabId: number): Promise<void>
  closeTab(tabId: number): Promise<void>
  createTabGroup(name: string, color: string): Promise<TabGroup>
  
  // 收藏夹管理
  getBookmarks(): Promise<BookmarkNode[]>
  addBookmark(url: string, title: string, folderId?: string): Promise<void>
  
  // 设置管理
  getSettings(): Promise<UserSettings>
  updateSettings(settings: Partial<UserSettings>): Promise<void>
  
  // 消息处理
  handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<any>
}
```

### 2. Content Script (content.js)

**职责：**
- 在网页中注入侧边栏UI组件
- 处理用户交互和动画效果
- 与Background Script通信获取数据

**核心接口：**

```typescript
interface SidebarManager {
  // 侧边栏生命周期
  initialize(): Promise<void>
  destroy(): void
  
  // 显示控制
  show(): void
  hide(): void
  toggle(): void
  
  // 状态管理
  expand(): void
  collapse(): void
  pin(): void
  unpin(): void
  
  // 内容更新
  updateTabs(tabs: TabInfo[]): void
  updateBookmarks(bookmarks: BookmarkNode[]): void
  updateSettings(settings: UserSettings): void
}
```

### 3. UI组件系统

**组件层次结构：**

```
SidebarContainer
├── SidebarHeader
│   ├── SearchBox
│   └── SettingsButton
├── SidebarContent
│   ├── TabsSection
│   │   ├── TabGroup[]
│   │   └── TabItem[]
│   ├── BookmarksSection
│   │   ├── BookmarkFolder[]
│   │   └── BookmarkItem[]
│   └── ToolsSection
└── SidebarFooter
    ├── PinButton
    └── CollapseButton
```

## 数据模型

### 核心数据结构

```typescript
// 标签页信息
interface TabInfo {
  id: number
  title: string
  url: string
  favIconUrl?: string
  active: boolean
  pinned: boolean
  groupId?: number
  index: number
}

// 标签页分组
interface TabGroup {
  id: number
  title: string
  color: string
  collapsed: boolean
  tabIds: number[]
}

// 收藏夹节点
interface BookmarkNode {
  id: string
  title: string
  url?: string
  children?: BookmarkNode[]
  dateAdded?: number
  parentId?: string
}

// 用户设置
interface UserSettings {
  position: 'left' | 'right'
  theme: 'light' | 'dark' | 'auto'
  expandDelay: number
  collapseDelay: number
  showBookmarks: boolean
  showTabs: boolean
  defaultPinned: boolean
  keyboardShortcuts: {
    toggle: string
    search: string
  }
}

// 侧边栏状态
interface SidebarState {
  visible: boolean
  expanded: boolean
  pinned: boolean
  activeSection: 'tabs' | 'bookmarks' | 'tools'
  searchQuery: string
}
```

### 消息传递协议

```typescript
// 消息类型定义
type MessageType = 
  | 'GET_TABS'
  | 'GET_BOOKMARKS' 
  | 'GET_SETTINGS'
  | 'UPDATE_SETTINGS'
  | 'SWITCH_TAB'
  | 'CLOSE_TAB'
  | 'CREATE_BOOKMARK'
  | 'SIDEBAR_STATE_CHANGED'

interface Message {
  type: MessageType
  payload?: any
  tabId?: number
}

// 响应格式
interface MessageResponse {
  success: boolean
  data?: any
  error?: string
}
```

## 错误处理

### 错误分类和处理策略

1. **网络和API错误**
   - Chrome API调用失败
   - 权限不足错误
   - 策略：重试机制 + 降级处理 + 用户提示

2. **DOM注入错误**
   - 页面结构冲突
   - CSP限制
   - 策略：兼容性检测 + 安全模式 + 错误上报

3. **数据同步错误**
   - 存储空间不足
   - 数据格式错误
   - 策略：数据验证 + 自动修复 + 备份恢复

4. **用户交互错误**
   - 无效操作
   - 状态冲突
   - 策略：输入验证 + 状态检查 + 友好提示

### 错误处理实现

```typescript
class ErrorHandler {
  static handleChromeAPIError(error: chrome.runtime.LastError, context: string) {
    console.error(`Chrome API Error in ${context}:`, error)
    
    // 根据错误类型采取不同策略
    if (error.message?.includes('permission')) {
      this.showPermissionError()
    } else if (error.message?.includes('network')) {
      this.retryWithBackoff(context)
    } else {
      this.reportError(error, context)
    }
  }
  
  static handleDOMError(error: Error, element: HTMLElement) {
    console.error('DOM Error:', error)
    
    // 尝试恢复或降级
    try {
      this.recoverFromDOMError(element)
    } catch (recoveryError) {
      this.enterSafeMode()
    }
  }
}
```

## 测试策略

### 测试层次

1. **单元测试**
   - 工具函数测试
   - 数据模型验证
   - 消息处理逻辑

2. **集成测试**
   - Chrome API集成
   - 组件间通信
   - 数据流测试

3. **端到端测试**
   - 用户交互流程
   - 跨页面状态同步
   - 性能基准测试

4. **兼容性测试**
   - 不同网站适配
   - Chrome版本兼容
   - 操作系统兼容

### 测试工具和框架

```typescript
// Jest单元测试示例
describe('SidebarManager', () => {
  let sidebarManager: SidebarManager
  
  beforeEach(() => {
    sidebarManager = new SidebarManager()
    // Mock Chrome APIs
    global.chrome = mockChromeAPIs()
  })
  
  test('should initialize sidebar correctly', async () => {
    await sidebarManager.initialize()
    expect(sidebarManager.isInitialized()).toBe(true)
  })
  
  test('should handle tab switching', async () => {
    const mockTab = { id: 1, title: 'Test', url: 'https://example.com' }
    await sidebarManager.switchToTab(mockTab.id)
    expect(chrome.tabs.update).toHaveBeenCalledWith(mockTab.id, { active: true })
  })
})
```

### 性能测试指标

- **初始化时间**：< 100ms
- **展开/收起动画**：60fps，< 300ms
- **内存占用**：< 10MB
- **CPU使用率**：< 5%（空闲时）
- **DOM节点数量**：< 1000个

## 安全考虑

### 内容安全策略(CSP)兼容

```typescript
// 动态样式注入，避免inline styles
class StyleManager {
  private static styleSheet: CSSStyleSheet
  
  static injectStyles() {
    if (!this.styleSheet) {
      const style = document.createElement('style')
      style.id = 'vertical-sidebar-styles'
      document.head.appendChild(style)
      this.styleSheet = style.sheet as CSSStyleSheet
    }
    
    // 使用insertRule而不是innerHTML
    this.styleSheet.insertRule(`
      .vertical-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 2147483647;
      }
    `)
  }
}
```

### 权限最小化原则

```json
// manifest.json权限配置
{
  "permissions": [
    "tabs",
    "bookmarks", 
    "storage",
    "activeTab"
  ],
  "host_permissions": [
    "<all_urls>"
  ],
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'"
  }
}
```

### 数据隐私保护

- 所有用户数据仅存储在本地
- 不收集或传输任何个人信息
- 提供数据清除功能
- 遵循最小数据收集原则