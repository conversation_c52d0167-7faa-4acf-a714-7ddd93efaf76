# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个为 Chrome 浏览器开发的垂直侧边栏扩展程序，提供类似 Microsoft Edge 的垂直标签页管理功能。项目基于 Chrome Extension Manifest V3，使用 TypeScript 开发，采用模块化架构设计。

## 开发环境与构建命令

### 核心开发命令
- `npm run build` - 构建生产版本到 dist/ 目录
- `npm run dev` - 开发模式，监听文件变化并自动构建
- `npm run clean` - 清理构建输出目录
- `npm run package` - 打包扩展为发布包
- `npm run release` - 完整发布流程（验证 + 构建 + 打包）

### 测试与验证
- `npm run test` - 运行 Jest 单元测试
- `npm run test:unit` - 只运行单元测试
- `npm run test:integration` - 运行集成测试
- `npm run test:all` - 运行所有测试
- `npm run verify` - 完整验证流程（推荐）
- `npm run verify:quick` - 快速验证
- `npm run verify:functional` - 功能性验证
- `npm run verify:quality` - 代码质量验证
- `npm run lint` - ESLint 代码检查

### 专用集成测试命令
- `npm run test:integration:userflow` - 用户流程测试
- `npm run test:integration:crosscomponent` - 跨组件测试  
- `npm run test:integration:dataconsistency` - 数据一致性测试
- `npm run test:integration:parallel` - 并行集成测试

## 项目架构

### 三层架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Background    │    │   Popup/Options │    │  Content Script │
│     Script      │◄──►│      Pages      │    │                 │
│                 │    │                 │    │                 │
│ - Tab管理       │    │ - 设置界面      │    │ - 侧边栏UI      │
│ - 数据存储      │    │ - 用户配置      │    │ - 页面注入      │
│ - 消息路由      │    │                 │    │ - 事件处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心目录结构
- **`src/background/`** - 后台脚本 (Service Worker)
  - `services/` - 核心服务模块 (TabManager, BookmarkManager, SettingsManager)
  - `utils/` - 后台工具类
- **`src/content/`** - 内容脚本
  - `sidebar/` - 侧边栏UI组件和管理器
  - `utils/` - 内容脚本工具类
- **`src/core/`** - 核心功能模块
  - `errors/` - 错误处理系统
  - `logging/` - 日志和监控
  - `performance/` - 性能优化
  - `compatibility/` - 兼容性处理
- **`src/interfaces/`** - TypeScript 接口定义
- **`types/`** - 全局类型定义

### 核心组件
- **SidebarManager** - 侧边栏主管理器，协调所有UI组件
- **TabManager** - 标签页管理，处理标签页的增删改查和分组
- **BookmarkManager** - 收藏夹管理，处理收藏夹的操作
- **SettingsManager** - 设置管理，处理用户配置的存储和同步
- **MessageRouter** - 消息路由，处理不同脚本间的通信

## 关键技术特性

### Chrome Extension Manifest V3
- 使用 Service Worker 替代传统的 Background Page
- 严格的 CSP 策略，所有脚本必须从扩展包加载
- 使用 `chrome.action` API 替代旧的 `browserAction`

### TypeScript 类型系统
- 完整的接口定义在 `src/interfaces/` 和 `types/`
- 核心类型：`TabInfo`, `BookmarkNode`, `UserSettings`, `SidebarState`
- 消息系统类型：`Message`, `MessageResponse`, `MessageType`

### 消息传递架构
```typescript
// Background Script 处理所有数据操作
// Content Script 处理UI交互
// 通过 chrome.runtime.sendMessage 进行通信
```

### 模块化设计
- ES6 模块系统，所有导入使用 `.js` 扩展名（TypeScript 编译要求）
- 服务层与UI层分离
- 可插拔的组件系统

## 测试策略

### 测试层次
1. **单元测试** - 使用 Jest，测试单个模块功能
2. **集成测试** - 测试组件间交互，使用 ts-node 运行
3. **验证测试** - 完整的功能和质量验证流程

### 测试配置
- **测试环境**: jsdom
- **覆盖率要求**: src/ 目录下所有 .ts 文件
- **设置文件**: `src/tests/setup.ts`

### 测试文件结构
- `tests/` - 根级测试目录
- `src/**/__tests__/` - 组件相关的单元测试
- `src/tests/integration/` - 集成测试套件
- `src/tests/verification/` - 验证测试套件

## 开发指南

### 本地开发流程
1. `npm install` - 安装依赖
2. `npm run dev` - 启动开发模式
3. 在 Chrome 中加载 `dist/` 目录作为扩展
4. 修改代码后自动重新构建

### 代码质量保证
- 使用 ESLint 进行代码检查
- TypeScript 严格模式启用
- 完整的测试覆盖
- 发布前必须通过 `npm run verify`

### 调试技巧
- Background Script: Chrome DevTools → 扩展程序 → 背景页
- Content Script: 在网页中按 F12，查看 Console
- 使用 `console.log` 进行调试，所有模块都有详细日志输出

### 性能考虑
- 延迟加载组件，避免阻塞页面加载
- 使用事件委托减少DOM监听器
- 内存管理：组件销毁时清理事件监听器和定时器
- DOM优化：最小化重排和重绘

## 扩展权限说明
- `tabs` - 标签页管理功能
- `bookmarks` - 收藏夹集成
- `storage` - 用户设置存储
- `activeTab` - 当前标签页访问
- `<all_urls>` - 在所有网站注入内容脚本

## 常见问题

### 构建问题
- 确保使用 Node.js 16+ 版本
- 如果构建失败，先运行 `npm run clean`

### 扩展加载问题
- 确保在 Chrome 中启用"开发者模式"
- 重新加载扩展后需要刷新网页才能看到内容脚本变化

### 测试问题
- 集成测试需要完整的构建输出，运行前先执行 `npm run build`
- 某些测试可能需要特定的Chrome版本或权限