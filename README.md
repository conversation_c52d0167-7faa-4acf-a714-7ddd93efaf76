# Chrome垂直侧边栏扩展

一个为Chrome浏览器提供类似Microsoft Edge垂直侧边栏功能的扩展程序。

## 功能特性

### 🎯 核心功能
- **垂直侧边栏显示** - 在页面左侧显示可收起的垂直侧边栏
- **智能展开/收起** - 鼠标悬停自动展开，离开后自动收起
- **固定窗格功能** - 支持固定侧边栏为展开状态
- **标签页管理** - 在侧边栏中显示和管理当前窗口的所有标签页
- **收藏夹集成** - 快速访问和管理Chrome收藏夹
- **搜索和过滤** - 实时搜索标签页和收藏夹内容
- **键盘快捷键** - 支持自定义快捷键操作
- **主题支持** - 明亮/暗黑主题切换
- **位置自定义** - 支持左侧/右侧位置选择

### 🔧 技术特性
- 基于Chrome Extension Manifest V3
- TypeScript开发，类型安全
- 模块化架构，易于维护
- 完整的测试覆盖
- 性能优化，内存占用低
- 兼容各种网站布局

## 安装使用

### 开发环境安装

1. 克隆项目
```bash
git clone <repository-url>
cd chrome-vertical-sidebar
```

2. 安装依赖
```bash
npm install
```

3. 构建项目
```bash
npm run build
```

4. 在Chrome中加载扩展
   - 打开Chrome扩展管理页面 (`chrome://extensions/`)
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目的`dist`目录

### 生产环境安装

1. 从Chrome Web Store安装（即将上线）
2. 或下载发布包手动安装

## 开发指南

### 项目结构

```
src/
├── background/          # 后台脚本
├── content/            # 内容脚本
├── core/               # 核心功能模块
├── ui/                 # 用户界面组件
├── tests/              # 测试文件
│   ├── integration/    # 集成测试
│   ├── verification/   # 验证测试
│   └── utils/          # 测试工具
└── types/              # 类型定义
```

### 开发命令

```bash
# 开发模式（监听文件变化）
npm run dev

# 构建生产版本
npm run build

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行所有测试
npm run test:all

# 代码检查
npm run lint

# 清理构建文件
npm run clean

# 打包扩展
npm run package
```

### 验证和质量保证

项目包含完整的验证流程，确保代码质量和功能正确性：

```bash
# 完整验证（推荐）
npm run verify

# 快速验证
npm run verify:quick

# 只验证功能
npm run verify:functional

# 只验证代码质量
npm run verify:quality

# 发布前验证
npm run release
```

### 测试覆盖

- **单元测试** - 核心功能模块测试
- **集成测试** - 组件间交互测试
- **用户流程测试** - 完整用户场景测试
- **跨组件测试** - 组件协作测试
- **数据一致性测试** - 状态同步验证
- **功能验证测试** - 需求符合性验证
- **代码质量分析** - 静态代码分析和安全检查

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Background    │    │   Popup/Options │    │  Content Script │
│     Script      │◄──►│      Pages      │    │                 │
│                 │    │                 │    │                 │
│ - Tab管理       │    │ - 设置界面      │    │ - 侧边栏UI      │
│ - 数据存储      │    │ - 用户配置      │    │ - 页面注入      │
│ - 消息路由      │    │                 │    │ - 事件处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

- **SidebarManager** - 侧边栏主管理器
- **TabManager** - 标签页管理
- **BookmarkManager** - 收藏夹管理
- **SettingsManager** - 设置管理
- **SearchManager** - 搜索功能
- **KeyboardManager** - 快捷键管理

## 配置选项

### 用户设置

- `position`: 侧边栏位置 ('left' | 'right')
- `theme`: 主题 ('light' | 'dark' | 'auto')
- `expandDelay`: 展开延迟时间 (100-2000ms)
- `collapseDelay`: 收起延迟时间 (100-2000ms)
- `showBookmarks`: 是否显示收藏夹
- `showTabs`: 是否显示标签页
- `defaultPinned`: 默认是否固定
- `keyboardShortcuts`: 自定义快捷键

### 开发配置

项目支持多种配置文件：
- `webpack.config.js` - 构建配置
- `jest.config.js` - 测试配置
- `tsconfig.json` - TypeScript配置
- `.eslintrc.js` - 代码检查配置

## 性能指标

- **初始化时间**: < 100ms
- **展开/收起动画**: 60fps, < 300ms
- **内存占用**: < 10MB
- **CPU使用率**: < 5% (空闲时)
- **DOM节点数量**: < 1000个

## 兼容性

- **Chrome版本**: 88+
- **操作系统**: Windows, macOS, Linux
- **网站兼容**: 支持所有网站，包括特殊页面
- **CSP兼容**: 完全兼容内容安全策略

## 安全性

- 遵循Chrome扩展安全最佳实践
- 最小权限原则
- 无敏感数据收集
- 本地数据存储
- CSP兼容的代码注入

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范

- 使用TypeScript
- 遵循ESLint规则
- 编写测试用例
- 添加适当的注释
- 遵循Git提交规范

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 更新日志

### v1.0.0 (即将发布)
- ✨ 初始版本发布
- 🎯 完整的垂直侧边栏功能
- 📱 标签页和收藏夹管理
- 🔍 搜索和过滤功能
- ⌨️ 键盘快捷键支持
- 🎨 主题和自定义选项
- 🧪 完整的测试覆盖
- 📊 性能优化

## 支持

如果您遇到问题或有建议，请：

1. 查看[常见问题](docs/FAQ.md)
2. 搜索现有的[Issues](../../issues)
3. 创建新的Issue描述问题
4. 联系开发团队

## 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**Chrome垂直侧边栏扩展** - 让您的Chrome浏览体验更加高效！ 🚀