<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GacCode.com Layout Simulation Test</title>
    <style>
        /* Simulate gaccode.com layout structure */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }

        /* Simulate the main container that doesn't respond to margin changes */
        .gaccode-main-container {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        /* Header that spans full width */
        .gaccode-header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .gaccode-logo {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .gaccode-nav {
            margin-left: auto;
            display: flex;
            gap: 20px;
        }

        .gaccode-nav a {
            color: #666;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .gaccode-nav a:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #333;
        }

        /* Content area */
        .gaccode-content {
            position: absolute;
            top: 60px;
            left: 0;
            width: 100vw;
            height: calc(100vh - 60px);
            padding: 40px;
            overflow-y: auto;
        }

        .credits-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .credits-title {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .credits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .credit-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .credit-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .credit-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Test sidebar */
        .test-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 60px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            z-index: 9999;
            transition: width 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .test-sidebar.expanded {
            width: 300px;
        }

        .test-sidebar .toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .test-sidebar .sidebar-content {
            margin-top: 20px;
            padding: 0 10px;
            text-align: center;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .test-sidebar.expanded .sidebar-content {
            opacity: 1;
        }

        /* Test controls */
        .test-controls {
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            min-width: 250px;
        }

        .test-controls h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }

        .test-controls button {
            display: block;
            width: 100%;
            margin: 8px 0;
            padding: 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .test-controls button:hover {
            background: #5a6fd8;
        }

        .test-controls button.active {
            background: #28a745;
        }

        .status {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
            border-left: 3px solid #667eea;
        }

        .problem-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .problem-indicator.hidden {
            display: none;
        }

        /* Simulate the problem: content doesn't adjust */
        .layout-problem .gaccode-main-container,
        .layout-problem .gaccode-header,
        .layout-problem .gaccode-content {
            /* These elements stubbornly maintain 100vw width */
            width: 100vw !important;
            left: 0 !important;
        }
    </style>
</head>
<body class="layout-problem">
    <!-- Test Sidebar -->
    <div class="test-sidebar" id="testSidebar">
        <button class="toggle-btn" onclick="toggleSidebar()">切换</button>
        <div class="sidebar-content">
            <div>测试侧边栏</div>
            <div style="margin-top: 10px; font-size: 10px;">
                模拟vTabs扩展
            </div>
        </div>
    </div>

    <!-- Test Controls -->
    <div class="test-controls">
        <h3>GacCode布局测试</h3>
        
        <button onclick="applyTraditionalMargin()" id="traditionalBtn">
            传统Margin方案
        </button>
        
        <button onclick="applyUniversalAdapter()" id="universalBtn">
            通用适配器方案
        </button>
        
        <button onclick="applyDOMManipulation()" id="domBtn">
            DOM操作方案
        </button>
        
        <button onclick="applyFlexWrapper()" id="flexBtn">
            Flex包装器方案
        </button>

        <button onclick="applyNuclearOption()" id="nuclearBtn">
            ☢️ 核武器方案
        </button>

        <button onclick="resetLayout()" id="resetBtn">
            重置布局
        </button>
        
        <div class="status" id="status">
            当前状态：模拟gaccode.com布局问题<br>
            问题：页面内容不会随侧边栏调整
        </div>
    </div>

    <!-- Problem Indicator -->
    <div class="problem-indicator" id="problemIndicator">
        ⚠️ 布局问题：页面内容没有为侧边栏让出空间！<br>
        这就是gaccode.com上遇到的问题。
    </div>

    <!-- Simulated GacCode Layout -->
    <div class="gaccode-main-container">
        <!-- Header -->
        <div class="gaccode-header">
            <div class="gaccode-logo">GacCode</div>
            <nav class="gaccode-nav">
                <a href="#">首页</a>
                <a href="#">API</a>
                <a href="#">文档</a>
                <a href="#">积分</a>
            </nav>
        </div>

        <!-- Content -->
        <div class="gaccode-content">
            <div class="credits-container">
                <h1 class="credits-title">积分系统</h1>
                <p style="text-align: center; color: #666; margin-bottom: 30px;">
                    这个页面模拟了gaccode.com/credits的布局结构
                </p>

                <div class="credits-grid">
                    <div class="credit-card">
                        <h3>免费积分</h3>
                        <p>每日登录可获得免费积分，用于体验API服务。新用户注册即可获得100积分。</p>
                    </div>
                    
                    <div class="credit-card">
                        <h3>充值积分</h3>
                        <p>支持多种支付方式充值积分，充值比例1:1，支持微信、支付宝等支付方式。</p>
                    </div>
                    
                    <div class="credit-card">
                        <h3>积分使用</h3>
                        <p>积分可用于调用各种AI API服务，不同模型消耗积分不同，详见价格表。</p>
                    </div>
                    
                    <div class="credit-card">
                        <h3>积分记录</h3>
                        <p>详细的积分消耗记录，包括使用时间、消耗数量、剩余积分等信息。</p>
                    </div>
                </div>

                <div style="margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
                    <h3 style="color: #1976d2; margin-bottom: 10px;">测试说明</h3>
                    <p style="color: #666; line-height: 1.6;">
                        这个页面模拟了gaccode.com的布局问题：页面使用100vw宽度，不会自动为侧边栏让出空间。
                        请测试不同的解决方案，观察哪种方案能够有效解决布局问题。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sidebarExpanded = false;
        let currentStrategy = null;

        function toggleSidebar() {
            const sidebar = document.getElementById('testSidebar');
            sidebarExpanded = !sidebarExpanded;
            
            if (sidebarExpanded) {
                sidebar.classList.add('expanded');
            } else {
                sidebar.classList.remove('expanded');
            }
            
            // Update problem indicator
            updateProblemIndicator();
            
            // Reapply current strategy if any
            if (currentStrategy) {
                currentStrategy();
            }
        }

        function updateProblemIndicator() {
            const indicator = document.getElementById('problemIndicator');
            const hasLayoutProblem = document.body.classList.contains('layout-problem');
            
            if (hasLayoutProblem && sidebarExpanded) {
                indicator.classList.remove('hidden');
            } else {
                indicator.classList.add('hidden');
            }
        }

        function applyTraditionalMargin() {
            resetLayout();
            currentStrategy = applyTraditionalMargin;
            
            const sidebarWidth = sidebarExpanded ? 300 : 60;
            document.body.style.marginLeft = sidebarWidth + 'px';
            document.body.style.transition = 'margin-left 0.3s ease';
            
            updateStatus('传统Margin方案已应用', 'traditionalBtn');
            
            // This won't work because of the layout-problem class
            setTimeout(() => {
                if (document.body.classList.contains('layout-problem')) {
                    updateStatus('❌ 传统Margin方案失败：页面仍然使用100vw宽度', 'traditionalBtn', false);
                }
            }, 500);
        }

        function applyUniversalAdapter() {
            resetLayout();
            currentStrategy = applyUniversalAdapter;
            
            const sidebarWidth = sidebarExpanded ? 300 : 60;
            
            // Apply CSS custom properties
            document.documentElement.style.setProperty('--sidebar-width', sidebarWidth + 'px');
            document.documentElement.style.setProperty('--content-width', `calc(100vw - ${sidebarWidth}px)`);
            
            // Add adaptation classes
            document.documentElement.classList.add('sidebar-adapted');
            document.body.classList.add('sidebar-adapted');
            
            // Apply aggressive universal styles
            const style = document.getElementById('universal-adapter-styles') || document.createElement('style');
            style.id = 'universal-adapter-styles';
            style.textContent = `
                html.sidebar-adapted body.sidebar-adapted,
                html.sidebar-adapted body.sidebar-adapted > *:not([id*="sidebar"]):not([class*="sidebar"]) {
                    margin-left: var(--sidebar-width) !important;
                    width: calc(100vw - var(--sidebar-width)) !important;
                    max-width: calc(100vw - var(--sidebar-width)) !important;
                    box-sizing: border-box !important;
                }
                
                html.sidebar-adapted .gaccode-main-container,
                html.sidebar-adapted .gaccode-header,
                html.sidebar-adapted .gaccode-content {
                    margin-left: var(--sidebar-width) !important;
                    width: calc(100vw - var(--sidebar-width)) !important;
                    max-width: calc(100vw - var(--sidebar-width)) !important;
                    min-width: calc(100vw - var(--sidebar-width)) !important;
                    left: var(--sidebar-width) !important;
                }
            `;
            
            if (!document.head.contains(style)) {
                document.head.appendChild(style);
            }
            
            updateStatus('✅ 通用适配器方案已应用', 'universalBtn', true);
            document.body.classList.remove('layout-problem');
            updateProblemIndicator();
        }

        function applyDOMManipulation() {
            resetLayout();
            currentStrategy = applyDOMManipulation;
            
            const sidebarWidth = sidebarExpanded ? 300 : 60;
            const mainContainer = document.querySelector('.gaccode-main-container');
            
            if (mainContainer) {
                // Force the container to respect sidebar
                mainContainer.style.setProperty('width', `calc(100vw - ${sidebarWidth}px)`, 'important');
                mainContainer.style.setProperty('margin-left', `${sidebarWidth}px`, 'important');
                mainContainer.style.setProperty('max-width', `calc(100vw - ${sidebarWidth}px)`, 'important');
                
                // Also fix header and content
                const header = document.querySelector('.gaccode-header');
                const content = document.querySelector('.gaccode-content');
                
                if (header) {
                    header.style.setProperty('width', `calc(100vw - ${sidebarWidth}px)`, 'important');
                    header.style.setProperty('left', `${sidebarWidth}px`, 'important');
                }
                
                if (content) {
                    content.style.setProperty('width', `calc(100vw - ${sidebarWidth}px)`, 'important');
                    content.style.setProperty('left', `${sidebarWidth}px`, 'important');
                }
                
                updateStatus('✅ DOM操作方案已应用', 'domBtn', true);
                document.body.classList.remove('layout-problem');
                updateProblemIndicator();
            } else {
                updateStatus('❌ DOM操作方案失败：找不到主容器', 'domBtn', false);
            }
        }

        function applyFlexWrapper() {
            resetLayout();
            currentStrategy = applyFlexWrapper;

            // Create flex wrapper
            const wrapper = document.createElement('div');
            wrapper.id = 'flex-wrapper';
            wrapper.style.cssText = `
                display: flex !important;
                width: 100vw !important;
                height: 100vh !important;
                position: relative !important;
            `;

            // Move sidebar to wrapper
            const sidebar = document.getElementById('testSidebar');
            const mainContainer = document.querySelector('.gaccode-main-container');

            if (sidebar && mainContainer) {
                // Insert wrapper before main container
                mainContainer.parentNode.insertBefore(wrapper, mainContainer);

                // Move sidebar and main container to wrapper
                wrapper.appendChild(sidebar);
                wrapper.appendChild(mainContainer);

                // Adjust sidebar styles for flex
                sidebar.style.position = 'relative';
                sidebar.style.flexShrink = '0';

                // Adjust main container for flex
                mainContainer.style.flex = '1';
                mainContainer.style.width = 'auto';
                mainContainer.style.height = '100vh';

                updateStatus('✅ Flex包装器方案已应用', 'flexBtn', true);
                document.body.classList.remove('layout-problem');
                updateProblemIndicator();
            } else {
                updateStatus('❌ Flex包装器方案失败', 'flexBtn', false);
            }
        }

        function applyNuclearOption() {
            resetLayout();
            currentStrategy = applyNuclearOption;

            const sidebarWidth = sidebarExpanded ? 300 : 60;

            // Nuclear option: Maximum specificity CSS override
            const nuclearStyle = document.createElement('style');
            nuclearStyle.id = 'nuclear-layout-override';
            nuclearStyle.textContent = `
                /* NUCLEAR OPTION: Override everything with maximum specificity */
                html html html html body body body body,
                html html html html body body body body *:not([id*="sidebar"]):not([class*="sidebar"]) {
                    margin-left: ${sidebarWidth}px !important;
                    width: calc(100vw - ${sidebarWidth}px) !important;
                    max-width: calc(100vw - ${sidebarWidth}px) !important;
                    min-width: calc(100vw - ${sidebarWidth}px) !important;
                    box-sizing: border-box !important;
                }

                /* Force viewport elements */
                html html html html [style*="width: 100vw"],
                html html html html [style*="width:100vw"],
                html html html html [style*="width: 100%"],
                html html html html [style*="width:100%"] {
                    width: calc(100vw - ${sidebarWidth}px) !important;
                    margin-left: ${sidebarWidth}px !important;
                    max-width: calc(100vw - ${sidebarWidth}px) !important;
                    min-width: calc(100vw - ${sidebarWidth}px) !important;
                }

                /* Force positioned elements */
                html html html html [style*="position: fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
                html html html html [style*="position:fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
                html html html html [style*="position: absolute"]:not([id*="sidebar"]):not([class*="sidebar"]),
                html html html html [style*="position:absolute"]:not([id*="sidebar"]):not([class*="sidebar"]) {
                    left: ${sidebarWidth}px !important;
                    width: calc(100vw - ${sidebarWidth}px) !important;
                    max-width: calc(100vw - ${sidebarWidth}px) !important;
                    min-width: calc(100vw - ${sidebarWidth}px) !important;
                }

                /* Prevent any element from exceeding the adjusted viewport */
                html html html html * {
                    max-width: calc(100vw - ${sidebarWidth}px) !important;
                }

                /* Force layout recalculation */
                html html html html body {
                    transform: translateZ(0) !important;
                }
            `;

            document.head.appendChild(nuclearStyle);

            // Force immediate layout recalculation
            document.body.offsetHeight;

            updateStatus('☢️ 核武器方案已应用 - 最大CSS特异性覆盖', 'nuclearBtn', true);
            document.body.classList.remove('layout-problem');
            updateProblemIndicator();
        }

        function resetLayout() {
            // Remove all applied styles and classes
            document.body.style.marginLeft = '';
            document.body.style.transition = '';
            document.documentElement.classList.remove('sidebar-adapted');
            document.body.classList.remove('sidebar-adapted');
            document.body.classList.add('layout-problem');
            
            // Remove universal adapter styles
            const style = document.getElementById('universal-adapter-styles');
            if (style) {
                style.remove();
            }

            // Remove nuclear styles
            const nuclearStyle = document.getElementById('nuclear-layout-override');
            if (nuclearStyle) {
                nuclearStyle.remove();
            }
            
            // Remove flex wrapper
            const wrapper = document.getElementById('flex-wrapper');
            if (wrapper) {
                const sidebar = document.getElementById('testSidebar');
                const mainContainer = document.querySelector('.gaccode-main-container');
                
                if (sidebar && mainContainer) {
                    // Move elements back to body
                    document.body.appendChild(sidebar);
                    document.body.appendChild(mainContainer);
                    
                    // Restore sidebar styles
                    sidebar.style.position = 'fixed';
                    sidebar.style.flexShrink = '';
                    
                    // Restore main container styles
                    mainContainer.style.flex = '';
                    mainContainer.style.width = '';
                    mainContainer.style.height = '';
                }
                
                wrapper.remove();
            }
            
            // Reset all element styles
            const elements = document.querySelectorAll('.gaccode-main-container, .gaccode-header, .gaccode-content');
            elements.forEach(el => {
                el.style.width = '';
                el.style.marginLeft = '';
                el.style.maxWidth = '';
                el.style.minWidth = '';
                el.style.left = '';
            });
            
            currentStrategy = null;
            updateStatus('布局已重置', 'resetBtn');
            updateProblemIndicator();
            
            // Clear all button states
            document.querySelectorAll('.test-controls button').forEach(btn => {
                btn.classList.remove('active');
            });
        }

        function updateStatus(message, activeBtn = null, success = null) {
            const status = document.getElementById('status');
            let icon = '';
            if (success === true) icon = '✅ ';
            else if (success === false) icon = '❌ ';
            
            status.innerHTML = `${icon}${message}`;
            
            // Update button states
            document.querySelectorAll('.test-controls button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            if (activeBtn) {
                document.getElementById(activeBtn).classList.add('active');
            }
        }

        // Initialize
        updateProblemIndicator();
    </script>
</body>
</html>
