<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Layout Adapter Test</title>
    <style>
        /* Test different layout patterns */
        
        /* CSS Grid Layout */
        .grid-container {
            display: grid;
            grid-template-columns: 1fr 3fr 1fr;
            grid-gap: 20px;
            height: 100vh;
            background: #f0f0f0;
        }
        
        /* Flexbox Layout */
        .flex-container {
            display: flex;
            flex-direction: row;
            height: 100vh;
            background: #e0e0e0;
        }
        
        .flex-sidebar {
            flex: 0 0 200px;
            background: #ccc;
        }
        
        .flex-main {
            flex: 1;
            background: #fff;
        }
        
        /* Absolute Positioning */
        .absolute-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #d0d0d0;
        }
        
        .absolute-header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: #333;
            color: white;
            z-index: 1000;
        }
        
        /* Viewport Units */
        .viewport-container {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }
        
        /* Traditional Layout */
        .traditional-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            background: #fafafa;
            min-height: 100vh;
        }
        
        /* SPA-like containers */
        .spa-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
        }
        
        /* Test controls */
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 10000;
            font-family: Arial, sans-serif;
        }
        
        .test-controls button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 10px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #005a87;
        }
        
        .test-controls select {
            width: 100%;
            margin: 5px 0;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
        }
        
        /* Hidden by default */
        .layout-test {
            display: none;
        }
        
        .layout-test.active {
            display: block;
        }
        
        /* Simulated sidebar */
        .test-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 60px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            z-index: 9999;
            transition: width 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 20px;
        }
        
        .test-sidebar.expanded {
            width: 300px;
        }
        
        .test-sidebar .toggle-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Test Sidebar -->
    <div class="test-sidebar" id="testSidebar">
        <button class="toggle-btn" onclick="toggleSidebar()">Toggle</button>
        <div style="margin-top: 20px; font-size: 12px; text-align: center;">
            Test Sidebar
        </div>
    </div>

    <!-- Test Controls -->
    <div class="test-controls">
        <h3>Layout Adapter Test</h3>
        
        <label>Layout Type:</label>
        <select id="layoutSelect" onchange="switchLayout()">
            <option value="grid">CSS Grid</option>
            <option value="flex">Flexbox</option>
            <option value="absolute">Absolute Positioning</option>
            <option value="viewport">Viewport Units</option>
            <option value="traditional">Traditional</option>
            <option value="spa">SPA Container</option>
        </select>
        
        <button onclick="testUniversalAdapter()">Test Universal Adapter</button>
        <button onclick="testTraditionalMethod()">Test Traditional Method</button>
        <button onclick="resetLayout()">Reset Layout</button>
        <button onclick="simulateGaccode()">Simulate gaccode.com</button>
        <button onclick="simulateXiezuocat()">Simulate xiezuocat.com</button>
        
        <div class="status" id="status">
            Ready to test...
        </div>
    </div>

    <!-- CSS Grid Layout Test -->
    <div class="layout-test grid-container" id="gridLayout">
        <div style="background: #ff6b6b; padding: 20px;">Grid Sidebar</div>
        <div style="background: #4ecdc4; padding: 20px;">
            <h2>CSS Grid Layout Test</h2>
            <p>This tests how the universal adapter handles CSS Grid layouts.</p>
            <p>Grid should adjust its columns when sidebar expands/collapses.</p>
        </div>
        <div style="background: #45b7d1; padding: 20px;">Grid Right</div>
    </div>

    <!-- Flexbox Layout Test -->
    <div class="layout-test flex-container" id="flexLayout">
        <div class="flex-sidebar">
            <h3>Flex Sidebar</h3>
        </div>
        <div class="flex-main">
            <h2>Flexbox Layout Test</h2>
            <p>This tests how the universal adapter handles Flexbox layouts.</p>
            <p>Flex container should adjust when sidebar expands/collapses.</p>
        </div>
    </div>

    <!-- Absolute Positioning Test -->
    <div class="layout-test absolute-container" id="absoluteLayout">
        <div class="absolute-header">
            <h3 style="margin: 15px;">Fixed Header</h3>
        </div>
        <div style="padding: 80px 20px 20px;">
            <h2>Absolute Positioning Test</h2>
            <p>This tests how the universal adapter handles absolutely positioned elements.</p>
            <p>Fixed header and container should adjust when sidebar expands/collapses.</p>
        </div>
    </div>

    <!-- Viewport Units Test -->
    <div class="layout-test viewport-container" id="viewportLayout">
        <div style="padding: 20px; color: white;">
            <h2>Viewport Units Test</h2>
            <p>This container uses 100vw and 100vh.</p>
            <p>Should adjust to account for sidebar width.</p>
        </div>
    </div>

    <!-- Traditional Layout Test -->
    <div class="layout-test traditional-container" id="traditionalLayout">
        <div style="padding: 20px;">
            <h2>Traditional Layout Test</h2>
            <p>This is a traditional centered container layout.</p>
            <p>Should use margin-based adjustment.</p>
        </div>
    </div>

    <!-- SPA Container Test -->
    <div class="layout-test spa-container" id="spaLayout">
        <div style="padding: 20px;">
            <h2>SPA Container Test</h2>
            <p>This simulates a Single Page Application container.</p>
            <p>Should handle absolute positioning with proper width adjustment.</p>
        </div>
    </div>

    <script>
        // Simulate Universal Layout Adapter functionality
        let currentLayout = 'grid';
        let sidebarExpanded = false;
        let adapterActive = false;

        function switchLayout() {
            const select = document.getElementById('layoutSelect');
            const newLayout = select.value;
            
            // Hide all layouts
            document.querySelectorAll('.layout-test').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected layout
            document.getElementById(newLayout + 'Layout').classList.add('active');
            currentLayout = newLayout;
            
            updateStatus(`Switched to ${newLayout} layout`);
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('testSidebar');
            sidebarExpanded = !sidebarExpanded;
            
            if (sidebarExpanded) {
                sidebar.classList.add('expanded');
            } else {
                sidebar.classList.remove('expanded');
            }
            
            if (adapterActive) {
                applyUniversalAdapter();
            }
        }

        function testUniversalAdapter() {
            adapterActive = true;
            applyUniversalAdapter();
            updateStatus('Universal Adapter applied - sidebar should now affect page layout');
        }

        function testTraditionalMethod() {
            adapterActive = true;
            applyTraditionalMethod();
            updateStatus('Traditional method applied - using margin-based adjustment');
        }

        function resetLayout() {
            adapterActive = false;
            removeAllAdaptations();
            updateStatus('Layout reset to original state');
        }

        function applyUniversalAdapter() {
            const sidebarWidth = sidebarExpanded ? 300 : 60;
            
            // Apply CSS custom properties
            document.documentElement.style.setProperty('--sidebar-width', sidebarWidth + 'px');
            document.documentElement.style.setProperty('--content-width', `calc(100vw - ${sidebarWidth}px)`);
            
            // Add adaptation classes
            document.documentElement.classList.add('sidebar-adapted');
            document.body.classList.add('sidebar-adapted');
            
            // Apply universal styles
            const style = document.getElementById('universal-adapter-styles') || document.createElement('style');
            style.id = 'universal-adapter-styles';
            style.textContent = `
                html.sidebar-adapted,
                body.sidebar-adapted {
                    margin-left: var(--sidebar-width) !important;
                    width: var(--content-width) !important;
                    max-width: var(--content-width) !important;
                    box-sizing: border-box !important;
                    transition: all 0.3s ease !important;
                }
                
                .sidebar-adapted .grid-container,
                .sidebar-adapted .flex-container,
                .sidebar-adapted .absolute-container,
                .sidebar-adapted .viewport-container,
                .sidebar-adapted .spa-container {
                    margin-left: var(--sidebar-width) !important;
                    width: var(--content-width) !important;
                    max-width: var(--content-width) !important;
                }
                
                .sidebar-adapted .absolute-header {
                    left: var(--sidebar-width) !important;
                    width: var(--content-width) !important;
                }
            `;
            
            if (!document.head.contains(style)) {
                document.head.appendChild(style);
            }
        }

        function applyTraditionalMethod() {
            const sidebarWidth = sidebarExpanded ? 300 : 60;
            
            // Traditional margin-based approach
            document.body.style.marginLeft = sidebarWidth + 'px';
            document.body.style.transition = 'margin-left 0.3s ease';
        }

        function removeAllAdaptations() {
            // Remove universal adapter
            document.documentElement.classList.remove('sidebar-adapted');
            document.body.classList.remove('sidebar-adapted');
            
            const style = document.getElementById('universal-adapter-styles');
            if (style) {
                style.remove();
            }
            
            // Remove traditional method
            document.body.style.marginLeft = '';
            document.body.style.transition = '';
            
            // Remove CSS custom properties
            document.documentElement.style.removeProperty('--sidebar-width');
            document.documentElement.style.removeProperty('--content-width');
        }

        function simulateGaccode() {
            // Simulate gaccode.com layout issues
            document.body.innerHTML += `
                <div style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.1); z-index: 5000; pointer-events: none;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; pointer-events: auto;">
                        <h3>Simulating gaccode.com layout</h3>
                        <p>This simulates the layout issues found on gaccode.com</p>
                        <button onclick="this.parentElement.parentElement.remove()">Close</button>
                    </div>
                </div>
            `;
            updateStatus('Simulating gaccode.com layout - test universal adapter now');
        }

        function simulateXiezuocat() {
            // Simulate xiezuocat.com layout issues
            document.body.innerHTML += `
                <div style="position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.1); z-index: 5000; pointer-events: none;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; pointer-events: auto;">
                        <h3>Simulating xiezuocat.com layout</h3>
                        <p>This simulates the Vue.js SPA layout found on xiezuocat.com</p>
                        <button onclick="this.parentElement.parentElement.remove()">Close</button>
                    </div>
                </div>
            `;
            updateStatus('Simulating xiezuocat.com layout - test universal adapter now');
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // Initialize
        switchLayout();
    </script>
</body>
</html>
