/* Edge风格侧边栏样式 */
#edge-vertical-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  height: 100vh !important;
  z-index: 2147483647 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  pointer-events: auto !important;
  /* 添加平滑的宽度和背景过渡动画 */
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              background 0.3s ease, 
              backdrop-filter 0.3s ease,
              box-shadow 0.3s ease !important;
}

/* 窄条状态 */
.sidebar-collapsed {
  width: 48px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-right: 1px solid #e1e4e8 !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

/* 窄条状态下展开按钮方向 */
.sidebar-collapsed .toggle-icon {
  transform: scaleX(-1) !important; /* 窄视图时显示展开方向 */
}

.sidebar-collapsed .tab-title,
.sidebar-collapsed .empty-text,
.sidebar-collapsed .empty-hint {
  display: none !important;
}

/* 窄视图下分组标题样式 - 响应式方案 */
.sidebar-collapsed .group-title {
  display: block !important;
  text-align: center !important;
  /* 隐藏原文本 */
  font-size: 0 !important;
  line-height: 0 !important;
}

/* 窄视图下使用伪元素显示首字符 */
.sidebar-collapsed .group-title::before {
  content: attr(data-first-char) !important;
  font-size: 13px !important;
  line-height: 1.2 !important;
  display: inline-block !important;
}

/* 移除窄视图下标签组的横向边距，统一使用容器内边距 */

/* 窄视图下隐藏关闭按钮 */
.sidebar-collapsed .tab-close-btn {
  display: none !important;
}

/* 窄视图下优化图标显示，居中显示 */
.sidebar-collapsed .tab-item {
  justify-content: center !important;
  padding: 4px 2px !important; /* 保持垂直padding与宽视图一致 */
  min-height: 24px !important; /* 与宽视图一致 */
  margin: 1px 0 !important; /* 只调整水平margin */
}

.sidebar-collapsed .group-header {
  justify-content: flex-start !important;
  padding: 6px 6px !important; /* 增加左右内边距到6px */
  text-align: left !important;
}

.sidebar-collapsed .tab-favicon {
  margin: 0 !important;
}

.sidebar-collapsed .tab-favicon-default {
  margin: 0 !important;
}

.sidebar-collapsed .empty-icon {
  font-size: 20px !important;
  margin-bottom: 0 !important;
}

/* 确保窄视图下按钮与分组宽度一致 */
.sidebar-collapsed .toggle-button {
  width: 100% !important;
  padding: 4px 2px !important; /* 窄视图下使用更紧凑的内边距 */
  font-size: 10px !important;
}

/* 展开状态 */
.sidebar-expanded {
  width: 320px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border-right: 1px solid #e1e4e8 !important;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15) !important;
}

/* 暗黑模式 */
@media (prefers-color-scheme: dark) {
  .sidebar-collapsed {
    background: rgba(30, 30, 30, 0.95) !important;
    border-right-color: #404040 !important;
  }
  
  .sidebar-expanded {
    background: rgba(30, 30, 30, 0.98) !important;
    border-right-color: #404040 !important;
  }
}

/* 侧边栏内容 */
.sidebar-content {
  position: relative !important;
  height: 100% !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 移除图标栏相关样式 */

/* 主要内容容器 */
.sidebar-main-content {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 12px !important;
  opacity: 0 !important;
  transform: translateX(-20px) !important;
  /* 使用更自然的缓动函数和延迟 */
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s, 
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s !important;
  /* 在折叠状态下隐藏滚动条 */
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.sidebar-expanded .sidebar-main-content {
  opacity: 1 !important;
  transform: translateX(0) !important;
  /* 展开时常驻显示滚动条 */
  scrollbar-width: thin !important;
  -ms-overflow-style: auto !important;
  overflow-y: scroll !important; /* 强制显示滚动条 */
}

/* 窄视图状态下也要显示内容 */
.sidebar-collapsed .sidebar-main-content {
  opacity: 1 !important;
  transform: translateX(0) !important;
  /* 窄视图下统一垂直内边距，只减少水平内边距 */
  padding: 12px 2px !important;
}

/* 展开状态下显示文本内容 */
.sidebar-expanded .tab-title,
.sidebar-expanded .empty-text,
.sidebar-expanded .empty-hint,
.sidebar-expanded .group-title {
  display: block !important;
}

.sidebar-expanded .tab-close-btn {
  display: flex !important;
}


/* 移除标题相关样式 */

/* 分组样式 */
.tab-group {
  margin-bottom: 8px !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: 1px solid #e1e4e8 !important;
}

.group-header {
  padding: 6px 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  transition: opacity 0.2s ease !important;
  font-size: 13px !important;
}

.group-header:hover {
  opacity: 0.8 !important;
}

.group-title {
  flex: 1 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  text-align: left !important;
}

.expand-icon {
  font-size: 10px !important;
  /* 使用更平滑的旋转动画 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.group-content {
  background: rgba(0, 0, 0, 0.02) !important;
  overflow: hidden !important;
  /* 优化分组展开/折叠动画 */
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s ease 0.1s,
              padding 0.3s ease !important;
}

@media (prefers-color-scheme: dark) {
  .tab-group {
    border-color: #404040 !important;
  }
  
  .group-content {
    background: rgba(255, 255, 255, 0.02) !important;
  }
}

/* 标签页样式 */
.tab-item {
  padding: 4px 12px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  cursor: pointer !important;
  /* 增强标签页的交互动画 */
  transition: background-color 0.2s ease, 
              transform 0.2s ease,
              box-shadow 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  min-height: 24px !important;
  border-radius: 4px !important;
  margin: 1px 4px !important;
}

.tab-item:last-child {
  border-bottom: none !important;
}

.tab-item:hover {
  background: rgba(0, 0, 0, 0.03) !important;
  transform: translateX(2px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* 标签页关闭按钮 */
.tab-close-btn {
  opacity: 0 !important;
  background: none !important;
  border: none !important;
  color: #666 !important;
  cursor: pointer !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-size: 14px !important;
  font-weight: bold !important;
  transition: all 0.2s ease !important;
  margin-left: auto !important;
  margin-right: -6px !important;
  width: 18px !important;
  height: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  position: relative !important;
  right: 0 !important;
}

.tab-item:hover .tab-close-btn {
  opacity: 1 !important;
}

.tab-close-btn:hover {
  background: rgba(255, 0, 0, 0.1) !important;
  color: #d32f2f !important;
  transform: scale(1.1) !important;
}

.tab-favicon {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
}

.tab-favicon-default {
  width: 16px !important;
  height: 16px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
  color: #666 !important;
}

.tab-title {
  flex: 1 !important;
  font-size: 12px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  color: #333 !important;
  text-align: left !important;
}

/* 活跃标签背景色 */
.tab-active {
  background: rgba(25, 118, 210, 0.1) !important;
}

@media (prefers-color-scheme: dark) {
  .tab-item:hover {
    background: rgba(255, 255, 255, 0.05) !important;
  }
  
  .tab-title {
    color: #e1e4e8 !important;
  }
  
  /* 暗黑模式下的活跃标签背景色 */
  .tab-active {
    background: rgba(96, 165, 250, 0.15) !important;
  }
  
  .tab-close-btn {
    color: #a0a0a0 !important;
  }
  
  .tab-close-btn:hover {
    background: rgba(255, 100, 100, 0.2) !important;
    color: #ff6b6b !important;
  }
}

/* 空状态 */
.empty-state {
  text-align: center !important;
  padding: 40px 20px !important;
  color: #6a737d !important;
}

.empty-icon {
  font-size: 32px !important;
  margin-bottom: 12px !important;
  opacity: 0.5 !important;
}

.empty-text {
  font-size: 14px !important;
  margin-bottom: 6px !important;
}

.empty-hint {
  font-size: 12px !important;
  opacity: 0.7 !important;
}

/* 滚动条样式 */
.sidebar-main-content::-webkit-scrollbar {
  width: 6px !important;
}

.sidebar-main-content::-webkit-scrollbar-track {
  background: transparent !important;
}

.sidebar-main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 3px !important;
  transition: background 0.2s ease !important;
}

.sidebar-main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3) !important;
}

@media (prefers-color-scheme: dark) {
  .sidebar-main-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2) !important;
  }
  
  .sidebar-main-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3) !important;
  }
}


/* 悬停时的微妙发光效果 */
.sidebar-icon:hover,
.group-header:hover,
.tab-item:hover {
  box-shadow: 0 0 0 1px rgba(25, 118, 210, 0.1) !important;
}

@media (prefers-color-scheme: dark) {
  .sidebar-icon:hover,
  .group-header:hover,
  .tab-item:hover {
    box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.2) !important;
  }
}

/* 侧边栏顶部区域 */
.sidebar-footer {
  padding: 0 !important;
  background: rgba(0, 0, 0, 0.02) !important;
}

/* 切换按钮样式 - 现代化设计 */
.toggle-button {
  width: 100% !important;
  padding: 6px 14px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 0 !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  color: #333 !important;
  cursor: pointer !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  /* 玻璃质感效果 */
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  /* 性能优化的过渡 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  user-select: none !important;
  position: relative !important;
  overflow: hidden !important;
  will-change: transform, box-shadow !important;
}

/* 按钮悬停效果 */
.toggle-button:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-color: rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

/* 按钮点击效果 */
.toggle-button:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}


/* 按钮点击波纹效果 */
.toggle-button::before {
  content: '' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 0 !important;
  height: 0 !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.5) !important;
  transform: translate(-50%, -50%) !important;
  transition: width 0.6s ease, height 0.6s ease !important;
}

.toggle-button:active::before {
  width: 100px !important;
  height: 100px !important;
}


/* 图标样式和动画 */
.toggle-icon {
  font-size: 16px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: inline-block !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

/* 悬停时图标弹跳效果 */
.toggle-button:hover .toggle-icon {
  transform: rotate(15deg) scale(1.1) !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15)) !important;
}

/* 窄视图下悬停时保持水平翻转 */
.sidebar-collapsed .toggle-button:hover .toggle-icon {
  transform: scaleX(-1) rotate(15deg) scale(1.1) !important;
}



/* 暗黑模式下的切换按钮样式 */
@media (prefers-color-scheme: dark) {
  .sidebar-footer {
    background: rgba(255, 255, 255, 0.03) !important;
  }
  
  .toggle-button {
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%) !important;
    color: #e1e4e8 !important;
    border-color: rgba(255, 255, 255, 0.08) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  }
  
  .toggle-button:hover {
    background: linear-gradient(135deg, #3d3d3d 0%, #2a2a2a 100%) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
  }
  
  .toggle-button:active {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
  }
  
  /* 暗黑模式图标样式 */
  .toggle-icon {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
  }
  
  .toggle-button:hover .toggle-icon {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4)) !important;
  }
  
  /* 暗黑模式波纹效果 */
  .toggle-button::before {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}