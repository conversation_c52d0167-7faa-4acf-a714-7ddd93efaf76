/**
 * Edge风格垂直侧边栏 - Content Script
 * @version 1.0.0
 * @description 为Chrome浏览器提供垂直标签页管理功能
 */

class EdgeSidebarManager {
  /**
   * 配置常量
   */
  static CONFIG = {
    UPDATE_INTERVAL: 60000, // 数据更新间隔 (60秒)
    TRANSITION_DURATION: 300, // 动画过渡时长
    MAX_TABS_DISPLAY: 1000, // 最大显示标签数
    DEBUG_MODE: false, // 调试模式，生产环境应设为false
    PERFORMANCE_MONITORING: true, // 性能监控
    ERROR_RECOVERY: true // 自动错误恢复
  };

  constructor() {
    this.sidebarElement = null;
    this.isExpanded = false;
    this.isEnabled = true;
    this.settings = null;
    this.tabs = [];
    this.tabGroups = [];
    this.updateInterval = null;
    this.expandTimeout = null;
    this.collapseTimeout = null;
    this.originalStyles = new Map(); // 用于DOM操作的样式恢复
    
    // 状态持久化键
    this.STORAGE_KEYS = {
      STATE: 'edgeSidebar_state',
      SETTINGS: 'edgeSidebar_settings',
      LAST_UPDATE: 'edgeSidebar_lastUpdate'
    };
    
    // 初始化调试日志
    this.log('Edge Vertical Sidebar - Content Script Loaded');
    
    // 绑定事件处理器以便正确移除
    this.toggleClickHandler = this.handleToggleClick.bind(this);
    // 保留这些绑定以防future use
    // this.mouseEnterHandler = this.scheduleExpand.bind(this);
    // this.mouseLeaveHandler = this.scheduleCollapse.bind(this);
    this.clickHandler = this.handleClick.bind(this);
    this.transitionStartHandler = this.handleTransitionStart.bind(this);
    this.transitionEndHandler = this.handleTransitionEnd.bind(this);
    this.messageHandler = this.handleMessage.bind(this);
  }

  /**
   * 调试日志方法
   * @param {string} message - 日志消息
   * @param {string} level - 日志级别 ('log', 'warn', 'error')
   * @param {*} data - 附加数据
   */
  log(message, level = 'log', data = null) {
    if (!EdgeSidebarManager.CONFIG.DEBUG_MODE) return;
    
    const prefix = `[EdgeSidebar]`;
    if (data !== null) {
      console[level](prefix, message, data);
    } else {
      console[level](prefix, message);
    }
  }

  /**
   * 错误日志方法 (始终输出)
   * @param {string} message - 错误消息
   * @param {Error|*} error - 错误对象
   */
  error(message, error = null) {
    const prefix = `[EdgeSidebar Error]`;
    if (error) {
      console.error(prefix, message, error);
    } else {
      console.error(prefix, message);
    }
  }

  /**
   * 性能监控方法
   * @param {string} label - 性能标签
   * @param {Function} fn - 要监控的函数
   * @returns {Promise<*>} 函数执行结果
   */
  async performanceMonitor(label, fn) {
    if (!EdgeSidebarManager.CONFIG.PERFORMANCE_MONITORING) {
      return await fn();
    }

    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 100) { // 只记录超过100ms的操作
        this.log(`性能监控: ${label} 耗时 ${duration.toFixed(2)}ms`, 'warn');
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.error(`性能监控: ${label} 执行失败 (耗时 ${duration.toFixed(2)}ms)`, error);
      throw error;
    }
  }

  /**
   * 安全的异步操作包装器
   * @param {Function} fn - 异步函数
   * @param {string} context - 上下文描述
   * @returns {Promise<*>} 
   */
  async safeAsync(fn, context = 'unknown') {
    try {
      return await fn();
    } catch (error) {
      this.error(`安全异步操作失败: ${context}`, error);
      
      // 自动错误恢复
      if (EdgeSidebarManager.CONFIG.ERROR_RECOVERY) {
        this.log(`尝试错误恢复: ${context}`, 'warn');
        // 这里可以添加具体的恢复逻辑
      }
      
      return null;
    }
  }

  /**
   * 保存侧边栏状态到localStorage
   */
  saveState() {
    try {
      const state = {
        expanded: this.isExpanded,
        enabled: this.isEnabled,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent.substring(0, 100) // 限制长度
      };
      
      localStorage.setItem(this.STORAGE_KEYS.STATE, JSON.stringify(state));
      this.log('状态已保存到localStorage', 'log', state);
    } catch (error) {
      this.error('保存状态失败', error);
    }
  }

  /**
   * 从localStorage恢复侧边栏状态
   */
  loadState() {
    try {
      const stateStr = localStorage.getItem(this.STORAGE_KEYS.STATE);
      if (!stateStr) {
        this.log('未找到保存的状态，使用默认状态');
        return { expanded: false, enabled: true };
      }

      const state = JSON.parse(stateStr);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      // 检查状态是否过期
      if (state.timestamp && (now - state.timestamp) > maxAge) {
        this.log('保存的状态已过期，使用默认状态');
        return { expanded: false, enabled: true };
      }

      this.log('成功恢复保存的状态', 'log', state);
      return {
        expanded: Boolean(state.expanded),
        enabled: state.enabled !== false
      };
    } catch (error) {
      this.error('恢复状态失败', error);
      return { expanded: false, enabled: true };
    }
  }

  /**
   * 清理过期的状态缓存
   */
  cleanupStateCache() {
    try {
      const keys = [this.STORAGE_KEYS.STATE, this.STORAGE_KEYS.SETTINGS, this.STORAGE_KEYS.LAST_UPDATE];
      keys.forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          try {
            const data = JSON.parse(item);
            const now = Date.now();
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
            
            if (data.timestamp && (now - data.timestamp) > maxAge) {
              localStorage.removeItem(key);
              this.log(`清理过期缓存: ${key}`);
            }
          } catch (e) {
            // 如果解析失败，也清理掉
            localStorage.removeItem(key);
            this.log(`清理无效缓存: ${key}`);
          }
        }
      });
    } catch (error) {
      this.error('清理缓存失败', error);
    }
  }

  async initialize() {
    return this.performanceMonitor('侧边栏初始化', async () => {
      this.log('初始化Edge风格侧边栏');
      
      try {
        // 检查是否可以注入
        if (!this.canInject()) {
          this.log('无法在此页面注入侧边栏');
          return;
        }

        // 清理过期缓存
        this.cleanupStateCache();

        // 恢复保存的状态
        const savedState = this.loadState();
        this.isExpanded = savedState.expanded;
        this.isEnabled = savedState.enabled;
        
        this.log(`状态恢复完成: 展开=${this.isExpanded}, 启用=${this.isEnabled}`);

        // 加载设置
        await this.safeAsync(() => this.loadSettings(), '加载设置');
        
        if (!this.isEnabled) {
          this.log('侧边栏已禁用');
          return;
        }

        // 创建侧边栏
        this.createSidebar();
        
        // 开始数据更新
        this.startDataUpdates();
        
        // 设置消息监听
        this.setupMessageListener();
        
        // 保存当前状态
        this.saveState();
        
        this.log('Edge风格侧边栏初始化成功');
      } catch (error) {
        this.error('侧边栏初始化失败', error);
      }
    });
  }

  canInject() {
    const url = window.location.href;
    return !url.startsWith('chrome://') && 
           !url.startsWith('chrome-extension://') &&
           !url.startsWith('moz-extension://') &&
           !url.startsWith('about:') &&
           !url.startsWith('file://');
  }

  async loadSettings() {
    try {
      // 检查Chrome API是否可用
      if (!chrome || !chrome.runtime) {
        console.log('Chrome API不可用，使用默认设置');
        this.settings = { enabled: true, expandDelay: 100, collapseDelay: 100 };
        this.isEnabled = true;
        return;
      }

      // 加载扩展设置
      const response = await chrome.runtime.sendMessage({
        type: 'GET_SETTINGS',
        timestamp: Date.now()
      });

      if (response && response.success) {
        this.settings = response.data || {};
        this.isEnabled = this.settings.enabled !== false;
      } else {
        this.settings = { enabled: true, expandDuration: 300, collapseDuration: 300 };
        this.isEnabled = true;
      }
      
      console.log('加载设置完成');
    } catch (error) {
      console.error('加载设置失败:', error);
      this.settings = { enabled: true, expandDuration: 300, collapseDuration: 300 };
      this.isEnabled = true;
    }
  }

  createSidebar() {
    // 防止重复创建
    if (this.sidebarElement) {
      this.log('侧边栏已存在，跳过创建');
      return;
    }

    // 移除现有侧边栏和包装器
    const existing = document.getElementById('edge-vertical-sidebar');
    if (existing) {
      existing.remove();
    }
    const existingWrapper = document.getElementById('edge-sidebar-wrapper');
    if (existingWrapper) {
      existingWrapper.remove();
    }

    // 首先初始化页面布局调整（添加CSS样式）
    this.initializePageLayout();

    // 创建侧边栏容器
    this.sidebarElement = document.createElement('div');
    this.sidebarElement.id = 'edge-vertical-sidebar';
    
    // 根据恢复的状态设置初始类名
    const initialClass = this.isExpanded ? 'sidebar-expanded' : 'sidebar-collapsed';
    this.sidebarElement.className = initialClass;
    
    // 使用DOM API创建结构，更安全且性能更好
    this.createSidebarStructure();

    // 使用Flex布局模式，侧边栏会被添加到包装器中
    // 这会在updatePageLayout中完成
    document.body.appendChild(this.sidebarElement);
    
    // 根据恢复的状态初始化布局
    this.updatePageLayout(this.isExpanded);
    
    // 禁用侧边栏进入动画，直接显示，确保无闪烁
    if (this.sidebarElement) {
      this.sidebarElement.style.transform = 'translateX(0)';
      this.sidebarElement.style.opacity = '1';
      this.sidebarElement.style.transition = 'none'; // 暂时禁用过渡动画
      
      // 延迟重新启用动画，确保初始状态稳定
      setTimeout(() => {
        if (this.sidebarElement) {
          this.sidebarElement.style.transition = '';
        }
      }, 100);
      
      this.log(`侧边栏创建完成，恢复状态: ${initialClass}`);
    }

    // 设置事件监听
    this.setupSidebarEvents();
  }

  /**
   * 创建侧边栏DOM结构
   * 使用DOM API而不是innerHTML提高安全性和性能
   */
  createSidebarStructure() {
    // 创建主要容器
    const sidebarContent = document.createElement('div');
    sidebarContent.className = 'sidebar-content';

    // 创建底部切换按钮区域
    const sidebarFooter = document.createElement('div');
    sidebarFooter.className = 'sidebar-footer';

    const toggleButton = document.createElement('button');
    toggleButton.className = 'toggle-button';
    toggleButton.id = 'toggle-button';
    toggleButton.title = '展开/收起侧边栏';

    const toggleIcon = document.createElement('span');
    toggleIcon.className = 'toggle-icon';
    toggleIcon.textContent = '«';

    toggleButton.appendChild(toggleIcon);
    sidebarFooter.appendChild(toggleButton);

    // 创建主要内容区域
    const mainContent = document.createElement('div');
    mainContent.className = 'sidebar-main-content';

    const sidebarMain = document.createElement('div');
    sidebarMain.className = 'sidebar-main';
    sidebarMain.id = 'sidebar-main';

    // 创建空状态
    const emptyState = document.createElement('div');
    emptyState.className = 'empty-state';

    const emptyIcon = document.createElement('div');
    emptyIcon.className = 'empty-icon';
    emptyIcon.textContent = '📑';

    const emptyText = document.createElement('div');
    emptyText.className = 'empty-text';
    emptyText.textContent = '正在加载...';

    emptyState.appendChild(emptyIcon);
    emptyState.appendChild(emptyText);
    sidebarMain.appendChild(emptyState);
    mainContent.appendChild(sidebarMain);

    // 组装结构
    sidebarContent.appendChild(sidebarFooter);
    sidebarContent.appendChild(mainContent);
    this.sidebarElement.appendChild(sidebarContent);
  }

  /**
   * 初始化页面布局调整
   */
  initializePageLayout() {
    // 清理之前可能存在的布局
    this.removeFlexLayout();
    
    // 设置页面布局为收起状态（使用简化的margin布局）
    console.log('🎬 初始化页面布局为收起状态');
    this.updatePageLayout(false);
  }

  /**
   * 添加Flex布局样式 - 侧边栏和网页并排显示
   */
  addDynamicLayoutStyles() {
    // 移除旧的样式（如果存在）
    const existingStyle = document.getElementById('dynamic-sidebar-styles');
    if (existingStyle) {
      existingStyle.remove();
      console.log('🧹 已移除旧的动态样式');
    }
    
    const style = document.createElement('style');
    style.id = 'dynamic-sidebar-styles';
    style.textContent = `
      /* Flex布局容器样式 */
      #edge-sidebar-wrapper {
        display: flex !important;
        flex-direction: row !important;
        width: 100vw !important;
        height: 100vh !important;
        position: relative !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      
      /* 确保body和html没有默认间距 */
      html, body.edge-flex-layout {
        margin: 0 !important;
        padding: 0 !important;
        height: 100% !important;
      }
      
      body.edge-flex-layout {
        overflow: hidden !important;
      }
      
      /* 侧边栏在Flex容器中的样式 */
      #edge-sidebar-wrapper #edge-vertical-sidebar {
        position: relative !important;
        top: 0 !important;
        left: 0 !important;
        height: 100vh !important;
        flex-shrink: 0 !important;
        z-index: auto !important;
        box-sizing: border-box !important;
      }
      
      /* 网页内容容器 */
      #edge-webpage-content {
        flex: 1 !important;
        height: 100vh !important;
        overflow: auto !important;
        position: relative !important;
        box-sizing: border-box !important;
      }
      
      /* 确保原始内容正常显示 */
      #edge-webpage-content > * {
        position: relative !important;
      }
    `;
    
    document.head.appendChild(style);
    console.log('📐 Flex布局样式已注入 - 侧边栏和网页并排显示');
  }

  /**
   * 更新页面布局 - Flex布局模式
   */
  updatePageLayout(isExpanded) {
    try {
      const state = isExpanded ? 'expanded' : 'collapsed';
      const sidebarWidth = isExpanded ? '320px' : '48px';
      
      console.log(`🔄 设置智能布局模式：${state}状态`);
      
      // 移除复杂的Flex布局，使用简单的margin布局
      this.removeFlexLayout();
      
      // 设置侧边栏样式
      if (this.sidebarElement) {
        this.sidebarElement.classList.remove('sidebar-collapsed', 'sidebar-expanded');
        this.sidebarElement.classList.add(`sidebar-${state}`);
        console.log(`📏 侧边栏切换到：${state}状态`);
      }
      
      // 检测页面布局类型并应用相应的margin策略
      const layoutInfo = this.detectPageLayoutType();
      console.log(`🎯 页面布局类型：${layoutInfo.description}`);
      
      // 清理之前的margin设置
      this.clearPreviousMargins();
      
      // 根据检测到的布局类型设置margin
      this.applyMarginByLayoutType(layoutInfo, sidebarWidth);
      
      // 确保html根元素没有意外的padding或margin
      document.documentElement.style.margin = '0';
      document.documentElement.style.padding = '0';
      
      this.log(`智能布局模式设置完成：${state}，目标元素：${layoutInfo.targetElement.tagName}${layoutInfo.targetElement.className ? '.' + layoutInfo.targetElement.className : ''}`);
      
    } catch (error) {
      console.error('❌ 更新页面布局失败:', error);
      this.fallbackLayoutUpdate(isExpanded);
    }
  }

  /**
   * 清理之前的margin设置
   */
  clearPreviousMargins() {
    // 清理body的margin
    document.body.style.marginLeft = '';
    
    // 清理常见容器的margin
    const containers = document.querySelectorAll(
      '.el-container, #app, #root, .app, main, .main, .container, .wrapper'
    );
    containers.forEach(container => {
      if (container.style.marginLeft) {
        container.style.marginLeft = '';
      }
    });
  }

  /**
   * 根据布局类型应用margin
   */
  applyMarginByLayoutType(layoutInfo, sidebarWidth) {
    const targetElement = layoutInfo.targetElement;

    // 检测是否为顽固网站，如果是则使用DOM操作方案
    if (this.isStubbornWebsite()) {
      console.log('🚨 检测到顽固网站，使用DOM操作方案');
      this.applyDOMManipulationLayout(sidebarWidth);
      return;
    }

    switch (layoutInfo.type) {
      case 'vue-spa-absolute':
      case 'spa-absolute':
      case 'content-absolute':
        // 对于绝对定位的容器，设置margin-left
        targetElement.style.marginLeft = sidebarWidth;
        targetElement.style.transition = 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        console.log(`📦 对绝对定位容器应用margin: ${sidebarWidth}`);
        break;

      case 'centered-fixed-width':
        // 对于固定宽度居中的容器，可能需要调整left定位
        targetElement.style.marginLeft = sidebarWidth;
        targetElement.style.transition = 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        console.log(`📦 对固定宽度容器应用margin: ${sidebarWidth}`);
        break;

      case 'traditional':
      default:
        // 传统方式：对body应用margin
        document.body.style.marginLeft = sidebarWidth;
        document.body.style.transition = 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        document.body.style.marginTop = '0';
        document.body.style.marginRight = '0';
        document.body.style.marginBottom = '0';
        document.body.style.paddingTop = '0';
        console.log(`📦 对body应用margin: ${sidebarWidth}`);
        break;
    }
  }

  /**
   * 检测是否为顽固网站
   */
  isStubbornWebsite() {
    const hostname = window.location.hostname;
    const stubbornSites = [
      'gaccode.com',
      'claude.ai',
      'chat.openai.com',
      'github.com'
    ];

    return stubbornSites.some(site => hostname.includes(site));
  }

  /**
   * 检测是否为claude.ai网站
   */
  isClaudeAI() {
    return window.location.hostname.includes('claude.ai');
  }

  /**
   * 获取claude.ai左侧工具栏的宽度
   */
  getClaudeToolbarWidth() {
    // 根据调试结果，claude.ai的工具栏是一个NAV元素
    // 查找所有左侧固定的NAV元素
    const navElements = document.querySelectorAll('nav');

    for (const nav of navElements) {
      // 不使用isSidebarElement检查，因为它现在会排除NAV元素
      if (nav.id !== 'edge-vertical-sidebar' && !nav.className.includes('edge-vertical')) {
        const rect = nav.getBoundingClientRect();
        const style = window.getComputedStyle(nav);

        // 检查是否为左侧工具栏：固定定位、在左侧、合理宽度
        if ((style.position === 'fixed' || style.position === 'absolute') &&
            rect.left <= 10 &&
            rect.width > 20 &&
            rect.width < 200 &&
            rect.height > 100) { // 高度足够，说明是主要工具栏

          console.log(`🔍 检测到claude.ai工具栏: NAV元素, 宽度: ${rect.width}px`);
          return Math.round(rect.width);
        }
      }
    }

    console.log('🔍 未检测到claude.ai工具栏，使用默认值');
    return 0;
  }

  /**
   * 为claude.ai调整侧边栏位置
   */
  adjustSidebarForClaudeAI(claudeToolbarWidth, sidebarWidth) {
    if (!this.sidebarElement) {
      console.warn('⚠️ 侧边栏元素不存在，无法调整位置');
      return;
    }

    try {
      // 计算侧边栏应该的位置：claude.ai工具栏宽度 + 一点间距
      const sidebarLeft = claudeToolbarWidth + 5; // 5px间距

      console.log(`🎭 调整侧边栏位置: 从 left:0 到 left:${sidebarLeft}px`);

      // 调整侧边栏位置
      this.sidebarElement.style.setProperty('left', `${sidebarLeft}px`, 'important');

      // 保存原始位置以便恢复
      if (!this.originalSidebarLeft) {
        this.originalSidebarLeft = this.sidebarElement.style.left || '0px';
      }

      console.log(`✅ 侧边栏位置已调整到 ${sidebarLeft}px`);

    } catch (error) {
      console.error('❌ 调整侧边栏位置失败:', error);
    }
  }

  /**
   * 恢复侧边栏原始位置
   */
  restoreSidebarPosition() {
    if (this.sidebarElement && this.originalSidebarLeft !== undefined) {
      try {
        console.log('🔄 恢复侧边栏原始位置');
        this.sidebarElement.style.left = this.originalSidebarLeft;
        this.originalSidebarLeft = undefined;
      } catch (error) {
        console.error('❌ 恢复侧边栏位置失败:', error);
      }
    }
  }

  /**
   * DOM操作布局方案 - 针对顽固网站（优化版）
   */
  applyDOMManipulationLayout(sidebarWidth) {
    try {
      console.log('💥 应用DOM操作布局方案（优化版）');

      const sidebarWidthNum = parseInt(sidebarWidth);
      let elementsModified = 0;

      // 保存原始样式（简化版）
      if (!this.originalStyles) {
        this.originalStyles = new Map();
      }

      // 使用目标宽度，并为claude.ai做特殊处理
      let actualSidebarWidth = sidebarWidthNum;
      let claudeToolbarWidth = 0;

      if (this.isClaudeAI()) {
        claudeToolbarWidth = this.getClaudeToolbarWidth();
        console.log(`🎭 Claude.ai特殊处理: 工具栏宽度 ${claudeToolbarWidth}px, 侧边栏宽度 ${actualSidebarWidth}px`);
      }

      console.log(`📏 使用目标宽度: ${actualSidebarWidth}px`);

      // 策略1: 修改body，为claude.ai做特殊处理
      this.saveElementStyle(document.body, ['margin-left', 'width', 'max-width']);

      if (this.isClaudeAI()) {
        // claude.ai特殊处理：调整我们的侧边栏位置，而不是页面内容
        console.log(`🎭 Claude.ai特殊处理: 调整侧边栏位置以避开工具栏`);
        this.adjustSidebarForClaudeAI(claudeToolbarWidth, actualSidebarWidth);

        // 页面内容只需要为我们的侧边栏让出空间
        document.body.style.setProperty('margin-left', `${actualSidebarWidth}px`, 'important');
        document.body.style.setProperty('width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
        document.body.style.setProperty('max-width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
      } else {
        // 其他网站的正常处理
        document.body.style.setProperty('margin-left', `${actualSidebarWidth}px`, 'important');
        document.body.style.setProperty('width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
        document.body.style.setProperty('max-width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
      }

      document.body.style.setProperty('box-sizing', 'border-box', 'important');
      elementsModified++;

      // 策略2: 只修改顶级容器，避免过度修改
      const topLevelContainers = this.findTopLevelContainers();
      topLevelContainers.forEach(container => {
        if (!this.isSidebarElement(container) && this.shouldModifyElement(container)) {
          this.saveElementStyle(container, ['margin-left', 'width', 'max-width', 'left']);

          const computedStyle = window.getComputedStyle(container);

          // 只修改真正需要修改的元素
          if (computedStyle.width === '100vw' ||
              computedStyle.width === '100%' ||
              container.style.width === '100vw' ||
              container.style.width === '100%') {

            if (this.isClaudeAI()) {
              // claude.ai特殊处理
              const totalLeftSpace = actualSidebarWidth + claudeToolbarWidth;
              container.style.setProperty('width', `calc(100vw - ${totalLeftSpace}px)`, 'important');
              container.style.setProperty('max-width', `calc(100vw - ${totalLeftSpace}px)`, 'important');

              if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
                container.style.setProperty('left', `${totalLeftSpace}px`, 'important');
              } else {
                container.style.setProperty('margin-left', `${totalLeftSpace}px`, 'important');
              }
            } else {
              // 其他网站正常处理
              container.style.setProperty('width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
              container.style.setProperty('max-width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');

              if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
                container.style.setProperty('left', `${actualSidebarWidth}px`, 'important');
              } else {
                container.style.setProperty('margin-left', `${actualSidebarWidth}px`, 'important');
              }
            }

            elementsModified++;
            console.log(`📦 修改了容器: ${container.tagName}${container.className ? '.' + container.className.split(' ')[0] : ''}`);
          }
        }
      });

      // 策略3: 针对明确使用100vw的元素
      const fullWidthElements = document.querySelectorAll('[style*="100vw"]');
      fullWidthElements.forEach(element => {
        if (!this.isSidebarElement(element) && !this.isChildOfModifiedElement(element)) {
          this.saveElementStyle(element, ['width', 'margin-left', 'max-width']);

          element.style.setProperty('width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');
          element.style.setProperty('margin-left', `${actualSidebarWidth}px`, 'important');
          element.style.setProperty('max-width', `calc(100vw - ${actualSidebarWidth}px)`, 'important');

          elementsModified++;
          console.log(`📦 修改了100vw元素: ${element.tagName}`);
        }
      });

      // 强制布局重新计算
      document.body.offsetHeight;

      console.log(`💥 DOM操作完成，修改了 ${elementsModified} 个元素`);

      // 设置调试标记
      window.vTabsDebug = {
        domManipulation: true,
        elementsModified: elementsModified,
        sidebarWidth: actualSidebarWidth,
        targetWidth: sidebarWidthNum
      };

    } catch (error) {
      console.error('❌ DOM操作布局方案失败:', error);
    }
  }

  /**
   * 获取侧边栏的目标宽度（基于当前状态）
   */
  getTargetSidebarWidth() {
    // 直接根据状态返回目标宽度，不依赖DOM测量
    return this.isExpanded ? 320 : 48;
  }

  /**
   * 找到顶级容器，避免修改过多元素
   */
  findTopLevelContainers() {
    const containers = [];

    // 直接body子元素
    const bodyChildren = Array.from(document.body.children);
    bodyChildren.forEach(child => {
      if (!this.isSidebarElement(child)) {
        containers.push(child);
      }
    });

    // 常见的主要容器
    const mainSelectors = ['#app', '#root', 'main', '.main'];
    mainSelectors.forEach(selector => {
      const element = document.querySelector(selector);
      if (element && !containers.includes(element) && !this.isSidebarElement(element)) {
        containers.push(element);
      }
    });

    return containers;
  }

  /**
   * 判断是否应该修改这个元素
   */
  shouldModifyElement(element) {
    // 跳过小元素
    const rect = element.getBoundingClientRect();
    if (rect.width < 200 || rect.height < 100) {
      return false;
    }

    // 跳过隐藏元素
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
      return false;
    }

    // 跳过内联元素
    if (computedStyle.display === 'inline') {
      return false;
    }

    return true;
  }

  /**
   * 检查元素是否是已修改元素的子元素
   */
  isChildOfModifiedElement(element) {
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      if (this.originalStyles.has(parent)) {
        return true;
      }
      parent = parent.parentElement;
    }
    return false;
  }

  /**
   * 保存元素原始样式
   */
  saveElementStyle(element, properties) {
    if (!this.originalStyles.has(element)) {
      this.originalStyles.set(element, new Map());
    }

    const styleMap = this.originalStyles.get(element);
    properties.forEach(property => {
      if (!styleMap.has(property)) {
        styleMap.set(property, element.style.getPropertyValue(property) || '');
      }
    });
  }

  /**
   * 检查是否为侧边栏元素或需要保护的元素
   */
  isSidebarElement(element) {
    // 安全地获取id和className
    const id = element.id ? String(element.id).toLowerCase() : '';
    const className = element.className ? String(element.className).toLowerCase() : '';

    // 检查是否为我们的侧边栏
    if (id.includes('sidebar') ||
        className.includes('sidebar') ||
        id.includes('edge-vertical') ||
        element === this.sidebarElement) {
      return true;
    }

    // claude.ai特殊保护：只保护左侧工具栏本身
    if (this.isClaudeAI()) {
      // 只保护NAV元素本身（claude.ai的主工具栏）
      if (element.tagName === 'NAV') {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);

        if ((style.position === 'fixed' || style.position === 'absolute') &&
            rect.left <= 10 &&
            rect.width > 20 &&
            rect.width < 200 &&
            rect.height > 100) {
          console.log(`🛡️ 保护claude.ai NAV工具栏: 宽度${rect.width}px`);
          return true;
        }
      }

      // 不保护NAV的子元素，让页面内容可以被调整
    }

    return false;
  }
  
  /**
   * 检测页面布局类型，选择最佳的margin应用策略
   */
  detectPageLayoutType() {
    const layoutInfo = {
      type: 'traditional',
      targetElement: document.body,
      description: '传统布局'
    };

    // 检测Vue.js/Element UI应用（如写作猫）
    const vueSpaContainers = document.querySelectorAll('.el-container.fullscreen, .el-container[class*="fullscreen"]');
    if (vueSpaContainers.length > 0) {
      const container = vueSpaContainers[0];
      const containerStyle = window.getComputedStyle(container);
      
      if (containerStyle.position === 'absolute' || containerStyle.position === 'fixed') {
        layoutInfo.type = 'vue-spa-absolute';
        layoutInfo.targetElement = container;
        layoutInfo.description = 'Vue.js SPA 绝对定位布局';
        this.log('检测到Vue.js SPA绝对定位布局');
        return layoutInfo;
      }
    }

    // 检测其他SPA应用容器
    const spaContainers = document.querySelectorAll('#app, #root, .app, .application, .v-application, .ant-layout');
    for (let container of spaContainers) {
      const containerStyle = window.getComputedStyle(container);
      if (containerStyle.position === 'absolute' || containerStyle.position === 'fixed') {
        layoutInfo.type = 'spa-absolute';
        layoutInfo.targetElement = container;
        layoutInfo.description = 'SPA应用绝对定位布局';
        this.log('检测到SPA绝对定位布局');
        return layoutInfo;
      }
    }

    // 检测固定宽度居中布局容器
    const centeredContainers = document.querySelectorAll('.container, .wrapper, .main-container, .page-container, [class*="main-wrapper"]');
    for (let container of centeredContainers) {
      const containerStyle = window.getComputedStyle(container);
      const containerWidth = parseInt(containerStyle.width);
      
      // 如果容器有固定宽度且小于视口宽度
      if (containerWidth && containerWidth < window.innerWidth && containerStyle.position !== 'absolute') {
        layoutInfo.type = 'centered-fixed-width';
        layoutInfo.targetElement = container;
        layoutInfo.description = '固定宽度居中布局';
        this.log('检测到固定宽度居中布局');
        return layoutInfo;
      }
    }

    // 检测绝对定位的主要内容容器
    const absoluteContainers = document.querySelectorAll('main, .main, #main, [class*="fullscreen"], [class*="full-screen"]');
    for (let container of absoluteContainers) {
      const containerStyle = window.getComputedStyle(container);
      if (containerStyle.position === 'absolute' || containerStyle.position === 'fixed') {
        layoutInfo.type = 'content-absolute';
        layoutInfo.targetElement = container;
        layoutInfo.description = '主内容绝对定位布局';
        this.log('检测到主内容绝对定位布局');
        return layoutInfo;
      }
    }

    this.log('使用传统body布局');
    return layoutInfo;
  }

  /**
   * 移除复杂的Flex布局，恢复简单布局
   */
  removeFlexLayout() {
    // 移除Flex包装器（如果存在）
    const wrapper = document.getElementById('edge-sidebar-wrapper');
    if (wrapper) {
      const webpageContent = document.getElementById('edge-webpage-content');
      if (webpageContent) {
        // 将网页内容移回body
        const contentChildren = Array.from(webpageContent.children);
        contentChildren.forEach(child => {
          document.body.appendChild(child);
        });
      }
      
      // 移除包装器
      wrapper.remove();
      console.log('🧹 已移除Flex布局包装器');
    }
    
    // 移除body上的Flex布局类
    document.body.classList.remove('edge-flex-layout');
    
    // 移除动态样式
    const existingStyle = document.getElementById('dynamic-sidebar-styles');
    if (existingStyle) {
      existingStyle.remove();
      console.log('🧹 已移除动态Flex布局样式');
    }
    
    // 清理可能存在的旧margin设置
    this.clearPreviousMargins();
  }

  /**
   * 确保Flex包装器存在（已弃用，保留以防兼容性问题）
   */
  ensureFlexWrapper() {
    let wrapper = document.getElementById('edge-sidebar-wrapper');
    
    if (!wrapper) {
      console.log('🔨 创建Flex布局包装器');
      
      // 创建包装器
      wrapper = document.createElement('div');
      wrapper.id = 'edge-sidebar-wrapper';
      
      // 创建网页内容容器
      const webpageContent = document.createElement('div');
      webpageContent.id = 'edge-webpage-content';
      
      // 将原始body的所有子元素移动到网页内容容器中
      const bodyChildren = Array.from(document.body.children);
      bodyChildren.forEach(child => {
        if (child.id !== 'edge-vertical-sidebar' && child.id !== 'edge-sidebar-wrapper') {
          webpageContent.appendChild(child);
        }
      });
      
      // 先将侧边栏添加到包装器中
      if (this.sidebarElement && this.sidebarElement.parentNode === document.body) {
        wrapper.appendChild(this.sidebarElement);
        console.log('📎 侧边栏已移动到Flex包装器中');
      }
      
      // 添加网页内容容器
      wrapper.appendChild(webpageContent);
      
      // 将包装器添加到body
      document.body.appendChild(wrapper);
      
      this.log('Flex布局包装器创建完成');
      this.log(`包装器子元素数量: ${wrapper.children.length}`);
      this.log(`网页内容容器子元素数量: ${webpageContent.children.length}`);
    } else {
      // 包装器已存在，确保侧边栏在其中
      if (this.sidebarElement && this.sidebarElement.parentNode !== wrapper) {
        // 将侧边栏移动到包装器的开头
        wrapper.insertBefore(this.sidebarElement, wrapper.firstChild);
        console.log('📎 侧边栏已移动到现有Flex包装器中');
      }
    }
    
    return wrapper;
  }
  
  /**
   * 深度清理所有边距 - 终极方案
   */
  deepCleanMargins() {
    console.log('🧹 执行深度边距清理');
    
    try {
      // 清理所有可能的元素
      const elementsToClean = [document.body, document.documentElement, document.querySelector('html')];
      elementsToClean.forEach(el => {
        if (el) {
          el.style.removeProperty('margin-left');
          el.style.removeProperty('padding-left');
          el.style.removeProperty('margin');
          el.style.removeProperty('padding');
          el.style.setProperty('margin-left', '0px', 'important');
        }
      });
      
      // 移除所有可能的CSS样式表中的边距规则
      const stylesheets = document.styleSheets;
      for (let i = 0; i < stylesheets.length; i++) {
        try {
          const sheet = stylesheets[i];
          if (sheet.href && sheet.href.includes('edge-sidebar')) {
            continue; // 跳过我们自己的样式表
          }
          // 尝试找到并禁用可能的边距规则（安全检查）
        } catch (e) {
          // 忽略跨域样式表错误
        }
      }
      
      // 创建覆盖样式确保无边距
      const overrideStyle = document.createElement('style');
      overrideStyle.id = 'edge-sidebar-margin-override';
      overrideStyle.textContent = `
        body { margin-left: 0 !important; padding-left: 0 !important; }
        html { margin-left: 0 !important; padding-left: 0 !important; }
        * { box-sizing: border-box; }
      `;
      
      // 移除之前的覆盖样式
      const existingOverride = document.getElementById('edge-sidebar-margin-override');
      if (existingOverride) {
        existingOverride.remove();
      }
      
      document.head.appendChild(overrideStyle);
      console.log('✨ 深度清理完成，已添加强制覆盖样式');
      
    } catch (error) {
      console.error('❌ 深度清理失败:', error);
    }
  }
  
  /**
   * 备用布局更新方案 - Flex布局模式
   */
  fallbackLayoutUpdate(isExpanded) {
    console.log(`🚨 使用Flex布局备用方案：${isExpanded ? '展开' : '收起'}`);
    try {
      // 确保body使用Flex布局类
      document.body.classList.add('edge-flex-layout');
      
      // 尝试基本的侧边栏状态切换
      if (this.sidebarElement) {
        this.sidebarElement.classList.remove('sidebar-collapsed', 'sidebar-expanded');
        this.sidebarElement.classList.add(`sidebar-${isExpanded ? 'expanded' : 'collapsed'}`);
      }
      
      this.log('备用方案：已设置基础Flex布局');
    } catch (error) {
      console.error('❌ 备用方案也失败了:', error);
      // 最后的最后，确保侧边栏至少可以显示
      if (this.sidebarElement) {
        this.sidebarElement.style.display = 'block';
      }
    }
  }

  setupSidebarEvents() {
    if (!this.sidebarElement) return;

    // 阻止事件冒泡
    this.sidebarElement.addEventListener('click', this.clickHandler);

    // 统一的切换按钮事件
    const toggleButton = document.getElementById('toggle-button');
    if (toggleButton) {
      toggleButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleClickHandler(e);
      });
    }

    // 移除窄视图下点击任意地方展开的行为
    // 现在只有点击展开按钮才会展开

    // 性能优化：在动画期间减少更新频率
    this.sidebarElement.addEventListener('transitionstart', this.transitionStartHandler);
    this.sidebarElement.addEventListener('transitionend', this.transitionEndHandler);
  }

  /**
   * 处理点击事件
   */
  handleClick(e) {
    e.stopPropagation();
  }

  /**
   * 处理过渡动画开始
   */
  handleTransitionStart() {
    this.isAnimating = true;
  }

  /**
   * 处理过渡动画结束
   */
  handleTransitionEnd() {
    this.isAnimating = false;
  }

  /**
   * 处理消息
   */
  handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'TOGGLE_SIDEBAR':
          this.isEnabled = message.enabled !== false;
          if (this.isEnabled) {
            // 如果侧边栏不存在，创建它
            if (!this.sidebarElement) {
              this.createSidebar();
              this.startDataUpdates();
            } else {
              // 如果已存在但隐藏，重新显示
              this.showSidebar();
            }
          } else {
            // 隐藏侧边栏而不是完全销毁
            this.hideSidebar();
          }
          sendResponse({ success: true });
          break;

        case 'SETTINGS_UPDATED':
          this.settings = { ...this.settings, ...message.payload };
          sendResponse({ success: true });
          break;

        case 'TAB_UPDATE_EVENT':
          // 接收到标签页更新事件，刷新数据
          console.log('收到标签页更新事件:', message.eventType, message.eventData);
          this.handleTabUpdateEvent(message.eventType, message.eventData);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: `Unknown message type: ${message.type}` });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }

    return true;
  }

  /**
   * 处理标签页更新事件
   */
  async handleTabUpdateEvent(eventType, eventData) {
    // 如果侧边栏不存在或未初始化，跳过
    if (!this.sidebarElement || !this.isEnabled) {
      return;
    }

    // 如果正在动画中，稍后处理
    if (this.isAnimating) {
      setTimeout(() => {
        this.handleTabUpdateEvent(eventType, eventData);
      }, 300);
      return;
    }

    console.log('处理标签页事件:', eventType);
    
    try {
      // 对于所有事件类型，都重新获取完整数据
      // 这样确保数据一致性，避免本地状态同步问题
      await this.fetchAndUpdateData();
    } catch (error) {
      console.error('处理标签页更新事件失败:', error);
    }
  }

  scheduleExpand() {
    // 清除折叠定时器
    if (this.collapseTimeout) {
      clearTimeout(this.collapseTimeout);
      this.collapseTimeout = null;
    }

    // 如果已经展开，直接返回
    if (this.isExpanded) return;

    // 设置展开定时器
    const delay = this.settings?.expandDelay || 100;
    this.expandTimeout = setTimeout(() => {
      this.expand();
    }, delay);
  }

  scheduleCollapse() {
    // 清除展开定时器
    if (this.expandTimeout) {
      clearTimeout(this.expandTimeout);
      this.expandTimeout = null;
    }

    // 如果已经折叠，直接返回
    if (!this.isExpanded) return;

    // 设置折叠定时器
    const delay = this.settings?.collapseDelay || 100;
    this.collapseTimeout = setTimeout(() => {
      this.collapse();
    }, delay);
  }

  /**
   * 处理切换按钮点击
   */
  handleToggleClick(e) {
    e.stopPropagation();
    
    if (this.isExpanded) {
      this.collapse();
      console.log('通过切换按钮收起侧边栏');
    } else {
      this.expand();
      console.log('通过切换按钮展开侧边栏');
    }
  }



  expand() {
    if (this.isExpanded || !this.sidebarElement) {
      console.log('🚫 展开被取消：已展开或元素不存在');
      return;
    }

    console.log('🔄 开始展开侧边栏...');
    
    // 添加展开动画类
    this.sidebarElement.className = 'sidebar-expanded';
    this.isExpanded = true;
    
    // 延迟更新页面布局，等待CSS动画开始
    setTimeout(() => {
      this.updatePageLayout(true);
    }, 50);
    
    // 保存新状态到localStorage
    this.saveState();
    
    this.log('侧边栏展开完成');
  }

  collapse() {
    if (!this.isExpanded || !this.sidebarElement) {
      console.log('🚫 收起被取消：已收起或元素不存在');
      return;
    }

    console.log('🔄 开始收起侧边栏...');

    this.sidebarElement.className = 'sidebar-collapsed';
    this.isExpanded = false;
    
    // 延迟更新页面布局，等待CSS动画开始
    setTimeout(() => {
      this.updatePageLayout(false);
    }, 50);
    
    // 保存新状态到localStorage
    this.saveState();
    
    this.log('侧边栏收起完成');
  }

  /**
   * 静默重试Chrome扩展操作（无用户感知）
   */
  async silentRetryOperation(type, payload, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        // 如果不是第一次尝试，添加递增的延迟等待上下文恢复
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 200 * i));
        }
        
        const response = await chrome.runtime.sendMessage({
          type,
          payload,
          timestamp: Date.now()
        });
        
        if (response && response.success) {
          // 成功后静默更新相关UI状态
          this.handleSilentRetrySuccess(type, payload, response);
          return response;
        }
      } catch (error) {
        // 静默处理错误，不输出日志干扰
        if (i === maxRetries - 1) {
          // 最后一次失败时完全静默忽略
          return null;
        }
      }
    }
  }

  /**
   * 处理静默重试成功后的UI更新
   */
  handleSilentRetrySuccess(type, payload, response) {
    switch (type) {
      case 'SWITCH_TAB':
        // 标签页切换成功，静默更新UI状态
        const tabElement = document.querySelector(`[data-tab-id="${payload.tabId}"]`);
        if (tabElement) {
          // 移除其他活跃标签的状态
          document.querySelectorAll('.tab-active').forEach(el => {
            el.classList.remove('tab-active');
          });
          // 设置当前标签为活跃状态
          tabElement.classList.add('tab-active');
        }
        break;
      case 'CLOSE_TAB':
        // 标签页关闭成功，静默刷新列表
        this.fetchAndUpdateData();
        break;
      default:
        // 其他操作成功，刷新数据
        this.fetchAndUpdateData();
        break;
    }
  }

  async startDataUpdates() {
    // 立即获取一次数据
    await this.fetchAndUpdateData();
    
    // 减少定时更新频率，主要依赖事件驱动更新
    // 保留一个较长间隔的兜底检查（60秒）以防事件丢失
    this.updateInterval = setInterval(() => {
      this.log('兜底检查：获取标签页数据');
      this.fetchAndUpdateData();
    }, EdgeSidebarManager.CONFIG.UPDATE_INTERVAL);
  }

  async fetchAndUpdateData() {
    // 如果正在动画中，跳过更新以提高性能
    if (this.isAnimating) {
      return;
    }

    try {
      // 检查Chrome API是否可用
      if (!chrome || !chrome.runtime) {
        console.log('Chrome API不可用，使用模拟数据');
        // 设置模拟的空数据以显示基本UI
        this.tabs = [];
        this.tabGroups = [];
        this.renderContent();
        return;
      }

      const response = await chrome.runtime.sendMessage({
        type: 'GET_TABS',
        timestamp: Date.now()
      });

      if (response && response.success && response.data) {
        const newTabs = response.data.tabs || [];
        const newTabGroups = response.data.groups || [];
        
        // 检查数据是否有变化
        const tabsChanged = !this.arraysEqual(this.tabs, newTabs);
        const groupsChanged = !this.arraysEqual(this.tabGroups, newTabGroups);
        
        if (tabsChanged || groupsChanged) {
          console.log('数据发生变化，更新侧边栏:', { tabsChanged, groupsChanged });
          this.tabs = newTabs;
          this.tabGroups = newTabGroups;
          this.renderContent();
        } else {
          console.log('数据无变化，跳过渲染');
        }
      }
    } catch (error) {
      if (error.message.includes('Extension context invalidated')) {
        console.log('扩展上下文失效，停止数据更新');
        if (this.updateInterval) {
          clearInterval(this.updateInterval);
        }
        return;
      }
      console.debug('获取数据失败:', error);
    }
  }

  /**
   * 渲染侧边栏内容
   * TODO: 未来可考虑使用更安全的DOM操作替代innerHTML
   */
  renderContent() {
    const mainElement = document.getElementById('sidebar-main');
    if (!mainElement) {
      this.error('找不到sidebar-main元素');
      return;
    }

    try {
      // 渲染侧边栏内容（统一使用原来的宽视图逻辑）
      const html = this.renderExpandedContent();
      
      // 安全检查：确保html不为空且为字符串
      if (typeof html !== 'string' || html.trim() === '') {
        this.log('渲染内容为空，显示默认状态', 'warn');
        mainElement.innerHTML = '<div class="empty-state"><div class="empty-icon">📑</div><div class="empty-text">暂无内容</div></div>';
        return;
      }
      
      mainElement.innerHTML = html;
      this.setupContentEvents();
      this.log('侧边栏内容渲染完成');
    } catch (error) {
      this.error('渲染侧边栏内容失败', error);
      // 降级处理
      mainElement.innerHTML = '<div class="empty-state"><div class="empty-icon">⚠️</div><div class="empty-text">加载失败</div></div>';
    }
  }

  renderExpandedContent() {
    let html = '';

    // 显示标签分组
    if (this.tabGroups.length > 0) {
      this.tabGroups.forEach(group => {
        const groupTabs = this.tabs.filter(tab => tab.groupId === group.id);
        if (groupTabs.length === 0) return;
        
        const groupColor = this.getChromeGroupColor(group.color);
        const textColor = this.getGroupTextColor(groupColor);
        const isCollapsed = group.collapsed;
        
        html += `<div class="tab-group">`;
        html += `<div class="group-header" data-group-id="${group.id}" style="background: ${groupColor}; color: ${textColor};">`;
        
        const fullTitle = group.title || '未命名分组';
        const firstChar = fullTitle.charAt(0);
        html += `<span class="group-title" data-first-char="${firstChar}">${fullTitle} (${groupTabs.length})</span>`;
        html += `<span class="expand-icon" style="transform: rotate(${isCollapsed ? 90 : 0}deg);">▼</span>`;
        
        html += `</div>`;
        
        html += `<div class="group-content" data-group-id="${group.id}" style="${isCollapsed ? 'max-height: 0; opacity: 0;' : 'max-height: 1000px; opacity: 1;'}">`;
        groupTabs.forEach(tab => {
          const activeClass = tab.active ? ' tab-active' : '';
          html += `<div class="tab-item${activeClass}" data-tab-id="${tab.id}">`;
          if (tab.favIconUrl) {
            html += `<img src="${tab.favIconUrl}" class="tab-favicon" onerror="this.src='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23666%22><path d=%22M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z%22/></svg>'">`;
          } else {
            html += `<div class="tab-favicon-default">🌐</div>`;
          }
          html += `<span class="tab-title">${tab.title}</span>`;
          html += `<button class="tab-close-btn" data-tab-id="${tab.id}" title="关闭标签页">×</button>`;
          html += `</div>`;
        });
        html += `</div></div>`;
      });
    }

    // 显示未分组的标签页
    const ungroupedTabs = this.tabs.filter(tab => !tab.groupId || tab.groupId === -1);
    if (ungroupedTabs.length > 0) {
      html += `<div class="tab-group">`;
      html += `<div class="group-header" style="background: #f8f9fa; color: #333;">`;
      
      html += `<span class="group-title" data-first-char="标">标签页 (${ungroupedTabs.length})</span>`;
      
      html += `</div>`;
      
      html += `<div class="group-content" style="max-height: 1000px; opacity: 1;">`;
      ungroupedTabs.forEach(tab => {
        const activeClass = tab.active ? ' tab-active' : '';
        html += `<div class="tab-item${activeClass}" data-tab-id="${tab.id}">`;
        if (tab.favIconUrl) {
          html += `<img src="${tab.favIconUrl}" class="tab-favicon" onerror="this.src='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23666%22><path d=%22M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z%22/></svg>'">`;
        } else {
          html += `<div class="tab-favicon-default">🌐</div>`;
        }
        html += `<span class="tab-title">${tab.title}</span>`;
        html += `<button class="tab-close-btn" data-tab-id="${tab.id}" title="关闭标签页">×</button>`;
        html += `</div>`;
      });
      html += `</div></div>`;
    }

    // 如果没有任何标签页
    if (this.tabs.length === 0) {
      html = `
        <div class="empty-state">
          <div class="empty-icon">📑</div>
          <div class="empty-text">没有标签页</div>
          <div class="empty-hint">打开一些网页来查看标签页</div>
        </div>
      `;
    }

    return html;
  }


  setupContentEvents() {
    // 分组标题点击事件
    const groupHeaders = document.querySelectorAll('.group-header[data-group-id]');
    groupHeaders.forEach(header => {
      header.addEventListener('click', async (e) => {
        const groupId = parseInt(header.getAttribute('data-group-id'));
        await this.toggleGroup(groupId, header);
      });
    });

    // 标签页点击事件
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(tabItem => {
      tabItem.addEventListener('click', async (e) => {
        const tabId = parseInt(tabItem.getAttribute('data-tab-id'));
        await this.switchToTab(tabId);
      });
    });

    // 关闭按钮点击事件
    const closeButtons = document.querySelectorAll('.tab-close-btn');
    closeButtons.forEach(closeBtn => {
      closeBtn.addEventListener('click', async (e) => {
        // 阻止事件冒泡，避免触发标签页切换
        e.stopPropagation();
        
        const tabId = parseInt(closeBtn.getAttribute('data-tab-id'));
        await this.closeTab(tabId, closeBtn);
      });
    });
  }

  async toggleGroup(groupId, headerElement) {
    try {
      // 立即更新UI动画
      const groupContent = document.querySelector(`.group-content[data-group-id="${groupId}"]`);
      const expandIcon = headerElement.querySelector('.expand-icon');
      
      if (groupContent && expandIcon) {
        const isCurrentlyCollapsed = groupContent.style.maxHeight === '0px';
        
        
        if (isCurrentlyCollapsed) {
          // 展开动画
          groupContent.style.maxHeight = '1000px';
          groupContent.style.opacity = '1';
          expandIcon.style.transform = 'rotate(0deg)';
          
          // 为展开的标签页添加渐入动画
          setTimeout(() => {
            const tabItems = groupContent.querySelectorAll('.tab-item');
          }, 100);
        } else {
          // 折叠动画
          groupContent.style.maxHeight = '0';
          groupContent.style.opacity = '0';
          expandIcon.style.transform = 'rotate(90deg)';
        }
      }
      
      // 调用API切换状态
      const response = await chrome.runtime.sendMessage({
        type: 'TOGGLE_TAB_GROUP',
        payload: { groupId },
        timestamp: Date.now()
      });
      
      if (response && response.success) {
        console.log('分组状态切换成功');
      }
    } catch (error) {
      console.error('切换分组失败:', error);
    }
  }

  async switchToTab(tabId) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'SWITCH_TAB',
        payload: { tabId },
        timestamp: Date.now()
      });
      
      if (response && response.success) {
        // 成功时静默更新UI状态
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
          // 移除其他活跃标签的状态
          document.querySelectorAll('.tab-active').forEach(el => {
            el.classList.remove('tab-active');
          });
          // 设置当前标签为活跃状态
          tabElement.classList.add('tab-active');
        }
      }
    } catch (error) {
      // 检查是否为扩展上下文失效错误
      if (error.message && error.message.includes('Extension context invalidated')) {
        // 静默重试，用户无感知
        await this.silentRetryOperation('SWITCH_TAB', { tabId });
        return;
      }
      
      // 其他错误也静默处理，不显示任何错误反馈
      // 仅在调试模式下记录日志
      if (EdgeSidebarManager.CONFIG.DEBUG_MODE) {
        console.debug('标签页切换失败:', error);
      }
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId, closeButton) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'CLOSE_TAB',
        payload: { tabId },
        timestamp: Date.now()
      });

      if (response && response.success) {
        // 成功时静默刷新标签页列表
        this.fetchAndUpdateData();
      }
    } catch (error) {
      // 检查是否为扩展上下文失效错误
      if (error.message && error.message.includes('Extension context invalidated')) {
        // 静默重试，用户无感知
        await this.silentRetryOperation('CLOSE_TAB', { tabId });
        return;
      }
      
      // 其他错误也静默处理，不显示任何错误反馈
      // 仅在调试模式下记录日志
      if (EdgeSidebarManager.CONFIG.DEBUG_MODE) {
        console.debug('关闭标签页失败:', error);
      }
    }
  }

  getChromeGroupColor(colorName) {
    const chromeGroupColors = {
      'grey': '#5f6368',
      'blue': '#1a73e8', 
      'red': '#d93025',
      'yellow': '#fbbc04',
      'green': '#34a853',
      'pink': '#e91e63',
      'purple': '#9c27b0',
      'cyan': '#00bcd4',
      'orange': '#ff6d01'
    };
    
    return chromeGroupColors[colorName] || '#5f6368';
  }

  getGroupTextColor(hexColor) {
    if (!hexColor || !hexColor.startsWith('#')) {
      return '#ffffff';
    }
    
    const hex = hexColor.replace('#', '');
    let r, g, b;
    
    if (hex.length === 3) {
      r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
      g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
      b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.substr(0, 2), 16);
      g = parseInt(hex.substr(2, 2), 16);
      b = parseInt(hex.substr(4, 2), 16);
    } else {
      return '#ffffff';
    }
    
    const brightness = (r * 0.299 + g * 0.587 + b * 0.114);
    return brightness > 128 ? '#333333' : '#ffffff';
  }

  /**
   * 比较两个数组是否相等（深度比较关键属性）
   */
  arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;
    
    for (let i = 0; i < arr1.length; i++) {
      const item1 = arr1[i];
      const item2 = arr2[i];
      
      // 比较关键属性
      if (item1.id !== item2.id ||
          item1.title !== item2.title ||
          item1.url !== item2.url ||
          item1.favIconUrl !== item2.favIconUrl ||
          item1.active !== item2.active ||
          item1.groupId !== item2.groupId ||
          item1.color !== item2.color ||
          item1.collapsed !== item2.collapsed) {
        return false;
      }
    }
    
    return true;
  }

  setupMessageListener() {
    // 检查Chrome API是否可用
    if (!chrome || !chrome.runtime) {
      console.log('Chrome API不可用，跳过消息监听器设置');
      return;
    }
    
    chrome.runtime.onMessage.addListener(this.messageHandler);
  }

  destroy() {
    // 清理定时器
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    if (this.expandTimeout) {
      clearTimeout(this.expandTimeout);
      this.expandTimeout = null;
    }
    if (this.collapseTimeout) {
      clearTimeout(this.collapseTimeout);
      this.collapseTimeout = null;
    }

    // 重置页面布局
    this.resetPageLayout();

    // 移除事件监听器
    if (this.sidebarElement) {
      // 移除所有事件监听器
      const toggleButton = document.getElementById('toggle-button');
      if (toggleButton) {
        toggleButton.removeEventListener('click', this.toggleClickHandler);
      }
      
      // 移除侧边栏事件监听器
      // this.sidebarElement.removeEventListener('mouseenter', this.mouseEnterHandler);
      // this.sidebarElement.removeEventListener('mouseleave', this.mouseLeaveHandler);
      this.sidebarElement.removeEventListener('click', this.clickHandler);
      this.sidebarElement.removeEventListener('transitionstart', this.transitionStartHandler);
      this.sidebarElement.removeEventListener('transitionend', this.transitionEndHandler);
      
      // 移除侧边栏
      this.sidebarElement.remove();
      this.sidebarElement = null;
    }

    // 清理消息监听器
    if (chrome.runtime.onMessage.hasListener(this.messageHandler)) {
      chrome.runtime.onMessage.removeListener(this.messageHandler);
    }

    this.isExpanded = false;
    this.settings = null;
  }

  /**
   * 显示侧边栏
   */
  showSidebar() {
    if (!this.sidebarElement) {
      console.log('侧边栏元素不存在，无法显示');
      return;
    }
    
    // 确保侧边栏在DOM中
    if (!document.body.contains(this.sidebarElement)) {
      document.body.appendChild(this.sidebarElement);
    }
    
    // 重新初始化页面布局
    this.initializePageLayout();
    
    // 禁用显示动画，直接显示
    if (this.sidebarElement) {
      this.sidebarElement.style.transform = 'translateX(0)';
      this.sidebarElement.style.opacity = '1';
      this.sidebarElement.style.display = 'block';
    }
    
    // 重新开始数据更新
    if (!this.updateInterval) {
      this.startDataUpdates();
    }
    
    console.log('侧边栏已重新显示');
  }

  /**
   * 隐藏侧边栏
   */
  hideSidebar() {
    if (!this.sidebarElement) {
      console.log('侧边栏元素不存在');
      return;
    }
    
    // 停止数据更新
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    // 重置页面布局
    this.resetPageLayout();
    
    // 禁用隐藏动画，直接隐藏
    this.sidebarElement.style.display = 'none';
    
    console.log('侧边栏已隐藏');
  }

  /**
   * 重置页面布局
   */
  resetPageLayout() {
    try {
      // 移除数据属性
      document.body.removeAttribute('data-sidebar-dynamic');
      document.body.removeAttribute('data-sidebar-state');

      // 恢复DOM操作的修改
      this.restoreDOMManipulation();

      // 恢复侧边栏位置（claude.ai特殊处理）
      this.restoreSidebarPosition();

      // 重置margin
      document.body.style.marginLeft = '';

      // 移除动态样式
      const dynamicStyles = document.getElementById('dynamic-sidebar-styles');
      if (dynamicStyles) {
        dynamicStyles.remove();
      }

      // 清除调试标记
      if (window.vTabsDebug) {
        delete window.vTabsDebug;
      }

      console.log('页面布局已重置');
    } catch (error) {
      console.error('重置页面布局失败:', error);
    }
  }

  /**
   * 恢复DOM操作的修改
   */
  restoreDOMManipulation() {
    if (!this.originalStyles) {
      return;
    }

    try {
      console.log('🔄 恢复DOM操作的修改');

      for (const [element, styleMap] of this.originalStyles) {
        for (const [property, value] of styleMap) {
          if (value) {
            element.style.setProperty(property, value);
          } else {
            element.style.removeProperty(property);
          }
        }
      }

      this.originalStyles.clear();
      console.log('✅ DOM操作修改已恢复');

    } catch (error) {
      console.error('❌ 恢复DOM操作失败:', error);
    }
  }
}

// 全局侧边栏管理器实例
let edgeSidebarManager = null;

/**
 * 早期侧边栏占位符注入 - 在页面开始加载时立即执行
 * 这能避免页面刷新时的布局闪烁
 */
function injectEarlySidebarPlaceholder() {
  try {
    // 检查是否已存在侧边栏
    if (document.getElementById('edge-vertical-sidebar')) {
      console.log('[Early Inject] 侧边栏已存在，跳过早期注入');
      return;
    }

    // 从localStorage快速恢复状态
    let isExpanded = false;
    try {
      const stateStr = localStorage.getItem('edgeSidebar_state');
      if (stateStr) {
        const state = JSON.parse(stateStr);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        
        if (state.timestamp && (now - state.timestamp) <= maxAge) {
          isExpanded = Boolean(state.expanded);
        }
      }
    } catch (e) {
      console.warn('[Early Inject] 快速状态恢复失败，使用默认状态');
    }

    // 检测暗黑模式
    const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // 创建最简单的侧边栏占位符
    const placeholder = document.createElement('div');
    placeholder.id = 'edge-vertical-sidebar-placeholder';
    
    // 根据主题和展开状态设置样式
    const lightBg = isExpanded ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 0.95)';
    const darkBg = isExpanded ? 'rgba(30, 30, 30, 0.98)' : 'rgba(30, 30, 30, 0.95)';
    const borderColor = isDarkMode ? '#404040' : '#e1e4e8';
    
    placeholder.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      height: 100vh !important;
      width: ${isExpanded ? '320px' : '48px'} !important;
      z-index: 2147483647 !important;
      background: ${isDarkMode ? darkBg : lightBg} !important;
      backdrop-filter: blur(${isExpanded ? '20px' : '10px'}) !important;
      border-right: 1px solid ${borderColor} !important;
      transition: none !important;
      pointer-events: none !important;
    `;

    // 立即添加到body
    if (document.body) {
      document.body.appendChild(placeholder);
    } else {
      // 如果body还不存在，等待并添加
      const observer = new MutationObserver((mutations, obs) => {
        if (document.body) {
          document.body.appendChild(placeholder);
          obs.disconnect();
        }
      });
      observer.observe(document.documentElement, { childList: true });
    }

    // 立即应用页面margin，避免布局跳动
    const sidebarWidth = isExpanded ? '320px' : '48px';
    if (document.body) {
      document.body.style.marginLeft = sidebarWidth;
      document.body.style.transition = 'none';
    }

    console.log(`[Early Inject] 早期侧边栏占位符注入完成 (${isExpanded ? '展开' : '收起'}状态)`);
  } catch (error) {
    console.error('[Early Inject] 早期注入失败:', error);
  }
}

// 立即执行早期注入（在任何DOM准备状态下）
injectEarlySidebarPlaceholder();

// 初始化
async function initializeEdgeSidebar() {
  try {
    edgeSidebarManager = new EdgeSidebarManager();
    await edgeSidebarManager.initialize();
    
    // 移除早期占位符
    const placeholder = document.getElementById('edge-vertical-sidebar-placeholder');
    if (placeholder) {
      placeholder.remove();
      console.log('[Init] 早期占位符已清理');
    }
  } catch (error) {
    console.error('初始化Edge侧边栏失败:', error);
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeEdgeSidebar);
} else {
  initializeEdgeSidebar();
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  if (edgeSidebarManager) {
    // 在页面卸载前保存最终状态
    edgeSidebarManager.saveState();
    edgeSidebarManager.destroy();
  }
});

// 导出供调试使用
window.__edgeSidebarManager = edgeSidebarManager;