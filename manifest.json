{"manifest_version": 3, "name": "VTabs 垂直侧边栏", "version": "1.0.1", "description": "为Chrome提供类似Microsoft Edge的垂直侧边栏功能", "permissions": ["tabs", "storage", "activeTab", "tabGroups"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "css": ["edge-sidebar.css"], "js": ["edge-sidebar.js"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup.html", "default_title": "VTabs 垂直侧边栏设置", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "options_page": "options.html", "icons": {"128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["icons/*", "edge-sidebar.css"], "matches": ["<all_urls>"]}]}