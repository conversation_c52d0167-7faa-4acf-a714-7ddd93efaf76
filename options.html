<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Chrome垂直侧边栏 - 设置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f5f5;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* 暗黑模式样式 */
    body.dark-theme {
      background: #0d1117;
      color: #f0f6fc;
    }

    body.dark-theme .container {
      background: #161b22;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    body.dark-theme .header {
      background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
    }

    body.dark-theme .section h2 {
      color: #f0f6fc;
    }

    body.dark-theme .section {
      border-bottom-color: #30363d;
    }

    body.dark-theme .setting-item label {
      color: #8b949e;
    }

    body.dark-theme .setting-item input,
    body.dark-theme .setting-item select {
      background: #21262d;
      border-color: #30363d;
      color: #f0f6fc;
    }

    body.dark-theme .setting-item input:focus,
    body.dark-theme .setting-item select:focus {
      border-color: #1a73e8;
      box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
    }

    body.dark-theme .toggle-switch {
      background: #484f58 !important;
    }

    body.dark-theme .toggle-switch.active {
      background: #1a73e8 !important;
    }

    body.dark-theme .button {
      background: #1a73e8;
      border: 1px solid #30363d;
    }

    body.dark-theme .button:hover {
      background: #1557b0;
    }

    body.dark-theme .button.secondary {
      background: #21262d;
      color: #f0f6fc;
      border-color: #30363d;
    }

    body.dark-theme .button.secondary:hover {
      background: #30363d;
      border-color: #484f58;
    }

    body.dark-theme .status.success {
      background: #0d1929;
      color: #4fc3f7;
      border-color: #1a73e8;
    }

    body.dark-theme .status.error {
      background: #2d1117;
      color: #f85149;
      border-color: #da3633;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .header {
      background: #1a73e8;
      color: white;
      padding: 20px;
      text-align: center;
    }
    
    .header h1 {
      margin: 0;
      font-size: 24px;
    }
    
    .content {
      padding: 30px;
    }
    
    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .section:last-child {
      border-bottom: none;
    }
    
    .section h2 {
      margin: 0 0 15px 0;
      font-size: 18px;
      color: #333;
    }
    
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .setting-item label {
      font-size: 14px;
      color: #555;
    }
    
    .setting-item small {
      display: block;
      color: #666;
      font-size: 12px;
      margin-top: 4px;
    }
    
    .setting-item input,
    .setting-item select {
      padding: 6px 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .toggle-switch {
      position: relative;
      width: 50px;
      height: 28px;
      background: #ccc !important;
      border-radius: 14px;
      cursor: pointer;
      transition: background 0.3s;
      user-select: none;
      display: inline-block;
      border: none;
      outline: none;
    }
    
    .toggle-switch.active {
      background: #1a73e8 !important;
    }
    
    .toggle-switch::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 24px;
      height: 24px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s;
      pointer-events: none;
    }
    
    .toggle-switch.active::after {
      transform: translateX(22px);
    }
    
    .toggle-switch:hover {
      opacity: 0.8;
    }
    
    .toggle-switch:active {
      transform: scale(0.95);
    }
    
    .toggle-text {
      font-size: 12px;
      color: #666;
      margin-left: 8px;
    }
    
    .button-group {
      text-align: center;
      margin-top: 30px;
    }
    
    .button {
      background: #1a73e8;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin: 0 10px;
      transition: background 0.3s;
    }
    
    .button:hover {
      background: #1557b0;
    }
    
    .button.secondary {
      background: #6c757d;
    }
    
    .button.secondary:hover {
      background: #545b62;
    }
    
    .status {
      text-align: center;
      padding: 10px;
      border-radius: 4px;
      margin-top: 20px;
      font-size: 14px;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔧 Chrome垂直侧边栏设置</h1>
    </div>
    
    <div class="content">
      <div class="section">
        <h2>基本设置</h2>
        
        <div class="setting-item">
          <label>启用侧边栏</label>
          <div class="toggle-switch" id="enabledToggle"></div>
        </div>
        
      </div>
      
      <div class="section">
        <h2>高级设置</h2>
        
        <div class="setting-item">
          <label>展开动画时长 (毫秒)</label>
          <input type="number" id="expandDuration" min="100" max="1000" step="50" value="300">
          <small>控制侧边栏展开动画的持续时间</small>
        </div>
        
        <div class="setting-item">
          <label>收起动画时长 (毫秒)</label>
          <input type="number" id="collapseDuration" min="100" max="1000" step="50" value="300">
          <small>控制侧边栏收起动画的持续时间</small>
        </div>
      </div>
      
      <div class="button-group">
        <button class="button" id="saveButton">保存设置</button>
        <button class="button secondary" id="resetButton">重置默认</button>
        <button class="button secondary" id="exportButton">导出设置</button>
        <button class="button secondary" id="importButton">导入设置</button>
      </div>
      
      <div class="status" id="status" style="display: none;"></div>
    </div>
  </div>
  
  <script src="src/options/options-theme.js"></script>
  <script src="options.js"></script>
</body>
</html>