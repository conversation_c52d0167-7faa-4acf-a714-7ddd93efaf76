// 选项页面脚本 - Side Panel版本
console.log('🔧 选项页面脚本加载...');

document.addEventListener('DOMContentLoaded', async () => {
  console.log('🔧 选项页面初始化...');
  
  const elements = {
    enabledToggle: document.getElementById('enabledToggle'),
    expandDuration: document.getElementById('expandDuration'),
    collapseDuration: document.getElementById('collapseDuration'),
    saveButton: document.getElementById('saveButton'),
    resetButton: document.getElementById('resetButton'),
    exportButton: document.getElementById('exportButton'),
    importButton: document.getElementById('importButton'),
    status: document.getElementById('status')
  };

  // 默认设置
  const defaultSettings = {
    enabled: true,
    expandDuration: 300,
    collapseDuration: 300
  };

  // 加载设置
  async function loadSettings() {
    try {
      console.log('🔧 加载设置...');
      
      // 获取用户设置
      const result = await chrome.storage.sync.get('userSettings');
      const userSettings = result.userSettings || {};
      
      // 合并设置
      const settings = {
        ...defaultSettings,
        ...userSettings
      };
      
      console.log('🔧 已加载设置:', settings);
      
      // 更新UI
      updateUI(settings);
      
      return settings;
    } catch (error) {
      console.error('🔧 加载设置失败:', error);
      showStatus('加载设置失败', 'error');
      return defaultSettings;
    }
  }

  // 更新UI
  function updateUI(settings) {
    console.log('🔧 更新UI:', settings);

    // 启用状态
    const isEnabled = settings.enabled !== false;
    updateToggleState(isEnabled);

    // 显示当前状态
    showStatus(isEnabled ? '侧边栏已启用' : '侧边栏已禁用', 'success');

    // 高级设置
    elements.expandDuration.value = settings.expandDuration || 300;
    elements.collapseDuration.value = settings.collapseDuration || 300;
  }

  // 从UI获取设置
  function getSettingsFromUI() {
    return {
      enabled: elements.enabledToggle.classList.contains('active'),
      expandDuration: parseInt(elements.expandDuration.value) || 300,
      collapseDuration: parseInt(elements.collapseDuration.value) || 300
    };
  }

  // 保存设置
  async function saveSettings() {
    try {
      console.log('🔧 保存设置...');
      
      const settings = getSettingsFromUI();
      console.log('🔧 要保存的设置:', settings);
      
      // 保存用户设置
      await chrome.storage.sync.set({ 
        userSettings: settings
      });
      
      console.log('🔧 设置保存成功');
      showStatus('设置已保存', 'success');
      
    } catch (error) {
      console.error('🔧 保存设置失败:', error);
      showStatus('保存设置失败', 'error');
    }
  }

  // 重置设置
  async function resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      try {
        console.log('🔧 重置设置...');
        
        await chrome.storage.sync.clear();
        await chrome.storage.sync.set({
          userSettings: defaultSettings
        });
        
        updateUI(defaultSettings);
        showStatus('设置已重置', 'success');
        
        console.log('🔧 设置重置成功');
      } catch (error) {
        console.error('🔧 重置设置失败:', error);
        showStatus('重置设置失败', 'error');
      }
    }
  }

  // 导出设置
  async function exportSettings() {
    try {
      const settings = getSettingsFromUI();
      const dataStr = JSON.stringify(settings, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'chrome-vertical-sidebar-settings.json';
      link.click();
      
      URL.revokeObjectURL(url);
      showStatus('设置已导出', 'success');
    } catch (error) {
      console.error('🔧 导出设置失败:', error);
      showStatus('导出设置失败', 'error');
    }
  }

  // 导入设置
  function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (event) => {
      try {
        const file = event.target.files[0];
        if (!file) return;
        
        const text = await file.text();
        const settings = JSON.parse(text);
        
        // 验证设置格式
        if (typeof settings !== 'object') {
          throw new Error('无效的设置文件格式');
        }
        
        // 合并默认设置
        const mergedSettings = { ...defaultSettings, ...settings };
        
        // 保存设置
        await chrome.storage.sync.set({
          userSettings: mergedSettings
        });
        
        updateUI(mergedSettings);
        showStatus('设置已导入', 'success');
        
      } catch (error) {
        console.error('🔧 导入设置失败:', error);
        showStatus('导入设置失败', 'error');
      }
    };
    input.click();
  }

  // 更新Toggle开关状态
  function updateToggleState(enabled) {
    if (enabled) {
      elements.enabledToggle.classList.add('active');
    } else {
      elements.enabledToggle.classList.remove('active');
    }
  }

  // 切换侧边栏启用状态
  async function toggleSidebar() {
    const isEnabled = !elements.enabledToggle.classList.contains('active');
    updateToggleState(isEnabled);
    
    // 实时应用设置
    const settings = getSettingsFromUI();
    try {
      await chrome.storage.sync.set({
        userSettings: settings
      });
      
      // 立即通知背景脚本和内容脚本
      await notifySidebar(settings.enabled);
      
      showStatus(isEnabled ? '侧边栏已启用' : '侧边栏已禁用', 'success');
    } catch (error) {
      console.error('🔧 切换侧边栏状态失败:', error);
      // 回滚UI状态
      updateToggleState(!isEnabled);
      showStatus('操作失败', 'error');
    }
  }

  // 通知侧边栏状态变化
  async function notifySidebar(enabled) {
    try {
      // 获取所有标签页
      const tabs = await chrome.tabs.query({});
      
      // 向每个标签页发送消息
      for (const tab of tabs) {
        if (tab.id) {
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'TOGGLE_SIDEBAR',
              enabled: enabled,
              timestamp: Date.now()
            });
          } catch (error) {
            // 某些标签页可能无法接收消息，忽略错误
            console.debug(`无法向标签页 ${tab.id} 发送消息:`, error.message);
          }
        }
      }
    } catch (error) {
      console.error('🔧 通知侧边栏失败:', error);
    }
  }

  // 显示状态消息
  function showStatus(message, type = 'success') {
    elements.status.textContent = message;
    elements.status.className = `status ${type}`;
    elements.status.style.display = 'block';
    
    setTimeout(() => {
      elements.status.style.display = 'none';
    }, 3000);
  }

  // 设置事件监听器
  function setupEventListeners() {
    
    // 启用侧边栏开关 - 实时响应
    elements.enabledToggle.addEventListener('click', toggleSidebar);
    
    // 按钮
    elements.saveButton.addEventListener('click', saveSettings);
    elements.resetButton.addEventListener('click', resetSettings);
    elements.exportButton.addEventListener('click', exportSettings);
    elements.importButton.addEventListener('click', importSettings);
    
    // 自动保存
    const autoSaveElements = [
      elements.expandDuration,
      elements.collapseDuration
    ];
    
    autoSaveElements.forEach(element => {
      element.addEventListener('change', () => {
        setTimeout(saveSettings, 500); // 延迟保存
      });
    });
  }

  // 初始化
  try {
    await loadSettings();
    setupEventListeners();
    console.log('🔧 选项页面初始化完成');
  } catch (error) {
    console.error('🔧 选项页面初始化失败:', error);
    showStatus('初始化失败', 'error');
  }
});