{"name": "chrome-vertical-sidebar", "version": "1.0.1", "description": "Chrome extension for vertical sidebar functionality", "main": "dist/background.js", "types": "types/index.ts", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "ts-node src/tests/runIntegrationTests.ts", "test:integration:userflow": "ts-node src/tests/runIntegrationTests.ts --suite userFlow", "test:integration:crosscomponent": "ts-node src/tests/runIntegrationTests.ts --suite crossComponent", "test:integration:dataconsistency": "ts-node src/tests/runIntegrationTests.ts --suite dataConsistency", "test:integration:parallel": "ts-node src/tests/runIntegrationTests.ts --parallel", "test:all": "npm run test:unit && npm run test:integration", "verify": "ts-node src/tests/runFinalVerification.ts", "verify:quick": "ts-node src/tests/runFinalVerification.ts --quick", "verify:functional": "ts-node src/tests/runFinalVerification.ts --skip-integration --skip-quality", "verify:quality": "ts-node src/tests/runFinalVerification.ts --skip-integration --skip-functional", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "package": "npm run build && web-ext build --source-dir=dist", "release": "npm run verify && npm run build && npm run package"}, "devDependencies": {"@types/chrome": "^0.0.246", "@types/jest": "^29.5.5", "@types/node": "^20.6.3", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "eslint": "^8.49.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "typescript": "^5.2.2", "web-ext": "^7.8.0", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.ts", "<rootDir>/src/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/tests/**/*"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "keywords": ["chrome-extension", "sidebar", "browser", "productivity"], "author": "Chrome Vertical Sidebar Team", "license": "MIT"}