/**
 * Background Script - 标签页事件监听和消息发送
 */

console.log('Chrome垂直侧边栏 - Background Script已加载');

// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('扩展已安装/更新，初始化事件监听器');
  setupTabEventListeners();
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('扩展启动，设置事件监听器');
  setupTabEventListeners();
});

// 设置标签页事件监听器
function setupTabEventListeners() {
  // 标签页创建
  chrome.tabs.onCreated.addListener((tab) => {
    console.log('标签页创建:', tab.id);
    notifyContentScript('TAB_CREATED', { tab });
  });

  // 标签页更新 (标题、URL、图标等变化)
  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 只在重要属性变化时通知
    if (changeInfo.title || changeInfo.url || changeInfo.favIconUrl || changeInfo.status === 'complete') {
      console.log('标签页更新:', tabId, changeInfo);
      notifyContentScript('TAB_UPDATED', { tabId, changeInfo, tab });
    }
  });

  // 标签页移除
  chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    console.log('标签页移除:', tabId);
    notifyContentScript('TAB_REMOVED', { tabId, removeInfo });
  });

  // 标签页激活
  chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log('标签页激活:', activeInfo.tabId);
    notifyContentScript('TAB_ACTIVATED', { activeInfo });
  });

  // 标签页移动
  chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
    console.log('标签页移动:', tabId);
    notifyContentScript('TAB_MOVED', { tabId, moveInfo });
  });

  // 标签页分组事件监听
  if (chrome.tabGroups) {
    // 分组创建
    chrome.tabGroups.onCreated.addListener((group) => {
      console.log('标签分组创建:', group.id);
      notifyContentScript('TAB_GROUP_CREATED', { group });
    });

    // 分组更新 (颜色、标题等变化)
    chrome.tabGroups.onUpdated.addListener((group) => {
      console.log('标签分组更新:', group.id);
      notifyContentScript('TAB_GROUP_UPDATED', { group });
    });

    // 分组移除
    chrome.tabGroups.onRemoved.addListener((group) => {
      console.log('标签分组移除:', group.id);
      notifyContentScript('TAB_GROUP_REMOVED', { group });
    });
  }

  console.log('标签页事件监听器已设置完成');
}

// 通知所有content script
async function notifyContentScript(eventType, eventData) {
  try {
    // 获取所有标签页
    const tabs = await chrome.tabs.query({});
    
    // 向每个标签页发送消息
    for (const tab of tabs) {
      if (tab.id) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'TAB_UPDATE_EVENT',
            eventType,
            eventData,
            timestamp: Date.now()
          });
        } catch (error) {
          // 某些标签页可能无法接收消息（如chrome://页面），忽略错误
          console.debug(`无法向标签页 ${tab.id} 发送消息:`, error.message);
        }
      }
    }
  } catch (error) {
    console.error('发送标签页更新事件失败:', error);
  }
}

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background接收到消息:', message);
  
  handleMessage(message, sender)
    .then(response => sendResponse(response))
    .catch(error => {
      console.error('处理消息失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    });
  
  return true; // 异步响应
});

// 消息处理器
async function handleMessage(message, sender) {
  try {
    switch (message.type) {
      case 'GET_TABS':
        return await handleGetTabs();
        
      case 'SWITCH_TAB':
        return await handleSwitchTab(message.payload.tabId);
        
      case 'CLOSE_TAB':
        return await handleCloseTab(message.payload.tabId);
        
      case 'TOGGLE_TAB_GROUP':
        return await handleToggleTabGroup(message.payload.groupId);
        
      case 'GET_SETTINGS':
        return await handleGetSettings();
        
      default:
        throw new Error(`未知的消息类型: ${message.type}`);
    }
  } catch (error) {
    console.error(`处理消息 ${message.type} 失败:`, error);
    throw error;
  }
}

// 获取标签页数据
async function handleGetTabs() {
  try {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    const tabGroups = chrome.tabGroups ? await chrome.tabGroups.query({ windowId: chrome.windows.WINDOW_ID_CURRENT }) : [];
    
    const tabsData = tabs.map(tab => ({
      id: tab.id,
      title: tab.title,
      url: tab.url,
      favIconUrl: tab.favIconUrl,
      active: tab.active,
      groupId: tab.groupId || -1,
      index: tab.index,
      pinned: tab.pinned,
      status: tab.status
    }));
    
    const groupsData = tabGroups.map(group => ({
      id: group.id,
      title: group.title,
      color: group.color,
      collapsed: group.collapsed
    }));
    
    return {
      success: true,
      data: {
        tabs: tabsData,
        groups: groupsData
      }
    };
  } catch (error) {
    console.error('获取标签页数据失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 切换到指定标签页
async function handleSwitchTab(tabId) {
  try {
    await chrome.tabs.update(tabId, { active: true });
    // 确保标签页所在的窗口也被激活
    const tab = await chrome.tabs.get(tabId);
    if (tab.windowId) {
      await chrome.windows.update(tab.windowId, { focused: true });
    }
    return { success: true };
  } catch (error) {
    console.error('切换标签页失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 关闭指定标签页
async function handleCloseTab(tabId) {
  try {
    await chrome.tabs.remove(tabId);
    return { success: true };
  } catch (error) {
    console.error('关闭标签页失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 切换标签分组展开/折叠状态
async function handleToggleTabGroup(groupId) {
  try {
    if (chrome.tabGroups) {
      const group = await chrome.tabGroups.get(groupId);
      await chrome.tabGroups.update(groupId, {
        collapsed: !group.collapsed
      });
    }
    return { success: true };
  } catch (error) {
    console.error('切换标签分组状态失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取设置
async function handleGetSettings() {
  try {
    // 先获取用户实际保存的设置，不使用默认值
    const result = await chrome.storage.sync.get('userSettings');
    const userSettings = result.userSettings || {};

    // 只有在用户没有明确设置enabled时，才使用默认值true
    // 如果用户设置了enabled: false，则应该尊重用户的选择
    const settings = {
      enabled: userSettings.enabled !== undefined ? userSettings.enabled : true,
      expandDuration: userSettings.expandDuration || 300,
      collapseDuration: userSettings.collapseDuration || 300,
      showBookmarks: userSettings.showBookmarks || false,
      autoHide: userSettings.autoHide || false
    };

    console.log('获取设置:', { userSettings, finalSettings: settings });

    return {
      success: true,
      data: settings
    };
  } catch (error) {
    console.error('获取设置失败:', error);
    return {
      success: false,
      error: error.message,
      data: {
        enabled: true,
        expandDuration: 300,
        collapseDuration: 300
      }
    };
  }
}

// 立即初始化
setupTabEventListeners();