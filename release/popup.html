<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #ffffff;
      color: #333333;
      transition: background-color 0.3s ease, color 0.3s ease;
    }
    
    /* 暗黑模式样式 */
    @media (prefers-color-scheme: dark) {
      body {
        background: #1e1e1e;
        color: #e1e4e8;
      }
      
      .header h1 {
        color: #1a73e8 !important;
      }
      
      .control-group label {
        color: #e1e4e8 !important;
      }
      
      .toggle-switch {
        background: #484f58 !important;
      }
      
      .toggle-switch.active {
        background: #1a73e8 !important;
      }
      
      .button {
        background: #1a73e8 !important;
      }
      
      .button:hover {
        background: #1557b0 !important;
      }
      
      select {
        background: #21262d !important;
        border-color: #30363d !important;
        color: #e1e4e8 !important;
      }
      
      .status.active {
        background: #0d1929 !important;
        color: #4fc3f7 !important;
      }
      
      .status.inactive {
        background: #2d1b0e !important;
        color: #fbbf24 !important;
      }
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      font-size: 18px;
      margin: 0;
      color: #1a73e8;
    }
    
    .controls {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .control-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .control-group label {
      font-size: 14px;
      color: #333;
    }
    
    .toggle-switch {
      position: relative;
      width: 44px;
      height: 24px;
      background: #ccc;
      border-radius: 12px;
      cursor: pointer;
      transition: background 0.3s;
    }
    
    .toggle-switch.active {
      background: #1a73e8;
    }
    
    .toggle-switch::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s;
    }
    
    .toggle-switch.active::after {
      transform: translateX(20px);
    }
    
    .button {
      background: #1a73e8;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s;
    }
    
    .button:hover {
      background: #1557b0;
    }
    
    select {
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
      background: #ffffff;
      color: #333333;
    }
    
    .status {
      text-align: center;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 12px;
    }
    
    .status.active {
      background: #e8f5e8;
      color: #2e7d32;
    }
    
    .status.inactive {
      background: #fff3e0;
      color: #f57c00;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>VTabs 垂直侧边栏设置</h1>
  </div>
  
  <div class="controls">
    <div class="control-group">
      <label>启用侧边栏</label>
      <div class="toggle-switch active" id="enableToggle"></div>
    </div>
    
    
    <button class="button" id="openOptions">高级设置</button>
  </div>
  
  <div class="status active" id="status">
    侧边栏已激活
  </div>
  
  <script src="popup.js"></script>
</body>
</html>