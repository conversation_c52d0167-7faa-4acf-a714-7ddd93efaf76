// Popup页面脚本 - Edge风格版本
document.addEventListener('DOMContentLoaded', async () => {
  const enableToggle = document.getElementById('enableToggle')
  const openOptionsBtn = document.getElementById('openOptions')

  const status = document.getElementById('status')

  // 加载当前设置
  try {
    const result = await chrome.storage.sync.get('userSettings');
    const settings = result.userSettings || {
      enabled: true
    };

    // 更新UI
    enableToggle.classList.toggle('active', settings.enabled !== false)

    updateStatus(settings.enabled !== false)

    console.log('加载的设置:', settings);

  } catch (error) {
    console.error('加载设置失败:', error)
    status.textContent = '设置加载失败'
    status.className = 'status inactive'
  }

  // 启用开关事件
  enableToggle.addEventListener('click', async () => {
    const isActive = enableToggle.classList.contains('active')
    const newState = !isActive
    
    enableToggle.classList.toggle('active', newState)
    await updateUserSettings({ enabled: newState })
    
    // 通知所有标签页
    try {
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'TOGGLE_SIDEBAR',
            enabled: newState,
            timestamp: Date.now()
          });
        } catch (error) {
          // 忽略无法发送消息的标签页
        }
      }
    } catch (error) {
      console.log('通知标签页失败:', error)
    }
    
    updateStatus(newState)
  })


  // 打开选项页面
  openOptionsBtn.addEventListener('click', () => {
    chrome.runtime.openOptionsPage()
  })

  // 辅助函数
  function updateStatus(enabled) {
    if (enabled) {
      status.textContent = '侧边栏已启用'
      status.className = 'status active'
    } else {
      status.textContent = '侧边栏已禁用'
      status.className = 'status inactive'
    }
  }

  async function updateUserSettings(updates) {
    try {
      // 获取当前设置
      const result = await chrome.storage.sync.get('userSettings');
      const currentSettings = result.userSettings || {};
      
      // 合并更新
      const newSettings = { ...currentSettings, ...updates };
      
      // 保存设置
      await chrome.storage.sync.set({ userSettings: newSettings });
      
      console.log('设置已更新:', newSettings);
      
    } catch (error) {
      console.error('更新设置失败:', error);
    }
  }
})