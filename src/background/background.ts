/**
 * Background Script - Chrome Extension Service Worker
 * 处理扩展的后台逻辑，包括标签页管理、收藏夹操作和设置同步
 */

import { Message, MessageResponse } from '../types/index';

console.log('Chrome Vertical Sidebar - Background Script Loaded');

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed:', details.reason);
  
  if (details.reason === 'install') {
    // 首次安装时的初始化逻辑
    initializeExtension();
  } else if (details.reason === 'update') {
    // 更新时的迁移逻辑
    handleExtensionUpdate(details.previousVersion);
  }
});

// 扩展启动时的初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension startup');
  initializeExtension();
});

// 消息监听
chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  // 异步处理消息
  handleMessage(message, sender)
    .then(response => sendResponse(response))
    .catch(error => {
      console.error('Error handling message:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    });
  
  // 返回true表示异步响应
  return true;
});

/**
 * 初始化扩展
 */
async function initializeExtension(): Promise<void> {
  try {
    // 设置默认设置
    const settings = await chrome.storage.sync.get('userSettings');
    if (!settings.userSettings) {
      // 导入默认设置
      const { DEFAULT_SETTINGS } = await import('../types/index');
      await chrome.storage.sync.set({ userSettings: DEFAULT_SETTINGS });
      console.log('Default settings initialized');
    }
    
    // 设置命令监听
    setupCommandListeners();
    
    // 设置标签页事件监听
    setupTabEventListeners();
    
    console.log('Extension initialized successfully');
  } catch (error) {
    console.error('Failed to initialize extension:', error);
  }
}

/**
 * 处理扩展更新
 */
async function handleExtensionUpdate(previousVersion?: string): Promise<void> {
  console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
  
  // 这里可以添加版本迁移逻辑
  // 例如：设置格式变更、数据迁移等
}

/**
 * 设置命令监听器
 */
function setupCommandListeners(): void {
  chrome.commands.onCommand.addListener((command) => {
    console.log('Command received:', command);
    
    switch (command) {
      case 'toggle-sidebar':
        // 向当前活动标签页发送切换侧边栏的消息
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]?.id) {
            chrome.tabs.sendMessage(tabs[0].id, {
              type: 'TOGGLE_SIDEBAR',
              timestamp: Date.now()
            });
          }
        });
        break;
    }
  });
}

/**
 * 设置标签页事件监听器
 */
function setupTabEventListeners(): void {
  console.log('Setting up tab event listeners');

  // 标签页创建
  chrome.tabs.onCreated.addListener((tab) => {
    console.log('Tab created:', tab.id, tab.title);
    broadcastTabUpdate('tab_created', { tab });
  });

  // 标签页删除
  chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    console.log('Tab removed:', tabId);
    broadcastTabUpdate('tab_removed', { tabId, removeInfo });
  });

  // 标签页更新（标题、URL、图标等）
  chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 只在重要属性变化时广播
    if (changeInfo.title !== undefined || 
        changeInfo.url !== undefined || 
        changeInfo.favIconUrl !== undefined ||
        changeInfo.status === 'complete') {
      console.log('Tab updated:', tabId, changeInfo);
      broadcastTabUpdate('tab_updated', { tabId, changeInfo, tab });
    }
  });

  // 标签页移动
  chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
    console.log('Tab moved:', tabId, moveInfo);
    broadcastTabUpdate('tab_moved', { tabId, moveInfo });
  });

  // 标签页激活
  chrome.tabs.onActivated.addListener((activeInfo) => {
    console.log('Tab activated:', activeInfo.tabId);
    broadcastTabUpdate('tab_activated', activeInfo);
  });

  // 标签组创建
  chrome.tabGroups.onCreated.addListener((group) => {
    console.log('Tab group created:', group.id, group.title);
    broadcastTabUpdate('group_created', { group });
  });

  // 标签组删除
  chrome.tabGroups.onRemoved.addListener((group) => {
    console.log('Tab group removed:', group.id);
    broadcastTabUpdate('group_removed', { group });
  });

  // 标签组更新
  chrome.tabGroups.onUpdated.addListener((group) => {
    console.log('Tab group updated:', group.id, group.title);
    broadcastTabUpdate('group_updated', { group });
  });
}

/**
 * 处理来自content script的消息
 */
async function handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
  const { type, payload } = message;
  
  try {
    switch (type) {
      case 'GET_TABS':
        const tabs = await chrome.tabs.query({ currentWindow: true });
        
        // 获取当前窗口ID
        const currentWindow = await chrome.windows.getCurrent();
        const tabGroups = await chrome.tabGroups.query({ windowId: currentWindow.id });
        
        console.log('Background: 获取标签页和分组数据', {
          tabsCount: tabs.length,
          groupsCount: tabGroups.length,
          windowId: currentWindow.id,
          groups: tabGroups.map(g => ({ id: g.id, title: g.title, color: g.color, collapsed: g.collapsed }))
        });
        
        return {
          success: true,
          data: {
            tabs: tabs.map(tab => ({
              id: tab.id!,
              title: tab.title || '',
              url: tab.url || '',
              favIconUrl: tab.favIconUrl,
              active: tab.active,
              pinned: tab.pinned,
              groupId: tab.groupId !== -1 ? tab.groupId : undefined,
              index: tab.index,
              windowId: tab.windowId
            })),
            groups: tabGroups.map(group => ({
              id: group.id,
              title: group.title || '',
              color: group.color,
              collapsed: group.collapsed
            }))
          }
        };
        
      case 'GET_BOOKMARKS':
        const bookmarks = await chrome.bookmarks.getTree();
        return {
          success: true,
          data: bookmarks
        };
        
      case 'GET_SETTINGS':
        const settings = await chrome.storage.sync.get('userSettings');
        return {
          success: true,
          data: settings.userSettings
        };
        
      case 'UPDATE_SETTINGS':
        await chrome.storage.sync.set({ userSettings: payload });
        return {
          success: true,
          data: payload
        };
        
      case 'SWITCH_TAB':
        await chrome.tabs.update(payload.tabId, { active: true });
        return {
          success: true
        };
        
      case 'CLOSE_TAB':
        await chrome.tabs.remove(payload.tabId);
        return {
          success: true
        };
        

        
      default:
        return {
          success: false,
          error: `Unknown message type: ${type}`
        };
    }
  } catch (error) {
    console.error(`Error handling ${type}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 向所有content scripts广播标签页更新事件
 */
async function broadcastTabUpdate(eventType: string, eventData: any): Promise<void> {
  try {
    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    
    // 向每个标签页发送更新通知
    const promises = tabs.map(async (tab) => {
      if (tab.id) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'TAB_UPDATE_EVENT',
            eventType,
            eventData,
            timestamp: Date.now()
          });
        } catch (error) {
          // 忽略无法发送消息的标签页（如chrome://页面）
          // console.debug(`Cannot send message to tab ${tab.id}:`, error);
        }
      }
    });
    
    await Promise.allSettled(promises);
    
  } catch (error) {
    console.error('Error broadcasting tab update:', error);
  }
}