/**
 * Background Service - 核心后台服务实现
 */

import { BackgroundService } from '../../interfaces/background.interface.js';
import { TabInfo, TabGroup, BookmarkNode, UserSettings, Message, MessageResponse } from '../types/index.js';
import { TabManager } from './TabManager.js';
import { BookmarkManager } from './BookmarkManager.js';
import { SettingsManager } from './SettingsManager.js';
import { MessageRouter } from './MessageRouter.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';

export class BackgroundServiceImpl implements BackgroundService {
  private tabManager: TabManager;
  private bookmarkManager: BookmarkManager;
  private settingsManager: SettingsManager;
  private messageRouter: MessageRouter;
  private initialized = false;

  constructor() {
    this.tabManager = new TabManager();
    this.bookmarkManager = new BookmarkManager();
    this.settingsManager = new SettingsManager();
    this.messageRouter = new MessageRouter(this);
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Background Service...');

      // 初始化各个管理器
      await this.settingsManager.initialize();
      await this.tabManager.initialize();
      await this.bookmarkManager.initialize();
      
      // 设置消息路由
      this.messageRouter.initialize();

      // 设置事件监听
      this.setupEventListeners();

      this.initialized = true;
      console.log('Background Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Background Service:', error);
      ErrorHandler.handleError(error, 'BackgroundService.initialize');
      throw error;
    }
  }

  /**
   * 检查服务是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  // 标签页管理方法
  async getAllTabs(): Promise<TabInfo[]> {
    this.ensureInitialized();
    return this.tabManager.getCurrentWindowTabs();
  }

  async getTabGroups(): Promise<TabGroup[]> {
    this.ensureInitialized();
    return this.tabManager.getTabGroups();
  }

  async switchToTab(tabId: number): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.activateTab(tabId);
  }

  async closeTab(tabId: number): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.closeTab(tabId);
  }

  async createTabGroup(name: string, color: string, tabIds: number[]): Promise<TabGroup> {
    this.ensureInitialized();
    return this.tabManager.createTabGroup(name, color, tabIds);
  }

  async updateTabGroup(groupId: number, updates: Partial<TabGroup>): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.updateTabGroup(groupId, updates);
  }

  async duplicateTab(tabId: number): Promise<TabInfo> {
    this.ensureInitialized();
    return this.tabManager.duplicateTab(tabId);
  }

  async createTab(url: string, active = false): Promise<TabInfo> {
    this.ensureInitialized();
    return this.tabManager.createTab(url, active);
  }

  async closeOtherTabs(keepTabId: number): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.closeOtherTabs(keepTabId);
  }

  async ungroupTabs(groupId: number): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.ungroupTabs(groupId);
  }

  async closeTabGroup(groupId: number): Promise<void> {
    this.ensureInitialized();
    await this.tabManager.closeTabGroup(groupId);
  }

  // 收藏夹管理方法
  async getBookmarks(): Promise<BookmarkNode[]> {
    this.ensureInitialized();
    return this.bookmarkManager.getBookmarkTree();
  }

  async addBookmark(url: string, title: string, folderId?: string): Promise<BookmarkNode> {
    this.ensureInitialized();
    return this.bookmarkManager.createBookmark({ url, title, parentId: folderId });
  }

  async removeBookmark(bookmarkId: string): Promise<void> {
    this.ensureInitialized();
    await this.bookmarkManager.deleteBookmark(bookmarkId);
  }

  async updateBookmark(bookmarkId: string, updates: Partial<BookmarkNode>): Promise<void> {
    this.ensureInitialized();
    await this.bookmarkManager.updateBookmark(bookmarkId, updates);
  }

  async createBookmarkFolder(title: string, parentId?: string): Promise<BookmarkNode> {
    this.ensureInitialized();
    return this.bookmarkManager.createBookmarkFolder(title, parentId);
  }

  async moveBookmark(bookmarkId: string, parentId: string, index?: number): Promise<void> {
    this.ensureInitialized();
    await this.bookmarkManager.moveBookmark(bookmarkId, parentId, index);
  }

  // 设置管理方法
  async getSettings(): Promise<UserSettings> {
    this.ensureInitialized();
    return this.settingsManager.load();
  }

  async updateSettings(settings: Partial<UserSettings>): Promise<void> {
    this.ensureInitialized();
    await this.settingsManager.save(settings);
  }

  async resetSettings(): Promise<void> {
    this.ensureInitialized();
    await this.settingsManager.reset();
  }

  // 消息处理
  async handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
    this.ensureInitialized();
    return this.messageRouter.handleMessage(message, sender);
  }

  // 事件监听设置
  onTabsChanged(callback: (tabs: TabInfo[]) => void): void {
    this.tabManager.onTabsChanged(callback);
  }

  onBookmarksChanged(callback: (bookmarks: BookmarkNode[]) => void): void {
    this.bookmarkManager.onBookmarksChanged(callback);
  }

  onSettingsChanged(callback: (settings: UserSettings) => void): void {
    this.settingsManager.onSettingsChanged(callback);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 标签页变化监听
    this.onTabsChanged((tabs) => {
      this.broadcastToContentScripts({
        type: 'TABS_UPDATED',
        payload: tabs,
        timestamp: Date.now()
      });
    });

    // 收藏夹变化监听
    this.onBookmarksChanged((bookmarks) => {
      this.broadcastToContentScripts({
        type: 'BOOKMARKS_UPDATED',
        payload: bookmarks,
        timestamp: Date.now()
      });
    });

    // 设置变化监听
    this.onSettingsChanged((settings) => {
      this.broadcastToContentScripts({
        type: 'SETTINGS_UPDATED',
        payload: settings,
        timestamp: Date.now()
      });
    });
  }

  /**
   * 向所有内容脚本广播消息
   */
  private async broadcastToContentScripts(message: Message): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({});
      const promises = tabs.map(tab => {
        if (tab.id) {
          return chrome.tabs.sendMessage(tab.id, message).catch(error => {
            // 忽略无法发送消息的标签页（如chrome://页面）
            if (!error.message?.includes('Could not establish connection')) {
              console.warn(`Failed to send message to tab ${tab.id}:`, error);
            }
          });
        }
      });
      
      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Failed to broadcast message to content scripts:', error);
    }
  }

  /**
   * 确保服务已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('BackgroundService not initialized');
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.messageRouter.destroy();
    this.tabManager.destroy();
    this.bookmarkManager.destroy();
    this.settingsManager.destroy();
    this.initialized = false;
  }
}