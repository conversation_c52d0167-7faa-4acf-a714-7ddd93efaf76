/**
 * Bookmark Manager - 收藏夹管理服务
 */

import { BookmarkNode } from '../types/index.js';
import { BookmarkManager as IBookmarkManager } from '../../interfaces/background.interface.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';

export class BookmarkManager implements IBookmarkManager {
  private bookmarkChangeCallbacks: ((bookmarks: BookmarkNode[]) => void)[] = [];
  private bookmarkCache: BookmarkNode[] = [];
  private cacheExpiry = 0;
  private readonly CACHE_DURATION = 30000; // 30秒缓存
  private initialized = false;

  /**
   * 初始化收藏夹管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Bookmark Manager...');

      // 设置收藏夹事件监听
      this.setupBookmarkEventListeners();

      // 预加载收藏夹数据
      await this.loadBookmarks();

      this.initialized = true;
      console.log('Bookmark Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Bookmark Manager:', error);
      ErrorHandler.handleError(error, 'BookmarkManager.initialize');
      throw error;
    }
  }

  /**
   * 获取收藏夹树结构
   */
  async getBookmarkTree(): Promise<BookmarkNode[]> {
    try {
      // 检查缓存是否有效
      if (this.isCacheValid()) {
        return this.bookmarkCache;
      }

      return await this.loadBookmarks();
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.getBookmarkTree');
      throw error;
    }
  }

  /**
   * 搜索收藏夹
   */
  async searchBookmarks(query: string): Promise<BookmarkNode[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const results = await chrome.bookmarks.search(query);
      return results.map(bookmark => this.convertChromeBookmarkToBookmarkNode(bookmark));
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.searchBookmarks');
      throw error;
    }
  }

  /**
   * 创建收藏夹
   */
  async createBookmark(bookmark: Partial<BookmarkNode>): Promise<BookmarkNode> {
    try {
      const createInfo: chrome.bookmarks.BookmarkCreateArg = {
        title: bookmark.title || '',
        url: bookmark.url,
        parentId: bookmark.parentId
      };

      if (bookmark.index !== undefined) {
        createInfo.index = bookmark.index;
      }

      const createdBookmark = await chrome.bookmarks.create(createInfo);
      const bookmarkNode = this.convertChromeBookmarkToBookmarkNode(createdBookmark);
      
      // 清除缓存以强制重新加载
      this.invalidateCache();
      
      return bookmarkNode;
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.createBookmark');
      throw error;
    }
  }

  /**
   * 更新收藏夹
   */
  async updateBookmark(id: string, updates: Partial<BookmarkNode>): Promise<void> {
    try {
      const updateInfo: chrome.bookmarks.BookmarkChangesArg = {};
      
      if (updates.title !== undefined) {
        updateInfo.title = updates.title;
      }
      
      if (updates.url !== undefined) {
        updateInfo.url = updates.url;
      }

      await chrome.bookmarks.update(id, updateInfo);
      
      // 清除缓存
      this.invalidateCache();
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.updateBookmark');
      throw error;
    }
  }

  /**
   * 删除收藏夹
   */
  async deleteBookmark(id: string): Promise<void> {
    try {
      // 检查是否为文件夹
      const bookmark = await chrome.bookmarks.get(id);
      if (bookmark[0] && bookmark[0].children) {
        // 递归删除文件夹
        await chrome.bookmarks.removeTree(id);
      } else {
        await chrome.bookmarks.remove(id);
      }
      
      // 清除缓存
      this.invalidateCache();
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.deleteBookmark');
      throw error;
    }
  }

  /**
   * 移动收藏夹
   */
  async moveBookmark(id: string, parentId: string, index?: number): Promise<void> {
    try {
      const moveInfo: chrome.bookmarks.BookmarkDestinationArg = {
        parentId
      };
      
      if (index !== undefined) {
        moveInfo.index = index;
      }

      await chrome.bookmarks.move(id, moveInfo);
      
      // 清除缓存
      this.invalidateCache();
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.moveBookmark');
      throw error;
    }
  }

  /**
   * 创建收藏夹文件夹
   */
  async createBookmarkFolder(title: string, parentId?: string): Promise<BookmarkNode> {
    try {
      const createInfo: chrome.bookmarks.BookmarkCreateArg = {
        title,
        parentId: parentId || '1' // 默认放在书签栏
      };

      const createdFolder = await chrome.bookmarks.create(createInfo);
      const folderNode = this.convertChromeBookmarkToBookmarkNode(createdFolder);
      
      // 清除缓存
      this.invalidateCache();
      
      return folderNode;
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.createBookmarkFolder');
      throw error;
    }
  }

  /**
   * 获取收藏夹的父级路径
   */
  async getBookmarkPath(id: string): Promise<BookmarkNode[]> {
    try {
      const path: BookmarkNode[] = [];
      let currentId = id;

      while (currentId && currentId !== '0') {
        const bookmarks = await chrome.bookmarks.get(currentId);
        if (bookmarks.length === 0) break;

        const bookmark = this.convertChromeBookmarkToBookmarkNode(bookmarks[0]);
        path.unshift(bookmark);
        currentId = bookmarks[0].parentId || '';
      }

      return path;
    } catch (error) {
      ErrorHandler.handleError(error, 'BookmarkManager.getBookmarkPath');
      throw error;
    }
  }

  /**
   * 添加收藏夹变化监听器
   */
  onBookmarksChanged(callback: (bookmarks: BookmarkNode[]) => void): void {
    this.bookmarkChangeCallbacks.push(callback);
  }

  /**
   * 移除收藏夹变化监听器
   */
  removeBookmarksChangedListener(callback: (bookmarks: BookmarkNode[]) => void): void {
    const index = this.bookmarkChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.bookmarkChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 加载收藏夹数据
   */
  private async loadBookmarks(): Promise<BookmarkNode[]> {
    try {
      const bookmarkTree = await chrome.bookmarks.getTree();
      const processedTree = this.processBookmarkTree(bookmarkTree);
      
      // 更新缓存
      this.bookmarkCache = processedTree;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;
      
      return processedTree;
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
      return [];
    }
  }

  /**
   * 处理收藏夹树结构
   */
  private processBookmarkTree(tree: chrome.bookmarks.BookmarkTreeNode[]): BookmarkNode[] {
    const result: BookmarkNode[] = [];

    for (const node of tree) {
      // 跳过根节点，直接处理其子节点
      if (node.id === '0' && node.children) {
        for (const child of node.children) {
          const processedChild = this.convertChromeBookmarkToBookmarkNode(child);
          if (processedChild) {
            result.push(processedChild);
          }
        }
      } else {
        const processedNode = this.convertChromeBookmarkToBookmarkNode(node);
        if (processedNode) {
          result.push(processedNode);
        }
      }
    }

    return result;
  }

  /**
   * 转换Chrome收藏夹对象为BookmarkNode
   */
  private convertChromeBookmarkToBookmarkNode(bookmark: chrome.bookmarks.BookmarkTreeNode): BookmarkNode {
    const node: BookmarkNode = {
      id: bookmark.id,
      title: bookmark.title,
      url: bookmark.url,
      dateAdded: bookmark.dateAdded,
      parentId: bookmark.parentId,
      index: bookmark.index
    };

    // 处理子节点
    if (bookmark.children && bookmark.children.length > 0) {
      node.children = bookmark.children.map(child => 
        this.convertChromeBookmarkToBookmarkNode(child)
      );
    }

    return node;
  }

  /**
   * 设置收藏夹事件监听器
   */
  private setupBookmarkEventListeners(): void {
    // 收藏夹创建
    chrome.bookmarks.onCreated.addListener((id, bookmark) => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
    });

    // 收藏夹移除
    chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
    });

    // 收藏夹更新
    chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
    });

    // 收藏夹移动
    chrome.bookmarks.onMoved.addListener((id, moveInfo) => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
    });

    // 收藏夹重新排序
    chrome.bookmarks.onChildrenReordered.addListener((id, reorderInfo) => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
    });

    // 收藏夹导入开始
    chrome.bookmarks.onImportBegan.addListener(() => {
      console.log('Bookmark import began');
    });

    // 收藏夹导入结束
    chrome.bookmarks.onImportEnded.addListener(() => {
      this.invalidateCache();
      this.notifyBookmarksChanged();
      console.log('Bookmark import ended');
    });
  }

  /**
   * 通知收藏夹变化
   */
  private async notifyBookmarksChanged(): Promise<void> {
    try {
      const bookmarks = await this.getBookmarkTree();
      this.bookmarkChangeCallbacks.forEach(callback => {
        try {
          callback(bookmarks);
        } catch (error) {
          console.error('Error in bookmark change callback:', error);
        }
      });
    } catch (error) {
      console.error('Error notifying bookmark changes:', error);
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return this.bookmarkCache.length > 0 && Date.now() < this.cacheExpiry;
  }

  /**
   * 使缓存失效
   */
  private invalidateCache(): void {
    this.cacheExpiry = 0;
    this.bookmarkCache = [];
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.bookmarkChangeCallbacks = [];
    this.invalidateCache();
    this.initialized = false;
  }
}