/**
 * Message Router - 消息路由和处理
 */

import { Message, MessageResponse, MessageType } from '../types/index.js';
import { BackgroundService } from '../../interfaces/background.interface.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';

export class MessageRouter {
  private backgroundService: BackgroundService;
  private messageHandlers: Map<MessageType, (message: Message, sender: chrome.runtime.MessageSender) => Promise<MessageResponse>>;

  constructor(backgroundService: BackgroundService) {
    this.backgroundService = backgroundService;
    this.messageHandlers = new Map();
    this.setupMessageHandlers();
  }

  /**
   * 初始化消息路由
   */
  initialize(): void {
    chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
      this.handleMessage(message, sender)
        .then(response => sendResponse(response))
        .catch(error => {
          console.error('Error handling message:', error);
          sendResponse({
            success: false,
            error: error.message || 'Unknown error',
            timestamp: Date.now()
          });
        });
      
      return true; // 异步响应
    });

    console.log('Message router initialized');
  }

  /**
   * 处理消息
   */
  async handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
    const startTime = performance.now();
    
    try {
      console.log(`Handling message: ${message.type}`, message);

      // 验证消息格式
      if (!this.validateMessage(message)) {
        throw new Error('Invalid message format');
      }

      // 获取消息处理器
      const handler = this.messageHandlers.get(message.type);
      if (!handler) {
        throw new Error(`No handler found for message type: ${message.type}`);
      }

      // 执行处理器
      const response = await handler(message, sender);
      
      // 添加性能指标
      const processingTime = performance.now() - startTime;
      response.timestamp = Date.now();
      
      console.log(`Message ${message.type} processed in ${processingTime.toFixed(2)}ms`);
      
      return response;
    } catch (error) {
      console.error(`Error handling message ${message.type}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandlers(): void {
    // 标签页相关消息
    this.messageHandlers.set('GET_TABS', async () => {
      const tabs = await this.backgroundService.getAllTabs();
      return { success: true, data: tabs };
    });

    this.messageHandlers.set('SWITCH_TAB', async (message) => {
      await this.backgroundService.switchToTab(message.payload.tabId);
      return { success: true };
    });

    this.messageHandlers.set('CLOSE_TAB', async (message) => {
      await this.backgroundService.closeTab(message.payload.tabId);
      return { success: true };
    });

    this.messageHandlers.set('CREATE_TAB_GROUP', async (message) => {
      const { name, color, tabIds } = message.payload;
      const group = await this.backgroundService.createTabGroup(name, color, tabIds);
      return { success: true, data: group };
    });

    this.messageHandlers.set('UPDATE_TAB_GROUP', async (message) => {
      const { groupId, updates } = message.payload;
      await this.backgroundService.updateTabGroup(groupId, updates);
      return { success: true };
    });

    this.messageHandlers.set('DUPLICATE_TAB', async (message) => {
      const { tabId } = message.payload;
      const newTab = await this.backgroundService.duplicateTab(tabId);
      return { success: true, data: newTab };
    });

    this.messageHandlers.set('CREATE_TAB', async (message) => {
      const { url, active } = message.payload;
      const newTab = await this.backgroundService.createTab(url, active);
      return { success: true, data: newTab };
    });

    this.messageHandlers.set('CLOSE_OTHER_TABS', async (message) => {
      const { keepTabId } = message.payload;
      await this.backgroundService.closeOtherTabs(keepTabId);
      return { success: true };
    });

    this.messageHandlers.set('PIN_TAB', async (message) => {
      const { tabId, pinned } = message.payload;
      await chrome.tabs.update(tabId, { pinned });
      return { success: true };
    });

    this.messageHandlers.set('UNGROUP_TABS', async (message) => {
      const { groupId } = message.payload;
      await this.backgroundService.ungroupTabs(groupId);
      return { success: true };
    });

    this.messageHandlers.set('CLOSE_TAB_GROUP', async (message) => {
      const { groupId } = message.payload;
      await this.backgroundService.closeTabGroup(groupId);
      return { success: true };
    });

    // 收藏夹相关消息
    this.messageHandlers.set('GET_BOOKMARKS', async () => {
      const bookmarks = await this.backgroundService.getBookmarks();
      return { success: true, data: bookmarks };
    });

    this.messageHandlers.set('CREATE_BOOKMARK', async (message) => {
      const { url, title, folderId } = message.payload;
      const bookmark = await this.backgroundService.addBookmark(url, title, folderId);
      return { success: true, data: bookmark };
    });

    this.messageHandlers.set('UPDATE_BOOKMARK', async (message) => {
      const { bookmarkId, updates } = message.payload;
      await this.backgroundService.updateBookmark(bookmarkId, updates);
      return { success: true };
    });

    this.messageHandlers.set('DELETE_BOOKMARK', async (message) => {
      const { bookmarkId } = message.payload;
      await this.backgroundService.removeBookmark(bookmarkId);
      return { success: true };
    });

    this.messageHandlers.set('CREATE_BOOKMARK_FOLDER', async (message) => {
      const { title, parentId } = message.payload;
      const folder = await this.backgroundService.createBookmarkFolder(title, parentId);
      return { success: true, data: folder };
    });

    this.messageHandlers.set('MOVE_BOOKMARK', async (message) => {
      const { bookmarkId, parentId, index } = message.payload;
      await this.backgroundService.moveBookmark(bookmarkId, parentId, index);
      return { success: true };
    });

    // 设置相关消息
    this.messageHandlers.set('GET_SETTINGS', async () => {
      const settings = await this.backgroundService.getSettings();
      return { success: true, data: settings };
    });

    this.messageHandlers.set('UPDATE_SETTINGS', async (message) => {
      await this.backgroundService.updateSettings(message.payload);
      return { success: true, data: message.payload };
    });

    // 侧边栏状态消息
    this.messageHandlers.set('SIDEBAR_STATE_CHANGED', async (message) => {
      // 广播状态变化到其他标签页
      await this.broadcastToOtherTabs(message, message.tabId);
      return { success: true };
    });

    this.messageHandlers.set('TOGGLE_SIDEBAR', async (message, sender) => {
      // 向发送者标签页发送切换命令
      if (sender.tab?.id) {
        await chrome.tabs.sendMessage(sender.tab.id, {
          type: 'TOGGLE_SIDEBAR_COMMAND',
          timestamp: Date.now()
        });
      }
      return { success: true };
    });

    this.messageHandlers.set('PIN_SIDEBAR', async (message, sender) => {
      if (sender.tab?.id) {
        await chrome.tabs.sendMessage(sender.tab.id, {
          type: 'PIN_SIDEBAR_COMMAND',
          timestamp: Date.now()
        });
      }
      return { success: true };
    });

    this.messageHandlers.set('UNPIN_SIDEBAR', async (message, sender) => {
      if (sender.tab?.id) {
        await chrome.tabs.sendMessage(sender.tab.id, {
          type: 'UNPIN_SIDEBAR_COMMAND',
          timestamp: Date.now()
        });
      }
      return { success: true };
    });
  }

  /**
   * 验证消息格式
   */
  private validateMessage(message: Message): boolean {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    return true;
  }

  /**
   * 向其他标签页广播消息
   */
  private async broadcastToOtherTabs(message: Message, excludeTabId?: number): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({});
      const promises = tabs
        .filter(tab => tab.id && tab.id !== excludeTabId)
        .map(tab => {
          return chrome.tabs.sendMessage(tab.id!, message).catch(error => {
            // 忽略无法发送消息的标签页
            if (!error.message?.includes('Could not establish connection')) {
              console.warn(`Failed to send message to tab ${tab.id}:`, error);
            }
          });
        });
      
      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Failed to broadcast message:', error);
    }
  }

  /**
   * 添加自定义消息处理器
   */
  addHandler(type: MessageType, handler: (message: Message, sender: chrome.runtime.MessageSender) => Promise<MessageResponse>): void {
    this.messageHandlers.set(type, handler);
  }

  /**
   * 移除消息处理器
   */
  removeHandler(type: MessageType): void {
    this.messageHandlers.delete(type);
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.messageHandlers.clear();
  }
}