/**
 * Settings Manager - 设置管理服务
 */

import { UserSettings, DEFAULT_SETTINGS } from '../types/index.js';
import { SettingsManager as ISettingsManager } from '../../interfaces/background.interface.js';
import { <PERSON>rror<PERSON>andler } from '../utils/ErrorHandler.js';
import { SettingsSync } from './SettingsSync.js';

export class SettingsManager implements ISettingsManager {
  private settingsChangeCallbacks: ((settings: UserSettings) => void)[] = [];
  private currentSettings: UserSettings = { ...DEFAULT_SETTINGS };
  private initialized = false;
  private readonly SETTINGS_KEY = 'userSettings';
  private readonly SETTINGS_VERSION = '1.0.0';
  private settingsSync: SettingsSync;

  /**
   * 初始化设置管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Settings Manager...');

      // 初始化同步服务
      this.settingsSync = new SettingsSync({
        syncInterval: 5000,
        maxRetries: 3,
        retryDelay: 1000
      });

      // 加载设置
      await this.loadSettings();

      // 设置存储变化监听
      this.setupStorageListener();

      // 初始化同步服务
      this.settingsSync.initialize();

      // 设置同步回调
      this.settingsSync.onSync((settings) => {
        this.currentSettings = settings;
        this.notifySettingsChanged(settings);
      });

      this.initialized = true;
      console.log('Settings Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Settings Manager:', error);
      ErrorHandler.handleError(error, 'SettingsManager.initialize');
      throw error;
    }
  }

  /**
   * 加载设置
   */
  async load(): Promise<UserSettings> {
    try {
      if (!this.initialized) {
        await this.loadSettings();
      }
      return { ...this.currentSettings };
    } catch (error) {
      ErrorHandler.handleError(error, 'SettingsManager.load');
      return { ...DEFAULT_SETTINGS };
    }
  }

  /**
   * 保存设置
   */
  async save(settings: Partial<UserSettings>): Promise<void> {
    try {
      // 验证设置
      const validatedSettings = this.validateAndMergeSettings(settings);
      
      // 更新当前设置
      this.currentSettings = validatedSettings;

      // 保存到存储
      await this.saveToStorage(validatedSettings);

      // 通知变化
      this.notifySettingsChanged(validatedSettings);

      // 同步到所有标签页
      if (this.settingsSync) {
        await this.settingsSync.syncToAllTabs(validatedSettings);
      }

      console.log('Settings saved successfully:', validatedSettings);
    } catch (error) {
      ErrorHandler.handleError(error, 'SettingsManager.save');
      throw error;
    }
  }

  /**
   * 重置设置为默认值
   */
  async reset(): Promise<void> {
    try {
      const defaultSettings = { ...DEFAULT_SETTINGS };
      
      // 更新当前设置
      this.currentSettings = defaultSettings;

      // 保存到存储
      await this.saveToStorage(defaultSettings);

      // 通知变化
      this.notifySettingsChanged(defaultSettings);

      // 同步到所有标签页
      if (this.settingsSync) {
        await this.settingsSync.syncToAllTabs(defaultSettings);
      }

      console.log('Settings reset to default');
    } catch (error) {
      ErrorHandler.handleError(error, 'SettingsManager.reset');
      throw error;
    }
  }

  /**
   * 导出设置
   */
  async export(): Promise<string> {
    try {
      const exportData = {
        version: this.SETTINGS_VERSION,
        timestamp: Date.now(),
        settings: this.currentSettings
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      ErrorHandler.handleError(error, 'SettingsManager.export');
      throw error;
    }
  }

  /**
   * 导入设置
   */
  async import(data: string): Promise<void> {
    try {
      const importData = JSON.parse(data);
      
      // 验证导入数据格式
      if (!this.validateImportData(importData)) {
        throw new Error('Invalid import data format');
      }

      // 验证版本兼容性
      if (!this.isVersionCompatible(importData.version)) {
        console.warn(`Importing from different version: ${importData.version}`);
      }

      // 验证并合并设置
      const validatedSettings = this.validateAndMergeSettings(importData.settings);
      
      // 保存设置
      await this.save(validatedSettings);

      console.log('Settings imported successfully');
    } catch (error) {
      ErrorHandler.handleError(error, 'SettingsManager.import');
      throw error;
    }
  }

  /**
   * 验证设置
   */
  validate(settings: any): boolean {
    try {
      if (!settings || typeof settings !== 'object') {
        return false;
      }

      // 验证必需字段
      const requiredFields = ['position', 'theme', 'expandDelay', 'collapseDelay'];
      for (const field of requiredFields) {
        if (!(field in settings)) {
          return false;
        }
      }

      // 验证字段类型和值
      if (!['left', 'right'].includes(settings.position)) {
        return false;
      }

      if (!['light', 'dark', 'auto'].includes(settings.theme)) {
        return false;
      }

      if (typeof settings.expandDelay !== 'number' || 
          settings.expandDelay < 100 || 
          settings.expandDelay > 2000) {
        return false;
      }

      if (typeof settings.collapseDelay !== 'number' || 
          settings.collapseDelay < 100 || 
          settings.collapseDelay > 2000) {
        return false;
      }

      if (typeof settings.showBookmarks !== 'boolean') {
        return false;
      }

      if (typeof settings.showTabs !== 'boolean') {
        return false;
      }

      if (typeof settings.defaultPinned !== 'boolean') {
        return false;
      }

      // 验证键盘快捷键
      if (!settings.keyboardShortcuts || 
          typeof settings.keyboardShortcuts !== 'object') {
        return false;
      }

      if (typeof settings.keyboardShortcuts.toggle !== 'string' ||
          typeof settings.keyboardShortcuts.search !== 'string') {
        return false;
      }

      // 验证侧边栏宽度
      if (!settings.sidebarWidth || 
          typeof settings.sidebarWidth !== 'object') {
        return false;
      }

      if (typeof settings.sidebarWidth.collapsed !== 'number' ||
          settings.sidebarWidth.collapsed < 40 ||
          settings.sidebarWidth.collapsed > 100) {
        return false;
      }

      if (typeof settings.sidebarWidth.expanded !== 'number' ||
          settings.sidebarWidth.expanded < 200 ||
          settings.sidebarWidth.expanded > 500) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Settings validation error:', error);
      return false;
    }
  }

  /**
   * 添加设置变化监听器
   */
  onSettingsChanged(callback: (settings: UserSettings) => void): void {
    this.settingsChangeCallbacks.push(callback);
  }

  /**
   * 移除设置变化监听器
   */
  removeSettingsChangedListener(callback: (settings: UserSettings) => void): void {
    const index = this.settingsChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.settingsChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 获取特定设置项
   */
  getSetting<K extends keyof UserSettings>(key: K): UserSettings[K] {
    return this.currentSettings[key];
  }

  /**
   * 设置特定设置项
   */
  async setSetting<K extends keyof UserSettings>(key: K, value: UserSettings[K]): Promise<void> {
    const updates = { [key]: value } as Partial<UserSettings>;
    await this.save(updates);
  }

  /**
   * 从存储加载设置
   */
  private async loadSettings(): Promise<void> {
    try {
      const result = await chrome.storage.sync.get(this.SETTINGS_KEY);
      
      if (result[this.SETTINGS_KEY]) {
        const storedSettings = result[this.SETTINGS_KEY];
        
        // 验证存储的设置
        if (this.validate(storedSettings)) {
          this.currentSettings = this.validateAndMergeSettings(storedSettings);
        } else {
          console.warn('Invalid stored settings, using defaults');
          this.currentSettings = { ...DEFAULT_SETTINGS };
          await this.saveToStorage(this.currentSettings);
        }
      } else {
        // 首次使用，保存默认设置
        this.currentSettings = { ...DEFAULT_SETTINGS };
        await this.saveToStorage(this.currentSettings);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.currentSettings = { ...DEFAULT_SETTINGS };
    }
  }

  /**
   * 保存设置到存储
   */
  private async saveToStorage(settings: UserSettings): Promise<void> {
    try {
      await chrome.storage.sync.set({
        [this.SETTINGS_KEY]: settings
      });
    } catch (error) {
      // 如果sync存储失败，尝试使用local存储
      console.warn('Failed to save to sync storage, trying local storage:', error);
      try {
        await chrome.storage.local.set({
          [this.SETTINGS_KEY]: settings
        });
      } catch (localError) {
        console.error('Failed to save to local storage:', localError);
        throw localError;
      }
    }
  }

  /**
   * 验证并合并设置
   */
  private validateAndMergeSettings(settings: Partial<UserSettings>): UserSettings {
    const mergedSettings = { ...this.currentSettings, ...settings };
    
    // 确保所有必需字段都存在
    const validatedSettings: UserSettings = {
      position: mergedSettings.position || DEFAULT_SETTINGS.position,
      theme: mergedSettings.theme || DEFAULT_SETTINGS.theme,
      expandDelay: this.validateNumber(mergedSettings.expandDelay, 100, 2000, DEFAULT_SETTINGS.expandDelay),
      collapseDelay: this.validateNumber(mergedSettings.collapseDelay, 100, 2000, DEFAULT_SETTINGS.collapseDelay),
      showBookmarks: mergedSettings.showBookmarks ?? DEFAULT_SETTINGS.showBookmarks,
      showTabs: mergedSettings.showTabs ?? DEFAULT_SETTINGS.showTabs,
      defaultPinned: mergedSettings.defaultPinned ?? DEFAULT_SETTINGS.defaultPinned,
      keyboardShortcuts: {
        toggle: mergedSettings.keyboardShortcuts?.toggle || DEFAULT_SETTINGS.keyboardShortcuts.toggle,
        search: mergedSettings.keyboardShortcuts?.search || DEFAULT_SETTINGS.keyboardShortcuts.search
      },
      sidebarWidth: {
        collapsed: this.validateNumber(
          mergedSettings.sidebarWidth?.collapsed, 
          40, 100, 
          DEFAULT_SETTINGS.sidebarWidth.collapsed
        ),
        expanded: this.validateNumber(
          mergedSettings.sidebarWidth?.expanded, 
          200, 500, 
          DEFAULT_SETTINGS.sidebarWidth.expanded
        )
      }
    };

    return validatedSettings;
  }

  /**
   * 验证数字范围
   */
  private validateNumber(value: any, min: number, max: number, defaultValue: number): number {
    if (typeof value === 'number' && value >= min && value <= max) {
      return value;
    }
    return defaultValue;
  }

  /**
   * 验证导入数据
   */
  private validateImportData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           data.version && 
           data.settings && 
           typeof data.settings === 'object';
  }

  /**
   * 检查版本兼容性
   */
  private isVersionCompatible(version: string): boolean {
    // 简单的版本兼容性检查
    const currentMajor = parseInt(this.SETTINGS_VERSION.split('.')[0]);
    const importMajor = parseInt(version.split('.')[0]);
    
    return currentMajor === importMajor;
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListener(): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'sync' && changes[this.SETTINGS_KEY]) {
        const newSettings = changes[this.SETTINGS_KEY].newValue;
        if (newSettings && this.validate(newSettings)) {
          this.currentSettings = newSettings;
          this.notifySettingsChanged(newSettings);
        }
      }
    });
  }

  /**
   * 通知设置变化
   */
  private notifySettingsChanged(settings: UserSettings): void {
    this.settingsChangeCallbacks.forEach(callback => {
      try {
        callback(settings);
      } catch (error) {
        console.error('Error in settings change callback:', error);
      }
    });
  }

  /**
   * 强制同步设置到所有标签页
   */
  async forceSync(): Promise<void> {
    if (this.settingsSync) {
      await this.settingsSync.forcSync(this.currentSettings);
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return this.settingsSync ? this.settingsSync.getSyncStatus() : null;
  }

  /**
   * 添加冲突解决器
   */
  addConflictResolver(resolver: (local: UserSettings, remote: UserSettings) => UserSettings): void {
    if (this.settingsSync) {
      this.settingsSync.addConflictResolver(resolver);
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.settingsChangeCallbacks = [];
    if (this.settingsSync) {
      this.settingsSync.destroy();
    }
    this.initialized = false;
  }
}