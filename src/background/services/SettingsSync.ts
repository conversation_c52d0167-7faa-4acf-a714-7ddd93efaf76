/**
 * Settings Sync - 设置同步服务
 * 处理跨标签页的设置同步和实时通知
 */

import { UserSettings } from '../types/index.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';

export interface SettingsSyncOptions {
  syncInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export class SettingsSync {
  private syncCallbacks: ((settings: UserSettings) => void)[] = [];
  private conflictResolvers: ((local: UserSettings, remote: UserSettings) => UserSettings)[] = [];
  private lastSyncTimestamp = 0;
  private syncInProgress = false;
  private options: Required<SettingsSyncOptions>;
  private syncTimer?: NodeJS.Timeout;

  constructor(options: SettingsSyncOptions = {}) {
    this.options = {
      syncInterval: options.syncInterval || 5000, // 5秒同步间隔
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000
    };
  }

  /**
   * 初始化设置同步
   */
  initialize(): void {
    console.log('Initializing Settings Sync...');

    // 设置存储变化监听
    this.setupStorageListener();

    // 设置定期同步
    this.startPeriodicSync();

    // 监听标签页激活事件，触发同步
    this.setupTabActivationListener();

    console.log('Settings Sync initialized');
  }

  /**
   * 同步设置到所有标签页
   */
  async syncToAllTabs(settings: UserSettings): Promise<void> {
    if (this.syncInProgress) {
      console.log('Sync already in progress, skipping');
      return;
    }

    this.syncInProgress = true;

    try {
      console.log('Syncing settings to all tabs:', settings);

      // 获取所有标签页
      const tabs = await chrome.tabs.query({});
      
      // 创建同步任务
      const syncTasks = tabs
        .filter(tab => tab.id && tab.url && !tab.url.startsWith('chrome://'))
        .map(tab => this.syncToTab(tab.id!, settings));

      // 并行执行同步任务
      const results = await Promise.allSettled(syncTasks);

      // 统计同步结果
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      console.log(`Settings sync completed: ${successful} successful, ${failed} failed`);

      // 更新同步时间戳
      this.lastSyncTimestamp = Date.now();

      // 通知同步完成
      this.notifySyncCallbacks(settings);

    } catch (error) {
      console.error('Failed to sync settings to all tabs:', error);
      ErrorHandler.handleError(error, 'SettingsSync.syncToAllTabs');
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 同步设置到指定标签页
   */
  private async syncToTab(tabId: number, settings: UserSettings, retries = 0): Promise<void> {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'SETTINGS_UPDATED',
        payload: settings,
        timestamp: Date.now()
      });
    } catch (error) {
      if (retries < this.options.maxRetries) {
        console.log(`Retrying sync to tab ${tabId}, attempt ${retries + 1}`);
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
        return this.syncToTab(tabId, settings, retries + 1);
      } else {
        // 忽略无法连接的标签页（如chrome://页面）
        if (!error.message?.includes('Could not establish connection')) {
          console.warn(`Failed to sync settings to tab ${tabId}:`, error);
        }
      }
    }
  }

  /**
   * 处理设置冲突
   */
  async resolveConflict(localSettings: UserSettings, remoteSettings: UserSettings): Promise<UserSettings> {
    try {
      // 使用注册的冲突解决器
      for (const resolver of this.conflictResolvers) {
        try {
          const resolved = resolver(localSettings, remoteSettings);
          if (resolved) {
            console.log('Settings conflict resolved using custom resolver');
            return resolved;
          }
        } catch (error) {
          console.error('Error in conflict resolver:', error);
        }
      }

      // 默认冲突解决策略：使用最新的设置
      const localTimestamp = this.getSettingsTimestamp(localSettings);
      const remoteTimestamp = this.getSettingsTimestamp(remoteSettings);

      if (remoteTimestamp > localTimestamp) {
        console.log('Using remote settings (newer)');
        return remoteSettings;
      } else {
        console.log('Using local settings (newer or same)');
        return localSettings;
      }
    } catch (error) {
      console.error('Error resolving settings conflict:', error);
      return localSettings; // 默认使用本地设置
    }
  }

  /**
   * 添加同步回调
   */
  onSync(callback: (settings: UserSettings) => void): void {
    this.syncCallbacks.push(callback);
  }

  /**
   * 移除同步回调
   */
  removeSyncCallback(callback: (settings: UserSettings) => void): void {
    const index = this.syncCallbacks.indexOf(callback);
    if (index > -1) {
      this.syncCallbacks.splice(index, 1);
    }
  }

  /**
   * 添加冲突解决器
   */
  addConflictResolver(resolver: (local: UserSettings, remote: UserSettings) => UserSettings): void {
    this.conflictResolvers.push(resolver);
  }

  /**
   * 移除冲突解决器
   */
  removeConflictResolver(resolver: (local: UserSettings, remote: UserSettings) => UserSettings): void {
    const index = this.conflictResolvers.indexOf(resolver);
    if (index > -1) {
      this.conflictResolvers.splice(index, 1);
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): {
    inProgress: boolean;
    lastSync: number;
    nextSync: number;
  } {
    return {
      inProgress: this.syncInProgress,
      lastSync: this.lastSyncTimestamp,
      nextSync: this.lastSyncTimestamp + this.options.syncInterval
    };
  }

  /**
   * 强制立即同步
   */
  async forcSync(settings: UserSettings): Promise<void> {
    await this.syncToAllTabs(settings);
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListener(): void {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === 'sync' && changes.userSettings) {
        const newSettings = changes.userSettings.newValue;
        if (newSettings) {
          console.log('Settings changed in storage, syncing to tabs');
          this.syncToAllTabs(newSettings).catch(error => {
            console.error('Failed to sync after storage change:', error);
          });
        }
      }
    });
  }

  /**
   * 设置标签页激活监听器
   */
  private setupTabActivationListener(): void {
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        // 当标签页激活时，确保其设置是最新的
        const result = await chrome.storage.sync.get('userSettings');
        if (result.userSettings) {
          await this.syncToTab(activeInfo.tabId, result.userSettings);
        }
      } catch (error) {
        // 忽略同步错误，不影响标签页切换
        console.debug('Failed to sync settings to activated tab:', error);
      }
    });
  }

  /**
   * 开始定期同步
   */
  private startPeriodicSync(): void {
    this.syncTimer = setInterval(async () => {
      try {
        const result = await chrome.storage.sync.get('userSettings');
        if (result.userSettings) {
          await this.syncToAllTabs(result.userSettings);
        }
      } catch (error) {
        console.error('Error in periodic sync:', error);
      }
    }, this.options.syncInterval);
  }

  /**
   * 停止定期同步
   */
  private stopPeriodicSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }
  }

  /**
   * 通知同步回调
   */
  private notifySyncCallbacks(settings: UserSettings): void {
    this.syncCallbacks.forEach(callback => {
      try {
        callback(settings);
      } catch (error) {
        console.error('Error in sync callback:', error);
      }
    });
  }

  /**
   * 获取设置的时间戳
   */
  private getSettingsTimestamp(settings: any): number {
    // 如果设置中包含时间戳，使用它
    if (settings._timestamp && typeof settings._timestamp === 'number') {
      return settings._timestamp;
    }
    
    // 否则使用当前时间
    return Date.now();
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopPeriodicSync();
    this.syncCallbacks = [];
    this.conflictResolvers = [];
    this.syncInProgress = false;
    console.log('Settings Sync destroyed');
  }
}