/**
 * Tab Manager - 标签页管理服务
 */

import { TabInfo, TabGroup } from '../types/index.js';
import { TabManager as ITabManager } from '../../interfaces/background.interface.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';

export class TabManager implements ITabManager {
  private tabChangeCallbacks: ((tabs: TabInfo[]) => void)[] = [];
  private tabGroups: Map<number, TabGroup> = new Map();
  private initialized = false;

  /**
   * 初始化标签页管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Tab Manager...');

      // 设置标签页事件监听
      this.setupTabEventListeners();

      // 初始化标签页分组数据
      await this.loadTabGroups();

      this.initialized = true;
      console.log('Tab Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Tab Manager:', error);
      ErrorHandler.handleError(error, 'TabManager.initialize');
      throw error;
    }
  }

  /**
   * 获取当前窗口的所有标签页
   */
  async getCurrentWindowTabs(): Promise<TabInfo[]> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      return tabs.map(tab => this.convertChromeTabToTabInfo(tab));
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.getCurrentWindowTabs');
      throw error;
    }
  }

  /**
   * 根据ID获取标签页
   */
  async getTabById(tabId: number): Promise<TabInfo | null> {
    try {
      const tab = await chrome.tabs.get(tabId);
      return tab ? this.convertChromeTabToTabInfo(tab) : null;
    } catch (error) {
      if (error instanceof Error && error.message.includes('No tab with id')) {
        return null;
      }
      ErrorHandler.handleError(error, 'TabManager.getTabById');
      throw error;
    }
  }

  /**
   * 激活指定标签页
   */
  async activateTab(tabId: number): Promise<void> {
    try {
      await chrome.tabs.update(tabId, { active: true });
      
      // 确保窗口也被激活
      const tab = await chrome.tabs.get(tabId);
      if (tab.windowId) {
        await chrome.windows.update(tab.windowId, { focused: true });
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.activateTab');
      throw error;
    }
  }

  /**
   * 关闭标签页
   */
  async closeTab(tabId: number): Promise<void> {
    try {
      await chrome.tabs.remove(tabId);
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.closeTab');
      throw error;
    }
  }

  /**
   * 固定/取消固定标签页
   */
  async pinTab(tabId: number, pinned: boolean): Promise<void> {
    try {
      await chrome.tabs.update(tabId, { pinned });
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.pinTab');
      throw error;
    }
  }

  /**
   * 移动标签页位置
   */
  async moveTab(tabId: number, index: number): Promise<void> {
    try {
      await chrome.tabs.move(tabId, { index });
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.moveTab');
      throw error;
    }
  }

  /**
   * 复制标签页
   */
  async duplicateTab(tabId: number): Promise<TabInfo> {
    try {
      const originalTab = await chrome.tabs.get(tabId);
      const duplicatedTab = await chrome.tabs.create({
        url: originalTab.url,
        windowId: originalTab.windowId,
        index: originalTab.index + 1,
        active: false
      });
      
      return this.convertChromeTabToTabInfo(duplicatedTab);
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.duplicateTab');
      throw error;
    }
  }

  /**
   * 创建新标签页
   */
  async createTab(url: string, active = false): Promise<TabInfo> {
    try {
      const newTab = await chrome.tabs.create({
        url,
        active
      });
      
      return this.convertChromeTabToTabInfo(newTab);
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.createTab');
      throw error;
    }
  }

  /**
   * 关闭其他标签页
   */
  async closeOtherTabs(keepTabId: number): Promise<void> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabsToClose = tabs
        .filter(tab => tab.id !== keepTabId && tab.id !== undefined)
        .map(tab => tab.id!);
      
      if (tabsToClose.length > 0) {
        await chrome.tabs.remove(tabsToClose);
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.closeOtherTabs');
      throw error;
    }
  }

  /**
   * 取消标签页分组
   */
  async ungroupTabs(groupId: number): Promise<void> {
    try {
      if (chrome.tabGroups) {
        await chrome.tabGroups.ungroup(groupId);
      } else {
        // 对于自定义分组，移除分组记录
        this.tabGroups.delete(groupId);
        await this.saveTabGroups();
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.ungroupTabs');
      throw error;
    }
  }

  /**
   * 关闭标签页分组
   */
  async closeTabGroup(groupId: number): Promise<void> {
    try {
      if (chrome.tabGroups) {
        // 获取分组中的所有标签页
        const tabs = await chrome.tabs.query({ groupId });
        const tabIds = tabs.map(tab => tab.id!).filter(id => id !== undefined);
        
        if (tabIds.length > 0) {
          await chrome.tabs.remove(tabIds);
        }
      } else {
        // 对于自定义分组
        const group = this.tabGroups.get(groupId);
        if (group && group.tabIds.length > 0) {
          await chrome.tabs.remove(group.tabIds);
          this.tabGroups.delete(groupId);
          await this.saveTabGroups();
        }
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.closeTabGroup');
      throw error;
    }
  }

  /**
   * 获取所有标签页分组
   */
  async getTabGroups(): Promise<TabGroup[]> {
    try {
      if (chrome.tabGroups) {
        const groups = await chrome.tabGroups.query({ windowId: chrome.windows.WINDOW_ID_CURRENT });
        return groups.map(group => this.convertChromeGroupToTabGroup(group));
      }
      
      // 如果不支持原生分组，返回自定义分组
      return Array.from(this.tabGroups.values());
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.getTabGroups');
      return Array.from(this.tabGroups.values());
    }
  }

  /**
   * 创建标签页分组
   */
  async createTabGroup(name: string, color: string, tabIds: number[]): Promise<TabGroup> {
    try {
      if (chrome.tabGroups && tabIds.length > 0) {
        // 使用原生Chrome分组API
        const groupId = await chrome.tabs.group({ tabIds });
        await chrome.tabGroups.update(groupId, {
          title: name,
          color: color as chrome.tabGroups.ColorEnum
        });
        
        const group = await chrome.tabGroups.get(groupId);
        return this.convertChromeGroupToTabGroup(group);
      } else {
        // 创建自定义分组
        const groupId = Date.now();
        const group: TabGroup = {
          id: groupId,
          title: name,
          color,
          collapsed: false,
          tabIds,
          windowId: chrome.windows.WINDOW_ID_CURRENT
        };
        
        this.tabGroups.set(groupId, group);
        await this.saveTabGroups();
        
        return group;
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.createTabGroup');
      throw error;
    }
  }

  /**
   * 更新标签页分组
   */
  async updateTabGroup(groupId: number, updates: Partial<TabGroup>): Promise<void> {
    try {
      if (chrome.tabGroups) {
        // 更新原生分组
        const updateData: any = {};
        if (updates.title) updateData.title = updates.title;
        if (updates.color) updateData.color = updates.color;
        if (updates.collapsed !== undefined) updateData.collapsed = updates.collapsed;
        
        await chrome.tabGroups.update(groupId, updateData);
      } else {
        // 更新自定义分组
        const existingGroup = this.tabGroups.get(groupId);
        if (existingGroup) {
          const updatedGroup = { ...existingGroup, ...updates };
          this.tabGroups.set(groupId, updatedGroup);
          await this.saveTabGroups();
        }
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.updateTabGroup');
      throw error;
    }
  }

  /**
   * 删除标签页分组
   */
  async deleteTabGroup(groupId: number): Promise<void> {
    try {
      if (chrome.tabGroups) {
        await chrome.tabGroups.ungroup(groupId);
      } else {
        this.tabGroups.delete(groupId);
        await this.saveTabGroups();
      }
    } catch (error) {
      ErrorHandler.handleError(error, 'TabManager.deleteTabGroup');
      throw error;
    }
  }

  /**
   * 添加标签页变化监听器
   */
  onTabsChanged(callback: (tabs: TabInfo[]) => void): void {
    this.tabChangeCallbacks.push(callback);
  }

  /**
   * 移除标签页变化监听器
   */
  removeTabsChangedListener(callback: (tabs: TabInfo[]) => void): void {
    const index = this.tabChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.tabChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 设置标签页事件监听器
   */
  private setupTabEventListeners(): void {
    // 标签页创建
    chrome.tabs.onCreated.addListener((tab) => {
      this.notifyTabsChanged();
    });

    // 标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      // 只在重要属性变化时通知
      if (changeInfo.title || changeInfo.url || changeInfo.status === 'complete') {
        this.notifyTabsChanged();
      }
    });

    // 标签页移除
    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.notifyTabsChanged();
    });

    // 标签页激活
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.notifyTabsChanged();
    });

    // 标签页移动
    chrome.tabs.onMoved.addListener((tabId, moveInfo) => {
      this.notifyTabsChanged();
    });

    // 标签页附加到窗口
    chrome.tabs.onAttached.addListener((tabId, attachInfo) => {
      this.notifyTabsChanged();
    });

    // 标签页从窗口分离
    chrome.tabs.onDetached.addListener((tabId, detachInfo) => {
      this.notifyTabsChanged();
    });

    // 标签页分组事件（如果支持）
    if (chrome.tabGroups) {
      chrome.tabGroups.onCreated.addListener(() => {
        this.notifyTabsChanged();
      });

      chrome.tabGroups.onUpdated.addListener(() => {
        this.notifyTabsChanged();
      });

      chrome.tabGroups.onRemoved.addListener(() => {
        this.notifyTabsChanged();
      });
    }
  }

  /**
   * 通知标签页变化
   */
  private async notifyTabsChanged(): Promise<void> {
    try {
      const tabs = await this.getCurrentWindowTabs();
      this.tabChangeCallbacks.forEach(callback => {
        try {
          callback(tabs);
        } catch (error) {
          console.error('Error in tab change callback:', error);
        }
      });
    } catch (error) {
      console.error('Error notifying tab changes:', error);
    }
  }

  /**
   * 转换Chrome标签页对象为TabInfo
   */
  private convertChromeTabToTabInfo(tab: chrome.tabs.Tab): TabInfo {
    return {
      id: tab.id!,
      title: tab.title || '',
      url: tab.url || '',
      favIconUrl: tab.favIconUrl,
      active: tab.active,
      pinned: tab.pinned,
      groupId: tab.groupId !== -1 ? tab.groupId : undefined,
      index: tab.index,
      windowId: tab.windowId
    };
  }

  /**
   * 转换Chrome分组对象为TabGroup
   */
  private convertChromeGroupToTabGroup(group: chrome.tabGroups.TabGroup): TabGroup {
    return {
      id: group.id,
      title: group.title || '',
      color: group.color,
      collapsed: group.collapsed,
      tabIds: [], // 需要单独查询
      windowId: group.windowId
    };
  }

  /**
   * 加载标签页分组数据
   */
  private async loadTabGroups(): Promise<void> {
    try {
      const result = await chrome.storage.local.get('tabGroups');
      if (result.tabGroups) {
        const groups = JSON.parse(result.tabGroups);
        this.tabGroups = new Map(groups);
      }
    } catch (error) {
      console.error('Failed to load tab groups:', error);
    }
  }

  /**
   * 保存标签页分组数据
   */
  private async saveTabGroups(): Promise<void> {
    try {
      const groups = Array.from(this.tabGroups.entries());
      await chrome.storage.local.set({ tabGroups: JSON.stringify(groups) });
    } catch (error) {
      console.error('Failed to save tab groups:', error);
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.tabChangeCallbacks = [];
    this.tabGroups.clear();
    this.initialized = false;
  }
}