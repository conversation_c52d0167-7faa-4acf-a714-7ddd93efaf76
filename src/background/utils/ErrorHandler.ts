/**
 * Error Handler - 统一错误处理
 */

import { ExtensionError } from '../types/index.js';

export class ErrorHandler {
  private static errorLog: ExtensionError[] = [];
  private static maxLogSize = 100;

  /**
   * 处理通用错误
   */
  static handleError(error: unknown, context: string): void {
    const extensionError = this.createExtensionError(error, context);
    this.logError(extensionError);
    
    // 根据错误类型采取不同策略
    if (this.isChromeAPIError(error)) {
      this.handleChromeAPIError(error as chrome.runtime.LastError, context);
    } else if (this.isNetworkError(error)) {
      this.handleNetworkError(error as Error, context);
    } else if (this.isDOMError(error)) {
      this.handleDOMError(error as Error, context);
    } else {
      this.handleGenericError(error as Error, context);
    }
  }

  /**
   * 处理Chrome API错误
   */
  static handleChromeAPIError(error: chrome.runtime.LastError, context: string): void {
    console.error(`Chrome API Error in ${context}:`, error);
    
    if (error.message?.includes('permission')) {
      this.showPermissionError(context);
    } else if (error.message?.includes('network')) {
      this.retryWithBackoff(context);
    } else {
      this.reportError(error, context);
    }
  }

  /**
   * 处理DOM错误
   */
  static handleDOMError(error: Error, context: string): void {
    console.error(`DOM Error in ${context}:`, error);
    
    try {
      this.recoverFromDOMError(context);
    } catch (recoveryError) {
      console.error('Failed to recover from DOM error:', recoveryError);
      this.enterSafeMode(context);
    }
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(error: Error, context: string): void {
    console.error(`Network Error in ${context}:`, error);
    
    // 实现重试逻辑
    this.retryWithBackoff(context);
  }

  /**
   * 处理通用错误
   */
  static handleGenericError(error: Error, context: string): void {
    console.error(`Generic Error in ${context}:`, error);
    
    // 记录错误并继续执行
    this.reportError(error, context);
  }

  /**
   * 创建扩展错误对象
   */
  private static createExtensionError(error: unknown, context: string): ExtensionError {
    const timestamp = Date.now();
    
    if (error instanceof Error) {
      return {
        code: error.name || 'UnknownError',
        message: error.message,
        context,
        timestamp,
        stack: error.stack
      };
    } else if (typeof error === 'string') {
      return {
        code: 'StringError',
        message: error,
        context,
        timestamp
      };
    } else {
      return {
        code: 'UnknownError',
        message: 'Unknown error occurred',
        context,
        timestamp
      };
    }
  }

  /**
   * 记录错误
   */
  private static logError(error: ExtensionError): void {
    this.errorLog.push(error);
    
    // 保持日志大小限制
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }
    
    // 在开发模式下输出详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Extension Error: ${error.code}`);
      console.error('Message:', error.message);
      console.error('Context:', error.context);
      console.error('Timestamp:', new Date(error.timestamp).toISOString());
      if (error.stack) {
        console.error('Stack:', error.stack);
      }
      console.groupEnd();
    }
  }

  /**
   * 检查是否为Chrome API错误
   */
  private static isChromeAPIError(error: unknown): boolean {
    return error === chrome.runtime.lastError || 
           (error instanceof Error && error.message?.includes('chrome'));
  }

  /**
   * 检查是否为网络错误
   */
  private static isNetworkError(error: unknown): boolean {
    return error instanceof Error && 
           (error.message?.includes('network') || 
            error.message?.includes('fetch') ||
            error.message?.includes('timeout'));
  }

  /**
   * 检查是否为DOM错误
   */
  private static isDOMError(error: unknown): boolean {
    return error instanceof Error && 
           (error.message?.includes('DOM') || 
            error.message?.includes('element') ||
            error.message?.includes('querySelector'));
  }

  /**
   * 显示权限错误
   */
  private static showPermissionError(context: string): void {
    console.warn(`Permission error in ${context}. Please check extension permissions.`);
    
    // 可以在这里添加用户通知逻辑
    chrome.notifications?.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Chrome Vertical Sidebar',
      message: '扩展需要额外权限才能正常工作，请检查扩展设置。'
    });
  }

  /**
   * 带退避的重试机制
   */
  private static async retryWithBackoff(context: string, maxRetries = 3): Promise<void> {
    for (let i = 0; i < maxRetries; i++) {
      const delay = Math.pow(2, i) * 1000; // 指数退避
      
      console.log(`Retrying ${context} in ${delay}ms (attempt ${i + 1}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      try {
        // 这里应该重新执行失败的操作
        // 具体实现取决于上下文
        break;
      } catch (error) {
        if (i === maxRetries - 1) {
          console.error(`All retry attempts failed for ${context}`);
          this.reportError(error, `${context}_retry_failed`);
        }
      }
    }
  }

  /**
   * 从DOM错误中恢复
   */
  private static recoverFromDOMError(context: string): void {
    console.log(`Attempting to recover from DOM error in ${context}`);
    
    // 实现DOM恢复逻辑
    // 例如：重新注入样式、重建DOM结构等
  }

  /**
   * 进入安全模式
   */
  private static enterSafeMode(context: string): void {
    console.warn(`Entering safe mode due to error in ${context}`);
    
    // 实现安全模式逻辑
    // 例如：禁用某些功能、使用降级方案等
  }

  /**
   * 报告错误
   */
  private static reportError(error: unknown, context: string): void {
    // 在生产环境中，这里可以发送错误报告到服务器
    // 目前只记录到控制台
    console.error(`Reported error in ${context}:`, error);
  }

  /**
   * 获取错误日志
   */
  static getErrorLog(): ExtensionError[] {
    return [...this.errorLog];
  }

  /**
   * 清除错误日志
   */
  static clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * 获取错误统计
   */
  static getErrorStats(): { total: number; byCode: Record<string, number>; byContext: Record<string, number> } {
    const stats = {
      total: this.errorLog.length,
      byCode: {} as Record<string, number>,
      byContext: {} as Record<string, number>
    };

    this.errorLog.forEach(error => {
      stats.byCode[error.code] = (stats.byCode[error.code] || 0) + 1;
      stats.byContext[error.context] = (stats.byContext[error.context] || 0) + 1;
    });

    return stats;
  }
}