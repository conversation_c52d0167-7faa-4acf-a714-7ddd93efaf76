/**
 * Content Script - 侧边栏注入和管理
 * 负责在网页中注入侧边栏UI并处理用户交互
 */

// import { SidebarManager } from '../../interfaces/content.interface.js';
import { SidebarState, UserSettings } from '../types/index.js';

console.log('Chrome Vertical Sidebar - Content Script Loaded');

// 全局侧边栏管理器实例
let sidebarManager: any | null = null;

/**
 * 初始化内容脚本
 */
async function initializeContentScript(): Promise<void> {
  try {
    // 检查是否可以注入侧边栏
    if (!canInjectSidebar()) {
      console.log('Cannot inject sidebar on this page');
      return;
    }
    
    // 创建侧边栏管理器实例
    const { SidebarManagerImpl } = await import('./sidebar/SidebarManager');
    sidebarManager = new SidebarManagerImpl();
    
    // 初始化侧边栏
    await sidebarManager.initialize();
    
    // 设置消息监听
    setupMessageListener();
    
    console.log('Content script initialized successfully');
  } catch (error) {
    console.error('Failed to initialize content script:', error);
  }
}

/**
 * 检查是否可以在当前页面注入侧边栏
 */
function canInjectSidebar(): boolean {
  // 检查页面类型
  const url = window.location.href;
  
  // 排除特殊页面
  if (url.startsWith('chrome://') || 
      url.startsWith('chrome-extension://') ||
      url.startsWith('moz-extension://') ||
      url.startsWith('about:') ||
      url.startsWith('file://')) {
    return false;
  }
  
  // 检查CSP限制
  const metaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
  for (const meta of metaTags) {
    const content = meta.getAttribute('content');
    if (content && content.includes('script-src') && !content.includes('unsafe-inline')) {
      console.warn('CSP restrictions detected, sidebar may not work properly');
    }
  }
  
  return true;
}

/**
 * 设置消息监听器
 */
function setupMessageListener(): void {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Content script received message:', message);
    
    if (!sidebarManager) {
      sendResponse({ success: false, error: 'Sidebar not initialized' });
      return;
    }
    
    try {
      switch (message.type) {
        case 'TOGGLE_SIDEBAR':
          sidebarManager.toggle();
          sendResponse({ success: true });
          break;
          
        case 'PIN_SIDEBAR':
          sidebarManager.pin();
          sendResponse({ success: true });
          break;
          
        case 'UNPIN_SIDEBAR':
          sidebarManager.unpin();
          sendResponse({ success: true });
          break;
          
        case 'UPDATE_SETTINGS':
          sidebarManager.updateSettings(message.payload);
          sendResponse({ success: true });
          break;
          
        default:
          sendResponse({ success: false, error: `Unknown message type: ${message.type}` });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
    
    return true; // 异步响应
  });
}

/**
 * 页面卸载时的清理
 */
function cleanup(): void {
  if (sidebarManager) {
    sidebarManager.destroy();
    sidebarManager = null;
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

// 页面卸载时清理
window.addEventListener('beforeunload', cleanup);

// 导出供测试使用
if (typeof window !== 'undefined') {
  (window as any).__sidebarManager = sidebarManager;
}