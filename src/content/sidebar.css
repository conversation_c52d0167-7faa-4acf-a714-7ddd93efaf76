/* Chrome Vertical Sidebar Styles */

/* 重置和基础样式 */
.vertical-sidebar * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 侧边栏容器 */
.vertical-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 60px;
  background: #ffffff;
  border-right: 1px solid #e0e0e0;
  z-index: 2147483647;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333333;
  transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 右侧位置 */
.vertical-sidebar.position-right {
  left: auto;
  right: 0;
  border-right: none;
  border-left: 1px solid #e0e0e0;
}

/* 展开状态 */
.vertical-sidebar.expanded {
  width: 300px;
}

/* 固定状态 */
.vertical-sidebar.pinned {
  position: relative;
  height: 100vh;
}

/* 暗色主题 */
.vertical-sidebar.theme-dark {
  background: #1e1e1e;
  border-color: #333333;
  color: #ffffff;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-dark .sidebar-header {
  background: #2d2d2d;
  border-color: #333333;
}

/* 搜索框 */
.sidebar-search {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  background: #ffffff;
  color: #333333;
  outline: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.vertical-sidebar.expanded .sidebar-search {
  opacity: 1;
}

.theme-dark .sidebar-search {
  background: #1e1e1e;
  border-color: #444444;
  color: #ffffff;
}

.sidebar-search:focus {
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
}

/* 侧边栏内容 */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #d0d7de;
  border-radius: 3px;
}

.theme-dark .sidebar-content::-webkit-scrollbar-thumb {
  background: #444444;
}

/* 标签页和收藏夹区域 */
.sidebar-section {
  padding: 8px 0;
}

.sidebar-section-title {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #656d76;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.vertical-sidebar.expanded .sidebar-section-title {
  opacity: 1;
}

.theme-dark .sidebar-section-title {
  color: #8b949e;
}

/* 项目列表 */
.sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 12px 8px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  min-height: 44px;
  position: relative;
  gap: 8px;
}

.sidebar-item:hover {
  background: #f6f8fa;
}

.theme-dark .sidebar-item:hover {
  background: #333333;
}

.sidebar-item.active {
  background: #e7f3ff;
  border-right: 3px solid #0969da;
}

.theme-dark .sidebar-item.active {
  background: #1f2937;
  border-color: #3b82f6;
}

/* 标签页项目特殊样式 */
.sidebar-item.tab-item {
  padding-right: 8px;
}

.sidebar-item.tab-item:hover .sidebar-item-actions {
  opacity: 1;
}

/* 固定标签页的特殊样式 */
.sidebar-item.pinned {
  border-left: 3px solid #0969da;
  padding-left: 13px;
}

.theme-dark .sidebar-item.pinned {
  border-left-color: #3b82f6;
}

/* 选中状态样式 */
.sidebar-item.selected {
  background: #e7f3ff;
  border: 1px solid #0969da;
  border-radius: 4px;
}

.theme-dark .sidebar-item.selected {
  background: #1f2937;
  border-color: #3b82f6;
}

/* 选中状态下的关闭按钮更明显 */
.sidebar-item.selected .sidebar-item-actions {
  opacity: 1;
}

.sidebar-item.selected .close-button {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

/* 固定标签页的关闭按钮特殊样式 */
.close-pinned-tab {
  border: 1px solid #ff4757;
  background: rgba(255, 71, 87, 0.1);
}

.close-pinned-tab:hover {
  background: #ff4757;
  color: #ffffff;
  border-color: #ff4757;
}

/* 工具提示样式 */
.sidebar-item-action[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #24292f;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  margin-bottom: 4px;
}

.theme-dark .sidebar-item-action[title]:hover::after {
  background: #f0f6fc;
  color: #24292f;
}

/* 工具提示箭头 */
.sidebar-item-action[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #24292f;
  z-index: 1001;
  pointer-events: none;
}

.theme-dark .sidebar-item-action[title]:hover::before {
  border-top-color: #f0f6fc;
}

/* 项目图标 */
.sidebar-item-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
}

.sidebar-item-icon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

/* 加载状态图标 */
.sidebar-item-icon.loading .loading-spinner {
  animation: spin 1s linear infinite;
  color: #0969da;
}

.theme-dark .sidebar-item-icon.loading .loading-spinner {
  color: #3b82f6;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 音频播放图标 */
.sidebar-item-icon.audible {
  color: #0969da;
}

.theme-dark .sidebar-item-icon.audible {
  color: #3b82f6;
}

/* 音频播放动画 */
.sidebar-item-icon.audible::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #0969da;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.theme-dark .sidebar-item-icon.audible::after {
  background: #3b82f6;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

/* 项目文本 */
.sidebar-item-text {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  opacity: 0;
  transition: opacity 0.2s ease;
  min-width: 0; /* 确保文本可以被压缩 */
  margin-right: 8px;
}

.vertical-sidebar.expanded .sidebar-item-text {
  opacity: 1;
}

/* 标签页文本特殊样式 */
.tab-item .sidebar-item-text {
  font-size: 13px;
  line-height: 1.3;
}

/* 活动标签页的文本样式 */
.sidebar-item.active .sidebar-item-text {
  font-weight: 500;
  color: #0969da;
}

.theme-dark .sidebar-item.active .sidebar-item-text {
  color: #3b82f6;
}

/* 项目操作按钮 */
.sidebar-item-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.15s ease;
  margin-left: auto;
  flex-shrink: 0;
}

.sidebar-item:hover .sidebar-item-actions {
  opacity: 1;
}

/* 在展开状态下，关闭按钮始终可见 */
.vertical-sidebar.expanded .sidebar-item-actions .close-button {
  opacity: 0.6;
}

.vertical-sidebar.expanded .sidebar-item:hover .sidebar-item-actions .close-button {
  opacity: 1;
}

.sidebar-item-action {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #656d76;
  transition: all 0.15s ease;
  position: relative;
}

.sidebar-item-action:hover {
  background: #d0d7de;
  color: #333333;
}

.sidebar-item-action:focus {
  outline: 2px solid #0969da;
  outline-offset: 1px;
}

/* 关闭按钮特殊样式 */
.sidebar-item-action.close-button {
  color: #656d76;
}

.sidebar-item-action.close-button:hover {
  background: #ff4757;
  color: #ffffff;
}

.sidebar-item-action.close-button:active {
  background: #ff3742;
  transform: scale(0.95);
}

/* 固定按钮样式 */
.sidebar-item-action.pin-button:hover {
  background: #0969da;
  color: #ffffff;
}

/* 暗色主题 */
.theme-dark .sidebar-item-action {
  color: #8b949e;
}

.theme-dark .sidebar-item-action:hover {
  background: #444444;
  color: #ffffff;
}

.theme-dark .sidebar-item-action:focus {
  outline-color: #3b82f6;
}

.theme-dark .sidebar-item-action.close-button:hover {
  background: #ff4757;
  color: #ffffff;
}

.theme-dark .sidebar-item-action.pin-button:hover {
  background: #3b82f6;
  color: #ffffff;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 12px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.theme-dark .sidebar-footer {
  background: #2d2d2d;
  border-color: #333333;
}

/* 底部按钮 */
.sidebar-footer-button {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #656d76;
  transition: all 0.15s ease;
}

.sidebar-footer-button:hover {
  background: #d0d7de;
  color: #333333;
}

.sidebar-footer-button.active {
  background: #0969da;
  color: #ffffff;
}

.theme-dark .sidebar-footer-button {
  color: #8b949e;
}

.theme-dark .sidebar-footer-button:hover {
  background: #444444;
  color: #ffffff;
}

.theme-dark .sidebar-footer-button.active {
  background: #3b82f6;
}

/* 空状态 */
.sidebar-empty {
  padding: 24px 16px;
  text-align: center;
  color: #656d76;
  font-size: 13px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.vertical-sidebar.expanded .sidebar-empty {
  opacity: 1;
}

.theme-dark .sidebar-empty {
  color: #8b949e;
}

/* 加载状态 */
.sidebar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #656d76;
}

.theme-dark .sidebar-loading {
  color: #8b949e;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideOut {
  from { 
    opacity: 1; 
    transform: translateX(0); 
    max-height: 44px;
    margin-bottom: 2px;
  }
  to { 
    opacity: 0; 
    transform: translateX(-100%); 
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}

/* 关闭动画 */
.sidebar-item.closing {
  animation: slideOut 0.2s ease-out forwards;
  pointer-events: none;
}

/* 按钮点击动画 */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.sidebar-item-action:active {
  animation: buttonPress 0.1s ease-out;
}

/* 关闭通知样式 */
.tab-close-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  background: #24292f;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  z-index: 2147483648;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  pointer-events: none;
  max-width: 350px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tab-close-notification.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.tab-close-notification span {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.undo-button {
  background: #0969da;
  color: #ffffff;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  flex-shrink: 0;
}

.undo-button:hover {
  background: #0860ca;
}

.undo-button:active {
  background: #0757ba;
}

.theme-dark .tab-close-notification {
  background: #f0f6fc;
  color: #24292f;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.theme-dark .undo-button {
  background: #3b82f6;
}

.theme-dark .undo-button:hover {
  background: #2563eb;
}

.theme-dark .undo-button:active {
  background: #1d4ed8;
}

/* 右侧位置的侧边栏通知调整 */
.vertical-sidebar.position-right ~ .tab-close-notification {
  right: 20px;
  left: auto;
  transform: translateY(100px);
}

.vertical-sidebar.position-right ~ .tab-close-notification.show {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vertical-sidebar.expanded {
    width: 280px;
  }
  
  /* 在小屏幕上，关闭按钮更容易点击 */
  .sidebar-item-action {
    width: 24px;
    height: 24px;
  }
  
  .sidebar-item-action.close-button {
    background: rgba(255, 71, 87, 0.1);
  }
  
  .sidebar-item-action.close-button:hover {
    background: #ff4757;
  }
}

/* 在收起状态下隐藏操作按钮 */
.vertical-sidebar:not(.expanded) .sidebar-item-actions {
  display: none;
}

/* 在展开状态下显示操作按钮 */
.vertical-sidebar.expanded .sidebar-item-actions {
  display: flex;
}

/* 鼠标悬停时的动画效果 */
.sidebar-item-action {
  transform: scale(1);
  transition: all 0.15s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.sidebar-item-action:hover {
  transform: scale(1.1);
}

.sidebar-item-action:active {
  transform: scale(0.95);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .vertical-sidebar {
    border-width: 2px;
  }
  
  .sidebar-item.active {
    border-width: 4px;
  }
}

/* 标签页分组样式 */
.tab-group {
  margin-bottom: 8px;
}

.tab-group-header {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #f6f8fa;
  border-radius: 4px;
  margin: 0 8px 4px 8px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.tab-group-header:hover {
  background: #eaeef2;
}

.theme-dark .tab-group-header {
  background: #2d2d2d;
}

.theme-dark .tab-group-header:hover {
  background: #3d3d3d;
}

.tab-group-toggle {
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #656d76;
  margin-right: 6px;
  transition: transform 0.15s ease;
}

.tab-group.collapsed .tab-group-toggle {
  transform: rotate(-90deg);
}

.tab-group-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  flex-shrink: 0;
}

.tab-group-title {
  flex: 1;
  font-size: 12px;
  font-weight: 600;
  color: #24292f;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.vertical-sidebar.expanded .tab-group-title {
  opacity: 1;
}

.theme-dark .tab-group-title {
  color: #f0f6fc;
}

.tab-group-count {
  font-size: 11px;
  color: #656d76;
  margin-left: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.vertical-sidebar.expanded .tab-group-count {
  opacity: 1;
}

.theme-dark .tab-group-count {
  color: #8b949e;
}

/* 分组操作按钮 */
.tab-group-actions {
  display: flex;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.15s ease;
  margin-left: 8px;
}

.tab-group-header:hover .tab-group-actions {
  opacity: 1;
}

.vertical-sidebar.expanded .tab-group-actions {
  opacity: 0.6;
}

.vertical-sidebar.expanded .tab-group-header:hover .tab-group-actions {
  opacity: 1;
}

.tab-group-action {
  width: 18px;
  height: 18px;
  border: none;
  background: transparent;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #656d76;
  transition: all 0.15s ease;
}

.tab-group-action:hover {
  background: #d0d7de;
  color: #333333;
}

.tab-group-action:focus {
  outline: 2px solid #0969da;
  outline-offset: 1px;
}

.close-group-button:hover {
  background: #ff4757;
  color: #ffffff;
}

.close-group-button:active {
  background: #ff3742;
  transform: scale(0.95);
}

.theme-dark .tab-group-action {
  color: #8b949e;
}

.theme-dark .tab-group-action:hover {
  background: #444444;
  color: #ffffff;
}

.theme-dark .tab-group-action:focus {
  outline-color: #3b82f6;
}

.theme-dark .close-group-button:hover {
  background: #ff4757;
  color: #ffffff;
}

.tab-group-tabs {
  margin-left: 16px;
  border-left: 1px solid #e1e4e8;
  padding-left: 8px;
}

.theme-dark .tab-group-tabs {
  border-left-color: #30363d;
}

.tab-group.collapsed .tab-group-tabs {
  display: none;
}

/* 分组中的标签页项目样式 */
.tab-group .sidebar-item {
  margin-bottom: 2px;
  border-radius: 4px;
  margin-right: 8px;
}

.tab-group .sidebar-item:last-child {
  margin-bottom: 0;
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .vertical-sidebar,
  .sidebar-search,
  .sidebar-section-title,
  .sidebar-item-text,
  .sidebar-item-actions,
  .sidebar-item-action,
  .sidebar-footer-button,
  .sidebar-empty,
  .tab-group-toggle,
  .tab-group-title,
  .tab-group-count {
    transition: none;
  }
}