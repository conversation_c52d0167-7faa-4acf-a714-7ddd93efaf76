/**
 * Sidebar Manager - 侧边栏管理器实现
 */

import { SidebarManager as ISidebarManager } from '../../interfaces/content.interface.js';
import { TabInfo, BookmarkNode, UserSettings, SidebarState, DEFAULT_SIDEBAR_STATE } from '../types/index.js';
import { SidebarContainer } from './components/SidebarContainer.js';
import { PageAdapter } from '../utils/PageAdapter.js';
import { StyleManager } from '../utils/StyleManager.js';
import { EventManager } from '../utils/EventManager.js';
import { AnimationManager } from '../utils/AnimationManager.js';
import { ErrorHandler } from '../../background/utils/ErrorHandler.js';

export class SidebarManagerImpl implements ISidebarManager {
  private sidebarContainer: SidebarContainer | null = null;
  private pageAdapter: PageAdapter;
  private styleManager: StyleManager;
  private eventManager: EventManager;
  private animationManager: AnimationManager;
  private currentState: SidebarState = { ...DEFAULT_SIDEBAR_STATE };
  private currentSettings: UserSettings | null = null;
  private initialized = false;
  private dataUpdateInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.styleManager = new StyleManager();
    this.pageAdapter = new PageAdapter(this.styleManager);
    this.eventManager = new EventManager();
    this.animationManager = new AnimationManager();
  }

  /**
   * 初始化侧边栏管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Sidebar Manager...');

      // 检查是否可以注入
      if (!this.pageAdapter.canInject()) {
        throw new Error('Cannot inject sidebar on this page');
      }

      // 获取用户设置（提前获取以便预留空间）
      await this.loadSettings();

      // 提前预留空间 - 在页面加载时就预留侧边栏空间
      if (this.currentSettings) {
        const defaultWidth = this.currentSettings.defaultPinned ? 
          this.currentSettings.sidebarWidth.expanded : 
          this.currentSettings.sidebarWidth.collapsed;
        
        console.log(`Early space reservation: ${defaultWidth}px on ${this.currentSettings.position}`);
        this.pageAdapter.reserveSpaceEarly(defaultWidth, this.currentSettings.position);
      }

      // 检测页面冲突
      const conflicts = this.pageAdapter.detectConflicts();
      if (conflicts.length > 0) {
        console.warn('Page conflicts detected:', conflicts);
        this.pageAdapter.handleConflicts(conflicts);
      }

      // 注入样式
      this.styleManager.injectStyles();

      // 创建侧边栏容器
      this.createSidebarContainer();

      // 设置事件监听
      this.setupEventListeners();

      // 设置消息监听
      this.setupMessageListener();

      // 初始化动画管理器
      this.animationManager.initialize();

      // 开始数据更新循环
      this.startDataUpdates();

      this.initialized = true;
      console.log('Sidebar Manager initialized successfully with early space reservation');

      // 根据设置决定是否显示侧边栏
      if (this.currentSettings?.defaultPinned) {
        await this.show();
        await this.pin();
      } else {
        await this.show();
      }

    } catch (error) {
      console.error('Failed to initialize Sidebar Manager:', error);
      ErrorHandler.handleError(error, 'SidebarManager.initialize');
      throw error;
    }
  }

  /**
   * 销毁侧边栏管理器
   */
  destroy(): void {
    try {
      console.log('Destroying Sidebar Manager...');

      // 清理空间预留
      this.pageAdapter.clearSpaceReservation();

      // 完全恢复页面布局和冲突处理
      if (this.currentState.visible || this.currentState.pinned) {
        this.pageAdapter.restorePageLayout(false); // 不使用动画
        this.pageAdapter.restoreConflictHandling();
      }

      // 移除侧边栏容器
      if (this.sidebarContainer) {
        this.sidebarContainer.destroy();
        this.sidebarContainer = null;
      }

      // 清理样式
      this.styleManager.removeStyles();

      // 清理事件监听
      this.eventManager.removeAllListeners();

      // 清理动画管理器
      this.animationManager.destroy();

      // 停止数据更新
      this.stopDataUpdates();

      // 重置状态
      this.currentState = { visible: false, expanded: false, pinned: false };
      this.currentSettings = null;
      this.initialized = false;
      
      console.log('Sidebar Manager destroyed and page fully restored');

    } catch (error) {
      console.error('Error destroying Sidebar Manager:', error);
      ErrorHandler.handleError(error, 'SidebarManager.destroy');
    }
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 显示侧边栏
   */
  show(): void {
    if (!this.sidebarContainer || !this.currentSettings) return;

    try {
      // 检测并处理页面冲突
      const conflicts = this.pageAdapter.detectConflicts();
      if (conflicts.length > 0) {
        console.warn('Page conflicts detected on show:', conflicts);
        this.pageAdapter.handleConflicts(conflicts);
      }

      // 使用智能布局调整
      const width = this.currentState.expanded ? 
        this.currentSettings.sidebarWidth.expanded : 
        this.currentSettings.sidebarWidth.collapsed;
      
      this.pageAdapter.smartAdjustLayout(width, this.currentSettings.position);
      
      // 应用强化空间保护
      this.pageAdapter.reinforceSpaceProtection(width, this.currentSettings.position);
      
      this.sidebarContainer.show();
      this.currentState.visible = true;
      this.notifyStateChange();
      
      console.log('Sidebar shown with smart layout adjustment');
    } catch (error) {
      console.error('Error showing sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.show');
    }
  }

  /**
   * 隐藏侧边栏
   */
  hide(): void {
    if (!this.sidebarContainer) return;

    try {
      // 恢复页面布局（包括冲突处理）
      this.pageAdapter.restorePageLayout(true);
      this.pageAdapter.restoreConflictHandling();
      
      this.sidebarContainer.hide();
      this.currentState.visible = false;
      this.currentState.expanded = false;
      
      // 如果是固定状态，取消固定
      if (this.currentState.pinned) {
        this.currentState.pinned = false;
      }

      this.notifyStateChange();
      console.log('Sidebar hidden and page layout restored');
    } catch (error) {
      console.error('Error hiding sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.hide');
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  toggle(): void {
    if (this.currentState.visible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 展开侧边栏
   */
  expand(): void {
    if (!this.sidebarContainer || !this.currentState.visible || !this.currentSettings) return;

    try {
      this.animationManager.expandSidebar();
      this.sidebarContainer.expand();
      this.currentState.expanded = true;
      
      // 如果已显示，重新调整页面布局以适应新宽度
      const width = this.currentSettings.sidebarWidth.expanded;
      this.pageAdapter.smartAdjustLayout(width, this.currentSettings.position);
      
      this.notifyStateChange();
      console.log('Sidebar expanded with layout adjustment');
    } catch (error) {
      console.error('Error expanding sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.expand');
    }
  }

  /**
   * 收起侧边栏
   */
  collapse(): void {
    if (!this.sidebarContainer || !this.currentState.visible || !this.currentSettings) return;

    try {
      this.animationManager.collapseSidebar();
      this.sidebarContainer.collapse();
      this.currentState.expanded = false;
      
      // 调整页面布局以适应收起后的宽度
      const width = this.currentSettings.sidebarWidth.collapsed;
      this.pageAdapter.smartAdjustLayout(width, this.currentSettings.position);
      
      this.notifyStateChange();
      console.log('Sidebar collapsed with layout adjustment');
    } catch (error) {
      console.error('Error collapsing sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.collapse');
    }
  }

  /**
   * 固定侧边栏
   */
  pin(): void {
    if (!this.sidebarContainer || !this.currentState.visible || !this.currentSettings) return;

    try {
      // 检测并处理页面冲突
      const conflicts = this.pageAdapter.detectConflicts();
      if (conflicts.length > 0) {
        console.warn('Page conflicts detected on pin:', conflicts);
        this.pageAdapter.handleConflicts(conflicts);
      }

      // 使用智能布局调整
      const width = this.currentState.expanded ? 
        this.currentSettings.sidebarWidth.expanded : 
        this.currentSettings.sidebarWidth.collapsed;
      
      this.pageAdapter.smartAdjustLayout(width, this.currentSettings.position);
      
      // 应用强化空间保护
      this.pageAdapter.reinforceSpaceProtection(width, this.currentSettings.position);

      // 更新侧边栏样式
      this.sidebarContainer.pin();
      this.currentState.pinned = true;
      this.notifyStateChange();

      console.log('Sidebar pinned with smart layout adjustment');
    } catch (error) {
      console.error('Error pinning sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.pin');
    }
  }

  /**
   * 取消固定侧边栏
   */
  unpin(): void {
    if (!this.sidebarContainer) return;

    try {
      // 恢复页面布局和冲突处理
      this.pageAdapter.restorePageLayout(true);
      this.pageAdapter.restoreConflictHandling();

      // 更新侧边栏样式
      this.sidebarContainer.unpin();
      this.currentState.pinned = false;
      this.notifyStateChange();

      console.log('Sidebar unpinned and page layout restored');
    } catch (error) {
      console.error('Error unpinning sidebar:', error);
      ErrorHandler.handleError(error, 'SidebarManager.unpin');
    }
  }

  /**
   * 更新标签页数据
   */
  updateTabs(tabs: TabInfo[]): void {
    if (!this.sidebarContainer) return;

    try {
      this.sidebarContainer.updateTabs(tabs);
    } catch (error) {
      ErrorHandler.handleError(error, 'SidebarManager.updateTabs');
    }
  }

  /**
   * 更新标签分组数据
   */
  updateTabGroups(groups: any[]): void {
    if (!this.sidebarContainer) return;

    try {
      this.sidebarContainer.updateTabGroups(groups);
    } catch (error) {
      ErrorHandler.handleError(error, 'SidebarManager.updateTabGroups');
    }
  }

  /**
   * 更新收藏夹数据
   */
  updateBookmarks(bookmarks: BookmarkNode[]): void {
    if (!this.sidebarContainer) return;

    try {
      this.sidebarContainer.updateBookmarks(bookmarks);
    } catch (error) {
      ErrorHandler.handleError(error, 'SidebarManager.updateBookmarks');
    }
  }

  /**
   * 更新设置
   */
  updateSettings(settings: UserSettings): void {
    try {
      const oldSettings = this.currentSettings;
      this.currentSettings = settings;

      // 更新样式管理器
      this.styleManager.updateTheme(settings.theme);
      this.styleManager.updatePosition(settings.position);
      this.styleManager.updateDimensions(
        settings.sidebarWidth.collapsed,
        settings.sidebarWidth.expanded
      );

      // 更新侧边栏容器
      if (this.sidebarContainer) {
        this.sidebarContainer.updateSettings(settings);
      }

      // 如果侧边栏可见，重新调整布局
      if (this.currentState.visible) {
        // 检查是否需要重新调整布局
        const needsLayoutUpdate = !oldSettings || 
          oldSettings.position !== settings.position ||
          oldSettings.sidebarWidth.collapsed !== settings.sidebarWidth.collapsed ||
          oldSettings.sidebarWidth.expanded !== settings.sidebarWidth.expanded;

        if (needsLayoutUpdate) {
          const width = this.currentState.expanded ? 
            settings.sidebarWidth.expanded : 
            settings.sidebarWidth.collapsed;
          
          // 使用智能调整以处理位置变化
          this.pageAdapter.smartAdjustLayout(width, settings.position);
          console.log('Layout readjusted for settings update');
        }
      }

    } catch (error) {
      console.error('Error updating settings:', error);
      ErrorHandler.handleError(error, 'SidebarManager.updateSettings');
    }
  }

  /**
   * 获取当前状态
   */
  getState(): SidebarState {
    return { ...this.currentState };
  }

  /**
   * 设置状态
   */
  setState(state: Partial<SidebarState>): void {
    try {
      const oldState = { ...this.currentState };
      this.currentState = { ...this.currentState, ...state };

      // 根据状态变化执行相应操作
      if (state.visible !== undefined && state.visible !== oldState.visible) {
        if (state.visible) {
          this.show();
        } else {
          this.hide();
        }
      }

      if (state.expanded !== undefined && state.expanded !== oldState.expanded) {
        if (state.expanded) {
          this.expand();
        } else {
          this.collapse();
        }
      }

      if (state.pinned !== undefined && state.pinned !== oldState.pinned) {
        if (state.pinned) {
          this.pin();
        } else {
          this.unpin();
        }
      }

      this.notifyStateChange();

    } catch (error) {
      ErrorHandler.handleError(error, 'SidebarManager.setState');
    }
  }

  /**
   * 加载用户设置
   */
  private async loadSettings(): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_SETTINGS',
        timestamp: Date.now()
      });

      if (response.success) {
        this.currentSettings = response.data;
      } else {
        console.error('Failed to load settings:', response.error);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  /**
   * 创建侧边栏容器
   */
  private createSidebarContainer(): void {
    if (!this.currentSettings) {
      throw new Error('Settings not loaded');
    }

    this.sidebarContainer = new SidebarContainer({
      position: this.currentSettings.position,
      theme: this.currentSettings.theme,
      collapsedWidth: this.currentSettings.sidebarWidth.collapsed,
      expandedWidth: this.currentSettings.sidebarWidth.expanded,
      showTabs: this.currentSettings.showTabs,
      showBookmarks: this.currentSettings.showBookmarks,
      showTabGroups: this.currentSettings.showTabGroups
    });

    // 设置容器事件监听
    this.sidebarContainer.onExpand(() => {
      this.currentState.expanded = true;
      this.notifyStateChange();
    });

    this.sidebarContainer.onCollapse(() => {
      this.currentState.expanded = false;
      this.notifyStateChange();
    });

    this.sidebarContainer.onPin(() => {
      this.pin();
    });

    this.sidebarContainer.onUnpin(() => {
      this.unpin();
    });
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听窗口大小变化
    this.eventManager.addEventListener(window, 'resize', () => {
      if (this.sidebarContainer) {
        this.sidebarContainer.handleResize();
      }
    });

    // 监听页面滚动
    this.eventManager.addEventListener(window, 'scroll', () => {
      if (this.sidebarContainer) {
        this.sidebarContainer.handleScroll();
      }
    });

    // 监听键盘事件
    this.eventManager.addEventListener(document, 'keydown', (event) => {
      this.handleKeyboardEvent(event as KeyboardEvent);
    });
  }

  /**
   * 设置消息监听
   */
  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      try {
        switch (message.type) {
          case 'TOGGLE_SIDEBAR_COMMAND':
            this.toggle();
            sendResponse({ success: true });
            break;

          case 'PIN_SIDEBAR_COMMAND':
            this.pin();
            sendResponse({ success: true });
            break;

          case 'UNPIN_SIDEBAR_COMMAND':
            this.unpin();
            sendResponse({ success: true });
            break;

          case 'SETTINGS_UPDATED':
            this.updateSettings(message.payload);
            sendResponse({ success: true });
            break;

          case 'TABS_UPDATED':
            this.updateTabs(message.payload);
            sendResponse({ success: true });
            break;

          case 'TAB_GROUPS_UPDATED':
            this.updateTabGroups(message.payload);
            sendResponse({ success: true });
            break;

          case 'BOOKMARKS_UPDATED':
            this.updateBookmarks(message.payload);
            sendResponse({ success: true });
            break;

          default:
            sendResponse({ success: false, error: `Unknown message type: ${message.type}` });
        }
      } catch (error) {
        console.error('Error handling message:', error);
        sendResponse({ success: false, error: error.message });
      }

      return true; // 异步响应
    });
  }

  /**
   * 处理键盘事件
   */
  private handleKeyboardEvent(event: KeyboardEvent): void {
    if (!this.currentSettings) return;

    // 检查切换快捷键
    const toggleShortcut = this.currentSettings.keyboardShortcuts.toggle;
    if (this.matchesShortcut(event, toggleShortcut)) {
      event.preventDefault();
      this.toggle();
      return;
    }

    // 检查搜索快捷键
    const searchShortcut = this.currentSettings.keyboardShortcuts.search;
    if (this.matchesShortcut(event, searchShortcut) && this.currentState.expanded) {
      event.preventDefault();
      if (this.sidebarContainer) {
        this.sidebarContainer.focusSearch();
      }
      return;
    }

    // ESC键处理
    if (event.key === 'Escape' && this.currentState.expanded && !this.currentState.pinned) {
      this.collapse();
    }
  }

  /**
   * 检查快捷键匹配
   */
  private matchesShortcut(event: KeyboardEvent, shortcut: string): boolean {
    const parts = shortcut.toLowerCase().split('+');
    const key = parts.pop();
    
    if (event.key.toLowerCase() !== key) {
      return false;
    }

    const hasCtrl = parts.includes('ctrl') || parts.includes('control');
    const hasShift = parts.includes('shift');
    const hasAlt = parts.includes('alt');
    const hasMeta = parts.includes('meta') || parts.includes('cmd') || parts.includes('command');

    return event.ctrlKey === hasCtrl &&
           event.shiftKey === hasShift &&
           event.altKey === hasAlt &&
           event.metaKey === hasMeta;
  }

  /**
   * 开始数据更新循环
   */
  private startDataUpdates(): void {
    // 立即获取一次数据
    this.fetchAndUpdateData();
    
    // 设置定期更新（每2秒）
    this.dataUpdateInterval = setInterval(() => {
      this.fetchAndUpdateData();
    }, 2000);
  }

  /**
   * 停止数据更新循环
   */
  private stopDataUpdates(): void {
    if (this.dataUpdateInterval) {
      clearInterval(this.dataUpdateInterval);
      this.dataUpdateInterval = null;
    }
  }

  /**
   * 获取并更新数据
   */
  private async fetchAndUpdateData(): Promise<void> {
    try {
      // 获取标签页和分组数据
      const response = await chrome.runtime.sendMessage({
        type: 'GET_TABS',
        timestamp: Date.now()
      });

      console.log('SidebarManager: 获取数据响应', response);

      if (response.success && response.data) {
        // 更新标签页数据
        if (response.data.tabs) {
          console.log('SidebarManager: 更新标签页数据', response.data.tabs.length);
          this.updateTabs(response.data.tabs);
        }
        
        // 更新标签分组数据
        if (response.data.groups && this.currentSettings?.showTabGroups) {
          console.log('SidebarManager: 更新标签分组数据', response.data.groups.length, response.data.groups);
          this.updateTabGroups(response.data.groups);
        } else {
          console.log('SidebarManager: 跳过标签分组更新', {
            hasGroups: !!response.data.groups,
            showTabGroups: this.currentSettings?.showTabGroups,
            groupsLength: response.data.groups?.length || 0
          });
        }
      }

      // 获取收藏夹数据（如果启用）
      if (this.currentSettings?.showBookmarks) {
        const bookmarksResponse = await chrome.runtime.sendMessage({
          type: 'GET_BOOKMARKS',
          timestamp: Date.now()
        });

        if (bookmarksResponse.success && bookmarksResponse.data) {
          this.updateBookmarks(bookmarksResponse.data);
        }
      }
    } catch (error) {
      console.debug('Error fetching data:', error);
    }
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange(): void {
    try {
      chrome.runtime.sendMessage({
        type: 'SIDEBAR_STATE_CHANGED',
        payload: this.currentState,
        timestamp: Date.now()
      }).catch(error => {
        // 忽略消息发送失败（可能是background script未准备好）
        console.debug('Failed to notify state change:', error);
      });
    } catch (error) {
      console.debug('Error notifying state change:', error);
    }
  }
}