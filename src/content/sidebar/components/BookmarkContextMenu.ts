/**
 * Bookmark Context Menu - 收藏夹右键菜单组件
 */

import { BookmarkNode } from '../../types/index.js';
import { BookmarkItem } from './BookmarkItem.js';
import { BookmarkFolder } from './BookmarkFolder.js';

export interface ContextMenuOptions {
  bookmark: BookmarkNode;
  component: BookmarkItem | BookmarkFolder;
  x: number;
  y: number;
}

export class BookmarkContextMenu {
  private element: HTMLElement;
  private options: ContextMenuOptions;
  private overlay: HTMLElement;

  constructor(options: ContextMenuOptions) {
    this.options = options;
    this.element = this.createElement();
    this.overlay = this.createOverlay();
    this.render();
    this.setupEventListeners();
  }

  /**
   * 显示菜单
   */
  show(): void {
    document.body.appendChild(this.overlay);
    document.body.appendChild(this.element);
    
    // 调整位置避免超出屏幕
    this.adjustPosition();
    
    // 添加全局点击监听
    setTimeout(() => {
      document.addEventListener('click', this.handleGlobalClick);
      document.addEventListener('keydown', this.handleKeydown);
    }, 0);
  }

  /**
   * 隐藏菜单
   */
  hide(): void {
    if (this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    
    document.removeEventListener('click', this.handleGlobalClick);
    document.removeEventListener('keydown', this.handleKeydown);
  }

  /**
   * 销毁菜单
   */
  destroy(): void {
    this.hide();
  }

  /**
   * 渲染菜单
   */
  private render(): void {
    this.element.innerHTML = '';
    
    const menuItems = this.createMenuItems();
    menuItems.forEach(item => {
      this.element.appendChild(item);
    });
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'bookmark-context-menu';
    element.style.position = 'fixed';
    element.style.left = `${this.options.x}px`;
    element.style.top = `${this.options.y}px`;
    element.style.backgroundColor = '#ffffff';
    element.style.border = '1px solid #d0d7de';
    element.style.borderRadius = '8px';
    element.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.12)';
    element.style.zIndex = '2147483647';
    element.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    element.style.fontSize = '14px';
    element.style.color = '#24292f';
    element.style.minWidth = '180px';
    element.style.padding = '4px 0';
    
    return element;
  }

  /**
   * 创建遮罩层
   */
  private createOverlay(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'bookmark-context-menu-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.zIndex = '2147483646';
    overlay.style.backgroundColor = 'transparent';
    
    return overlay;
  }

  /**
   * 创建菜单项
   */
  private createMenuItems(): HTMLElement[] {
    const items: HTMLElement[] = [];
    const isFolder = !!this.options.bookmark.children;

    if (!isFolder) {
      // 收藏夹项目菜单
      items.push(this.createMenuItem('打开', '🔗', () => {
        (this.options.component as BookmarkItem).openBookmark(true);
        this.hide();
      }));

      items.push(this.createMenuItem('在新标签页中打开', '🆕', () => {
        (this.options.component as BookmarkItem).openBookmark(true);
        this.hide();
      }));

      items.push(this.createSeparator());
    } else {
      // 文件夹菜单
      items.push(this.createMenuItem('添加收藏夹', '➕', () => {
        (this.options.component as BookmarkFolder).addBookmark();
        this.hide();
      }));

      items.push(this.createMenuItem('添加文件夹', '📁', () => {
        (this.options.component as BookmarkFolder).addFolder();
        this.hide();
      }));

      items.push(this.createSeparator());
    }

    // 通用菜单项
    items.push(this.createMenuItem('编辑', '✏️', () => {
      if (isFolder) {
        (this.options.component as BookmarkFolder).editFolder();
      } else {
        (this.options.component as BookmarkItem).editBookmark();
      }
      this.hide();
    }));

    items.push(this.createSeparator());

    items.push(this.createMenuItem('删除', '🗑️', () => {
      if (isFolder) {
        (this.options.component as BookmarkFolder).deleteFolder();
      } else {
        (this.options.component as BookmarkItem).deleteBookmark();
      }
      this.hide();
    }, 'danger'));

    return items;
  }

  /**
   * 创建菜单项
   */
  private createMenuItem(
    text: string, 
    icon: string, 
    onClick: () => void, 
    type: 'normal' | 'danger' = 'normal'
  ): HTMLElement {
    const item = document.createElement('div');
    item.className = 'context-menu-item';
    item.style.display = 'flex';
    item.style.alignItems = 'center';
    item.style.padding = '8px 12px';
    item.style.cursor = 'pointer';
    item.style.transition = 'background-color 0.15s ease';
    
    if (type === 'danger') {
      item.style.color = '#d1242f';
    }

    // 图标
    const iconElement = document.createElement('span');
    iconElement.style.marginRight = '8px';
    iconElement.style.fontSize = '16px';
    iconElement.textContent = icon;

    // 文本
    const textElement = document.createElement('span');
    textElement.textContent = text;

    item.appendChild(iconElement);
    item.appendChild(textElement);

    // 悬停效果
    item.addEventListener('mouseenter', () => {
      item.style.backgroundColor = type === 'danger' ? '#fff1f1' : '#f6f8fa';
    });

    item.addEventListener('mouseleave', () => {
      item.style.backgroundColor = 'transparent';
    });

    // 点击事件
    item.addEventListener('click', (event) => {
      event.stopPropagation();
      onClick();
    });

    return item;
  }

  /**
   * 创建分隔线
   */
  private createSeparator(): HTMLElement {
    const separator = document.createElement('div');
    separator.className = 'context-menu-separator';
    separator.style.height = '1px';
    separator.style.backgroundColor = '#e1e4e8';
    separator.style.margin = '4px 0';
    return separator;
  }

  /**
   * 调整菜单位置
   */
  private adjustPosition(): void {
    const rect = this.element.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x = this.options.x;
    let y = this.options.y;

    // 防止超出右边界
    if (x + rect.width > viewportWidth) {
      x = viewportWidth - rect.width - 10;
    }

    // 防止超出下边界
    if (y + rect.height > viewportHeight) {
      y = viewportHeight - rect.height - 10;
    }

    // 防止超出左边界
    if (x < 10) {
      x = 10;
    }

    // 防止超出上边界
    if (y < 10) {
      y = 10;
    }

    this.element.style.left = `${x}px`;
    this.element.style.top = `${y}px`;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 阻止菜单内的点击事件冒泡
    this.element.addEventListener('click', (event) => {
      event.stopPropagation();
    });

    // 遮罩层点击隐藏菜单
    this.overlay.addEventListener('click', () => {
      this.hide();
    });
  }

  /**
   * 处理全局点击
   */
  private handleGlobalClick = (event: MouseEvent): void => {
    if (!this.element.contains(event.target as Node)) {
      this.hide();
    }
  };

  /**
   * 处理键盘事件
   */
  private handleKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      event.preventDefault();
      this.hide();
    }
  };
}