/**
 * Bookmark Editor - 收藏夹编辑器组件
 */

import { BookmarkNode } from '../../types/index.js';

export interface BookmarkEditorOptions {
  bookmark?: BookmarkNode;
  parentFolder?: BookmarkNode;
  mode: 'create' | 'edit';
  onSave: (data: { title: string; url?: string; parentId?: string }) => Promise<void>;
  onCancel: () => void;
}

export class BookmarkEditor {
  private element: HTMLElement;
  private options: BookmarkEditorOptions;
  private titleInput: HTMLInputElement;
  private urlInput: HTMLInputElement;
  private parentSelect: HTMLSelectElement;
  private saveButton: HTMLButtonElement;
  private cancelButton: HTMLButtonElement;
  private overlay: HTMLElement;

  constructor(options: BookmarkEditorOptions) {
    this.options = options;
    this.element = this.createElement();
    this.overlay = this.createOverlay();
    this.titleInput = this.createTitleInput();
    this.urlInput = this.createUrlInput();
    this.parentSelect = this.createParentSelect();
    this.saveButton = this.createSaveButton();
    this.cancelButton = this.createCancelButton();
    
    this.render();
    this.setupEventListeners();
  }

  /**
   * 显示编辑器
   */
  show(): void {
    document.body.appendChild(this.overlay);
    document.body.appendChild(this.element);
    
    // 聚焦标题输入框
    setTimeout(() => {
      this.titleInput.focus();
      this.titleInput.select();
    }, 100);
    
    // 添加ESC键监听
    document.addEventListener('keydown', this.handleKeydown);
  }

  /**
   * 隐藏编辑器
   */
  hide(): void {
    if (this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    
    document.removeEventListener('keydown', this.handleKeydown);
  }

  /**
   * 销毁编辑器
   */
  destroy(): void {
    this.hide();
  }

  /**
   * 渲染编辑器
   */
  private render(): void {
    this.element.innerHTML = '';
    
    // 创建头部
    const header = this.createHeader();
    this.element.appendChild(header);
    
    // 创建表单
    const form = this.createForm();
    this.element.appendChild(form);
    
    // 创建按钮区域
    const buttons = this.createButtons();
    this.element.appendChild(buttons);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'bookmark-editor';
    element.style.position = 'fixed';
    element.style.top = '50%';
    element.style.left = '50%';
    element.style.transform = 'translate(-50%, -50%)';
    element.style.width = '400px';
    element.style.backgroundColor = '#ffffff';
    element.style.border = '1px solid #d0d7de';
    element.style.borderRadius = '12px';
    element.style.boxShadow = '0 16px 32px rgba(0, 0, 0, 0.12)';
    element.style.zIndex = '2147483649';
    element.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    element.style.fontSize = '14px';
    element.style.color = '#24292f';
    
    return element;
  }

  /**
   * 创建遮罩层
   */
  private createOverlay(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'bookmark-editor-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '2147483648';
    
    overlay.addEventListener('click', () => {
      this.options.onCancel();
    });
    
    return overlay;
  }

  /**
   * 创建头部
   */
  private createHeader(): HTMLElement {
    const header = document.createElement('div');
    header.style.padding = '20px 20px 0 20px';
    header.style.borderBottom = '1px solid #e1e4e8';
    header.style.marginBottom = '20px';
    
    const title = document.createElement('h3');
    title.style.margin = '0 0 16px 0';
    title.style.fontSize = '18px';
    title.style.fontWeight = '600';
    title.textContent = this.options.mode === 'create' ? '添加收藏夹' : '编辑收藏夹';
    
    header.appendChild(title);
    return header;
  }

  /**
   * 创建表单
   */
  private createForm(): HTMLElement {
    const form = document.createElement('div');
    form.style.padding = '0 20px';
    
    // 标题字段
    const titleGroup = this.createFormGroup('标题', this.titleInput);
    form.appendChild(titleGroup);
    
    // URL字段（仅在编辑收藏夹时显示）
    if (!this.options.bookmark?.children) {
      const urlGroup = this.createFormGroup('URL', this.urlInput);
      form.appendChild(urlGroup);
    }
    
    // 父文件夹字段
    const parentGroup = this.createFormGroup('保存到', this.parentSelect);
    form.appendChild(parentGroup);
    
    return form;
  }

  /**
   * 创建表单组
   */
  private createFormGroup(label: string, input: HTMLElement): HTMLElement {
    const group = document.createElement('div');
    group.style.marginBottom = '16px';
    
    const labelElement = document.createElement('label');
    labelElement.style.display = 'block';
    labelElement.style.marginBottom = '6px';
    labelElement.style.fontWeight = '500';
    labelElement.textContent = label;
    
    group.appendChild(labelElement);
    group.appendChild(input);
    
    return group;
  }

  /**
   * 创建标题输入框
   */
  private createTitleInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = '输入标题';
    input.value = this.options.bookmark?.title || '';
    this.applyInputStyles(input);
    return input;
  }

  /**
   * 创建URL输入框
   */
  private createUrlInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'url';
    input.placeholder = 'https://example.com';
    input.value = this.options.bookmark?.url || '';
    this.applyInputStyles(input);
    return input;
  }

  /**
   * 创建父文件夹选择器
   */
  private createParentSelect(): HTMLSelectElement {
    const select = document.createElement('select');
    this.applyInputStyles(select);
    
    // 添加选项（这里简化处理，实际应该获取所有文件夹）
    const defaultOption = document.createElement('option');
    defaultOption.value = '1';
    defaultOption.textContent = '书签栏';
    select.appendChild(defaultOption);
    
    if (this.options.parentFolder) {
      const parentOption = document.createElement('option');
      parentOption.value = this.options.parentFolder.id;
      parentOption.textContent = this.options.parentFolder.title;
      parentOption.selected = true;
      select.appendChild(parentOption);
    }
    
    return select;
  }

  /**
   * 应用输入框样式
   */
  private applyInputStyles(input: HTMLInputElement | HTMLSelectElement): void {
    input.style.width = '100%';
    input.style.padding = '8px 12px';
    input.style.border = '1px solid #d0d7de';
    input.style.borderRadius = '6px';
    input.style.fontSize = '14px';
    input.style.fontFamily = 'inherit';
    input.style.backgroundColor = '#ffffff';
    input.style.color = '#24292f';
    input.style.outline = 'none';
    input.style.transition = 'border-color 0.15s ease';
    
    input.addEventListener('focus', () => {
      input.style.borderColor = '#0969da';
      input.style.boxShadow = '0 0 0 3px rgba(9, 105, 218, 0.1)';
    });
    
    input.addEventListener('blur', () => {
      input.style.borderColor = '#d0d7de';
      input.style.boxShadow = 'none';
    });
  }

  /**
   * 创建保存按钮
   */
  private createSaveButton(): HTMLButtonElement {
    const button = document.createElement('button');
    button.textContent = this.options.mode === 'create' ? '添加' : '保存';
    button.style.backgroundColor = '#0969da';
    button.style.color = '#ffffff';
    button.style.border = 'none';
    button.style.borderRadius = '6px';
    button.style.padding = '8px 16px';
    button.style.fontSize = '14px';
    button.style.fontWeight = '500';
    button.style.cursor = 'pointer';
    button.style.transition = 'background-color 0.15s ease';
    
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = '#0860ca';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = '#0969da';
    });
    
    return button;
  }

  /**
   * 创建取消按钮
   */
  private createCancelButton(): HTMLButtonElement {
    const button = document.createElement('button');
    button.textContent = '取消';
    button.style.backgroundColor = 'transparent';
    button.style.color = '#656d76';
    button.style.border = '1px solid #d0d7de';
    button.style.borderRadius = '6px';
    button.style.padding = '8px 16px';
    button.style.fontSize = '14px';
    button.style.fontWeight = '500';
    button.style.cursor = 'pointer';
    button.style.marginRight = '8px';
    button.style.transition = 'all 0.15s ease';
    
    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = '#f6f8fa';
      button.style.borderColor = '#bbb';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'transparent';
      button.style.borderColor = '#d0d7de';
    });
    
    return button;
  }

  /**
   * 创建按钮区域
   */
  private createButtons(): HTMLElement {
    const buttons = document.createElement('div');
    buttons.style.padding = '20px';
    buttons.style.textAlign = 'right';
    buttons.style.borderTop = '1px solid #e1e4e8';
    buttons.style.marginTop = '20px';
    
    buttons.appendChild(this.cancelButton);
    buttons.appendChild(this.saveButton);
    
    return buttons;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 保存按钮点击
    this.saveButton.addEventListener('click', () => {
      this.handleSave();
    });
    
    // 取消按钮点击
    this.cancelButton.addEventListener('click', () => {
      this.options.onCancel();
    });
    
    // 表单提交
    this.titleInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        this.handleSave();
      }
    });
    
    if (this.urlInput) {
      this.urlInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          this.handleSave();
        }
      });
    }
  }

  /**
   * 处理保存
   */
  private async handleSave(): Promise<void> {
    const title = this.titleInput.value.trim();
    const url = this.urlInput?.value.trim();
    const parentId = this.parentSelect.value;
    
    // 验证输入
    if (!title) {
      this.showError('请输入标题');
      this.titleInput.focus();
      return;
    }
    
    if (!this.options.bookmark?.children && url && !this.isValidUrl(url)) {
      this.showError('请输入有效的URL');
      this.urlInput.focus();
      return;
    }
    
    try {
      // 禁用保存按钮防止重复提交
      this.saveButton.disabled = true;
      this.saveButton.textContent = '保存中...';
      
      // 调用保存回调
      await this.options.onSave({
        title,
        url: this.options.bookmark?.children ? undefined : url,
        parentId
      });
      
    } catch (error) {
      console.error('Error saving bookmark:', error);
      this.showError('保存失败，请重试');
      
      // 恢复保存按钮
      this.saveButton.disabled = false;
      this.saveButton.textContent = this.options.mode === 'create' ? '添加' : '保存';
    }
  }
  
  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * 显示错误信息
   */
  private showError(message: string): void {
    // 移除现有错误提示
    const existingError = this.element.querySelector('.error-message');
    if (existingError) {
      existingError.remove();
    }
    
    // 创建错误提示
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.style.color = '#d1242f';
    errorElement.style.fontSize = '12px';
    errorElement.style.marginTop = '4px';
    errorElement.style.padding = '8px 12px';
    errorElement.style.backgroundColor = '#fff1f1';
    errorElement.style.border = '1px solid #ffccd7';
    errorElement.style.borderRadius = '6px';
    errorElement.textContent = message;
    
    // 插入到表单后面
    const form = this.element.querySelector('div:nth-child(2)');
    if (form) {
      form.appendChild(errorElement);
    }
    
    // 3秒后自动移除
    setTimeout(() => {
      if (errorElement.parentNode) {
        errorElement.parentNode.removeChild(errorElement);
      }
    }, 3000);
  }
  
  /**
   * 处理键盘事件
   */
  private handleKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      event.preventDefault();
      this.options.onCancel();
    }
  };
}