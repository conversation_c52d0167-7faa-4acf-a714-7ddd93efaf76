/**
 * Bookmark Folder - 收藏夹文件夹组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { BookmarkNode } from '../../types/index.js';

export class BookmarkFolder implements UIComponent {
  element: HTMLElement;
  private bookmark: BookmarkNode;
  private depth: number;
  private expanded = false;
  private childContainer: HTMLElement;
  private toggleCallbacks: ((expanded: boolean) => void)[] = [];
  private contextMenuCallbacks: ((event: MouseEvent, bookmark: BookmarkNode) => void)[] = [];
  private dragOverCallbacks: ((event: DragEvent, bookmark: BookmarkNode) => void)[] = [];
  private dropCallbacks: ((event: DragEvent, bookmark: BookmarkNode) => void)[] = [];
  private visible = true;

  constructor(bookmark: BookmarkNode, depth = 0) {
    this.bookmark = bookmark;
    this.depth = depth;
    this.element = this.createElement();
    this.childContainer = this.createChildContainer();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    // 创建文件夹头部
    const header = this.createHeader();
    this.element.appendChild(header);

    // 添加子容器
    this.element.appendChild(this.childContainer);

    // 应用展开状态
    this.applyExpandedState();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.toggleCallbacks = [];
    this.contextMenuCallbacks = [];
    this.dragOverCallbacks = [];
    this.dropCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.bookmark) {
      this.bookmark = data.bookmark;
      this.render();
    }
  }

  /**
   * 获取收藏夹数据
   */
  getBookmark(): BookmarkNode {
    return this.bookmark;
  }

  /**
   * 获取深度
   */
  getDepth(): number {
    return this.depth;
  }

  /**
   * 获取子容器
   */
  getChildContainer(): HTMLElement {
    return this.childContainer;
  }

  /**
   * 检查是否展开
   */
  isExpanded(): boolean {
    return this.expanded;
  }

  /**
   * 设置展开状态
   */
  setExpanded(expanded: boolean): void {
    if (this.expanded !== expanded) {
      this.expanded = expanded;
      this.applyExpandedState();
    }
  }

  /**
   * 切换展开状态
   */
  toggle(): void {
    this.setExpanded(!this.expanded);
    this.notifyToggle(this.expanded);
  }

  /**
   * 显示文件夹
   */
  show(): void {
    this.visible = true;
    this.element.style.display = 'block';
    this.element.classList.remove('hidden');
  }

  /**
   * 隐藏文件夹
   */
  hide(): void {
    this.visible = false;
    this.element.style.display = 'none';
    this.element.classList.add('hidden');
  }

  /**
   * 检查是否可见
   */
  isVisible(): boolean {
    return this.visible;
  }

  /**
   * 检查是否有可见项目
   */
  hasVisibleItems(): boolean {
    const visibleChildren = this.childContainer.querySelectorAll('.sidebar-item:not(.hidden)');
    return visibleChildren.length > 0;
  }

  /**
   * 高亮搜索结果
   */
  highlight(query: string): void {
    const textElement = this.element.querySelector('.folder-title');
    if (!textElement) return;

    const title = this.bookmark.title;
    const regex = new RegExp(`(${query})`, 'gi');
    const highlightedTitle = title.replace(regex, '<mark>$1</mark>');
    textElement.innerHTML = highlightedTitle;
  }

  /**
   * 清除高亮
   */
  clearHighlight(): void {
    const textElement = this.element.querySelector('.folder-title');
    if (textElement) {
      textElement.textContent = this.bookmark.title;
    }
  }

  /**
   * 添加切换回调
   */
  onToggle(callback: (expanded: boolean) => void): void {
    this.toggleCallbacks.push(callback);
  }

  /**
   * 添加右键菜单回调
   */
  onContextMenu(callback: (event: MouseEvent, bookmark: BookmarkNode) => void): void {
    this.contextMenuCallbacks.push(callback);
  }

  /**
   * 添加拖拽悬停回调
   */
  onDragOver(callback: (event: DragEvent, bookmark: BookmarkNode) => void): void {
    this.dragOverCallbacks.push(callback);
  }

  /**
   * 添加拖拽放置回调
   */
  onDrop(callback: (event: DragEvent, bookmark: BookmarkNode) => void): void {
    this.dropCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'bookmark-folder';
    element.setAttribute('data-bookmark-id', this.bookmark.id);
    element.setAttribute('data-depth', this.depth.toString());
    return element;
  }

  /**
   * 创建子容器
   */
  private createChildContainer(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'bookmark-folder-children';
    return container;
  }

  /**
   * 创建文件夹头部
   */
  private createHeader(): HTMLElement {
    const header = document.createElement('div');
    header.className = 'sidebar-item bookmark-folder-header';
    header.setAttribute('role', 'button');
    header.setAttribute('tabindex', '0');
    header.setAttribute('aria-label', `文件夹: ${this.bookmark.title}`);
    header.setAttribute('aria-expanded', this.expanded.toString());

    // 创建缩进
    const indent = this.createIndent();
    header.appendChild(indent);

    // 创建展开/折叠按钮
    const toggleButton = this.createToggleButton();
    header.appendChild(toggleButton);

    // 创建文件夹图标
    const icon = this.createFolderIcon();
    header.appendChild(icon);

    // 创建文件夹标题
    const title = this.createTitle();
    header.appendChild(title);

    // 创建子项目数量
    const count = this.createCount();
    header.appendChild(count);

    // 应用深度样式
    header.style.paddingLeft = `${8 + this.depth * 16}px`;

    return header;
  }

  /**
   * 创建缩进
   */
  private createIndent(): HTMLElement {
    const indent = document.createElement('div');
    indent.className = 'bookmark-indent';
    indent.style.width = `${this.depth * 16}px`;
    indent.style.flexShrink = '0';
    return indent;
  }

  /**
   * 创建切换按钮
   */
  private createToggleButton(): HTMLElement {
    const button = document.createElement('button');
    button.className = 'folder-toggle';
    button.setAttribute('aria-label', this.expanded ? '折叠文件夹' : '展开文件夹');
    button.innerHTML = this.getToggleIcon();
    
    button.addEventListener('click', (event) => {
      event.stopPropagation();
      this.toggle();
    });

    return button;
  }

  /**
   * 创建文件夹图标
   */
  private createFolderIcon(): HTMLElement {
    const icon = document.createElement('div');
    icon.className = 'sidebar-item-icon folder-icon';
    icon.innerHTML = this.getFolderIcon();
    return icon;
  }

  /**
   * 创建标题
   */
  private createTitle(): HTMLElement {
    const title = document.createElement('div');
    title.className = 'sidebar-item-text folder-title';
    title.textContent = this.bookmark.title;
    title.title = this.bookmark.title;
    return title;
  }

  /**
   * 创建数量显示
   */
  private createCount(): HTMLElement {
    const count = document.createElement('div');
    count.className = 'folder-count';
    const childCount = this.bookmark.children?.length || 0;
    count.textContent = `(${childCount})`;
    count.style.opacity = '0.7';
    count.style.fontSize = '12px';
    count.style.marginLeft = 'auto';
    return count;
  }

  /**
   * 应用展开状态
   */
  private applyExpandedState(): void {
    const header = this.element.querySelector('.bookmark-folder-header');
    const toggleButton = this.element.querySelector('.folder-toggle');
    
    if (this.expanded) {
      this.element.classList.add('expanded');
      this.childContainer.style.display = 'block';
      if (header) header.setAttribute('aria-expanded', 'true');
      if (toggleButton) {
        toggleButton.innerHTML = this.getToggleIcon();
        toggleButton.setAttribute('aria-label', '折叠文件夹');
      }
    } else {
      this.element.classList.remove('expanded');
      this.childContainer.style.display = 'none';
      if (header) header.setAttribute('aria-expanded', 'false');
      if (toggleButton) {
        toggleButton.innerHTML = this.getToggleIcon();
        toggleButton.setAttribute('aria-label', '展开文件夹');
      }
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    const header = this.element.querySelector('.bookmark-folder-header');
    if (!header) return;

    // 点击头部切换展开状态
    header.addEventListener('click', (event) => {
      // 如果点击的是切换按钮，不处理
      if ((event.target as HTMLElement).classList.contains('folder-toggle')) {
        return;
      }
      this.toggle();
    });

    // 右键菜单
    header.addEventListener('contextmenu', (event) => {
      event.preventDefault();
      this.notifyContextMenu(event);
    });

    // 键盘支持
    header.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Enter':
        case ' ':
          event.preventDefault();
          this.toggle();
          break;
        case 'ArrowRight':
          if (!this.expanded) {
            event.preventDefault();
            this.setExpanded(true);
            this.notifyToggle(true);
          }
          break;
        case 'ArrowLeft':
          if (this.expanded) {
            event.preventDefault();
            this.setExpanded(false);
            this.notifyToggle(false);
          }
          break;
      }
    });

    // 鼠标悬停效果
    header.addEventListener('mouseenter', () => {
      header.classList.add('hover');
    });

    header.addEventListener('mouseleave', () => {
      header.classList.remove('hover');
    });

    // 拖拽支持
    this.element.addEventListener('dragover', (event) => {
      event.preventDefault();
      this.element.classList.add('drag-over');
      this.notifyDragOver(event);
    });

    this.element.addEventListener('dragleave', (event) => {
      // 检查是否真的离开了元素
      if (!this.element.contains(event.relatedTarget as Node)) {
        this.element.classList.remove('drag-over');
      }
    });

    this.element.addEventListener('drop', (event) => {
      event.preventDefault();
      this.element.classList.remove('drag-over');
      this.notifyDrop(event);
    });

    // 焦点支持
    header.addEventListener('focus', () => {
      header.classList.add('focused');
    });

    header.addEventListener('blur', () => {
      header.classList.remove('focused');
    });
  }

  /**
   * 获取切换图标
   */
  private getToggleIcon(): string {
    if (this.expanded) {
      return `
        <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
          <path d="M12.78 6.22a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L3.22 7.28a.75.75 0 1 1 1.06-1.06L8 9.94l3.72-3.72a.75.75 0 0 1 1.06 0z"/>
        </svg>
      `;
    } else {
      return `
        <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
          <path d="M6.22 3.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 1 1-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 0 1 0-1.06z"/>
        </svg>
      `;
    }
  }

  /**
   * 获取文件夹图标
   */
  private getFolderIcon(): string {
    if (this.expanded) {
      return `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M.5 3l.04.87a1.99 1.99 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14H9.81a2 2 0 0 0 1.99-1.819l.637-7a1.99 1.99 0 0 0-.342-1.311L12.5 3H2.5zM2.19 4l-.693 7.6a1 1 0 0 0 .995 1.1H9.81a1 1 0 0 0 .995-1.1L9.81 4H2.19z"/>
          <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h3A1.5 1.5 0 0 1 6 1.5V3H1.5A1.5 1.5 0 0 1 0 1.5z"/>
        </svg>
      `;
    } else {
      return `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M.5 3l.04.87a1.99 1.99 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14h10.348a2 2 0 0 0 1.991-1.819l.637-7A1.99 1.99 0 0 0 15.46 3.87L15.5 3H.5zM2.19 4h11.62a1 1 0 0 1 .996 1.09L14.17 12.1a1 1 0 0 1-.996.9H2.826a1 1 0 0 1-.995-.9L1.194 5.09A1 1 0 0 1 2.19 4z"/>
          <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h3A1.5 1.5 0 0 1 6 1.5V3H1.5A1.5 1.5 0 0 1 0 1.5z"/>
        </svg>
      `;
    }
  }

  /**
   * 通知切换事件
   */
  private notifyToggle(expanded: boolean): void {
    this.toggleCallbacks.forEach(callback => {
      try {
        callback(expanded);
      } catch (error) {
        console.error('Error in toggle callback:', error);
      }
    });
  }

  /**
   * 通知右键菜单事件
   */
  private notifyContextMenu(event: MouseEvent): void {
    this.contextMenuCallbacks.forEach(callback => {
      try {
        callback(event, this.bookmark);
      } catch (error) {
        console.error('Error in context menu callback:', error);
      }
    });
  }

  /**
   * 通知拖拽悬停事件
   */
  private notifyDragOver(event: DragEvent): void {
    this.dragOverCallbacks.forEach(callback => {
      try {
        callback(event, this.bookmark);
      } catch (error) {
        console.error('Error in drag over callback:', error);
      }
    });
  }

  /**
   * 通知拖拽放置事件
   */
  private notifyDrop(event: DragEvent): void {
    this.dropCallbacks.forEach(callback => {
      try {
        callback(event, this.bookmark);
      } catch (error) {
        console.error('Error in drop callback:', error);
      }
    });
  }

  /**
   * 添加新收藏夹到此文件夹
   */
  addBookmark(): void {
    // 动态导入BookmarkEditor
    import('./BookmarkEditor.js').then(({ BookmarkEditor }) => {
      const editor = new BookmarkEditor({
        parentFolder: this.bookmark,
        mode: 'create',
        onSave: async (data) => {
          try {
            await chrome.runtime.sendMessage({
              type: 'CREATE_BOOKMARK',
              payload: {
                url: data.url || '',
                title: data.title,
                folderId: data.parentId || this.bookmark.id
              },
              timestamp: Date.now()
            });
            
            editor.hide();
            console.log('Bookmark created successfully');
            
            // 刷新文件夹内容
            this.refreshContent();
          } catch (error) {
            console.error('Error creating bookmark:', error);
            throw error;
          }
        },
        onCancel: () => {
          editor.hide();
        }
      });
      
      editor.show();
    }).catch(error => {
      console.error('Error loading BookmarkEditor:', error);
    });
  }

  /**
   * 添加新文件夹到此文件夹
   */
  addFolder(): void {
    const folderName = prompt('请输入文件夹名称:');
    if (!folderName || !folderName.trim()) {
      return;
    }

    chrome.runtime.sendMessage({
      type: 'CREATE_BOOKMARK_FOLDER',
      payload: {
        title: folderName.trim(),
        parentId: this.bookmark.id
      },
      timestamp: Date.now()
    }).then(() => {
      console.log('Folder created successfully');
      this.refreshContent();
    }).catch(error => {
      console.error('Error creating folder:', error);
      alert('创建文件夹失败，请重试');
    });
  }

  /**
   * 编辑文件夹
   */
  editFolder(): void {
    // 动态导入BookmarkEditor
    import('./BookmarkEditor.js').then(({ BookmarkEditor }) => {
      const editor = new BookmarkEditor({
        bookmark: this.bookmark,
        mode: 'edit',
        onSave: async (data) => {
          try {
            await chrome.runtime.sendMessage({
              type: 'UPDATE_BOOKMARK',
              payload: {
                bookmarkId: this.bookmark.id,
                updates: {
                  title: data.title
                }
              },
              timestamp: Date.now()
            });
            
            // 更新本地显示
            this.bookmark.title = data.title;
            this.render();
            
            editor.hide();
            console.log('Folder updated successfully');
          } catch (error) {
            console.error('Error updating folder:', error);
            throw error;
          }
        },
        onCancel: () => {
          editor.hide();
        }
      });
      
      editor.show();
    }).catch(error => {
      console.error('Error loading BookmarkEditor:', error);
    });
  }

  /**
   * 删除文件夹
   */
  async deleteFolder(): Promise<void> {
    const hasChildren = this.bookmark.children && this.bookmark.children.length > 0;
    const confirmMessage = hasChildren 
      ? `文件夹"${this.bookmark.title}"包含${this.bookmark.children!.length}个项目，确定要删除吗？`
      : `确定要删除文件夹"${this.bookmark.title}"吗？`;
    
    if (!confirm(confirmMessage)) {
      return;
    }
    
    try {
      await chrome.runtime.sendMessage({
        type: 'DELETE_BOOKMARK',
        payload: { bookmarkId: this.bookmark.id },
        timestamp: Date.now()
      });
      
      // 从DOM中移除
      if (this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }
      
      console.log('Folder deleted successfully');
    } catch (error) {
      console.error('Error deleting folder:', error);
      alert('删除失败，请重试');
    }
  }

  /**
   * 刷新文件夹内容
   */
  private refreshContent(): void {
    // 重新获取收藏夹数据并更新显示
    chrome.runtime.sendMessage({
      type: 'GET_BOOKMARKS',
      timestamp: Date.now()
    }).then(response => {
      if (response.success && response.data) {
        // 找到更新后的文件夹数据
        const updatedFolder = this.findFolderInTree(response.data, this.bookmark.id);
        if (updatedFolder) {
          this.bookmark = updatedFolder;
          this.render();
        }
      }
    }).catch(error => {
      console.error('Error refreshing folder content:', error);
    });
  }

  /**
   * 在收藏夹树中查找指定文件夹
   */
  private findFolderInTree(nodes: BookmarkNode[], folderId: string): BookmarkNode | null {
    for (const node of nodes) {
      if (node.id === folderId) {
        return node;
      }
      if (node.children) {
        const found = this.findFolderInTree(node.children, folderId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }
}