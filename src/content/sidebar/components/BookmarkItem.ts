/**
 * Bookmark Item - 收藏夹项目组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { BookmarkNode } from '../../types/index.js';

export class BookmarkItem implements UIComponent {
  element: HTMLElement;
  private bookmark: BookmarkNode;
  private depth: number;
  private clickCallbacks: ((event: MouseEvent) => void)[] = [];
  private contextMenuCallbacks: ((event: MouseEvent, bookmark: BookmarkNode) => void)[] = [];
  private dragStartCallbacks: ((event: DragEvent, bookmark: BookmarkNode) => void)[] = [];
  private visible = true;

  constructor(bookmark: BookmarkNode, depth = 0) {
    this.bookmark = bookmark;
    this.depth = depth;
    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    // 创建缩进
    const indent = this.createIndent();
    this.element.appendChild(indent);

    // 创建图标
    const icon = this.createIcon();
    this.element.appendChild(icon);

    // 创建文本内容
    const text = this.createText();
    this.element.appendChild(text);

    // 应用深度样式
    this.applyDepthStyles();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.clickCallbacks = [];
    this.contextMenuCallbacks = [];
    this.dragStartCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.bookmark) {
      this.bookmark = data.bookmark;
      this.render();
    }
  }

  /**
   * 获取收藏夹数据
   */
  getBookmark(): BookmarkNode {
    return this.bookmark;
  }

  /**
   * 获取深度
   */
  getDepth(): number {
    return this.depth;
  }

  /**
   * 更新收藏夹数据
   */
  updateBookmark(bookmark: BookmarkNode): void {
    this.bookmark = bookmark;
    this.render();
  }

  /**
   * 显示项目
   */
  show(): void {
    this.visible = true;
    this.element.style.display = 'flex';
    this.element.classList.remove('hidden');
  }

  /**
   * 隐藏项目
   */
  hide(): void {
    this.visible = false;
    this.element.style.display = 'none';
    this.element.classList.add('hidden');
  }

  /**
   * 检查是否可见
   */
  isVisible(): boolean {
    return this.visible;
  }

  /**
   * 高亮搜索结果
   */
  highlight(query: string): void {
    const textElement = this.element.querySelector('.sidebar-item-text');
    if (!textElement) return;

    const title = this.bookmark.title;
    const regex = new RegExp(`(${query})`, 'gi');
    const highlightedTitle = title.replace(regex, '<mark>$1</mark>');
    textElement.innerHTML = highlightedTitle;
  }

  /**
   * 清除高亮
   */
  clearHighlight(): void {
    const textElement = this.element.querySelector('.sidebar-item-text');
    if (textElement) {
      textElement.textContent = this.bookmark.title;
    }
  }

  /**
   * 设置选中状态
   */
  setSelected(selected: boolean): void {
    if (selected) {
      this.element.classList.add('selected');
      this.element.setAttribute('aria-selected', 'true');
    } else {
      this.element.classList.remove('selected');
      this.element.setAttribute('aria-selected', 'false');
    }
  }

  /**
   * 添加点击回调
   */
  onClick(callback: (event: MouseEvent) => void): void {
    this.clickCallbacks.push(callback);
  }

  /**
   * 添加右键菜单回调
   */
  onContextMenu(callback: (event: MouseEvent, bookmark: BookmarkNode) => void): void {
    this.contextMenuCallbacks.push(callback);
  }

  /**
   * 添加拖拽开始回调
   */
  onDragStart(callback: (event: DragEvent, bookmark: BookmarkNode) => void): void {
    this.dragStartCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-item bookmark-item';
    element.setAttribute('role', 'button');
    element.setAttribute('tabindex', '0');
    element.setAttribute('aria-label', `收藏夹: ${this.bookmark.title}`);
    element.setAttribute('data-bookmark-id', this.bookmark.id);
    element.draggable = true;
    return element;
  }

  /**
   * 创建缩进
   */
  private createIndent(): HTMLElement {
    const indent = document.createElement('div');
    indent.className = 'bookmark-indent';
    indent.style.width = `${this.depth * 16}px`;
    indent.style.flexShrink = '0';
    return indent;
  }

  /**
   * 创建图标
   */
  private createIcon(): HTMLElement {
    const icon = document.createElement('div');
    icon.className = 'sidebar-item-icon bookmark-icon';

    // 尝试获取网站图标
    if (this.bookmark.url) {
      const domain = this.extractDomain(this.bookmark.url);
      if (domain) {
        const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
        const img = document.createElement('img');
        img.src = faviconUrl;
        img.alt = '';
        img.width = 16;
        img.height = 16;
        img.onerror = () => {
          // 如果图标加载失败，显示默认图标
          img.style.display = 'none';
          icon.innerHTML = this.getDefaultBookmarkIcon();
        };
        icon.appendChild(img);
      } else {
        icon.innerHTML = this.getDefaultBookmarkIcon();
      }
    } else {
      icon.innerHTML = this.getDefaultBookmarkIcon();
    }

    return icon;
  }

  /**
   * 创建文本内容
   */
  private createText(): HTMLElement {
    const text = document.createElement('div');
    text.className = 'sidebar-item-text bookmark-text';
    text.textContent = this.bookmark.title;
    text.title = `${this.bookmark.title}\n${this.bookmark.url || ''}`;
    return text;
  }

  /**
   * 应用深度样式
   */
  private applyDepthStyles(): void {
    // 根据深度调整样式
    this.element.style.paddingLeft = `${8 + this.depth * 16}px`;
    
    // 深度越深，背景色越浅
    if (this.depth > 0) {
      const opacity = Math.max(0.1, 0.5 - this.depth * 0.1);
      this.element.style.setProperty('--depth-bg-opacity', opacity.toString());
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 点击事件
    this.element.addEventListener('click', (event) => {
      event.preventDefault();
      this.notifyClick(event);
    });

    // 右键菜单
    this.element.addEventListener('contextmenu', (event) => {
      event.preventDefault();
      this.notifyContextMenu(event);
    });

    // 键盘支持
    this.element.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.notifyClick(event as any);
      }
    });

    // 鼠标悬停效果
    this.element.addEventListener('mouseenter', () => {
      this.element.classList.add('hover');
    });

    this.element.addEventListener('mouseleave', () => {
      this.element.classList.remove('hover');
    });

    // 拖拽支持
    this.element.addEventListener('dragstart', (event) => {
      this.element.classList.add('dragging');
      this.notifyDragStart(event);
    });

    this.element.addEventListener('dragend', () => {
      this.element.classList.remove('dragging');
    });

    // 焦点支持
    this.element.addEventListener('focus', () => {
      this.element.classList.add('focused');
    });

    this.element.addEventListener('blur', () => {
      this.element.classList.remove('focused');
    });
  }

  /**
   * 提取域名
   */
  private extractDomain(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取默认收藏夹图标
   */
  private getDefaultBookmarkIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
      </svg>
    `;
  }

  /**
   * 通知点击事件
   */
  private notifyClick(event: MouseEvent): void {
    this.clickCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in click callback:', error);
      }
    });
  }

  /**
   * 通知右键菜单事件
   */
  private notifyContextMenu(event: MouseEvent): void {
    this.contextMenuCallbacks.forEach(callback => {
      try {
        callback(event, this.bookmark);
      } catch (error) {
        console.error('Error in context menu callback:', error);
      }
    });
  }

  /**
   * 通知拖拽开始事件
   */
  private notifyDragStart(event: DragEvent): void {
    this.dragStartCallbacks.forEach(callback => {
      try {
        callback(event, this.bookmark);
      } catch (error) {
        console.error('Error in drag start callback:', error);
      }
    });
  }

  /**
   * 编辑收藏夹
   */
  editBookmark(): void {
    // 动态导入BookmarkEditor
    import('./BookmarkEditor.js').then(({ BookmarkEditor }) => {
      const editor = new BookmarkEditor({
        bookmark: this.bookmark,
        mode: 'edit',
        onSave: async (data) => {
          try {
            await chrome.runtime.sendMessage({
              type: 'UPDATE_BOOKMARK',
              payload: {
                bookmarkId: this.bookmark.id,
                updates: {
                  title: data.title,
                  url: data.url
                }
              },
              timestamp: Date.now()
            });
            
            // 更新本地显示
            this.bookmark.title = data.title;
            if (data.url) {
              this.bookmark.url = data.url;
            }
            this.render();
            
            editor.hide();
            console.log('Bookmark updated successfully');
          } catch (error) {
            console.error('Error updating bookmark:', error);
            throw error;
          }
        },
        onCancel: () => {
          editor.hide();
        }
      });
      
      editor.show();
    }).catch(error => {
      console.error('Error loading BookmarkEditor:', error);
    });
  }
  
  /**
   * 删除收藏夹
   */
  async deleteBookmark(): Promise<void> {
    if (!confirm(`确定要删除收藏夹"${this.bookmark.title}"吗？`)) {
      return;
    }
    
    try {
      await chrome.runtime.sendMessage({
        type: 'DELETE_BOOKMARK',
        payload: { bookmarkId: this.bookmark.id },
        timestamp: Date.now()
      });
      
      // 从DOM中移除
      if (this.element.parentNode) {
        this.element.parentNode.removeChild(this.element);
      }
      
      console.log('Bookmark deleted successfully');
    } catch (error) {
      console.error('Error deleting bookmark:', error);
      alert('删除失败，请重试');
    }
  }

  /**
   * 打开收藏夹链接
   */
  openBookmark(inNewTab = true): void {
    if (!this.bookmark.url) return;
    
    chrome.runtime.sendMessage({
      type: 'OPEN_TAB',
      payload: { 
        url: this.bookmark.url,
        active: !inNewTab
      },
      timestamp: Date.now()
    }).catch(error => {
      console.error('Error opening bookmark:', error);
    });
  }
}