/**
 * Bookmark List - 收藏夹列表组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { BookmarkNode } from '../../types/index.js';
import { BookmarkItem } from './BookmarkItem.js';
import { BookmarkFolder } from './BookmarkFolder.js';
import { BookmarkContextMenu } from './BookmarkContextMenu.js';
import { BookmarkDragHandler } from '../../utils/BookmarkDragHandler.js';
import { DragManager } from '../../utils/DragManager.js';

export class BookmarkList implements UIComponent {
  element: HTMLElement;
  private bookmarks: BookmarkNode[] = [];
  private components: Map<string, BookmarkItem | BookmarkFolder> = new Map();
  private dragManager: DragManager;
  private dragHandler: BookmarkDragHandler;
  private currentContextMenu: BookmarkContextMenu | null = null;
  private searchQuery = '';
  private expandedFolders: Set<string> = new Set();

  constructor() {
    this.element = this.createElement();
    this.dragManager = new DragManager();
    this.dragHandler = new BookmarkDragHandler(this.dragManager);
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    this.components.clear();

    if (this.bookmarks.length === 0) {
      this.renderEmptyState();
      return;
    }

    this.renderBookmarks(this.bookmarks, 0);
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.components.forEach(component => component.destroy());
    this.components.clear();
    this.dragHandler.destroy();
    
    if (this.currentContextMenu) {
      this.currentContextMenu.destroy();
      this.currentContextMenu = null;
    }

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.bookmarks) {
      this.bookmarks = data.bookmarks;
      this.render();
    }
  }

  /**
   * 设置收藏夹数据
   */
  setBookmarks(bookmarks: BookmarkNode[]): void {
    this.bookmarks = bookmarks;
    this.render();
  }

  /**
   * 获取收藏夹数据
   */
  getBookmarks(): BookmarkNode[] {
    return this.bookmarks;
  }

  /**
   * 搜索收藏夹
   */
  search(query: string): void {
    this.searchQuery = query.toLowerCase().trim();
    
    if (!this.searchQuery) {
      // 清除搜索，显示所有项目
      this.components.forEach(component => {
        component.show();
        if (component instanceof BookmarkItem) {
          component.clearHighlight();
        } else if (component instanceof BookmarkFolder) {
          component.clearHighlight();
        }
      });
      return;
    }

    // 执行搜索
    this.components.forEach((component, id) => {
      const bookmark = component.getBookmark();
      const matches = this.matchesSearch(bookmark, this.searchQuery);
      
      if (matches) {
        component.show();
        if (component instanceof BookmarkItem) {
          component.highlight(this.searchQuery);
        } else if (component instanceof BookmarkFolder) {
          component.highlight(this.searchQuery);
          // 展开匹配的文件夹
          component.setExpanded(true);
        }
      } else {
        component.hide();
        if (component instanceof BookmarkItem) {
          component.clearHighlight();
        } else if (component instanceof BookmarkFolder) {
          component.clearHighlight();
        }
      }
    });
  }

  /**
   * 清除搜索
   */
  clearSearch(): void {
    this.search('');
  }

  /**
   * 展开所有文件夹
   */
  expandAll(): void {
    this.components.forEach(component => {
      if (component instanceof BookmarkFolder) {
        component.setExpanded(true);
        this.expandedFolders.add(component.getBookmark().id);
      }
    });
  }

  /**
   * 折叠所有文件夹
   */
  collapseAll(): void {
    this.components.forEach(component => {
      if (component instanceof BookmarkFolder) {
        component.setExpanded(false);
        this.expandedFolders.delete(component.getBookmark().id);
      }
    });
  }

  /**
   * 刷新收藏夹列表
   */
  async refresh(): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BOOKMARKS',
        timestamp: Date.now()
      });

      if (response.success && response.data) {
        this.setBookmarks(response.data);
      }
    } catch (error) {
      console.error('Error refreshing bookmarks:', error);
    }
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'bookmark-list';
    element.setAttribute('role', 'tree');
    element.setAttribute('aria-label', '收藏夹列表');
    return element;
  }

  /**
   * 渲染收藏夹
   */
  private renderBookmarks(bookmarks: BookmarkNode[], depth: number): void {
    bookmarks.forEach(bookmark => {
      if (bookmark.children) {
        // 文件夹
        const folder = new BookmarkFolder(bookmark, depth);
        this.components.set(bookmark.id, folder);
        
        // 设置展开状态
        if (this.expandedFolders.has(bookmark.id)) {
          folder.setExpanded(true);
        }

        // 设置事件监听
        this.setupFolderEventListeners(folder);
        
        this.element.appendChild(folder.element);
        
        // 渲染子项目
        if (bookmark.children.length > 0) {
          this.renderBookmarks(bookmark.children, depth + 1);
          
          // 将子项目添加到文件夹的子容器中
          const childContainer = folder.getChildContainer();
          bookmark.children.forEach(child => {
            const childComponent = this.components.get(child.id);
            if (childComponent) {
              childContainer.appendChild(childComponent.element);
            }
          });
        }
      } else {
        // 收藏夹项目
        const item = new BookmarkItem(bookmark, depth);
        this.components.set(bookmark.id, item);
        
        // 设置事件监听
        this.setupItemEventListeners(item);
        
        this.element.appendChild(item.element);
      }
    });
  }

  /**
   * 渲染空状态
   */
  private renderEmptyState(): void {
    const emptyState = document.createElement('div');
    emptyState.className = 'bookmark-list-empty';
    emptyState.style.textAlign = 'center';
    emptyState.style.padding = '40px 20px';
    emptyState.style.color = '#656d76';
    emptyState.style.fontSize = '14px';

    const icon = document.createElement('div');
    icon.style.fontSize = '48px';
    icon.style.marginBottom = '16px';
    icon.textContent = '📚';

    const text = document.createElement('div');
    text.textContent = '暂无收藏夹';

    emptyState.appendChild(icon);
    emptyState.appendChild(text);
    this.element.appendChild(emptyState);
  }

  /**
   * 设置文件夹事件监听
   */
  private setupFolderEventListeners(folder: BookmarkFolder): void {
    // 切换展开状态
    folder.onToggle((expanded) => {
      if (expanded) {
        this.expandedFolders.add(folder.getBookmark().id);
      } else {
        this.expandedFolders.delete(folder.getBookmark().id);
      }
    });

    // 右键菜单
    folder.onContextMenu((event, bookmark) => {
      this.showContextMenu(event, bookmark, folder);
    });

    // 拖拽悬停
    folder.onDragOver((event, bookmark) => {
      this.dragHandler.handleFolderDragOver(event, bookmark);
    });

    // 拖拽放置
    folder.onDrop((event, bookmark) => {
      this.dragHandler.handleFolderDrop(event, bookmark);
    });
  }

  /**
   * 设置项目事件监听
   */
  private setupItemEventListeners(item: BookmarkItem): void {
    // 点击打开
    item.onClick((event) => {
      const bookmark = item.getBookmark();
      if (bookmark.url) {
        item.openBookmark(true);
      }
    });

    // 右键菜单
    item.onContextMenu((event, bookmark) => {
      this.showContextMenu(event, bookmark, item);
    });

    // 拖拽开始
    item.onDragStart((event, bookmark) => {
      this.dragHandler.handleDragStart(item.element, bookmark, event);
    });
  }

  /**
   * 显示上下文菜单
   */
  private showContextMenu(
    event: MouseEvent, 
    bookmark: BookmarkNode, 
    component: BookmarkItem | BookmarkFolder
  ): void {
    // 隐藏现有菜单
    if (this.currentContextMenu) {
      this.currentContextMenu.destroy();
    }

    // 创建新菜单
    this.currentContextMenu = new BookmarkContextMenu({
      bookmark,
      component,
      x: event.clientX,
      y: event.clientY
    });

    this.currentContextMenu.show();
  }

  /**
   * 检查是否匹配搜索
   */
  private matchesSearch(bookmark: BookmarkNode, query: string): boolean {
    // 检查标题
    if (bookmark.title.toLowerCase().includes(query)) {
      return true;
    }

    // 检查URL
    if (bookmark.url && bookmark.url.toLowerCase().includes(query)) {
      return true;
    }

    // 检查子项目
    if (bookmark.children) {
      return bookmark.children.some(child => this.matchesSearch(child, query));
    }

    return false;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 全局点击隐藏上下文菜单
    document.addEventListener('click', () => {
      if (this.currentContextMenu) {
        this.currentContextMenu.destroy();
        this.currentContextMenu = null;
      }
    });

    // 键盘导航支持
    this.element.addEventListener('keydown', (event) => {
      this.handleKeyboardNavigation(event);
    });
  }

  /**
   * 处理键盘导航
   */
  private handleKeyboardNavigation(event: KeyboardEvent): void {
    const focusedElement = document.activeElement as HTMLElement;
    if (!focusedElement || !this.element.contains(focusedElement)) {
      return;
    }

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        this.focusPreviousItem(focusedElement);
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.focusNextItem(focusedElement);
        break;
      case 'Home':
        event.preventDefault();
        this.focusFirstItem();
        break;
      case 'End':
        event.preventDefault();
        this.focusLastItem();
        break;
    }
  }

  /**
   * 聚焦上一个项目
   */
  private focusPreviousItem(currentElement: HTMLElement): void {
    const items = Array.from(this.element.querySelectorAll('[tabindex="0"]'));
    const currentIndex = items.indexOf(currentElement);
    if (currentIndex > 0) {
      (items[currentIndex - 1] as HTMLElement).focus();
    }
  }

  /**
   * 聚焦下一个项目
   */
  private focusNextItem(currentElement: HTMLElement): void {
    const items = Array.from(this.element.querySelectorAll('[tabindex="0"]'));
    const currentIndex = items.indexOf(currentElement);
    if (currentIndex < items.length - 1) {
      (items[currentIndex + 1] as HTMLElement).focus();
    }
  }

  /**
   * 聚焦第一个项目
   */
  private focusFirstItem(): void {
    const firstItem = this.element.querySelector('[tabindex="0"]') as HTMLElement;
    if (firstItem) {
      firstItem.focus();
    }
  }

  /**
   * 聚焦最后一个项目
   */
  private focusLastItem(): void {
    const items = Array.from(this.element.querySelectorAll('[tabindex="0"]'));
    const lastItem = items[items.length - 1] as HTMLElement;
    if (lastItem) {
      lastItem.focus();
    }
  }
}