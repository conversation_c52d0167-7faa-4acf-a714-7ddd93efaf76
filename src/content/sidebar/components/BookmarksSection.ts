/**
 * Bookmarks Section - 收藏夹区域组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { BookmarkNode, UserSettings } from '../../types/index.js';
import { BookmarkItem } from './BookmarkItem.js';
import { BookmarkFolder } from './BookmarkFolder.js';
import { BookmarkEditor, BookmarkEditData } from '../../utils/BookmarkEditor.js';
import { BookmarkDragHandler } from '../../utils/BookmarkDragHandler.js';
import { DragManager } from '../../utils/DragManager.js';
import { ContextMenuManager, ContextMenuItem } from '../../utils/ContextMenuManager.js';

export class BookmarksSection implements UIComponent {
  element: HTMLElement;
  private bookmarks: BookmarkNode[] = [];
  private bookmarkItems: Map<string, BookmarkItem> = new Map();
  private folderComponents: Map<string, BookmarkFolder> = new Map();
  private searchQuery = '';
  private settings: UserSettings | null = null;
  private expandedFolders: Set<string> = new Set();
  private bookmarkEditor: BookmarkEditor;
  private dragManager: DragManager;
  private dragHandler: BookmarkDragHandler;
  private contextMenuManager: ContextMenuManager;

  constructor() {
    this.element = this.createElement();
    this.loadExpandedState();
    
    // 初始化工具类
    this.bookmarkEditor = new BookmarkEditor();
    this.dragManager = new DragManager();
    this.dragHandler = new BookmarkDragHandler(this.dragManager);
    this.contextMenuManager = new ContextMenuManager();
    
    this.setupEventListeners();
    this.dragManager.initialize();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    
    // 添加区域标题
    const title = this.createSectionTitle();
    this.element.appendChild(title);

    // 添加收藏夹列表容器
    const listContainer = this.createListContainer();
    this.element.appendChild(listContainer);

    // 渲染收藏夹
    this.renderBookmarksTree();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 销毁所有收藏夹项目
    this.bookmarkItems.forEach(item => item.destroy());
    this.bookmarkItems.clear();

    // 销毁所有文件夹组件
    this.folderComponents.forEach(folder => folder.destroy());
    this.folderComponents.clear();

    // 销毁工具类
    this.bookmarkEditor.hide();
    this.dragManager.destroy();
    this.dragHandler.cleanup();
    this.contextMenuManager.destroy();

    // 保存展开状态
    this.saveExpandedState();

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    this.renderBookmarksTree();
  }

  /**
   * 更新收藏夹数据
   */
  updateBookmarks(bookmarks: BookmarkNode[]): void {
    this.bookmarks = bookmarks;
    this.renderBookmarksTree();
  }

  /**
   * 更新设置
   */
  updateSettings(settings: UserSettings): void {
    this.settings = settings;
    this.renderBookmarksTree();
  }

  /**
   * 搜索收藏夹
   */
  search(query: string): void {
    this.searchQuery = query.toLowerCase();
    this.filterBookmarks();
  }

  /**
   * 检查是否有可见项目
   */
  hasVisibleItems(): boolean {
    return Array.from(this.bookmarkItems.values()).some(item => item.isVisible()) ||
           Array.from(this.folderComponents.values()).some(folder => folder.hasVisibleItems());
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-section bookmarks-section';
    return element;
  }

  /**
   * 创建区域标题
   */
  private createSectionTitle(): HTMLElement {
    const title = document.createElement('div');
    title.className = 'sidebar-section-title';
    title.textContent = '收藏夹';
    return title;
  }

  /**
   * 创建列表容器
   */
  private createListContainer(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'bookmarks-list';
    return container;
  }

  /**
   * 渲染收藏夹树
   */
  private renderBookmarksTree(): void {
    const listContainer = this.element.querySelector('.bookmarks-list');
    if (!listContainer) return;

    // 清空现有内容
    listContainer.innerHTML = '';

    // 清理现有组件
    this.bookmarkItems.forEach(item => item.destroy());
    this.bookmarkItems.clear();
    this.folderComponents.forEach(folder => folder.destroy());
    this.folderComponents.clear();

    if (this.bookmarks.length === 0) {
      this.showEmptyState(listContainer);
      return;
    }

    // 渲染根级收藏夹
    this.renderBookmarkNodes(this.bookmarks, listContainer, 0);

    // 应用搜索过滤
    if (this.searchQuery) {
      this.filterBookmarks();
    }
  }

  /**
   * 渲染收藏夹节点
   */
  private renderBookmarkNodes(nodes: BookmarkNode[], container: Element, depth: number): void {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        // 渲染文件夹
        this.renderBookmarkFolder(node, container, depth);
      } else if (node.url) {
        // 渲染收藏夹项目
        this.renderBookmarkItem(node, container, depth);
      }
    });
  }

  /**
   * 渲染收藏夹文件夹
   */
  private renderBookmarkFolder(folder: BookmarkNode, container: Element, depth: number): void {
    const folderComponent = new BookmarkFolder(folder, depth);
    this.folderComponents.set(folder.id, folderComponent);

    // 设置展开状态
    const isExpanded = this.expandedFolders.has(folder.id);
    folderComponent.setExpanded(isExpanded);

    // 设置事件监听
    this.setupFolderEventListeners(folderComponent);

    container.appendChild(folderComponent.element);
    folderComponent.render();

    // 如果文件夹展开，渲染子项目
    if (isExpanded && folder.children) {
      const childContainer = folderComponent.getChildContainer();
      this.renderBookmarkNodes(folder.children, childContainer, depth + 1);
    }
  }

  /**
   * 渲染收藏夹项目
   */
  private renderBookmarkItem(bookmark: BookmarkNode, container: Element, depth: number): void {
    const bookmarkItem = new BookmarkItem(bookmark, depth);
    this.bookmarkItems.set(bookmark.id, bookmarkItem);

    // 设置事件监听
    this.setupBookmarkEventListeners(bookmarkItem);

    container.appendChild(bookmarkItem.element);
    bookmarkItem.render();
  }

  /**
   * 设置文件夹事件监听
   */
  private setupFolderEventListeners(folderComponent: BookmarkFolder): void {
    // 展开/折叠事件
    folderComponent.onToggle((expanded) => {
      const folder = folderComponent.getBookmark();
      
      if (expanded) {
        this.expandedFolders.add(folder.id);
        
        // 渲染子项目
        if (folder.children) {
          const childContainer = folderComponent.getChildContainer();
          childContainer.innerHTML = '';
          this.renderBookmarkNodes(folder.children, childContainer, folderComponent.getDepth() + 1);
        }
      } else {
        this.expandedFolders.delete(folder.id);
      }

      this.saveExpandedState();
    });

    // 右键菜单事件
    folderComponent.onContextMenu((event, folder) => {
      this.showFolderContextMenu(event, folder);
    });

    // 拖拽事件 - 使用新的拖拽处理器
    folderComponent.onDragOver((event, folder) => {
      event.preventDefault();
      event.dataTransfer!.dropEffect = 'move';
    });

    folderComponent.onDrop((event, folder) => {
      event.preventDefault();
      const dragData = JSON.parse(event.dataTransfer?.getData('application/json') || '{}');
      this.dragHandler.handleBookmarkDropOnFolder(dragData, folder, 0);
    });

    // 注册为拖拽放置区域
    this.dragManager.registerDropZone({
      element: folderComponent.element,
      type: 'bookmark',
      onDrop: (dragData, dropIndex) => {
        this.dragHandler.handleBookmarkDropOnFolder(dragData, folderComponent.getBookmark(), dropIndex);
      }
    });
  }

  /**
   * 设置收藏夹事件监听
   */
  private setupBookmarkEventListeners(bookmarkItem: BookmarkItem): void {
    // 点击打开收藏夹
    bookmarkItem.onClick((event) => {
      const bookmark = bookmarkItem.getBookmark();
      this.openBookmark(bookmark, event.ctrlKey || event.metaKey);
    });

    // 右键菜单
    bookmarkItem.onContextMenu((event, bookmark) => {
      this.showBookmarkContextMenu(event, bookmark);
    });

    // 拖拽开始
    bookmarkItem.onDragStart((event, bookmark) => {
      this.dragHandler.handleBookmarkDragStart(bookmarkItem.element, bookmark, event);
    });

    // 注册为拖拽放置区域
    this.dragManager.registerDropZone({
      element: bookmarkItem.element,
      type: 'bookmark',
      onDrop: (dragData, dropIndex) => {
        this.dragHandler.handleBookmarkReorder(dragData, bookmarkItem.getBookmark(), dropIndex);
      }
    });
  }

  /**
   * 过滤收藏夹
   */
  private filterBookmarks(): void {
    if (!this.searchQuery) {
      // 显示所有项目
      this.bookmarkItems.forEach(item => item.show());
      this.folderComponents.forEach(folder => folder.show());
      return;
    }

    // 过滤收藏夹项目
    this.bookmarkItems.forEach(item => {
      const bookmark = item.getBookmark();
      const matches = this.matchesSearch(bookmark.title, bookmark.url || '');
      if (matches) {
        item.show();
        item.highlight(this.searchQuery);
      } else {
        item.hide();
      }
    });

    // 过滤文件夹
    this.folderComponents.forEach(folder => {
      const hasVisibleChildren = this.folderHasVisibleChildren(folder.getBookmark());
      const folderMatches = this.matchesSearch(folder.getBookmark().title, '');
      
      if (hasVisibleChildren || folderMatches) {
        folder.show();
        if (folderMatches) {
          folder.highlight(this.searchQuery);
        }
        
        // 如果有匹配的子项目，自动展开文件夹
        if (hasVisibleChildren && !folder.isExpanded()) {
          folder.setExpanded(true);
          this.expandedFolders.add(folder.getBookmark().id);
        }
      } else {
        folder.hide();
      }
    });
  }

  /**
   * 检查文件夹是否有可见子项目
   */
  private folderHasVisibleChildren(folder: BookmarkNode): boolean {
    if (!folder.children) return false;

    return folder.children.some(child => {
      if (child.url) {
        // 检查收藏夹项目是否匹配
        return this.matchesSearch(child.title, child.url);
      } else if (child.children) {
        // 递归检查子文件夹
        return this.folderHasVisibleChildren(child);
      }
      return false;
    });
  }

  /**
   * 检查是否匹配搜索
   */
  private matchesSearch(title: string, url: string): boolean {
    const query = this.searchQuery.toLowerCase();
    return title.toLowerCase().includes(query) || 
           url.toLowerCase().includes(query);
  }

  /**
   * 打开收藏夹
   */
  private async openBookmark(bookmark: BookmarkNode, newTab = false): Promise<void> {
    if (!bookmark.url) return;

    try {
      if (newTab) {
        // 在新标签页中打开
        await chrome.runtime.sendMessage({
          type: 'CREATE_TAB',
          payload: { url: bookmark.url, active: false },
          timestamp: Date.now()
        });
      } else {
        // 在当前标签页中打开
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs[0]?.id) {
          await chrome.tabs.update(tabs[0].id, { url: bookmark.url });
        }
      }
    } catch (error) {
      console.error('Error opening bookmark:', error);
    }
  }

  /**
   * 显示收藏夹右键菜单
   */
  private showBookmarkContextMenu(event: MouseEvent, bookmark: BookmarkNode): void {
    event.preventDefault();
    
    const menuItems: ContextMenuItem[] = [
      { 
        text: '打开', 
        icon: this.getOpenIcon(),
        action: () => this.openBookmark(bookmark, false) 
      },
      { 
        text: '在新标签页中打开', 
        icon: this.getNewTabIcon(),
        action: () => this.openBookmark(bookmark, true) 
      },
      { separator: true },
      { 
        text: '编辑', 
        icon: this.getEditIcon(),
        action: () => this.editBookmark(bookmark) 
      },
      { 
        text: '复制链接', 
        icon: this.getCopyIcon(),
        action: () => this.copyBookmarkUrl(bookmark) 
      },
      { separator: true },
      { 
        text: '删除', 
        icon: this.getDeleteIcon(),
        action: () => this.deleteBookmark(bookmark) 
      }
    ];

    this.contextMenuManager.showMenu(menuItems, event.clientX, event.clientY);
  }

  /**
   * 显示文件夹右键菜单
   */
  private showFolderContextMenu(event: MouseEvent, folder: BookmarkNode): void {
    event.preventDefault();
    
    const folderComponent = this.folderComponents.get(folder.id);
    const isExpanded = folderComponent?.isExpanded() || false;

    const menuItems: ContextMenuItem[] = [
      { 
        text: isExpanded ? '折叠文件夹' : '展开文件夹',
        icon: isExpanded ? this.getCollapseIcon() : this.getExpandIcon(),
        action: () => this.toggleFolder(folder.id) 
      },
      { 
        text: '在新标签页中打开所有',
        icon: this.getOpenAllIcon(),
        action: () => this.openAllInFolder(folder) 
      },
      { separator: true },
      { 
        text: '添加收藏夹',
        icon: this.getAddBookmarkIcon(),
        action: () => this.addBookmarkToFolder(folder) 
      },
      { 
        text: '新建文件夹',
        icon: this.getAddFolderIcon(),
        action: () => this.createSubfolder(folder) 
      },
      { separator: true },
      { 
        text: '重命名',
        icon: this.getEditIcon(),
        action: () => this.renameFolder(folder) 
      },
      { 
        text: '删除文件夹',
        icon: this.getDeleteIcon(),
        action: () => this.deleteFolder(folder) 
      }
    ];

    this.contextMenuManager.showMenu(menuItems, event.clientX, event.clientY);
  }

  /**
   * 显示上下文菜单
   */
  private showContextMenu(items: any[], x: number, y: number): void {
    // 移除现有菜单
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
      existingMenu.remove();
    }

    // 创建菜单
    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.style.position = 'fixed';
    menu.style.left = `${x}px`;
    menu.style.top = `${y}px`;
    menu.style.zIndex = '2147483648';
    menu.style.backgroundColor = '#ffffff';
    menu.style.border = '1px solid #d0d7de';
    menu.style.borderRadius = '6px';
    menu.style.boxShadow = '0 8px 24px rgba(140, 149, 159, 0.2)';
    menu.style.padding = '4px 0';
    menu.style.minWidth = '160px';

    items.forEach(item => {
      const menuItem = document.createElement('div');
      menuItem.className = 'context-menu-item';
      menuItem.textContent = item.text;
      menuItem.style.padding = '6px 12px';
      menuItem.style.cursor = 'pointer';
      menuItem.style.fontSize = '14px';
      
      menuItem.addEventListener('click', () => {
        item.action();
        menu.remove();
      });

      menuItem.addEventListener('mouseenter', () => {
        menuItem.style.backgroundColor = '#f6f8fa';
      });

      menuItem.addEventListener('mouseleave', () => {
        menuItem.style.backgroundColor = 'transparent';
      });

      menu.appendChild(menuItem);
    });

    document.body.appendChild(menu);

    // 点击外部关闭菜单
    const closeMenu = (event: MouseEvent) => {
      if (!menu.contains(event.target as Node)) {
        menu.remove();
        document.removeEventListener('click', closeMenu);
      }
    };

    setTimeout(() => {
      document.addEventListener('click', closeMenu);
    }, 0);
  }

  /**
   * 显示空状态
   */
  private showEmptyState(container: Element): void {
    const emptyElement = document.createElement('div');
    emptyElement.className = 'bookmarks-empty';
    emptyElement.innerHTML = `
      <div class="empty-icon">⭐</div>
      <div class="empty-text">没有收藏夹</div>
      <div class="empty-hint">添加一些收藏夹来快速访问</div>
    `;
    container.appendChild(emptyElement);
  }

  /**
   * 加载展开状态
   */
  private loadExpandedState(): void {
    try {
      const saved = localStorage.getItem('sidebar-expanded-folders');
      if (saved) {
        this.expandedFolders = new Set(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading expanded state:', error);
    }
  }

  /**
   * 保存展开状态
   */
  private saveExpandedState(): void {
    try {
      localStorage.setItem('sidebar-expanded-folders', JSON.stringify(Array.from(this.expandedFolders)));
    } catch (error) {
      console.error('Error saving expanded state:', error);
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 键盘导航支持
    this.element.addEventListener('keydown', (event) => {
      this.handleKeyboardNavigation(event);
    });
  }

  /**
   * 处理键盘导航
   */
  private handleKeyboardNavigation(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        this.navigateUp();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.navigateDown();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.collapseSelected();
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.expandSelected();
        break;
      case 'Enter':
        event.preventDefault();
        this.activateSelected();
        break;
    }
  }

  // 辅助方法实现
  private navigateUp(): void {
    // 实现向上导航逻辑
  }

  private navigateDown(): void {
    // 实现向下导航逻辑
  }

  private collapseSelected(): void {
    // 实现折叠选中文件夹逻辑
  }

  private expandSelected(): void {
    // 实现展开选中文件夹逻辑
  }

  private activateSelected(): void {
    // 实现激活选中项逻辑
  }

  private toggleFolder(folderId: string): void {
    const folderComponent = this.folderComponents.get(folderId);
    if (folderComponent) {
      folderComponent.toggle();
    }
  }

  private async openAllInFolder(folder: BookmarkNode): Promise<void> {
    if (!folder.children) return;

    const bookmarks = this.getAllBookmarksInFolder(folder);
    for (const bookmark of bookmarks) {
      if (bookmark.url) {
        await this.openBookmark(bookmark, true);
      }
    }
  }

  private getAllBookmarksInFolder(folder: BookmarkNode): BookmarkNode[] {
    const bookmarks: BookmarkNode[] = [];
    
    if (folder.children) {
      for (const child of folder.children) {
        if (child.url) {
          bookmarks.push(child);
        } else if (child.children) {
          bookmarks.push(...this.getAllBookmarksInFolder(child));
        }
      }
    }
    
    return bookmarks;
  }

  private editBookmark(bookmark: BookmarkNode): void {
    this.bookmarkEditor.editBookmark(bookmark, async (data: BookmarkEditData) => {
      try {
        const updates: Partial<BookmarkNode> = {
          title: data.title
        };
        
        if (data.url !== undefined) {
          updates.url = this.bookmarkEditor.normalizeUrl(data.url);
        }

        await chrome.runtime.sendMessage({
          type: 'UPDATE_BOOKMARK',
          payload: { 
            bookmarkId: bookmark.id, 
            updates 
          },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error updating bookmark:', error);
      }
    });
  }

  private copyBookmarkUrl(bookmark: BookmarkNode): void {
    if (bookmark.url) {
      navigator.clipboard.writeText(bookmark.url).catch(error => {
        console.error('Error copying URL:', error);
      });
    }
  }

  private async deleteBookmark(bookmark: BookmarkNode): Promise<void> {
    if (confirm(`确定要删除收藏夹"${bookmark.title}"吗？`)) {
      try {
        await chrome.runtime.sendMessage({
          type: 'DELETE_BOOKMARK',
          payload: { bookmarkId: bookmark.id },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error deleting bookmark:', error);
      }
    }
  }

  private addBookmarkToFolder(folder: BookmarkNode): void {
    this.bookmarkEditor.createBookmark(folder, async (data: BookmarkEditData) => {
      try {
        await chrome.runtime.sendMessage({
          type: 'CREATE_BOOKMARK',
          payload: { 
            title: data.title, 
            url: this.bookmarkEditor.normalizeUrl(data.url || ''), 
            folderId: folder.id 
          },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error creating bookmark:', error);
      }
    });
  }

  private createSubfolder(parentFolder: BookmarkNode): void {
    this.bookmarkEditor.createFolder(parentFolder, async (data: BookmarkEditData) => {
      try {
        await chrome.runtime.sendMessage({
          type: 'CREATE_BOOKMARK_FOLDER',
          payload: { title: data.title, parentId: parentFolder.id },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error creating folder:', error);
      }
    });
  }

  private renameFolder(folder: BookmarkNode): void {
    // 创建一个临时的收藏夹对象用于编辑
    const tempFolder: BookmarkNode = {
      ...folder,
      url: undefined // 确保没有URL字段，这样编辑器就知道这是文件夹
    };

    this.bookmarkEditor.editBookmark(tempFolder, async (data: BookmarkEditData) => {
      try {
        await chrome.runtime.sendMessage({
          type: 'UPDATE_BOOKMARK',
          payload: { 
            bookmarkId: folder.id, 
            updates: { title: data.title } 
          },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error renaming folder:', error);
      }
    });
  }

  private async deleteFolder(folder: BookmarkNode): Promise<void> {
    if (confirm(`确定要删除文件夹"${folder.title}"及其所有内容吗？`)) {
      try {
        await chrome.runtime.sendMessage({
          type: 'DELETE_BOOKMARK',
          payload: { bookmarkId: folder.id },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error deleting folder:', error);
      }
    }
  }

  private handleFolderDragOver(event: DragEvent, folder: BookmarkNode): void {
    event.preventDefault();
    event.dataTransfer!.dropEffect = 'move';
  }

  private handleFolderDrop(event: DragEvent, folder: BookmarkNode): void {
    event.preventDefault();
    
    try {
      const dragData = event.dataTransfer?.getData('text/plain');
      if (dragData) {
        // 处理拖拽放置逻辑
        console.log('Dropped on folder:', folder.id, 'Data:', dragData);
      }
    } catch (error) {
      console.error('Error handling folder drop:', error);
    }
  }

  private handleBookmarkDragStart(event: DragEvent, bookmark: BookmarkNode): void {
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', bookmark.id);
    }
  }

  // 图标方法
  private getOpenIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M1.5 1A1.5 1.5 0 0 0 0 2.5v11A1.5 1.5 0 0 0 1.5 15h13a1.5 1.5 0 0 0 1.5-1.5V6.954a1.5 1.5 0 0 0-.44-1.06L11.06.44A1.5 1.5 0 0 0 10 0H1.5zM1 2.5a.5.5 0 0 1 .5-.5H10v5a1.5 1.5 0 0 0 1.5 1.5h3v6a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5v-11z"/>
    </svg>`;
  }

  private getNewTabIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M14 1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
      <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
    </svg>`;
  }

  private getEditIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708L10.5 8.207l-3-3L12.146.146zM11.207 9l-3-3L2.5 11.707V14.5a.5.5 0 0 0 .5.5h2.793L11.207 9zM1 11.5a.5.5 0 0 1 .146-.354l8-8a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-8 8A.5.5 0 0 1 4.5 15H1.5a.5.5 0 0 1-.5-.5v-3z"/>
    </svg>`;
  }

  private getCopyIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
      <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
    </svg>`;
  }

  private getDeleteIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M6.5 1h3a.5.5 0 0 1 .5.5v1H6v-1a.5.5 0 0 1 .5-.5ZM11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3A1.5 1.5 0 0 0 5 1.5v1H2.506a.58.58 0 0 0-.01 0H1.5a.5.5 0 0 0 0 1h.538l.853 10.66A2 2 0 0 0 4.885 16h6.23a2 2 0 0 0 1.994-1.84l.853-10.66h.538a.5.5 0 0 0 0-1h-.995a.59.59 0 0 0-.01 0H11Zm1.958 1-.846 10.58a1 1 0 0 1-.997.92h-6.23a1 1 0 0 1-.997-.92L3.042 3.5h9.916Zm-7.487 1a.5.5 0 0 1 .528.47l.5 8.5a.5.5 0 0 1-.998.06L5 5.03a.5.5 0 0 1 .47-.53Zm5.058 0a.5.5 0 0 1 .47.53l-.5 8.5a.5.5 0 1 1-.998-.06l.5-8.5a.5.5 0 0 1 .528-.47ZM8 4.5a.5.5 0 0 1 .5.5v8.5a.5.5 0 0 1-1 0V5a.5.5 0 0 1 .5-.5Z"/>
    </svg>`;
  }

  private getExpandIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M6.22 3.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 1 1-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 0 1 0-1.06z"/>
    </svg>`;
  }

  private getCollapseIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M12.78 6.22a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L3.22 7.28a.75.75 0 1 1 1.06-1.06L8 9.94l3.72-3.72a.75.75 0 0 1 1.06 0z"/>
    </svg>`;
  }

  private getOpenAllIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M14 1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
      <path d="M11.5 5.5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h5zm0 2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h5zm0 2a.5.5 0 0 1 0 1h-5a.5.5 0 0 1 0-1h5z"/>
    </svg>`;
  }

  private getAddBookmarkIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
      <path d="M8 4a.5.5 0 0 1 .5.5V6H10a.5.5 0 0 1 0 1H8.5v1.5a.5.5 0 0 1-1 0V7H6a.5.5 0 0 1 0-1h1.5V4.5A.5.5 0 0 1 8 4z"/>
    </svg>`;
  }

  private getAddFolderIcon(): string {
    return `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <path d="M.5 3l.04.87a1.99 1.99 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14H9.81a2 2 0 0 0 1.99-1.819l.637-7a1.99 1.99 0 0 0-.342-1.311L12.5 3H2.5zM2.19 4l-.693 7.6a1 1 0 0 0 .995 1.1H9.81a1 1 0 0 0 .995-1.1L9.81 4H2.19z"/>
      <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h3A1.5 1.5 0 0 1 6 1.5V3H1.5A1.5 1.5 0 0 1 0 1.5z"/>
      <path d="M8 6a.5.5 0 0 1 .5.5V8H10a.5.5 0 0 1 0 1H8.5v1.5a.5.5 0 0 1-1 0V9H6a.5.5 0 0 1 0-1h1.5V6.5A.5.5 0 0 1 8 6z"/>
    </svg>`;
  }
}