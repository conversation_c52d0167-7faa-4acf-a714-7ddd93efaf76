/**
 * Search Box - 搜索框组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';

export interface SearchBoxOptions {
  placeholder?: string;
  debounceDelay?: number;
  showClearButton?: boolean;
  showSearchHistory?: boolean;
  maxHistoryItems?: number;
}

export class SearchBox implements UIComponent {
  element: HTMLElement;
  private input: HTMLInputElement;
  private clearButton: HTMLElement;
  private searchIcon: HTMLElement;
  private historyDropdown: HTMLElement;
  private options: SearchBoxOptions;
  private searchCallbacks: ((query: string) => void)[] = [];
  private clearCallbacks: (() => void)[] = [];
  private focusCallbacks: (() => void)[] = [];
  private blurCallbacks: (() => void)[] = [];
  private debounceTimer: number | null = null;
  private searchHistory: string[] = [];
  private isHistoryVisible = false;
  private currentQuery = '';

  constructor(options: SearchBoxOptions = {}) {
    this.options = {
      placeholder: '搜索收藏夹和标签页...',
      debounceDelay: 300,
      showClearButton: true,
      showSearchHistory: true,
      maxHistoryItems: 10,
      ...options
    };

    this.element = this.createElement();
    this.input = this.createInput();
    this.clearButton = this.createClearButton();
    this.searchIcon = this.createSearchIcon();
    this.historyDropdown = this.createHistoryDropdown();
    
    this.render();
    this.setupEventListeners();
    this.loadSearchHistory();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    
    // 添加搜索图标
    this.element.appendChild(this.searchIcon);
    
    // 添加输入框
    this.element.appendChild(this.input);
    
    // 添加清除按钮
    if (this.options.showClearButton) {
      this.element.appendChild(this.clearButton);
    }
    
    // 添加历史记录下拉框
    if (this.options.showSearchHistory) {
      this.element.appendChild(this.historyDropdown);
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.clearDebounceTimer();
    this.searchCallbacks = [];
    this.clearCallbacks = [];
    this.focusCallbacks = [];
    this.blurCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && typeof data.query === 'string') {
      this.setValue(data.query);
    }
  }

  /**
   * 获取搜索值
   */
  getValue(): string {
    return this.input.value;
  }

  /**
   * 设置搜索值
   */
  setValue(value: string): void {
    this.input.value = value;
    this.currentQuery = value;
    this.updateClearButtonVisibility();
  }

  /**
   * 清除搜索
   */
  clear(): void {
    this.setValue('');
    this.notifySearch('');
    this.notifyClear();
    this.input.focus();
  }

  /**
   * 聚焦输入框
   */
  focus(): void {
    this.input.focus();
  }

  /**
   * 失焦输入框
   */
  blur(): void {
    this.input.blur();
  }

  /**
   * 检查是否有搜索内容
   */
  hasQuery(): boolean {
    return this.currentQuery.trim().length > 0;
  }

  /**
   * 获取搜索历史
   */
  getSearchHistory(): string[] {
    return [...this.searchHistory];
  }

  /**
   * 添加到搜索历史
   */
  addToHistory(query: string): void {
    const trimmedQuery = query.trim();
    if (!trimmedQuery || trimmedQuery.length < 2) return;

    // 移除重复项
    this.searchHistory = this.searchHistory.filter(item => item !== trimmedQuery);
    
    // 添加到开头
    this.searchHistory.unshift(trimmedQuery);
    
    // 限制历史记录数量
    if (this.searchHistory.length > this.options.maxHistoryItems!) {
      this.searchHistory = this.searchHistory.slice(0, this.options.maxHistoryItems);
    }
    
    this.saveSearchHistory();
    this.updateHistoryDropdown();
  }

  /**
   * 清除搜索历史
   */
  clearHistory(): void {
    this.searchHistory = [];
    this.saveSearchHistory();
    this.updateHistoryDropdown();
    this.hideHistory();
  }

  /**
   * 显示搜索历史
   */
  showHistory(): void {
    if (this.searchHistory.length === 0) return;
    
    this.isHistoryVisible = true;
    this.historyDropdown.style.display = 'block';
    this.element.classList.add('history-visible');
  }

  /**
   * 隐藏搜索历史
   */
  hideHistory(): void {
    this.isHistoryVisible = false;
    this.historyDropdown.style.display = 'none';
    this.element.classList.remove('history-visible');
  }

  /**
   * 添加搜索回调
   */
  onSearch(callback: (query: string) => void): void {
    this.searchCallbacks.push(callback);
  }

  /**
   * 添加清除回调
   */
  onClear(callback: () => void): void {
    this.clearCallbacks.push(callback);
  }

  /**
   * 添加聚焦回调
   */
  onFocus(callback: () => void): void {
    this.focusCallbacks.push(callback);
  }

  /**
   * 添加失焦回调
   */
  onBlur(callback: () => void): void {
    this.blurCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-box';
    element.style.position = 'relative';
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.backgroundColor = '#f6f8fa';
    element.style.border = '1px solid #d0d7de';
    element.style.borderRadius = '8px';
    element.style.padding = '0';
    element.style.margin = '8px';
    element.style.transition = 'all 0.15s ease';
    
    return element;
  }

  /**
   * 创建输入框
   */
  private createInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'search-input';
    input.placeholder = this.options.placeholder!;
    input.style.flex = '1';
    input.style.border = 'none';
    input.style.outline = 'none';
    input.style.backgroundColor = 'transparent';
    input.style.padding = '8px 12px';
    input.style.fontSize = '14px';
    input.style.fontFamily = 'inherit';
    input.style.color = '#24292f';
    
    return input;
  }

  /**
   * 创建清除按钮
   */
  private createClearButton(): HTMLElement {
    const button = document.createElement('button');
    button.className = 'search-clear';
    button.style.display = 'none';
    button.style.border = 'none';
    button.style.background = 'none';
    button.style.padding = '4px 8px';
    button.style.cursor = 'pointer';
    button.style.color = '#656d76';
    button.style.borderRadius = '4px';
    button.style.transition = 'all 0.15s ease';
    button.innerHTML = this.getClearIcon();
    button.title = '清除搜索';
    
    return button;
  }

  /**
   * 创建搜索图标
   */
  private createSearchIcon(): HTMLElement {
    const icon = document.createElement('div');
    icon.className = 'search-icon';
    icon.style.padding = '0 8px';
    icon.style.color = '#656d76';
    icon.style.display = 'flex';
    icon.style.alignItems = 'center';
    icon.innerHTML = this.getSearchIcon();
    
    return icon;
  }

  /**
   * 创建历史记录下拉框
   */
  private createHistoryDropdown(): HTMLElement {
    const dropdown = document.createElement('div');
    dropdown.className = 'search-history-dropdown';
    dropdown.style.position = 'absolute';
    dropdown.style.top = '100%';
    dropdown.style.left = '0';
    dropdown.style.right = '0';
    dropdown.style.backgroundColor = '#ffffff';
    dropdown.style.border = '1px solid #d0d7de';
    dropdown.style.borderTop = 'none';
    dropdown.style.borderRadius = '0 0 8px 8px';
    dropdown.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
    dropdown.style.maxHeight = '200px';
    dropdown.style.overflowY = 'auto';
    dropdown.style.zIndex = '1000';
    dropdown.style.display = 'none';
    
    return dropdown;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 输入框事件
    this.input.addEventListener('input', (event) => {
      this.handleInput(event);
    });

    this.input.addEventListener('keydown', (event) => {
      this.handleKeydown(event);
    });

    this.input.addEventListener('focus', () => {
      this.handleFocus();
    });

    this.input.addEventListener('blur', (event) => {
      // 延迟处理失焦，允许点击历史记录项
      setTimeout(() => this.handleBlur(event), 150);
    });

    // 清除按钮事件
    this.clearButton.addEventListener('click', () => {
      this.clear();
    });

    this.clearButton.addEventListener('mouseenter', () => {
      this.clearButton.style.backgroundColor = '#f3f4f6';
    });

    this.clearButton.addEventListener('mouseleave', () => {
      this.clearButton.style.backgroundColor = 'transparent';
    });

    // 容器聚焦样式
    this.element.addEventListener('focusin', () => {
      this.element.style.borderColor = '#0969da';
      this.element.style.boxShadow = '0 0 0 3px rgba(9, 105, 218, 0.1)';
    });

    this.element.addEventListener('focusout', () => {
      this.element.style.borderColor = '#d0d7de';
      this.element.style.boxShadow = 'none';
    });
  }

  /**
   * 处理输入事件
   */
  private handleInput(event: Event): void {
    const query = (event.target as HTMLInputElement).value;
    this.currentQuery = query;
    this.updateClearButtonVisibility();
    
    // 防抖搜索
    this.clearDebounceTimer();
    this.debounceTimer = window.setTimeout(() => {
      this.notifySearch(query);
      
      // 添加到历史记录（如果查询有效）
      if (query.trim().length >= 2) {
        this.addToHistory(query);
      }
    }, this.options.debounceDelay);
  }

  /**
   * 处理键盘事件
   */
  private handleKeydown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        if (this.hasQuery()) {
          this.clear();
        } else {
          this.blur();
        }
        break;
      case 'Enter':
        event.preventDefault();
        const query = this.getValue().trim();
        if (query) {
          this.addToHistory(query);
          this.hideHistory();
        }
        break;
      case 'ArrowDown':
        if (this.isHistoryVisible) {
          event.preventDefault();
          this.focusFirstHistoryItem();
        } else if (this.searchHistory.length > 0) {
          event.preventDefault();
          this.showHistory();
        }
        break;
      case 'ArrowUp':
        if (this.isHistoryVisible) {
          event.preventDefault();
          this.focusLastHistoryItem();
        }
        break;
    }
  }

  /**
   * 处理聚焦事件
   */
  private handleFocus(): void {
    this.notifyFocus();
    
    // 如果有历史记录且输入框为空，显示历史记录
    if (this.searchHistory.length > 0 && !this.hasQuery()) {
      this.showHistory();
    }
  }

  /**
   * 处理失焦事件
   */
  private handleBlur(event: FocusEvent): void {
    // 检查焦点是否移动到历史记录下拉框
    if (!this.element.contains(event.relatedTarget as Node)) {
      this.hideHistory();
      this.notifyBlur();
    }
  }

  /**
   * 更新清除按钮可见性
   */
  private updateClearButtonVisibility(): void {
    if (this.options.showClearButton) {
      this.clearButton.style.display = this.hasQuery() ? 'block' : 'none';
    }
  }

  /**
   * 更新历史记录下拉框
   */
  private updateHistoryDropdown(): void {
    this.historyDropdown.innerHTML = '';
    
    if (this.searchHistory.length === 0) {
      const emptyItem = document.createElement('div');
      emptyItem.className = 'search-history-empty';
      emptyItem.style.padding = '12px 16px';
      emptyItem.style.color = '#656d76';
      emptyItem.style.fontSize = '14px';
      emptyItem.style.textAlign = 'center';
      emptyItem.textContent = '暂无搜索历史';
      this.historyDropdown.appendChild(emptyItem);
      return;
    }

    // 添加历史记录项
    this.searchHistory.forEach((query, index) => {
      const item = this.createHistoryItem(query, index);
      this.historyDropdown.appendChild(item);
    });

    // 添加清除历史记录按钮
    const clearAllItem = document.createElement('div');
    clearAllItem.className = 'search-history-clear-all';
    clearAllItem.style.padding = '8px 16px';
    clearAllItem.style.borderTop = '1px solid #e1e4e8';
    clearAllItem.style.color = '#d1242f';
    clearAllItem.style.fontSize = '14px';
    clearAllItem.style.cursor = 'pointer';
    clearAllItem.style.textAlign = 'center';
    clearAllItem.style.transition = 'background-color 0.15s ease';
    clearAllItem.textContent = '清除搜索历史';
    
    clearAllItem.addEventListener('click', () => {
      this.clearHistory();
    });
    
    clearAllItem.addEventListener('mouseenter', () => {
      clearAllItem.style.backgroundColor = '#fff1f1';
    });
    
    clearAllItem.addEventListener('mouseleave', () => {
      clearAllItem.style.backgroundColor = 'transparent';
    });
    
    this.historyDropdown.appendChild(clearAllItem);
  }

  /**
   * 创建历史记录项
   */
  private createHistoryItem(query: string, index: number): HTMLElement {
    const item = document.createElement('div');
    item.className = 'search-history-item';
    item.style.display = 'flex';
    item.style.alignItems = 'center';
    item.style.padding = '8px 16px';
    item.style.cursor = 'pointer';
    item.style.fontSize = '14px';
    item.style.color = '#24292f';
    item.style.transition = 'background-color 0.15s ease';
    item.setAttribute('tabindex', '0');
    item.setAttribute('data-index', index.toString());

    // 历史图标
    const icon = document.createElement('span');
    icon.style.marginRight = '8px';
    icon.style.color = '#656d76';
    icon.innerHTML = this.getHistoryIcon();

    // 查询文本
    const text = document.createElement('span');
    text.style.flex = '1';
    text.textContent = query;

    // 删除按钮
    const deleteButton = document.createElement('button');
    deleteButton.style.border = 'none';
    deleteButton.style.background = 'none';
    deleteButton.style.padding = '4px';
    deleteButton.style.cursor = 'pointer';
    deleteButton.style.color = '#656d76';
    deleteButton.style.borderRadius = '4px';
    deleteButton.style.transition = 'all 0.15s ease';
    deleteButton.innerHTML = this.getDeleteIcon();
    deleteButton.title = '删除此搜索记录';

    item.appendChild(icon);
    item.appendChild(text);
    item.appendChild(deleteButton);

    // 事件监听
    item.addEventListener('click', (event) => {
      if (event.target === deleteButton) return;
      this.setValue(query);
      this.notifySearch(query);
      this.hideHistory();
      this.input.focus();
    });

    item.addEventListener('mouseenter', () => {
      item.style.backgroundColor = '#f6f8fa';
    });

    item.addEventListener('mouseleave', () => {
      item.style.backgroundColor = 'transparent';
    });

    item.addEventListener('keydown', (event) => {
      this.handleHistoryItemKeydown(event, query, index);
    });

    deleteButton.addEventListener('click', (event) => {
      event.stopPropagation();
      this.removeFromHistory(index);
    });

    deleteButton.addEventListener('mouseenter', () => {
      deleteButton.style.backgroundColor = '#f3f4f6';
    });

    deleteButton.addEventListener('mouseleave', () => {
      deleteButton.style.backgroundColor = 'transparent';
    });

    return item;
  }

  /**
   * 处理历史记录项键盘事件
   */
  private handleHistoryItemKeydown(event: KeyboardEvent, query: string, index: number): void {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        this.setValue(query);
        this.notifySearch(query);
        this.hideHistory();
        this.input.focus();
        break;
      case 'Delete':
      case 'Backspace':
        event.preventDefault();
        this.removeFromHistory(index);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.focusPreviousHistoryItem(index);
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.focusNextHistoryItem(index);
        break;
      case 'Escape':
        event.preventDefault();
        this.hideHistory();
        this.input.focus();
        break;
    }
  }

  /**
   * 从历史记录中移除项目
   */
  private removeFromHistory(index: number): void {
    this.searchHistory.splice(index, 1);
    this.saveSearchHistory();
    this.updateHistoryDropdown();
    
    if (this.searchHistory.length === 0) {
      this.hideHistory();
    }
  }

  /**
   * 聚焦第一个历史记录项
   */
  private focusFirstHistoryItem(): void {
    const firstItem = this.historyDropdown.querySelector('.search-history-item') as HTMLElement;
    if (firstItem) {
      firstItem.focus();
    }
  }

  /**
   * 聚焦最后一个历史记录项
   */
  private focusLastHistoryItem(): void {
    const items = this.historyDropdown.querySelectorAll('.search-history-item');
    const lastItem = items[items.length - 1] as HTMLElement;
    if (lastItem) {
      lastItem.focus();
    }
  }

  /**
   * 聚焦上一个历史记录项
   */
  private focusPreviousHistoryItem(currentIndex: number): void {
    if (currentIndex > 0) {
      const prevItem = this.historyDropdown.querySelector(`[data-index="${currentIndex - 1}"]`) as HTMLElement;
      if (prevItem) {
        prevItem.focus();
      }
    } else {
      this.input.focus();
    }
  }

  /**
   * 聚焦下一个历史记录项
   */
  private focusNextHistoryItem(currentIndex: number): void {
    const nextItem = this.historyDropdown.querySelector(`[data-index="${currentIndex + 1}"]`) as HTMLElement;
    if (nextItem) {
      nextItem.focus();
    }
  }

  /**
   * 清除防抖定时器
   */
  private clearDebounceTimer(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  /**
   * 加载搜索历史
   */
  private loadSearchHistory(): void {
    try {
      const stored = localStorage.getItem('sidebar-search-history');
      if (stored) {
        this.searchHistory = JSON.parse(stored);
        this.updateHistoryDropdown();
      }
    } catch (error) {
      console.warn('Failed to load search history:', error);
      this.searchHistory = [];
    }
  }

  /**
   * 保存搜索历史
   */
  private saveSearchHistory(): void {
    try {
      localStorage.setItem('sidebar-search-history', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.warn('Failed to save search history:', error);
    }
  }

  /**
   * 通知搜索事件
   */
  private notifySearch(query: string): void {
    this.searchCallbacks.forEach(callback => {
      try {
        callback(query);
      } catch (error) {
        console.error('Error in search callback:', error);
      }
    });
  }

  /**
   * 通知清除事件
   */
  private notifyClear(): void {
    this.clearCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in clear callback:', error);
      }
    });
  }

  /**
   * 通知聚焦事件
   */
  private notifyFocus(): void {
    this.focusCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in focus callback:', error);
      }
    });
  }

  /**
   * 通知失焦事件
   */
  private notifyBlur(): void {
    this.blurCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in blur callback:', error);
      }
    });
  }

  /**
   * 获取搜索图标
   */
  private getSearchIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
      </svg>
    `;
  }

  /**
   * 获取清除图标
   */
  private getClearIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06z"/>
      </svg>
    `;
  }

  /**
   * 获取历史图标
   */
  private getHistoryIcon(): string {
    return `
      <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
        <path d="M1.643 3.143L.427 1.927A.25.25 0 0 0 0 2.104V5.75c0 .138.112.25.25.25h3.646a.25.25 0 0 0 .177-.427L2.715 4.215a6.5 6.5 0 1 1-1.18 4.458.75.75 0 1 0-1.493.154 8.001 8.001 0 1 0 1.6-5.684zM7.75 4a.75.75 0 0 1 .75.75v2.992l2.028.812a.75.75 0 0 1-.557 1.392l-2.5-1A.75.75 0 0 1 7 8.25v-3.5A.75.75 0 0 1 7.75 4z"/>
      </svg>
    `;
  }

  /**
   * 获取删除图标
   */
  private getDeleteIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
        <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06z"/>
      </svg>
    `;
  }
}