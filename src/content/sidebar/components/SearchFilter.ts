/**
 * Search Filter - 搜索过滤器组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';

export interface FilterOption {
  id: string;
  label: string;
  icon?: string;
  count?: number;
  active?: boolean;
}

export interface SearchFilterOptions {
  showCounts?: boolean;
  allowMultiple?: boolean;
  showClearAll?: boolean;
}

export class SearchFilter implements UIComponent {
  element: HTMLElement;
  private options: SearchFilterOptions;
  private filters: Map<string, FilterOption> = new Map();
  private activeFilters: Set<string> = new Set();
  private filterCallbacks: ((activeFilters: string[]) => void)[] = [];
  private clearCallbacks: (() => void)[] = [];

  constructor(options: SearchFilterOptions = {}) {
    this.options = {
      showCounts: true,
      allowMultiple: true,
      showClearAll: true,
      ...options
    };

    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    if (this.filters.size === 0) {
      this.renderEmptyState();
      return;
    }

    // 创建过滤器容器
    const filtersContainer = this.createFiltersContainer();
    this.element.appendChild(filtersContainer);

    // 渲染过滤器选项
    this.filters.forEach((filter, id) => {
      const filterElement = this.createFilterElement(filter);
      filtersContainer.appendChild(filterElement);
    });

    // 添加清除所有按钮
    if (this.options.showClearAll && this.activeFilters.size > 0) {
      const clearAllButton = this.createClearAllButton();
      filtersContainer.appendChild(clearAllButton);
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.filterCallbacks = [];
    this.clearCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.filters) {
      this.setFilters(data.filters);
    }
  }

  /**
   * 设置过滤器选项
   */
  setFilters(filters: FilterOption[]): void {
    this.filters.clear();
    
    filters.forEach(filter => {
      this.filters.set(filter.id, { ...filter });
      if (filter.active) {
        this.activeFilters.add(filter.id);
      }
    });

    this.render();
  }

  /**
   * 添加过滤器选项
   */
  addFilter(filter: FilterOption): void {
    this.filters.set(filter.id, { ...filter });
    if (filter.active) {
      this.activeFilters.add(filter.id);
    }
    this.render();
  }

  /**
   * 移除过滤器选项
   */
  removeFilter(filterId: string): void {
    this.filters.delete(filterId);
    this.activeFilters.delete(filterId);
    this.render();
  }

  /**
   * 更新过滤器计数
   */
  updateFilterCount(filterId: string, count: number): void {
    const filter = this.filters.get(filterId);
    if (filter) {
      filter.count = count;
      this.render();
    }
  }

  /**
   * 激活过滤器
   */
  activateFilter(filterId: string): void {
    if (!this.filters.has(filterId)) return;

    if (!this.options.allowMultiple) {
      this.activeFilters.clear();
    }

    this.activeFilters.add(filterId);
    this.render();
    this.notifyFilterChange();
  }

  /**
   * 取消激活过滤器
   */
  deactivateFilter(filterId: string): void {
    this.activeFilters.delete(filterId);
    this.render();
    this.notifyFilterChange();
  }

  /**
   * 切换过滤器状态
   */
  toggleFilter(filterId: string): void {
    if (this.activeFilters.has(filterId)) {
      this.deactivateFilter(filterId);
    } else {
      this.activateFilter(filterId);
    }
  }

  /**
   * 清除所有过滤器
   */
  clearAll(): void {
    this.activeFilters.clear();
    this.render();
    this.notifyFilterChange();
    this.notifyClear();
  }

  /**
   * 获取激活的过滤器
   */
  getActiveFilters(): string[] {
    return Array.from(this.activeFilters);
  }

  /**
   * 检查过滤器是否激活
   */
  isFilterActive(filterId: string): boolean {
    return this.activeFilters.has(filterId);
  }

  /**
   * 获取所有过滤器
   */
  getAllFilters(): FilterOption[] {
    return Array.from(this.filters.values());
  }

  /**
   * 添加过滤器变更回调
   */
  onFilterChange(callback: (activeFilters: string[]) => void): void {
    this.filterCallbacks.push(callback);
  }

  /**
   * 添加清除回调
   */
  onClear(callback: () => void): void {
    this.clearCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-filter';
    element.style.padding = '8px';
    element.style.borderBottom = '1px solid #e1e4e8';
    return element;
  }

  /**
   * 创建过滤器容器
   */
  private createFiltersContainer(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'search-filter-container';
    container.style.display = 'flex';
    container.style.flexWrap = 'wrap';
    container.style.gap = '6px';
    container.style.alignItems = 'center';
    return container;
  }

  /**
   * 创建过滤器元素
   */
  private createFilterElement(filter: FilterOption): HTMLElement {
    const element = document.createElement('button');
    element.className = 'search-filter-item';
    element.setAttribute('data-filter-id', filter.id);
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.padding = '4px 8px';
    element.style.border = '1px solid #d0d7de';
    element.style.borderRadius = '16px';
    element.style.backgroundColor = this.activeFilters.has(filter.id) ? '#0969da' : '#ffffff';
    element.style.color = this.activeFilters.has(filter.id) ? '#ffffff' : '#24292f';
    element.style.fontSize = '12px';
    element.style.fontWeight = '500';
    element.style.cursor = 'pointer';
    element.style.transition = 'all 0.15s ease';
    element.style.whiteSpace = 'nowrap';

    // 图标
    if (filter.icon) {
      const icon = document.createElement('span');
      icon.className = 'filter-icon';
      icon.style.marginRight = '4px';
      icon.style.fontSize = '14px';
      icon.innerHTML = filter.icon;
      element.appendChild(icon);
    }

    // 标签
    const label = document.createElement('span');
    label.className = 'filter-label';
    label.textContent = filter.label;
    element.appendChild(label);

    // 计数
    if (this.options.showCounts && typeof filter.count === 'number') {
      const count = document.createElement('span');
      count.className = 'filter-count';
      count.style.marginLeft = '4px';
      count.style.opacity = '0.8';
      count.textContent = `(${filter.count})`;
      element.appendChild(count);
    }

    // 事件监听
    element.addEventListener('click', () => {
      this.toggleFilter(filter.id);
    });

    element.addEventListener('mouseenter', () => {
      if (!this.activeFilters.has(filter.id)) {
        element.style.backgroundColor = '#f6f8fa';
        element.style.borderColor = '#bbb';
      }
    });

    element.addEventListener('mouseleave', () => {
      if (!this.activeFilters.has(filter.id)) {
        element.style.backgroundColor = '#ffffff';
        element.style.borderColor = '#d0d7de';
      }
    });

    return element;
  }

  /**
   * 创建清除所有按钮
   */
  private createClearAllButton(): HTMLElement {
    const button = document.createElement('button');
    button.className = 'search-filter-clear-all';
    button.style.padding = '4px 8px';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.backgroundColor = 'transparent';
    button.style.color = '#656d76';
    button.style.fontSize = '12px';
    button.style.cursor = 'pointer';
    button.style.transition = 'all 0.15s ease';
    button.textContent = '清除所有';

    button.addEventListener('click', () => {
      this.clearAll();
    });

    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = '#f3f4f6';
    });

    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'transparent';
    });

    return button;
  }

  /**
   * 渲染空状态
   */
  private renderEmptyState(): void {
    const emptyState = document.createElement('div');
    emptyState.className = 'search-filter-empty';
    emptyState.style.textAlign = 'center';
    emptyState.style.padding = '16px';
    emptyState.style.color = '#656d76';
    emptyState.style.fontSize = '12px';
    emptyState.textContent = '暂无过滤选项';
    this.element.appendChild(emptyState);
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 键盘支持
    this.element.addEventListener('keydown', (event) => {
      this.handleKeydown(event);
    });
  }

  /**
   * 处理键盘事件
   */
  private handleKeydown(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    if (!target.classList.contains('search-filter-item')) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        const filterId = target.getAttribute('data-filter-id');
        if (filterId) {
          this.toggleFilter(filterId);
        }
        break;
      case 'ArrowLeft':
        event.preventDefault();
        this.focusPreviousFilter(target);
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.focusNextFilter(target);
        break;
    }
  }

  /**
   * 聚焦上一个过滤器
   */
  private focusPreviousFilter(currentElement: HTMLElement): void {
    const filters = Array.from(this.element.querySelectorAll('.search-filter-item'));
    const currentIndex = filters.indexOf(currentElement);
    if (currentIndex > 0) {
      (filters[currentIndex - 1] as HTMLElement).focus();
    }
  }

  /**
   * 聚焦下一个过滤器
   */
  private focusNextFilter(currentElement: HTMLElement): void {
    const filters = Array.from(this.element.querySelectorAll('.search-filter-item'));
    const currentIndex = filters.indexOf(currentElement);
    if (currentIndex < filters.length - 1) {
      (filters[currentIndex + 1] as HTMLElement).focus();
    }
  }

  /**
   * 通知过滤器变更
   */
  private notifyFilterChange(): void {
    const activeFilters = this.getActiveFilters();
    this.filterCallbacks.forEach(callback => {
      try {
        callback(activeFilters);
      } catch (error) {
        console.error('Error in filter change callback:', error);
      }
    });
  }

  /**
   * 通知清除事件
   */
  private notifyClear(): void {
    this.clearCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in clear callback:', error);
      }
    });
  }

  /**
   * 预设过滤器选项
   */
  static createDefaultFilters(): FilterOption[] {
    return [
      {
        id: 'bookmarks',
        label: '收藏夹',
        icon: '🔖',
        count: 0
      },
      {
        id: 'folders',
        label: '文件夹',
        icon: '📁',
        count: 0
      },
      {
        id: 'tabs',
        label: '标签页',
        icon: '🗂️',
        count: 0
      },
      {
        id: 'recent',
        label: '最近访问',
        icon: '🕒',
        count: 0
      }
    ];
  }
}