/**
 * Search Manager - 搜索管理器
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { SearchBox } from './SearchBox.js';
import { SearchFilter } from './SearchFilter.js';
import { SearchResults } from './SearchResults.js';
import { SearchEngine, SearchResult } from '../../utils/SearchEngine.js';
import { BookmarkNode } from '../../types/index.js';
import { TabInfo } from '../../types/index.js';

export interface SearchManagerOptions {
  showFilters?: boolean;
  showStats?: boolean;
  groupResults?: boolean;
  maxResults?: number;
  debounceDelay?: number;
}

export class SearchManager implements UIComponent {
  element: HTMLElement;
  private options: SearchManagerOptions;
  private searchBox: SearchBox;
  private searchFilter: SearchFilter;
  private searchResults: SearchResults;
  private searchEngine: SearchEngine;
  
  private bookmarks: BookmarkNode[] = [];
  private tabs: TabInfo[] = [];
  private currentQuery = '';
  private activeFilters: string[] = [];
  private allResults: SearchResult<BookmarkNode | TabInfo>[] = [];
  
  private searchCallbacks: ((query: string, results: SearchResult<BookmarkNode | TabInfo>[]) => void)[] = [];
  private itemSelectCallbacks: ((item: BookmarkNode | TabInfo, type: string) => void)[] = [];
  private emptyResultsCallbacks: ((query: string) => void)[] = [];

  constructor(options: SearchManagerOptions = {}) {
    this.options = {
      showFilters: true,
      showStats: true,
      groupResults: true,
      maxResults: 50,
      debounceDelay: 300,
      ...options
    };

    this.searchEngine = new SearchEngine({
      maxResults: this.options.maxResults,
      threshold: 0.1
    });

    this.element = this.createElement();
    this.searchBox = this.createSearchBox();
    this.searchFilter = this.createSearchFilter();
    this.searchResults = this.createSearchResults();
    
    this.render();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    
    // 添加搜索框
    this.element.appendChild(this.searchBox.element);
    
    // 添加过滤器（如果启用）
    if (this.options.showFilters) {
      this.element.appendChild(this.searchFilter.element);
    }
    
    // 添加搜索结果
    this.element.appendChild(this.searchResults.element);
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.searchBox.destroy();
    this.searchFilter.destroy();
    this.searchResults.destroy();
    
    this.searchCallbacks = [];
    this.itemSelectCallbacks = [];
    this.emptyResultsCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data) {
      if (data.bookmarks) {
        this.setBookmarks(data.bookmarks);
      }
      if (data.tabs) {
        this.setTabs(data.tabs);
      }
    }
  }

  /**
   * 设置收藏夹数据
   */
  setBookmarks(bookmarks: BookmarkNode[]): void {
    this.bookmarks = bookmarks;
    this.updateFilterCounts();
    
    // 如果有当前查询，重新搜索
    if (this.currentQuery) {
      this.performSearch(this.currentQuery);
    }
  }

  /**
   * 设置标签页数据
   */
  setTabs(tabs: TabInfo[]): void {
    this.tabs = tabs;
    this.updateFilterCounts();
    
    // 如果有当前查询，重新搜索
    if (this.currentQuery) {
      this.performSearch(this.currentQuery);
    }
  }

  /**
   * 执行搜索
   */
  search(query: string): void {
    this.currentQuery = query.trim();
    this.searchBox.setValue(this.currentQuery);
    this.performSearch(this.currentQuery);
  }

  /**
   * 清除搜索
   */
  clear(): void {
    this.currentQuery = '';
    this.allResults = [];
    this.searchBox.clear();
    this.searchResults.clear();
    this.updateFilterCounts();
  }

  /**
   * 聚焦搜索框
   */
  focus(): void {
    this.searchBox.focus();
  }

  /**
   * 获取当前搜索查询
   */
  getCurrentQuery(): string {
    return this.currentQuery;
  }

  /**
   * 获取当前搜索结果
   */
  getCurrentResults(): SearchResult<BookmarkNode | TabInfo>[] {
    return [...this.allResults];
  }

  /**
   * 获取搜索统计
   */
  getSearchStats(): any {
    return this.searchEngine.getSearchStats(this.allResults);
  }

  /**
   * 添加搜索回调
   */
  onSearch(callback: (query: string, results: SearchResult<BookmarkNode | TabInfo>[]) => void): void {
    this.searchCallbacks.push(callback);
  }

  /**
   * 添加项目选择回调
   */
  onItemSelect(callback: (item: BookmarkNode | TabInfo, type: string) => void): void {
    this.itemSelectCallbacks.push(callback);
  }

  /**
   * 添加空结果回调
   */
  onEmptyResults(callback: (query: string) => void): void {
    this.emptyResultsCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-manager';
    element.style.display = 'flex';
    element.style.flexDirection = 'column';
    element.style.height = '100%';
    return element;
  }

  /**
   * 创建搜索框
   */
  private createSearchBox(): SearchBox {
    const searchBox = new SearchBox({
      placeholder: '搜索收藏夹和标签页...',
      debounceDelay: this.options.debounceDelay,
      showClearButton: true,
      showSearchHistory: true
    });

    return searchBox;
  }

  /**
   * 创建搜索过滤器
   */
  private createSearchFilter(): SearchFilter {
    const searchFilter = new SearchFilter({
      showCounts: true,
      allowMultiple: true,
      showClearAll: true
    });

    // 设置默认过滤器
    searchFilter.setFilters([
      {
        id: 'bookmarks',
        label: '收藏夹',
        icon: '🔖',
        count: 0
      },
      {
        id: 'folders',
        label: '文件夹',
        icon: '📁',
        count: 0
      },
      {
        id: 'tabs',
        label: '标签页',
        icon: '🗂️',
        count: 0
      }
    ]);

    return searchFilter;
  }

  /**
   * 创建搜索结果
   */
  private createSearchResults(): SearchResults {
    const searchResults = new SearchResults({
      maxResults: this.options.maxResults,
      showStats: this.options.showStats,
      groupByType: this.options.groupResults,
      showEmptyState: true
    });

    return searchResults;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 搜索框事件
    this.searchBox.onSearch((query) => {
      this.performSearch(query);
    });

    this.searchBox.onClear(() => {
      this.clear();
    });

    this.searchBox.onFocus(() => {
      // 搜索框获得焦点时的处理
      this.element.classList.add('search-focused');
    });

    this.searchBox.onBlur(() => {
      // 搜索框失去焦点时的处理
      this.element.classList.remove('search-focused');
    });

    // 过滤器事件
    this.searchFilter.onFilterChange((activeFilters) => {
      this.activeFilters = activeFilters;
      this.applyFilters();
    });

    this.searchFilter.onClear(() => {
      this.activeFilters = [];
      this.applyFilters();
    });

    // 搜索结果事件
    this.searchResults.onItemClick((item, type) => {
      this.handleItemSelect(item, type);
    });

    this.searchResults.onItemContextMenu((event, item, type) => {
      this.handleItemContextMenu(event, item, type);
    });

    // 键盘快捷键
    this.element.addEventListener('keydown', (event) => {
      this.handleKeyboardShortcuts(event);
    });
  }

  /**
   * 执行搜索
   */
  private performSearch(query: string): void {
    this.currentQuery = query;

    if (!query.trim()) {
      this.allResults = [];
      this.searchResults.clear();
      this.updateFilterCounts();
      return;
    }

    // 搜索收藏夹
    const bookmarkResults = this.searchEngine.searchBookmarks(this.bookmarks, query);
    
    // 搜索标签页
    const tabResults = this.searchEngine.searchTabs(this.tabs, query);
    
    // 合并结果并按分数排序
    this.allResults = [...bookmarkResults, ...tabResults]
      .sort((a, b) => b.score - a.score)
      .slice(0, this.options.maxResults!);

    // 应用过滤器
    this.applyFilters();
    
    // 更新过滤器计数
    this.updateFilterCounts();
    
    // 通知搜索完成
    this.notifySearch(query, this.allResults);
    
    // 检查是否为空结果
    if (this.allResults.length === 0) {
      this.notifyEmptyResults(query);
    }
  }

  /**
   * 应用过滤器
   */
  private applyFilters(): void {
    let filteredResults = this.allResults;

    if (this.activeFilters.length > 0) {
      filteredResults = this.allResults.filter(result => {
        // 映射结果类型到过滤器ID
        const typeMapping: Record<string, string> = {
          'bookmark': 'bookmarks',
          'folder': 'folders',
          'tab': 'tabs'
        };

        const filterId = typeMapping[result.type];
        return filterId && this.activeFilters.includes(filterId);
      });
    }

    this.searchResults.setResults(filteredResults, this.currentQuery);
  }

  /**
   * 更新过滤器计数
   */
  private updateFilterCounts(): void {
    const stats = this.searchEngine.getSearchStats(this.allResults);
    
    this.searchFilter.updateFilterCount('bookmarks', stats.bookmarks);
    this.searchFilter.updateFilterCount('folders', stats.folders);
    this.searchFilter.updateFilterCount('tabs', stats.tabs);
  }

  /**
   * 处理项目选择
   */
  private handleItemSelect(item: BookmarkNode | TabInfo, type: string): void {
    // 添加到搜索历史
    if (this.currentQuery) {
      this.searchBox.addToHistory(this.currentQuery);
    }

    // 执行相应的操作
    if (type === 'bookmark' || type === 'folder') {
      const bookmark = item as BookmarkNode;
      if (bookmark.url) {
        // 打开收藏夹链接
        chrome.runtime.sendMessage({
          type: 'OPEN_TAB',
          payload: { url: bookmark.url },
          timestamp: Date.now()
        }).catch(error => {
          console.error('Error opening bookmark:', error);
        });
      }
    } else if (type === 'tab') {
      const tab = item as TabInfo;
      // 切换到标签页
      chrome.runtime.sendMessage({
        type: 'SWITCH_TAB',
        payload: { tabId: tab.id },
        timestamp: Date.now()
      }).catch(error => {
        console.error('Error switching tab:', error);
      });
    }

    // 通知项目选择
    this.notifyItemSelect(item, type);
  }

  /**
   * 处理项目右键菜单
   */
  private handleItemContextMenu(event: MouseEvent, item: BookmarkNode | TabInfo, type: string): void {
    // 这里可以显示上下文菜单
    // 暂时使用简单的处理方式
    console.log('Context menu for:', item, type);
  }

  /**
   * 处理键盘快捷键
   */
  private handleKeyboardShortcuts(event: KeyboardEvent): void {
    // Ctrl/Cmd + F: 聚焦搜索框
    if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
      event.preventDefault();
      this.focus();
    }

    // Escape: 清除搜索或失焦
    if (event.key === 'Escape') {
      if (this.currentQuery) {
        this.clear();
      } else {
        this.searchBox.blur();
      }
    }

    // Ctrl/Cmd + K: 快速搜索
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault();
      this.focus();
    }
  }

  /**
   * 通知搜索事件
   */
  private notifySearch(query: string, results: SearchResult<BookmarkNode | TabInfo>[]): void {
    this.searchCallbacks.forEach(callback => {
      try {
        callback(query, results);
      } catch (error) {
        console.error('Error in search callback:', error);
      }
    });
  }

  /**
   * 通知项目选择事件
   */
  private notifyItemSelect(item: BookmarkNode | TabInfo, type: string): void {
    this.itemSelectCallbacks.forEach(callback => {
      try {
        callback(item, type);
      } catch (error) {
        console.error('Error in item select callback:', error);
      }
    });
  }

  /**
   * 通知空结果事件
   */
  private notifyEmptyResults(query: string): void {
    this.emptyResultsCallbacks.forEach(callback => {
      try {
        callback(query);
      } catch (error) {
        console.error('Error in empty results callback:', error);
      }
    });
  }

  /**
   * 获取搜索建议
   */
  getSuggestions(query: string): string[] {
    const allItems = [...this.bookmarks, ...this.tabs];
    return this.searchEngine.createSuggestions(query, allItems);
  }

  /**
   * 导出搜索结果
   */
  exportResults(format: 'json' | 'csv' = 'json'): string {
    const results = this.allResults.map(result => ({
      title: result.item.title,
      url: (result.item as any).url || '',
      type: result.type,
      score: result.score
    }));

    if (format === 'csv') {
      const headers = ['Title', 'URL', 'Type', 'Score'];
      const csvRows = [
        headers.join(','),
        ...results.map(result => [
          `"${result.title.replace(/"/g, '""')}"`,
          `"${result.url.replace(/"/g, '""')}"`,
          result.type,
          result.score.toFixed(3)
        ].join(','))
      ];
      return csvRows.join('\n');
    }

    return JSON.stringify(results, null, 2);
  }

  /**
   * 获取搜索性能指标
   */
  getPerformanceMetrics(): {
    totalItems: number;
    searchTime: number;
    resultsCount: number;
    averageScore: number;
  } {
    const stats = this.getSearchStats();
    return {
      totalItems: this.bookmarks.length + this.tabs.length,
      searchTime: 0, // 需要在实际搜索时测量
      resultsCount: this.allResults.length,
      averageScore: stats.avgScore
    };
  }
}