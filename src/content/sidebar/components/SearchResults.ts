/**
 * Search Results - 搜索结果显示组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { SearchResult, SearchEngine } from '../../utils/SearchEngine.js';
import { BookmarkNode } from '../../types/index.js';
import { TabInfo } from '../../types/index.js';

export interface SearchResultsOptions {
  maxResults?: number;
  showStats?: boolean;
  groupByType?: boolean;
  showEmptyState?: boolean;
}

export class SearchResults implements UIComponent {
  element: HTMLElement;
  private options: SearchResultsOptions;
  private results: SearchResult<BookmarkNode | TabInfo>[] = [];
  private searchEngine: SearchEngine;
  private currentQuery = '';
  private itemClickCallbacks: ((item: BookmarkNode | TabInfo, type: string) => void)[] = [];
  private itemContextMenuCallbacks: ((event: MouseEvent, item: BookmarkNode | TabInfo, type: string) => void)[] = [];

  constructor(options: SearchResultsOptions = {}) {
    this.options = {
      maxResults: 50,
      showStats: true,
      groupByType: true,
      showEmptyState: true,
      ...options
    };

    this.searchEngine = new SearchEngine();
    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    if (this.results.length === 0) {
      if (this.options.showEmptyState) {
        this.renderEmptyState();
      }
      return;
    }

    // 显示搜索统计
    if (this.options.showStats) {
      const statsElement = this.createStatsElement();
      this.element.appendChild(statsElement);
    }

    // 按类型分组显示结果
    if (this.options.groupByType) {
      this.renderGroupedResults();
    } else {
      this.renderFlatResults();
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.itemClickCallbacks = [];
    this.itemContextMenuCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.results) {
      this.setResults(data.results, data.query || '');
    }
  }

  /**
   * 设置搜索结果
   */
  setResults(results: SearchResult<BookmarkNode | TabInfo>[], query = ''): void {
    this.results = results.slice(0, this.options.maxResults);
    this.currentQuery = query;
    this.render();
  }

  /**
   * 清除搜索结果
   */
  clear(): void {
    this.results = [];
    this.currentQuery = '';
    this.render();
  }

  /**
   * 获取当前结果
   */
  getResults(): SearchResult<BookmarkNode | TabInfo>[] {
    return [...this.results];
  }

  /**
   * 获取结果统计
   */
  getStats(): any {
    return this.searchEngine.getSearchStats(this.results);
  }

  /**
   * 添加项目点击回调
   */
  onItemClick(callback: (item: BookmarkNode | TabInfo, type: string) => void): void {
    this.itemClickCallbacks.push(callback);
  }

  /**
   * 添加项目右键菜单回调
   */
  onItemContextMenu(callback: (event: MouseEvent, item: BookmarkNode | TabInfo, type: string) => void): void {
    this.itemContextMenuCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-results';
    element.style.flex = '1';
    element.style.overflowY = 'auto';
    element.setAttribute('role', 'listbox');
    element.setAttribute('aria-label', '搜索结果');
    return element;
  }

  /**
   * 渲染空状态
   */
  private renderEmptyState(): void {
    const emptyState = document.createElement('div');
    emptyState.className = 'search-results-empty';
    emptyState.style.display = 'flex';
    emptyState.style.flexDirection = 'column';
    emptyState.style.alignItems = 'center';
    emptyState.style.justifyContent = 'center';
    emptyState.style.padding = '40px 20px';
    emptyState.style.color = '#656d76';
    emptyState.style.textAlign = 'center';

    const icon = document.createElement('div');
    icon.style.fontSize = '48px';
    icon.style.marginBottom = '16px';
    icon.textContent = this.currentQuery ? '🔍' : '💭';

    const title = document.createElement('div');
    title.style.fontSize = '16px';
    title.style.fontWeight = '500';
    title.style.marginBottom = '8px';
    title.textContent = this.currentQuery ? '未找到相关结果' : '开始搜索';

    const description = document.createElement('div');
    description.style.fontSize = '14px';
    description.style.opacity = '0.8';
    description.textContent = this.currentQuery 
      ? `没有找到与"${this.currentQuery}"相关的内容`
      : '输入关键词搜索收藏夹和标签页';

    emptyState.appendChild(icon);
    emptyState.appendChild(title);
    emptyState.appendChild(description);
    this.element.appendChild(emptyState);
  }

  /**
   * 创建统计元素
   */
  private createStatsElement(): HTMLElement {
    const stats = this.getStats();
    const statsElement = document.createElement('div');
    statsElement.className = 'search-results-stats';
    statsElement.style.padding = '8px 16px';
    statsElement.style.borderBottom = '1px solid #e1e4e8';
    statsElement.style.fontSize = '12px';
    statsElement.style.color = '#656d76';
    statsElement.style.backgroundColor = '#f6f8fa';

    const text = `找到 ${stats.total} 个结果`;
    const breakdown = [];
    if (stats.bookmarks > 0) breakdown.push(`${stats.bookmarks} 个收藏夹`);
    if (stats.folders > 0) breakdown.push(`${stats.folders} 个文件夹`);
    if (stats.tabs > 0) breakdown.push(`${stats.tabs} 个标签页`);

    statsElement.textContent = breakdown.length > 0 
      ? `${text}（${breakdown.join('，')}）`
      : text;

    return statsElement;
  }

  /**
   * 渲染分组结果
   */
  private renderGroupedResults(): void {
    const groups = this.groupResultsByType();

    Object.entries(groups).forEach(([type, results]) => {
      if (results.length === 0) return;

      // 创建分组标题
      const groupHeader = this.createGroupHeader(type, results.length);
      this.element.appendChild(groupHeader);

      // 创建分组容器
      const groupContainer = document.createElement('div');
      groupContainer.className = `search-results-group search-results-${type}`;

      // 渲染分组内的结果
      results.forEach((result, index) => {
        const resultElement = this.createResultElement(result, index);
        groupContainer.appendChild(resultElement);
      });

      this.element.appendChild(groupContainer);
    });
  }

  /**
   * 渲染平铺结果
   */
  private renderFlatResults(): void {
    const container = document.createElement('div');
    container.className = 'search-results-flat';

    this.results.forEach((result, index) => {
      const resultElement = this.createResultElement(result, index);
      container.appendChild(resultElement);
    });

    this.element.appendChild(container);
  }

  /**
   * 按类型分组结果
   */
  private groupResultsByType(): Record<string, SearchResult<BookmarkNode | TabInfo>[]> {
    const groups: Record<string, SearchResult<BookmarkNode | TabInfo>[]> = {
      bookmark: [],
      folder: [],
      tab: []
    };

    this.results.forEach(result => {
      if (groups[result.type]) {
        groups[result.type].push(result);
      }
    });

    return groups;
  }

  /**
   * 创建分组标题
   */
  private createGroupHeader(type: string, count: number): HTMLElement {
    const header = document.createElement('div');
    header.className = 'search-results-group-header';
    header.style.padding = '12px 16px 8px';
    header.style.fontSize = '14px';
    header.style.fontWeight = '600';
    header.style.color = '#24292f';
    header.style.borderBottom = '1px solid #e1e4e8';
    header.style.backgroundColor = '#f6f8fa';

    const typeNames: Record<string, string> = {
      bookmark: '收藏夹',
      folder: '文件夹',
      tab: '标签页'
    };

    const icon = this.getTypeIcon(type);
    header.innerHTML = `${icon} ${typeNames[type] || type} (${count})`;

    return header;
  }

  /**
   * 创建结果元素
   */
  private createResultElement(result: SearchResult<BookmarkNode | TabInfo>, index: number): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-result-item';
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.padding = '12px 16px';
    element.style.borderBottom = '1px solid #f0f0f0';
    element.style.cursor = 'pointer';
    element.style.transition = 'background-color 0.15s ease';
    element.setAttribute('role', 'option');
    element.setAttribute('tabindex', '0');
    element.setAttribute('data-index', index.toString());

    // 图标
    const icon = document.createElement('div');
    icon.className = 'search-result-icon';
    icon.style.marginRight = '12px';
    icon.style.flexShrink = '0';
    icon.innerHTML = this.getItemIcon(result.item, result.type);
    element.appendChild(icon);

    // 内容
    const content = document.createElement('div');
    content.className = 'search-result-content';
    content.style.flex = '1';
    content.style.minWidth = '0';

    // 标题
    const title = document.createElement('div');
    title.className = 'search-result-title';
    title.style.fontSize = '14px';
    title.style.fontWeight = '500';
    title.style.color = '#24292f';
    title.style.marginBottom = '4px';
    title.style.overflow = 'hidden';
    title.style.textOverflow = 'ellipsis';
    title.style.whiteSpace = 'nowrap';

    // 高亮标题中的匹配项
    const titleMatch = result.matches.find(match => match.field === 'title');
    if (titleMatch) {
      title.innerHTML = this.searchEngine.highlightMatches(
        result.item.title, 
        [titleMatch], 
        'search-highlight'
      );
    } else {
      title.textContent = result.item.title;
    }

    content.appendChild(title);

    // URL（如果有）
    const url = (result.item as any).url;
    if (url) {
      const urlElement = document.createElement('div');
      urlElement.className = 'search-result-url';
      urlElement.style.fontSize = '12px';
      urlElement.style.color = '#656d76';
      urlElement.style.overflow = 'hidden';
      urlElement.style.textOverflow = 'ellipsis';
      urlElement.style.whiteSpace = 'nowrap';

      // 高亮URL中的匹配项
      const urlMatch = result.matches.find(match => match.field === 'url');
      if (urlMatch) {
        urlElement.innerHTML = this.searchEngine.highlightMatches(
          url, 
          [urlMatch], 
          'search-highlight'
        );
      } else {
        urlElement.textContent = url;
      }

      content.appendChild(urlElement);
    }

    element.appendChild(content);

    // 分数（调试用）
    if (process.env.NODE_ENV === 'development') {
      const score = document.createElement('div');
      score.className = 'search-result-score';
      score.style.fontSize = '10px';
      score.style.color = '#999';
      score.style.marginLeft = '8px';
      score.textContent = result.score.toFixed(2);
      element.appendChild(score);
    }

    // 事件监听
    element.addEventListener('click', () => {
      this.notifyItemClick(result.item, result.type);
    });

    element.addEventListener('contextmenu', (event) => {
      event.preventDefault();
      this.notifyItemContextMenu(event, result.item, result.type);
    });

    element.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.notifyItemClick(result.item, result.type);
      }
    });

    element.addEventListener('mouseenter', () => {
      element.style.backgroundColor = '#f6f8fa';
    });

    element.addEventListener('mouseleave', () => {
      element.style.backgroundColor = 'transparent';
    });

    return element;
  }

  /**
   * 获取类型图标
   */
  private getTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      bookmark: '🔖',
      folder: '📁',
      tab: '🗂️'
    };
    return icons[type] || '📄';
  }

  /**
   * 获取项目图标
   */
  private getItemIcon(item: BookmarkNode | TabInfo, type: string): string {
    if (type === 'folder') {
      return `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M.5 3l.04.87a1.99 1.99 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14h10.348a2 2 0 0 0 1.991-1.819l.637-7A1.99 1.99 0 0 0 15.46 3.87L15.5 3H.5zM2.19 4h11.62a1 1 0 0 1 .996 1.09L14.17 12.1a1 1 0 0 1-.996.9H2.826a1 1 0 0 1-.995-.9L1.194 5.09A1 1 0 0 1 2.19 4z"/>
          <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h3A1.5 1.5 0 0 1 6 1.5V3H1.5A1.5 1.5 0 0 1 0 1.5z"/>
        </svg>
      `;
    }

    if (type === 'tab') {
      return `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2z"/>
          <path d="M2 3h12v1H2V3z"/>
        </svg>
      `;
    }

    // 收藏夹，尝试显示网站图标
    const url = (item as any).url;
    if (url) {
      try {
        const domain = new URL(url).hostname;
        return `<img src="https://www.google.com/s2/favicons?domain=${domain}&sz=16" width="16" height="16" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';" />
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" style="display:none;">
                  <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
                </svg>`;
      } catch (error) {
        // 无效URL，使用默认图标
      }
    }

    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
      </svg>
    `;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 键盘导航
    this.element.addEventListener('keydown', (event) => {
      this.handleKeyboardNavigation(event);
    });
  }

  /**
   * 处理键盘导航
   */
  private handleKeyboardNavigation(event: KeyboardEvent): void {
    const focusedElement = document.activeElement as HTMLElement;
    if (!focusedElement || !this.element.contains(focusedElement)) {
      return;
    }

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        this.focusPreviousItem(focusedElement);
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.focusNextItem(focusedElement);
        break;
      case 'Home':
        event.preventDefault();
        this.focusFirstItem();
        break;
      case 'End':
        event.preventDefault();
        this.focusLastItem();
        break;
    }
  }

  /**
   * 聚焦上一个项目
   */
  private focusPreviousItem(currentElement: HTMLElement): void {
    const items = Array.from(this.element.querySelectorAll('.search-result-item'));
    const currentIndex = items.indexOf(currentElement);
    if (currentIndex > 0) {
      (items[currentIndex - 1] as HTMLElement).focus();
    }
  }

  /**
   * 聚焦下一个项目
   */
  private focusNextItem(currentElement: HTMLElement): void {
    const items = Array.from(this.element.querySelectorAll('.search-result-item'));
    const currentIndex = items.indexOf(currentElement);
    if (currentIndex < items.length - 1) {
      (items[currentIndex + 1] as HTMLElement).focus();
    }
  }

  /**
   * 聚焦第一个项目
   */
  private focusFirstItem(): void {
    const firstItem = this.element.querySelector('.search-result-item') as HTMLElement;
    if (firstItem) {
      firstItem.focus();
    }
  }

  /**
   * 聚焦最后一个项目
   */
  private focusLastItem(): void {
    const items = Array.from(this.element.querySelectorAll('.search-result-item'));
    const lastItem = items[items.length - 1] as HTMLElement;
    if (lastItem) {
      lastItem.focus();
    }
  }

  /**
   * 通知项目点击事件
   */
  private notifyItemClick(item: BookmarkNode | TabInfo, type: string): void {
    this.itemClickCallbacks.forEach(callback => {
      try {
        callback(item, type);
      } catch (error) {
        console.error('Error in item click callback:', error);
      }
    });
  }

  /**
   * 通知项目右键菜单事件
   */
  private notifyItemContextMenu(event: MouseEvent, item: BookmarkNode | TabInfo, type: string): void {
    this.itemContextMenuCallbacks.forEach(callback => {
      try {
        callback(event, item, type);
      } catch (error) {
        console.error('Error in item context menu callback:', error);
      }
    });
  }
}