/**
 * Search Suggestions - 搜索建议组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';

export interface SuggestionItem {
  text: string;
  type: 'history' | 'suggestion' | 'completion';
  icon?: string;
  description?: string;
}

export interface SearchSuggestionsOptions {
  maxSuggestions?: number;
  showIcons?: boolean;
  showDescriptions?: boolean;
  groupByType?: boolean;
}

export class SearchSuggestions implements UIComponent {
  element: HTMLElement;
  private options: SearchSuggestionsOptions;
  private suggestions: SuggestionItem[] = [];
  private selectedIndex = -1;
  private visible = false;
  private selectCallbacks: ((suggestion: SuggestionItem) => void)[] = [];
  private hoverCallbacks: ((suggestion: SuggestionItem, index: number) => void)[] = [];

  constructor(options: SearchSuggestionsOptions = {}) {
    this.options = {
      maxSuggestions: 8,
      showIcons: true,
      showDescriptions: true,
      groupByType: false,
      ...options
    };

    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    if (this.suggestions.length === 0) {
      this.hide();
      return;
    }

    if (this.options.groupByType) {
      this.renderGroupedSuggestions();
    } else {
      this.renderFlatSuggestions();
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.selectCallbacks = [];
    this.hoverCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.suggestions) {
      this.setSuggestions(data.suggestions);
    }
  }

  /**
   * 设置建议列表
   */
  setSuggestions(suggestions: SuggestionItem[]): void {
    this.suggestions = suggestions.slice(0, this.options.maxSuggestions);
    this.selectedIndex = -1;
    this.render();
  }

  /**
   * 显示建议
   */
  show(): void {
    if (this.suggestions.length > 0) {
      this.visible = true;
      this.element.style.display = 'block';
      this.element.classList.add('visible');
    }
  }

  /**
   * 隐藏建议
   */
  hide(): void {
    this.visible = false;
    this.element.style.display = 'none';
    this.element.classList.remove('visible');
    this.selectedIndex = -1;
  }

  /**
   * 检查是否可见
   */
  isVisible(): boolean {
    return this.visible;
  }

  /**
   * 选择下一个建议
   */
  selectNext(): void {
    if (this.suggestions.length === 0) return;

    this.selectedIndex = (this.selectedIndex + 1) % this.suggestions.length;
    this.updateSelection();
  }

  /**
   * 选择上一个建议
   */
  selectPrevious(): void {
    if (this.suggestions.length === 0) return;

    this.selectedIndex = this.selectedIndex <= 0 
      ? this.suggestions.length - 1 
      : this.selectedIndex - 1;
    this.updateSelection();
  }

  /**
   * 选择指定索引的建议
   */
  selectIndex(index: number): void {
    if (index >= 0 && index < this.suggestions.length) {
      this.selectedIndex = index;
      this.updateSelection();
    }
  }

  /**
   * 获取当前选中的建议
   */
  getSelectedSuggestion(): SuggestionItem | null {
    if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
      return this.suggestions[this.selectedIndex];
    }
    return null;
  }

  /**
   * 确认选择当前建议
   */
  confirmSelection(): void {
    const selected = this.getSelectedSuggestion();
    if (selected) {
      this.notifySelect(selected);
    }
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    this.selectedIndex = -1;
    this.updateSelection();
  }

  /**
   * 添加选择回调
   */
  onSelect(callback: (suggestion: SuggestionItem) => void): void {
    this.selectCallbacks.push(callback);
  }

  /**
   * 添加悬停回调
   */
  onHover(callback: (suggestion: SuggestionItem, index: number) => void): void {
    this.hoverCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-suggestions';
    element.style.position = 'absolute';
    element.style.top = '100%';
    element.style.left = '0';
    element.style.right = '0';
    element.style.backgroundColor = '#ffffff';
    element.style.border = '1px solid #d0d7de';
    element.style.borderTop = 'none';
    element.style.borderRadius = '0 0 8px 8px';
    element.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
    element.style.maxHeight = '300px';
    element.style.overflowY = 'auto';
    element.style.zIndex = '1001';
    element.style.display = 'none';
    element.setAttribute('role', 'listbox');
    element.setAttribute('aria-label', '搜索建议');
    
    return element;
  }

  /**
   * 渲染平铺建议
   */
  private renderFlatSuggestions(): void {
    this.suggestions.forEach((suggestion, index) => {
      const suggestionElement = this.createSuggestionElement(suggestion, index);
      this.element.appendChild(suggestionElement);
    });
  }

  /**
   * 渲染分组建议
   */
  private renderGroupedSuggestions(): void {
    const groups = this.groupSuggestionsByType();

    Object.entries(groups).forEach(([type, suggestions]) => {
      if (suggestions.length === 0) return;

      // 创建分组标题
      const groupHeader = this.createGroupHeader(type);
      this.element.appendChild(groupHeader);

      // 创建分组建议
      suggestions.forEach((suggestion, index) => {
        const globalIndex = this.suggestions.indexOf(suggestion);
        const suggestionElement = this.createSuggestionElement(suggestion, globalIndex);
        this.element.appendChild(suggestionElement);
      });
    });
  }

  /**
   * 按类型分组建议
   */
  private groupSuggestionsByType(): Record<string, SuggestionItem[]> {
    const groups: Record<string, SuggestionItem[]> = {
      history: [],
      suggestion: [],
      completion: []
    };

    this.suggestions.forEach(suggestion => {
      if (groups[suggestion.type]) {
        groups[suggestion.type].push(suggestion);
      }
    });

    return groups;
  }

  /**
   * 创建分组标题
   */
  private createGroupHeader(type: string): HTMLElement {
    const header = document.createElement('div');
    header.className = 'search-suggestions-group-header';
    header.style.padding = '8px 12px';
    header.style.fontSize = '12px';
    header.style.fontWeight = '600';
    header.style.color = '#656d76';
    header.style.backgroundColor = '#f6f8fa';
    header.style.borderBottom = '1px solid #e1e4e8';

    const typeNames: Record<string, string> = {
      history: '搜索历史',
      suggestion: '搜索建议',
      completion: '自动完成'
    };

    header.textContent = typeNames[type] || type;
    return header;
  }

  /**
   * 创建建议元素
   */
  private createSuggestionElement(suggestion: SuggestionItem, index: number): HTMLElement {
    const element = document.createElement('div');
    element.className = 'search-suggestion-item';
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.padding = '8px 12px';
    element.style.cursor = 'pointer';
    element.style.fontSize = '14px';
    element.style.color = '#24292f';
    element.style.transition = 'background-color 0.15s ease';
    element.setAttribute('role', 'option');
    element.setAttribute('data-index', index.toString());

    // 图标
    if (this.options.showIcons && suggestion.icon) {
      const icon = document.createElement('span');
      icon.className = 'suggestion-icon';
      icon.style.marginRight = '8px';
      icon.style.fontSize = '16px';
      icon.innerHTML = suggestion.icon;
      element.appendChild(icon);
    } else if (this.options.showIcons) {
      const defaultIcon = document.createElement('span');
      defaultIcon.className = 'suggestion-icon';
      defaultIcon.style.marginRight = '8px';
      defaultIcon.style.fontSize = '16px';
      defaultIcon.innerHTML = this.getDefaultIcon(suggestion.type);
      element.appendChild(defaultIcon);
    }

    // 主要内容
    const content = document.createElement('div');
    content.className = 'suggestion-content';
    content.style.flex = '1';
    content.style.minWidth = '0';

    // 文本
    const text = document.createElement('div');
    text.className = 'suggestion-text';
    text.style.fontSize = '14px';
    text.style.fontWeight = '500';
    text.style.overflow = 'hidden';
    text.style.textOverflow = 'ellipsis';
    text.style.whiteSpace = 'nowrap';
    text.textContent = suggestion.text;
    content.appendChild(text);

    // 描述
    if (this.options.showDescriptions && suggestion.description) {
      const description = document.createElement('div');
      description.className = 'suggestion-description';
      description.style.fontSize = '12px';
      description.style.color = '#656d76';
      description.style.marginTop = '2px';
      description.style.overflow = 'hidden';
      description.style.textOverflow = 'ellipsis';
      description.style.whiteSpace = 'nowrap';
      description.textContent = suggestion.description;
      content.appendChild(description);
    }

    element.appendChild(content);

    // 类型标识
    const typeLabel = document.createElement('span');
    typeLabel.className = 'suggestion-type';
    typeLabel.style.fontSize = '10px';
    typeLabel.style.color = '#656d76';
    typeLabel.style.backgroundColor = '#f6f8fa';
    typeLabel.style.padding = '2px 6px';
    typeLabel.style.borderRadius = '10px';
    typeLabel.style.marginLeft = '8px';
    typeLabel.textContent = this.getTypeLabel(suggestion.type);
    element.appendChild(typeLabel);

    // 事件监听
    element.addEventListener('click', () => {
      this.selectedIndex = index;
      this.notifySelect(suggestion);
    });

    element.addEventListener('mouseenter', () => {
      this.selectedIndex = index;
      this.updateSelection();
      this.notifyHover(suggestion, index);
    });

    return element;
  }

  /**
   * 更新选择状态
   */
  private updateSelection(): void {
    // 清除所有选中状态
    const items = this.element.querySelectorAll('.search-suggestion-item');
    items.forEach((item, index) => {
      if (index === this.selectedIndex) {
        item.classList.add('selected');
        item.setAttribute('aria-selected', 'true');
        item.style.backgroundColor = '#0969da';
        item.style.color = '#ffffff';
        
        // 更新类型标签颜色
        const typeLabel = item.querySelector('.suggestion-type') as HTMLElement;
        if (typeLabel) {
          typeLabel.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
          typeLabel.style.color = '#ffffff';
        }
        
        // 滚动到可见区域
        item.scrollIntoView({ block: 'nearest' });
      } else {
        item.classList.remove('selected');
        item.setAttribute('aria-selected', 'false');
        item.style.backgroundColor = 'transparent';
        item.style.color = '#24292f';
        
        // 恢复类型标签颜色
        const typeLabel = item.querySelector('.suggestion-type') as HTMLElement;
        if (typeLabel) {
          typeLabel.style.backgroundColor = '#f6f8fa';
          typeLabel.style.color = '#656d76';
        }
      }
    });
  }

  /**
   * 获取默认图标
   */
  private getDefaultIcon(type: string): string {
    const icons: Record<string, string> = {
      history: `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M1.643 3.143L.427 1.927A.25.25 0 0 0 0 2.104V5.75c0 .138.112.25.25.25h3.646a.25.25 0 0 0 .177-.427L2.715 4.215a6.5 6.5 0 1 1-1.18 4.458.75.75 0 1 0-1.493.154 8.001 8.001 0 1 0 1.6-5.684zM7.75 4a.75.75 0 0 1 .75.75v2.992l2.028.812a.75.75 0 0 1-.557 1.392l-2.5-1A.75.75 0 0 1 7 8.25v-3.5A.75.75 0 0 1 7.75 4z"/>
        </svg>
      `,
      suggestion: `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M8 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zM2 8a6 6 0 1 1 10.89 3.476l4.817 4.817a1 1 0 0 1-1.414 1.414l-4.816-4.816A6 6 0 0 1 2 8z"/>
        </svg>
      `,
      completion: `
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25a1.75 1.75 0 0 1 .445-.758l8.61-8.61zm1.414 1.06a.25.25 0 0 0-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 0 0 0-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 0 0-.064.108l-.558 1.953 1.953-.558a.249.249 0 0 0 .108-.064l6.286-6.286z"/>
        </svg>
      `
    };

    return icons[type] || icons.suggestion;
  }

  /**
   * 获取类型标签
   */
  private getTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      history: '历史',
      suggestion: '建议',
      completion: '完成'
    };

    return labels[type] || type;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 阻止建议框内的点击事件冒泡
    this.element.addEventListener('click', (event) => {
      event.stopPropagation();
    });

    // 鼠标离开时清除悬停状态
    this.element.addEventListener('mouseleave', () => {
      this.clearSelection();
    });
  }

  /**
   * 通知选择事件
   */
  private notifySelect(suggestion: SuggestionItem): void {
    this.selectCallbacks.forEach(callback => {
      try {
        callback(suggestion);
      } catch (error) {
        console.error('Error in select callback:', error);
      }
    });
  }

  /**
   * 通知悬停事件
   */
  private notifyHover(suggestion: SuggestionItem, index: number): void {
    this.hoverCallbacks.forEach(callback => {
      try {
        callback(suggestion, index);
      } catch (error) {
        console.error('Error in hover callback:', error);
      }
    });
  }

  /**
   * 创建建议项
   */
  static createSuggestion(
    text: string, 
    type: 'history' | 'suggestion' | 'completion',
    options: { icon?: string; description?: string } = {}
  ): SuggestionItem {
    return {
      text,
      type,
      icon: options.icon,
      description: options.description
    };
  }

  /**
   * 从搜索历史创建建议
   */
  static fromHistory(historyItems: string[]): SuggestionItem[] {
    return historyItems.map(item => ({
      text: item,
      type: 'history' as const,
      icon: undefined,
      description: '搜索历史'
    }));
  }

  /**
   * 从搜索建议创建建议
   */
  static fromSuggestions(suggestions: string[]): SuggestionItem[] {
    return suggestions.map(suggestion => ({
      text: suggestion,
      type: 'suggestion' as const,
      icon: undefined,
      description: '搜索建议'
    }));
  }
}