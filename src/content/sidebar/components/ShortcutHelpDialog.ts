/**
 * Shortcut Help Dialog - 快捷键帮助对话框
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { GlobalShortcut, GlobalKeyboardManager } from '../../utils/GlobalKeyboardManager.js';

export interface ShortcutHelpOptions {
  showCategories?: boolean;
  showDescriptions?: boolean;
  showCustomShortcuts?: boolean;
  allowEditing?: boolean;
}

export class ShortcutHelpDialog implements UIComponent {
  element: HTMLElement;
  private options: ShortcutHelpOptions;
  private overlay: HTMLElement;
  private shortcuts: GlobalShortcut[] = [];
  private visible = false;
  private editCallbacks: ((shortcut: GlobalShortcut) => void)[] = [];
  private closeCallbacks: (() => void)[] = [];

  constructor(options: ShortcutHelpOptions = {}) {
    this.options = {
      showCategories: true,
      showDescriptions: true,
      showCustomShortcuts: true,
      allowEditing: false,
      ...options
    };

    this.element = this.createElement();
    this.overlay = this.createOverlay();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    
    // 创建头部
    const header = this.createHeader();
    this.element.appendChild(header);
    
    // 创建内容
    const content = this.createContent();
    this.element.appendChild(content);
    
    // 创建底部
    const footer = this.createFooter();
    this.element.appendChild(footer);
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.hide();
    this.editCallbacks = [];
    this.closeCallbacks = [];
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.shortcuts) {
      this.setShortcuts(data.shortcuts);
    }
  }

  /**
   * 设置快捷键列表
   */
  setShortcuts(shortcuts: GlobalShortcut[]): void {
    this.shortcuts = shortcuts;
    if (this.visible) {
      this.render();
    }
  }

  /**
   * 显示对话框
   */
  show(): void {
    if (this.visible) return;

    this.render();
    document.body.appendChild(this.overlay);
    document.body.appendChild(this.element);
    
    this.visible = true;
    this.element.classList.add('visible');
    
    // 聚焦到对话框
    this.element.focus();
    
    // 添加ESC键监听
    document.addEventListener('keydown', this.handleKeydown);
  }

  /**
   * 隐藏对话框
   */
  hide(): void {
    if (!this.visible) return;

    if (this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    
    this.visible = false;
    this.element.classList.remove('visible');
    
    document.removeEventListener('keydown', this.handleKeydown);
    this.notifyClose();
  }

  /**
   * 检查是否可见
   */
  isVisible(): boolean {
    return this.visible;
  }

  /**
   * 切换显示状态
   */
  toggle(): void {
    if (this.visible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 添加编辑回调
   */
  onEdit(callback: (shortcut: GlobalShortcut) => void): void {
    this.editCallbacks.push(callback);
  }

  /**
   * 添加关闭回调
   */
  onClose(callback: () => void): void {
    this.closeCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'shortcut-help-dialog';
    element.style.position = 'fixed';
    element.style.top = '50%';
    element.style.left = '50%';
    element.style.transform = 'translate(-50%, -50%)';
    element.style.width = '600px';
    element.style.maxWidth = '90vw';
    element.style.maxHeight = '80vh';
    element.style.backgroundColor = '#ffffff';
    element.style.border = '1px solid #d0d7de';
    element.style.borderRadius = '12px';
    element.style.boxShadow = '0 16px 32px rgba(0, 0, 0, 0.12)';
    element.style.zIndex = '2147483650';
    element.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    element.style.display = 'flex';
    element.style.flexDirection = 'column';
    element.setAttribute('role', 'dialog');
    element.setAttribute('aria-modal', 'true');
    element.setAttribute('aria-labelledby', 'shortcut-help-title');
    element.setAttribute('tabindex', '-1');
    
    return element;
  }

  /**
   * 创建遮罩层
   */
  private createOverlay(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'shortcut-help-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '2147483649';
    
    return overlay;
  }

  /**
   * 创建头部
   */
  private createHeader(): HTMLElement {
    const header = document.createElement('div');
    header.className = 'shortcut-help-header';
    header.style.padding = '20px 24px';
    header.style.borderBottom = '1px solid #e1e4e8';
    header.style.display = 'flex';
    header.style.alignItems = 'center';
    header.style.justifyContent = 'space-between';

    // 标题
    const title = document.createElement('h2');
    title.id = 'shortcut-help-title';
    title.style.margin = '0';
    title.style.fontSize = '20px';
    title.style.fontWeight = '600';
    title.style.color = '#24292f';
    title.textContent = '键盘快捷键';

    // 关闭按钮
    const closeButton = document.createElement('button');
    closeButton.className = 'shortcut-help-close';
    closeButton.style.border = 'none';
    closeButton.style.background = 'none';
    closeButton.style.padding = '8px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.borderRadius = '6px';
    closeButton.style.color = '#656d76';
    closeButton.style.transition = 'all 0.15s ease';
    closeButton.innerHTML = this.getCloseIcon();
    closeButton.setAttribute('aria-label', '关闭对话框');

    closeButton.addEventListener('click', () => {
      this.hide();
    });

    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.backgroundColor = '#f6f8fa';
    });

    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.backgroundColor = 'transparent';
    });

    header.appendChild(title);
    header.appendChild(closeButton);

    return header;
  }

  /**
   * 创建内容
   */
  private createContent(): HTMLElement {
    const content = document.createElement('div');
    content.className = 'shortcut-help-content';
    content.style.flex = '1';
    content.style.overflowY = 'auto';
    content.style.padding = '0';

    if (this.shortcuts.length === 0) {
      const emptyState = this.createEmptyState();
      content.appendChild(emptyState);
      return content;
    }

    if (this.options.showCategories) {
      const groupedShortcuts = this.groupShortcutsByCategory();
      Object.entries(groupedShortcuts).forEach(([category, shortcuts]) => {
        if (shortcuts.length > 0) {
          const categorySection = this.createCategorySection(category, shortcuts);
          content.appendChild(categorySection);
        }
      });
    } else {
      const shortcutList = this.createShortcutList(this.shortcuts);
      content.appendChild(shortcutList);
    }

    return content;
  }

  /**
   * 创建底部
   */
  private createFooter(): HTMLElement {
    const footer = document.createElement('div');
    footer.className = 'shortcut-help-footer';
    footer.style.padding = '16px 24px';
    footer.style.borderTop = '1px solid #e1e4e8';
    footer.style.backgroundColor = '#f6f8fa';
    footer.style.borderRadius = '0 0 12px 12px';
    footer.style.fontSize = '12px';
    footer.style.color = '#656d76';
    footer.style.textAlign = 'center';

    const tip = document.createElement('div');
    tip.innerHTML = '💡 提示：按 <kbd>?</kbd> 键可以随时打开此帮助';
    
    // 设置kbd样式
    const kbdStyle = `
      kbd {
        background-color: #f6f8fa;
        border: 1px solid #d0d7de;
        border-radius: 3px;
        padding: 2px 4px;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        font-size: 11px;
      }
    `;
    
    if (!document.querySelector('#shortcut-help-kbd-style')) {
      const style = document.createElement('style');
      style.id = 'shortcut-help-kbd-style';
      style.textContent = kbdStyle;
      document.head.appendChild(style);
    }

    footer.appendChild(tip);
    return footer;
  }

  /**
   * 创建空状态
   */
  private createEmptyState(): HTMLElement {
    const emptyState = document.createElement('div');
    emptyState.className = 'shortcut-help-empty';
    emptyState.style.padding = '60px 40px';
    emptyState.style.textAlign = 'center';
    emptyState.style.color = '#656d76';

    const icon = document.createElement('div');
    icon.style.fontSize = '48px';
    icon.style.marginBottom = '16px';
    icon.textContent = '⌨️';

    const message = document.createElement('div');
    message.style.fontSize = '16px';
    message.textContent = '暂无可用的快捷键';

    emptyState.appendChild(icon);
    emptyState.appendChild(message);

    return emptyState;
  }

  /**
   * 按类别分组快捷键
   */
  private groupShortcutsByCategory(): Record<string, GlobalShortcut[]> {
    const groups: Record<string, GlobalShortcut[]> = {};

    this.shortcuts.forEach(shortcut => {
      if (!groups[shortcut.category]) {
        groups[shortcut.category] = [];
      }
      groups[shortcut.category].push(shortcut);
    });

    // 排序类别
    const sortedGroups: Record<string, GlobalShortcut[]> = {};
    const categoryOrder = ['general', 'sidebar', 'search', 'navigation', 'tabs', 'bookmarks'];
    
    categoryOrder.forEach(category => {
      if (groups[category]) {
        sortedGroups[category] = groups[category];
      }
    });

    // 添加其他类别
    Object.keys(groups).forEach(category => {
      if (!categoryOrder.includes(category)) {
        sortedGroups[category] = groups[category];
      }
    });

    return sortedGroups;
  }

  /**
   * 创建类别区域
   */
  private createCategorySection(category: string, shortcuts: GlobalShortcut[]): HTMLElement {
    const section = document.createElement('div');
    section.className = 'shortcut-category-section';
    section.style.marginBottom = '24px';

    // 类别标题
    const header = document.createElement('div');
    header.className = 'shortcut-category-header';
    header.style.padding = '12px 24px';
    header.style.backgroundColor = '#f6f8fa';
    header.style.borderBottom = '1px solid #e1e4e8';
    header.style.fontSize = '14px';
    header.style.fontWeight = '600';
    header.style.color = '#24292f';
    header.textContent = this.getCategoryName(category);

    // 快捷键列表
    const list = this.createShortcutList(shortcuts);

    section.appendChild(header);
    section.appendChild(list);

    return section;
  }

  /**
   * 创建快捷键列表
   */
  private createShortcutList(shortcuts: GlobalShortcut[]): HTMLElement {
    const list = document.createElement('div');
    list.className = 'shortcut-list';

    shortcuts.forEach(shortcut => {
      const item = this.createShortcutItem(shortcut);
      list.appendChild(item);
    });

    return list;
  }

  /**
   * 创建快捷键项目
   */
  private createShortcutItem(shortcut: GlobalShortcut): HTMLElement {
    const item = document.createElement('div');
    item.className = 'shortcut-item';
    item.style.display = 'flex';
    item.style.alignItems = 'center';
    item.style.padding = '12px 24px';
    item.style.borderBottom = '1px solid #f0f0f0';
    item.style.transition = 'background-color 0.15s ease';

    // 快捷键显示
    const keyDisplay = document.createElement('div');
    keyDisplay.className = 'shortcut-keys';
    keyDisplay.style.minWidth = '120px';
    keyDisplay.style.marginRight = '16px';
    keyDisplay.innerHTML = this.formatShortcutKeys(shortcut);

    // 描述
    const description = document.createElement('div');
    description.className = 'shortcut-description';
    description.style.flex = '1';
    description.style.color = '#24292f';
    description.textContent = shortcut.description;

    // 状态指示器
    const status = document.createElement('div');
    status.className = 'shortcut-status';
    status.style.marginLeft = '12px';
    status.style.fontSize = '12px';

    if (!shortcut.enabled) {
      status.style.color = '#d1242f';
      status.textContent = '已禁用';
    } else if (shortcut.global) {
      status.style.color = '#0969da';
      status.textContent = '全局';
    } else {
      status.style.color = '#656d76';
      status.textContent = '局部';
    }

    item.appendChild(keyDisplay);
    item.appendChild(description);
    item.appendChild(status);

    // 编辑功能
    if (this.options.allowEditing) {
      const editButton = this.createEditButton(shortcut);
      item.appendChild(editButton);
    }

    // 悬停效果
    item.addEventListener('mouseenter', () => {
      item.style.backgroundColor = '#f6f8fa';
    });

    item.addEventListener('mouseleave', () => {
      item.style.backgroundColor = 'transparent';
    });

    return item;
  }

  /**
   * 创建编辑按钮
   */
  private createEditButton(shortcut: GlobalShortcut): HTMLElement {
    const button = document.createElement('button');
    button.className = 'shortcut-edit-button';
    button.style.marginLeft = '8px';
    button.style.padding = '4px 8px';
    button.style.border = '1px solid #d0d7de';
    button.style.borderRadius = '4px';
    button.style.backgroundColor = 'transparent';
    button.style.color = '#656d76';
    button.style.fontSize = '12px';
    button.style.cursor = 'pointer';
    button.style.transition = 'all 0.15s ease';
    button.textContent = '编辑';

    button.addEventListener('click', (event) => {
      event.stopPropagation();
      this.notifyEdit(shortcut);
    });

    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = '#f6f8fa';
      button.style.borderColor = '#bbb';
    });

    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'transparent';
      button.style.borderColor = '#d0d7de';
    });

    return button;
  }

  /**
   * 格式化快捷键显示
   */
  private formatShortcutKeys(shortcut: GlobalShortcut): string {
    const formatted = GlobalKeyboardManager.formatShortcut(shortcut);
    const keys = formatted.split(/[\+\s]/);
    
    return keys.map(key => `<kbd>${key}</kbd>`).join(' + ');
  }

  /**
   * 获取类别名称
   */
  private getCategoryName(category: string): string {
    const categoryNames: Record<string, string> = {
      general: '通用',
      sidebar: '侧边栏',
      search: '搜索',
      navigation: '导航',
      tabs: '标签页',
      bookmarks: '收藏夹'
    };

    return categoryNames[category] || category;
  }

  /**
   * 获取关闭图标
   */
  private getCloseIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06z"/>
      </svg>
    `;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 遮罩层点击关闭
    this.overlay.addEventListener('click', () => {
      this.hide();
    });

    // 阻止对话框内的点击事件冒泡
    this.element.addEventListener('click', (event) => {
      event.stopPropagation();
    });
  }

  /**
   * 处理键盘事件
   */
  private handleKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      event.preventDefault();
      this.hide();
    }
  };

  /**
   * 通知编辑事件
   */
  private notifyEdit(shortcut: GlobalShortcut): void {
    this.editCallbacks.forEach(callback => {
      try {
        callback(shortcut);
      } catch (error) {
        console.error('Error in edit callback:', error);
      }
    });
  }

  /**
   * 通知关闭事件
   */
  private notifyClose(): void {
    this.closeCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in close callback:', error);
      }
    });
  }

  /**
   * 创建快捷键帮助的静态方法
   */
  static createQuickHelp(shortcuts: GlobalShortcut[]): string {
    const grouped = shortcuts.reduce((groups, shortcut) => {
      if (!groups[shortcut.category]) {
        groups[shortcut.category] = [];
      }
      groups[shortcut.category].push(shortcut);
      return groups;
    }, {} as Record<string, GlobalShortcut[]>);

    let html = '<div class="shortcut-quick-help">';
    
    Object.entries(grouped).forEach(([category, categoryShortcuts]) => {
      html += `<div class="shortcut-category">`;
      html += `<h4>${category}</h4>`;
      html += `<ul>`;
      
      categoryShortcuts.forEach(shortcut => {
        const keys = GlobalKeyboardManager.formatShortcut(shortcut);
        html += `<li><kbd>${keys}</kbd> ${shortcut.description}</li>`;
      });
      
      html += `</ul></div>`;
    });
    
    html += '</div>';
    return html;
  }
}