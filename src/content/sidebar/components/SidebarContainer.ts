/**
 * Sidebar Container - 侧边栏容器组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { TabInfo, BookmarkNode, UserSettings } from '../../types/index.js';
import { SidebarHeader } from './SidebarHeader.js';
import { SidebarContent } from './SidebarContent.js';
import { SidebarFooter } from './SidebarFooter.js';

export interface SidebarContainerOptions {
  position: 'left' | 'right';
  theme: 'light' | 'dark' | 'auto';
  collapsedWidth: number;
  expandedWidth: number;
  showTabs: boolean;
  showBookmarks: boolean;
  showTabGroups: boolean;
}

export class SidebarContainer implements UIComponent {
  element: HTMLElement;
  private header: SidebarHeader;
  private content: SidebarContent;
  private footer: SidebarFooter;
  private options: SidebarContainerOptions;
  private expandCallbacks: (() => void)[] = [];
  private collapseCallbacks: (() => void)[] = [];
  private pinCallbacks: (() => void)[] = [];
  private unpinCallbacks: (() => void)[] = [];
  private hoverTimer: NodeJS.Timeout | null = null;
  private collapseTimer: NodeJS.Timeout | null = null;

  constructor(options: SidebarContainerOptions) {
    this.options = options;
    this.element = this.createElement();
    this.header = new SidebarHeader();
    this.content = new SidebarContent({
      showTabs: options.showTabs,
      showBookmarks: options.showBookmarks,
      showTabGroups: options.showTabGroups
    });
    this.footer = new SidebarFooter();
    
    this.render();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    // 清空容器
    this.element.innerHTML = '';

    // 添加子组件
    this.element.appendChild(this.header.element);
    this.element.appendChild(this.content.element);
    this.element.appendChild(this.footer.element);

    // 渲染子组件
    this.header.render();
    this.content.render();
    this.footer.render();

    // 应用样式
    this.applyStyles();

    // 添加到页面
    if (!document.body.contains(this.element)) {
      document.body.appendChild(this.element);
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 清理定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer);
    }
    if (this.collapseTimer) {
      clearTimeout(this.collapseTimer);
    }

    // 销毁子组件
    this.header.destroy();
    this.content.destroy();
    this.footer.destroy();

    // 从DOM中移除
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }

    // 清理回调
    this.expandCallbacks = [];
    this.collapseCallbacks = [];
    this.pinCallbacks = [];
    this.unpinCallbacks = [];
  }

  /**
   * 更新组件数据
   */
  update(data?: any): void {
    this.header.update(data);
    this.content.update(data);
    this.footer.update(data);
  }

  /**
   * 显示侧边栏
   */
  show(): void {
    this.element.style.display = 'flex';
    this.element.classList.add('visible');
  }

  /**
   * 隐藏侧边栏
   */
  hide(): void {
    this.element.style.display = 'none';
    this.element.classList.remove('visible', 'expanded', 'pinned');
  }

  /**
   * 展开侧边栏
   */
  expand(): void {
    this.element.classList.add('expanded');
    this.element.style.width = `${this.options.expandedWidth}px`;
    
    // 通知回调
    this.expandCallbacks.forEach(callback => callback());
  }

  /**
   * 收起侧边栏
   */
  collapse(): void {
    this.element.classList.remove('expanded');
    this.element.style.width = `${this.options.collapsedWidth}px`;
    
    // 通知回调
    this.collapseCallbacks.forEach(callback => callback());
  }

  /**
   * 固定侧边栏
   */
  pin(): void {
    this.element.classList.add('pinned');
    this.footer.showUnpinButton();
    
    // 通知回调
    this.pinCallbacks.forEach(callback => callback());
  }

  /**
   * 取消固定侧边栏
   */
  unpin(): void {
    this.element.classList.remove('pinned');
    this.footer.showPinButton();
    
    // 通知回调
    this.unpinCallbacks.forEach(callback => callback());
  }

  /**
   * 更新标签页数据
   */
  updateTabs(tabs: TabInfo[]): void {
    this.content.updateTabs(tabs);
  }

  /**
   * 更新标签分组数据
   */
  updateTabGroups(groups: any[]): void {
    this.content.updateTabGroups(groups);
  }

  /**
   * 更新收藏夹数据
   */
  updateBookmarks(bookmarks: BookmarkNode[]): void {
    this.content.updateBookmarks(bookmarks);
  }

  /**
   * 更新设置
   */
  updateSettings(settings: UserSettings): void {
    this.options = {
      position: settings.position,
      theme: settings.theme,
      collapsedWidth: settings.sidebarWidth.collapsed,
      expandedWidth: settings.sidebarWidth.expanded,
      showTabs: settings.showTabs,
      showBookmarks: settings.showBookmarks,
      showTabGroups: settings.showTabGroups
    };

    this.applyStyles();
    this.content.updateSettings(settings);
  }

  /**
   * 聚焦搜索框
   */
  focusSearch(): void {
    this.header.focusSearch();
  }

  /**
   * 处理窗口大小变化
   */
  handleResize(): void {
    // 重新计算位置和大小
    this.applyStyles();
  }

  /**
   * 处理页面滚动
   */
  handleScroll(): void {
    // 保持侧边栏位置固定
    this.element.style.top = '0px';
  }

  /**
   * 添加展开回调
   */
  onExpand(callback: () => void): void {
    this.expandCallbacks.push(callback);
  }

  /**
   * 添加收起回调
   */
  onCollapse(callback: () => void): void {
    this.collapseCallbacks.push(callback);
  }

  /**
   * 添加固定回调
   */
  onPin(callback: () => void): void {
    this.pinCallbacks.push(callback);
  }

  /**
   * 添加取消固定回调
   */
  onUnpin(callback: () => void): void {
    this.unpinCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'vertical-sidebar';
    element.id = 'chrome-vertical-sidebar';
    return element;
  }

  /**
   * 应用样式
   */
  private applyStyles(): void {
    // 基础样式
    this.element.style.position = 'fixed';
    this.element.style.top = '0';
    this.element.style.height = '100vh';
    this.element.style.width = `${this.options.collapsedWidth}px`;
    this.element.style.zIndex = '**********';
    this.element.style.display = 'flex';
    this.element.style.flexDirection = 'column';
    this.element.style.backgroundColor = this.getBackgroundColor();
    this.element.style.borderRight = this.getBorderStyle();
    this.element.style.transition = 'width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
    this.element.style.overflow = 'hidden';

    // 位置样式
    if (this.options.position === 'left') {
      this.element.style.left = '0';
      this.element.style.right = 'auto';
      this.element.classList.add('position-left');
      this.element.classList.remove('position-right');
    } else {
      this.element.style.left = 'auto';
      this.element.style.right = '0';
      this.element.classList.add('position-right');
      this.element.classList.remove('position-left');
    }

    // 主题样式
    this.element.classList.remove('theme-light', 'theme-dark', 'theme-auto');
    this.element.classList.add(`theme-${this.options.theme}`);

    // 如果是auto主题，检测系统主题
    if (this.options.theme === 'auto') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.element.classList.add(isDark ? 'theme-dark' : 'theme-light');
    }
  }

  /**
   * 获取背景颜色
   */
  private getBackgroundColor(): string {
    const isDark = this.options.theme === 'dark' || 
      (this.options.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    return isDark ? '#1e1e1e' : '#ffffff';
  }

  /**
   * 获取边框样式
   */
  private getBorderStyle(): string {
    const isDark = this.options.theme === 'dark' || 
      (this.options.theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    const borderColor = isDark ? '#333333' : '#e0e0e0';
    
    if (this.options.position === 'left') {
      return `1px solid ${borderColor}`;
    } else {
      return `1px solid ${borderColor}`;
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 鼠标悬停事件
    this.element.addEventListener('mouseenter', () => {
      if (this.element.classList.contains('pinned')) return;
      
      if (this.collapseTimer) {
        clearTimeout(this.collapseTimer);
        this.collapseTimer = null;
      }

      this.hoverTimer = setTimeout(() => {
        if (!this.element.classList.contains('expanded')) {
          this.expand();
        }
      }, 300); // 300ms延迟展开
    });

    this.element.addEventListener('mouseleave', () => {
      if (this.element.classList.contains('pinned')) return;
      
      if (this.hoverTimer) {
        clearTimeout(this.hoverTimer);
        this.hoverTimer = null;
      }

      this.collapseTimer = setTimeout(() => {
        if (this.element.classList.contains('expanded')) {
          this.collapse();
        }
      }, 500); // 500ms延迟收起
    });

    // 固定按钮事件
    this.footer.onPinClick(() => {
      this.pin();
    });

    this.footer.onUnpinClick(() => {
      this.unpin();
    });

    // 搜索事件
    this.header.onSearch((query: string) => {
      this.content.search(query);
    });

    // 主题变化监听
    if (this.options.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => {
        this.applyStyles();
      });
    }
  }
}