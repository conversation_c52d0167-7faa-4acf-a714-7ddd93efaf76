/**
 * Sidebar Content - 侧边栏内容组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { TabInfo, BookmarkNode, UserSettings } from '../../types/index.js';
import { TabsSection } from './TabsSection.js';
import { BookmarksSection } from './BookmarksSection.js';

export interface SidebarContentOptions {
  showTabs: boolean;
  showBookmarks: boolean;
  showTabGroups: boolean;
}

export class SidebarContent implements UIComponent {
  element: HTMLElement;
  private tabsSection: TabsSection | null = null;
  private bookmarksSection: BookmarksSection | null = null;
  private options: SidebarContentOptions;
  private currentSearchQuery = '';

  constructor(options: SidebarContentOptions) {
    this.options = options;
    this.element = this.createElement();
    this.createSections();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    // 渲染标签页区域
    if (this.options.showTabs && this.tabsSection) {
      this.element.appendChild(this.tabsSection.element);
      this.tabsSection.render();
    }

    // 渲染收藏夹区域
    if (this.options.showBookmarks && this.bookmarksSection) {
      this.element.appendChild(this.bookmarksSection.element);
      this.bookmarksSection.render();
    }

    // 如果没有内容显示空状态
    if (!this.options.showTabs && !this.options.showBookmarks) {
      this.showEmptyState();
    }
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.tabsSection) {
      this.tabsSection.destroy();
    }
    if (this.bookmarksSection) {
      this.bookmarksSection.destroy();
    }
    
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (this.tabsSection) {
      this.tabsSection.update(data);
    }
    if (this.bookmarksSection) {
      this.bookmarksSection.update(data);
    }
  }

  /**
   * 更新标签页数据
   */
  updateTabs(tabs: TabInfo[]): void {
    if (this.tabsSection) {
      this.tabsSection.updateTabs(tabs);
      
      // 如果有搜索查询，应用过滤
      if (this.currentSearchQuery) {
        this.tabsSection.search(this.currentSearchQuery);
      }
    }
  }

  /**
   * 更新标签分组数据
   */
  updateTabGroups(groups: any[]): void {
    if (this.tabsSection && this.options.showTabGroups) {
      this.tabsSection.updateTabGroups(groups);
    }
  }

  /**
   * 更新收藏夹数据
   */
  updateBookmarks(bookmarks: BookmarkNode[]): void {
    if (this.bookmarksSection) {
      this.bookmarksSection.updateBookmarks(bookmarks);
      
      // 如果有搜索查询，应用过滤
      if (this.currentSearchQuery) {
        this.bookmarksSection.search(this.currentSearchQuery);
      }
    }
  }

  /**
   * 更新设置
   */
  updateSettings(settings: UserSettings): void {
    const newOptions = {
      showTabs: settings.showTabs,
      showBookmarks: settings.showBookmarks,
      showTabGroups: settings.showTabGroups
    };

    // 检查是否需要重新创建区域
    if (newOptions.showTabs !== this.options.showTabs ||
        newOptions.showBookmarks !== this.options.showBookmarks ||
        newOptions.showTabGroups !== this.options.showTabGroups) {
      
      this.options = newOptions;
      this.createSections();
      this.render();
    }

    // 更新子组件设置
    if (this.tabsSection) {
      this.tabsSection.updateSettings(settings);
    }
    if (this.bookmarksSection) {
      this.bookmarksSection.updateSettings(settings);
    }
  }

  /**
   * 搜索内容
   */
  search(query: string): void {
    this.currentSearchQuery = query.trim().toLowerCase();

    if (this.tabsSection) {
      this.tabsSection.search(this.currentSearchQuery);
    }
    if (this.bookmarksSection) {
      this.bookmarksSection.search(this.currentSearchQuery);
    }

    // 更新搜索状态显示
    this.updateSearchState();
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-content';
    return element;
  }

  /**
   * 创建区域组件
   */
  private createSections(): void {
    // 销毁现有区域
    if (this.tabsSection) {
      this.tabsSection.destroy();
      this.tabsSection = null;
    }
    if (this.bookmarksSection) {
      this.bookmarksSection.destroy();
      this.bookmarksSection = null;
    }

    // 创建标签页区域
    if (this.options.showTabs) {
      this.tabsSection = new TabsSection();
    }

    // 创建收藏夹区域
    if (this.options.showBookmarks) {
      this.bookmarksSection = new BookmarksSection();
    }
  }

  /**
   * 显示空状态
   */
  private showEmptyState(): void {
    const emptyElement = document.createElement('div');
    emptyElement.className = 'sidebar-empty';
    emptyElement.innerHTML = `
      <div class="empty-icon">📋</div>
      <div class="empty-text">没有可显示的内容</div>
      <div class="empty-hint">请在设置中启用标签页或收藏夹显示</div>
    `;
    this.element.appendChild(emptyElement);
  }

  /**
   * 更新搜索状态显示
   */
  private updateSearchState(): void {
    if (this.currentSearchQuery) {
      this.element.classList.add('searching');
      
      // 检查是否有搜索结果
      const hasResults = this.hasSearchResults();
      if (!hasResults) {
        this.showNoResults();
      } else {
        this.hideNoResults();
      }
    } else {
      this.element.classList.remove('searching');
      this.hideNoResults();
    }
  }

  /**
   * 检查是否有搜索结果
   */
  private hasSearchResults(): boolean {
    let hasResults = false;

    if (this.tabsSection) {
      hasResults = hasResults || this.tabsSection.hasVisibleItems();
    }
    if (this.bookmarksSection) {
      hasResults = hasResults || this.bookmarksSection.hasVisibleItems();
    }

    return hasResults;
  }

  /**
   * 显示无搜索结果
   */
  private showNoResults(): void {
    let noResultsElement = this.element.querySelector('.no-search-results');
    if (!noResultsElement) {
      noResultsElement = document.createElement('div');
      noResultsElement.className = 'no-search-results';
      noResultsElement.innerHTML = `
        <div class="no-results-icon">🔍</div>
        <div class="no-results-text">未找到匹配项</div>
        <div class="no-results-hint">尝试使用不同的关键词搜索</div>
      `;
      this.element.appendChild(noResultsElement);
    }
    noResultsElement.classList.add('visible');
  }

  /**
   * 隐藏无搜索结果
   */
  private hideNoResults(): void {
    const noResultsElement = this.element.querySelector('.no-search-results');
    if (noResultsElement) {
      noResultsElement.classList.remove('visible');
    }
  }
}