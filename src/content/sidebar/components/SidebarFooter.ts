/**
 * Sidebar Footer - 侧边栏底部组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';

export class SidebarFooter implements UIComponent {
  element: HTMLElement;
  private pinButton: HTMLButtonElement;
  private unpinButton: HTMLButtonElement;
  private pinCallbacks: (() => void)[] = [];
  private unpinCallbacks: (() => void)[] = [];

  constructor() {
    this.element = this.createElement();
    this.pinButton = this.createPinButton();
    this.unpinButton = this.createUnpinButton();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    this.element.appendChild(this.pinButton);
    this.element.appendChild(this.unpinButton);
    
    // 默认显示固定按钮
    this.showPinButton();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.pinCallbacks = [];
    this.unpinCallbacks = [];
    
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    // 底部组件暂时不需要更新逻辑
  }

  /**
   * 显示固定按钮
   */
  showPinButton(): void {
    this.pinButton.style.display = 'flex';
    this.unpinButton.style.display = 'none';
  }

  /**
   * 显示取消固定按钮
   */
  showUnpinButton(): void {
    this.pinButton.style.display = 'none';
    this.unpinButton.style.display = 'flex';
  }

  /**
   * 添加固定按钮点击回调
   */
  onPinClick(callback: () => void): void {
    this.pinCallbacks.push(callback);
  }

  /**
   * 添加取消固定按钮点击回调
   */
  onUnpinClick(callback: () => void): void {
    this.unpinCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-footer';
    return element;
  }

  /**
   * 创建固定按钮
   */
  private createPinButton(): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = 'sidebar-footer-button pin-button';
    button.title = '固定侧边栏';
    button.setAttribute('aria-label', '固定侧边栏');
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M4.456.734a1.75 1.75 0 0 1 2.826.504l.613 1.327a3.081 3.081 0 0 0 2.084 1.707l2.454.584c1.332.317 1.8 1.972.832 2.94L11.06 10l3.72 3.72a.75.75 0 1 1-1.061 1.06L10 11.06l-2.204 2.205c-.968.968-2.623.5-2.94-.832l-.584-2.454a3.081 3.081 0 0 0-1.707-2.084l-1.327-.613a1.75 1.75 0 0 1-.504-2.826L4.456.734z"/>
      </svg>
    `;
    return button;
  }

  /**
   * 创建取消固定按钮
   */
  private createUnpinButton(): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = 'sidebar-footer-button unpin-button active';
    button.title = '取消固定侧边栏';
    button.setAttribute('aria-label', '取消固定侧边栏');
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M9.586 2.414A2 2 0 0 0 8.172 2H7.828a2 2 0 0 0-1.414.586L5 4H4a1 1 0 0 0 0 2v6a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6a1 1 0 1 0 0-2h-1l-1.414-1.586zM8.172 3a1 1 0 0 1 .707.293L9.586 4H6.414l.707-.707A1 1 0 0 1 7.828 3h.344zM5 6h6v6a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6z"/>
        <path d="M1 1l14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    `;
    return button;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 固定按钮点击事件
    this.pinButton.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.notifyPinClick();
    });

    // 取消固定按钮点击事件
    this.unpinButton.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.notifyUnpinClick();
    });

    // 鼠标悬停效果
    this.pinButton.addEventListener('mouseenter', () => {
      this.pinButton.classList.add('hover');
    });

    this.pinButton.addEventListener('mouseleave', () => {
      this.pinButton.classList.remove('hover');
    });

    this.unpinButton.addEventListener('mouseenter', () => {
      this.unpinButton.classList.add('hover');
    });

    this.unpinButton.addEventListener('mouseleave', () => {
      this.unpinButton.classList.remove('hover');
    });

    // 键盘支持
    this.pinButton.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.notifyPinClick();
      }
    });

    this.unpinButton.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.notifyUnpinClick();
      }
    });
  }

  /**
   * 通知固定按钮点击
   */
  private notifyPinClick(): void {
    this.pinCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in pin callback:', error);
      }
    });
  }

  /**
   * 通知取消固定按钮点击
   */
  private notifyUnpinClick(): void {
    this.unpinCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in unpin callback:', error);
      }
    });
  }
}