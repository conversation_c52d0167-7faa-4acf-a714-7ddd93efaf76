/**
 * Sidebar Header - 侧边栏头部组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';

export class SidebarHeader implements UIComponent {
  element: HTMLElement;
  private searchInput: HTMLInputElement;
  private searchCallbacks: ((query: string) => void)[] = [];

  constructor() {
    this.element = this.createElement();
    this.searchInput = this.createSearchInput();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    this.element.appendChild(this.searchInput);
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.searchCallbacks = [];
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    // 头部组件暂时不需要更新逻辑
  }

  /**
   * 聚焦搜索框
   */
  focusSearch(): void {
    this.searchInput.focus();
  }

  /**
   * 清空搜索框
   */
  clearSearch(): void {
    this.searchInput.value = '';
    this.notifySearch('');
  }

  /**
   * 添加搜索回调
   */
  onSearch(callback: (query: string) => void): void {
    this.searchCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-header';
    return element;
  }

  /**
   * 创建搜索输入框
   */
  private createSearchInput(): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'sidebar-search';
    input.placeholder = '搜索标签页和收藏夹...';
    input.setAttribute('aria-label', '搜索标签页和收藏夹');
    return input;
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 搜索输入事件
    this.searchInput.addEventListener('input', (event) => {
      const query = (event.target as HTMLInputElement).value;
      this.notifySearch(query);
    });

    // 键盘事件
    this.searchInput.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.clearSearch();
        this.searchInput.blur();
      }
    });

    // 焦点事件
    this.searchInput.addEventListener('focus', () => {
      this.element.classList.add('search-focused');
    });

    this.searchInput.addEventListener('blur', () => {
      this.element.classList.remove('search-focused');
    });
  }

  /**
   * 通知搜索变化
   */
  private notifySearch(query: string): void {
    this.searchCallbacks.forEach(callback => {
      try {
        callback(query);
      } catch (error) {
        console.error('Error in search callback:', error);
      }
    });
  }
}