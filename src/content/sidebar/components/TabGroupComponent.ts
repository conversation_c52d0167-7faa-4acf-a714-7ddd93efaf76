/**
 * Tab Group Component - 标签页分组组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { TabGroup, TabInfo } from '../../types/index.js';
import { TabItem } from './TabItem.js';

export class TabGroupComponent implements UIComponent {
  element: HTMLElement;
  private group: TabGroup;
  private tabs: TabInfo[] = [];
  private tabItems: Map<number, TabItem> = new Map();
  private toggleCallbacks: ((collapsed: boolean) => void)[] = [];
  private contextMenuCallbacks: ((event: MouseEvent, group: TabGroup) => void)[] = [];
  private tabClickCallbacks: ((tabId: number) => void)[] = [];
  private tabCloseCallbacks: ((tabId: number) => void)[] = [];
  private tabPinCallbacks: ((tabId: number, pinned: boolean) => void)[] = [];
  private visible = true;

  constructor(group: TabGroup) {
    this.group = group;
    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    // 创建分组头部
    const header = this.createHeader();
    this.element.appendChild(header);

    // 创建标签页列表容器
    const tabsList = this.createTabsList();
    this.element.appendChild(tabsList);

    // 渲染标签页
    this.renderTabs();

    // 应用折叠状态
    this.applyCollapseState();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 销毁所有标签页项目
    this.tabItems.forEach(item => item.destroy());
    this.tabItems.clear();

    // 清空回调
    this.toggleCallbacks = [];
    this.contextMenuCallbacks = [];
    this.tabClickCallbacks = [];
    this.tabCloseCallbacks = [];
    this.tabPinCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.group) {
      this.group = data.group;
      this.render();
    }
  }

  /**
   * 获取分组数据
   */
  getGroup(): TabGroup {
    return this.group;
  }

  /**
   * 更新分组数据
   */
  updateGroup(group: TabGroup): void {
    this.group = group;
    this.render();
  }

  /**
   * 更新标签页数据
   */
  updateTabs(tabs: TabInfo[]): void {
    this.tabs = tabs.filter(tab => tab.groupId === this.group.id);
    this.renderTabs();
  }

  /**
   * 显示分组
   */
  show(): void {
    this.visible = true;
    this.element.style.display = 'block';
    this.element.classList.remove('hidden');
  }

  /**
   * 隐藏分组
   */
  hide(): void {
    this.visible = false;
    this.element.style.display = 'none';
    this.element.classList.add('hidden');
  }

  /**
   * 检查是否有可见标签页
   */
  hasVisibleTabs(): boolean {
    return Array.from(this.tabItems.values()).some(item => item.isVisible());
  }

  /**
   * 过滤标签页
   */
  filterTabs(query: string): boolean {
    let hasVisibleTabs = false;
    const lowerQuery = query.toLowerCase();

    this.tabItems.forEach(item => {
      const tab = item.getTab();
      const matches = tab.title.toLowerCase().includes(lowerQuery) || 
                     tab.url.toLowerCase().includes(lowerQuery);
      
      if (matches) {
        item.show();
        item.highlight(query);
        hasVisibleTabs = true;
      } else {
        item.hide();
      }
    });

    return hasVisibleTabs;
  }

  /**
   * 清除过滤
   */
  clearFilter(): void {
    this.tabItems.forEach(item => {
      item.show();
      item.clearHighlight();
    });
  }

  /**
   * 添加切换回调
   */
  onToggle(callback: (collapsed: boolean) => void): void {
    this.toggleCallbacks.push(callback);
  }

  /**
   * 添加右键菜单回调
   */
  onContextMenu(callback: (event: MouseEvent, group: TabGroup) => void): void {
    this.contextMenuCallbacks.push(callback);
  }

  /**
   * 添加标签页点击回调
   */
  onTabClick(callback: (tabId: number) => void): void {
    this.tabClickCallbacks.push(callback);
  }

  /**
   * 添加标签页关闭回调
   */
  onTabClose(callback: (tabId: number) => void): void {
    this.tabCloseCallbacks.push(callback);
  }

  /**
   * 添加标签页固定回调
   */
  onTabPin(callback: (tabId: number, pinned: boolean) => void): void {
    this.tabPinCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'tab-group';
    element.setAttribute('data-group-id', this.group.id.toString());
    return element;
  }

  /**
   * 创建分组头部
   */
  private createHeader(): HTMLElement {
    const header = document.createElement('div');
    header.className = 'tab-group-header';

    // 展开/折叠按钮
    const toggleButton = this.createToggleButton();
    header.appendChild(toggleButton);

    // 分组颜色指示器
    const colorIndicator = this.createColorIndicator();
    header.appendChild(colorIndicator);

    // 分组标题
    const title = this.createTitle();
    header.appendChild(title);

    // 标签页数量
    const count = this.createCount();
    header.appendChild(count);

    // 分组操作按钮
    const actions = this.createGroupActions();
    header.appendChild(actions);

    return header;
  }

  /**
   * 创建切换按钮
   */
  private createToggleButton(): HTMLElement {
    const button = document.createElement('button');
    button.className = 'tab-group-toggle';
    button.title = this.group.collapsed ? '展开分组' : '折叠分组';
    button.innerHTML = this.getToggleIcon();
    
    button.addEventListener('click', (event) => {
      event.stopPropagation();
      this.toggleGroup();
    });

    return button;
  }

  /**
   * 创建颜色指示器
   */
  private createColorIndicator(): HTMLElement {
    const indicator = document.createElement('div');
    indicator.className = 'tab-group-color';
    indicator.style.backgroundColor = this.getGroupColor();
    return indicator;
  }

  /**
   * 创建标题
   */
  private createTitle(): HTMLElement {
    const title = document.createElement('div');
    title.className = 'tab-group-title';
    title.textContent = this.group.title || '未命名分组';
    title.title = this.group.title || '未命名分组';
    return title;
  }

  /**
   * 创建数量显示
   */
  private createCount(): HTMLElement {
    const count = document.createElement('div');
    count.className = 'tab-group-count';
    count.textContent = `(${this.group.tabIds.length})`;
    return count;
  }

  /**
   * 创建分组操作按钮
   */
  private createGroupActions(): HTMLElement {
    const actions = document.createElement('div');
    actions.className = 'tab-group-actions';

    // 关闭分组按钮
    const closeButton = document.createElement('button');
    closeButton.className = 'tab-group-action close-group-button';
    closeButton.title = '关闭分组中的所有标签页';
    closeButton.innerHTML = this.getCloseGroupIcon();
    closeButton.addEventListener('click', (event) => {
      event.stopPropagation();
      this.closeGroup();
    });

    actions.appendChild(closeButton);
    return actions;
  }

  /**
   * 创建标签页列表
   */
  private createTabsList(): HTMLElement {
    const list = document.createElement('div');
    list.className = 'tab-group-tabs';
    return list;
  }

  /**
   * 渲染标签页
   */
  private renderTabs(): void {
    const tabsList = this.element.querySelector('.tab-group-tabs');
    if (!tabsList) return;

    // 清空现有内容
    tabsList.innerHTML = '';

    // 清理现有组件
    this.tabItems.forEach(item => item.destroy());
    this.tabItems.clear();

    // 按索引排序标签页
    const sortedTabs = this.tabs.sort((a, b) => a.index - b.index);

    // 创建标签页项目
    sortedTabs.forEach(tab => {
      const tabItem = new TabItem(tab);
      this.tabItems.set(tab.id, tabItem);

      // 设置事件监听
      this.setupTabItemEventListeners(tabItem);

      tabsList.appendChild(tabItem.element);
      tabItem.render();
    });
  }

  /**
   * 设置标签页项目事件监听
   */
  private setupTabItemEventListeners(tabItem: TabItem): void {
    tabItem.onClick(() => {
      this.notifyTabClick(tabItem.getTab().id);
    });

    tabItem.onClose(() => {
      this.notifyTabClose(tabItem.getTab().id);
    });

    tabItem.onPin((pinned) => {
      this.notifyTabPin(tabItem.getTab().id, pinned);
    });
  }

  /**
   * 应用折叠状态
   */
  private applyCollapseState(): void {
    const tabsList = this.element.querySelector('.tab-group-tabs');
    const toggleButton = this.element.querySelector('.tab-group-toggle');
    
    if (this.group.collapsed) {
      this.element.classList.add('collapsed');
      if (tabsList) tabsList.style.display = 'none';
      if (toggleButton) toggleButton.innerHTML = this.getToggleIcon();
    } else {
      this.element.classList.remove('collapsed');
      if (tabsList) tabsList.style.display = 'block';
      if (toggleButton) toggleButton.innerHTML = this.getToggleIcon();
    }
  }

  /**
   * 切换分组状态
   */
  private toggleGroup(): void {
    const newCollapsed = !this.group.collapsed;
    this.group.collapsed = newCollapsed;
    this.applyCollapseState();
    this.notifyToggle(newCollapsed);
  }

  /**
   * 获取切换图标
   */
  private getToggleIcon(): string {
    if (this.group.collapsed) {
      return `
        <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
          <path d="M6.22 3.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 1 1-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 0 1 0-1.06z"/>
        </svg>
      `;
    } else {
      return `
        <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
          <path d="M12.78 6.22a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L3.22 7.28a.75.75 0 1 1 1.06-1.06L8 9.94l3.72-3.72a.75.75 0 0 1 1.06 0z"/>
        </svg>
      `;
    }
  }

  /**
   * 获取分组颜色
   */
  private getGroupColor(): string {
    const colorMap: Record<string, string> = {
      'grey': '#8e8e93',
      'blue': '#007aff',
      'red': '#ff3b30',
      'yellow': '#ffcc02',
      'green': '#34c759',
      'pink': '#ff2d92',
      'purple': '#af52de',
      'cyan': '#32d74b',
      'orange': '#ff9500'
    };

    return colorMap[this.group.color] || colorMap['blue'];
  }

  /**
   * 获取关闭分组图标
   */
  private getCloseGroupIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
      </svg>
    `;
  }

  /**
   * 关闭分组中的所有标签页
   */
  private async closeGroup(): Promise<void> {
    const tabCount = this.group.tabIds.length;
    const confirmed = confirm(`确定要关闭分组"${this.group.title || '未命名分组'}"中的所有 ${tabCount} 个标签页吗？`);
    
    if (!confirmed) {
      return;
    }

    try {
      // 逐个关闭标签页
      for (const tabId of this.group.tabIds) {
        this.notifyTabClose(tabId);
      }
    } catch (error) {
      console.error('Error closing group tabs:', error);
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 分组头部右键菜单
    const header = this.element.querySelector('.tab-group-header');
    if (header) {
      header.addEventListener('contextmenu', (event) => {
        this.notifyContextMenu(event);
      });
    }

    // 键盘支持
    this.element.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        const target = event.target as HTMLElement;
        if (target.classList.contains('tab-group-toggle')) {
          event.preventDefault();
          this.toggleGroup();
        }
      }
    });

    // 拖拽支持（为将来的拖拽排序功能预留）
    this.element.addEventListener('dragover', (event) => {
      event.preventDefault();
      this.element.classList.add('drag-over');
    });

    this.element.addEventListener('dragleave', () => {
      this.element.classList.remove('drag-over');
    });

    this.element.addEventListener('drop', (event) => {
      event.preventDefault();
      this.element.classList.remove('drag-over');
      
      const tabId = event.dataTransfer?.getData('text/plain');
      if (tabId) {
        this.handleTabDrop(parseInt(tabId));
      }
    });
  }

  /**
   * 处理标签页拖拽放置
   */
  private handleTabDrop(tabId: number): void {
    // 实现标签页拖拽到分组的逻辑
    console.log(`Tab ${tabId} dropped on group ${this.group.id}`);
  }

  /**
   * 通知切换事件
   */
  private notifyToggle(collapsed: boolean): void {
    this.toggleCallbacks.forEach(callback => {
      try {
        callback(collapsed);
      } catch (error) {
        console.error('Error in toggle callback:', error);
      }
    });
  }

  /**
   * 通知右键菜单事件
   */
  private notifyContextMenu(event: MouseEvent): void {
    this.contextMenuCallbacks.forEach(callback => {
      try {
        callback(event, this.group);
      } catch (error) {
        console.error('Error in context menu callback:', error);
      }
    });
  }

  /**
   * 通知标签页点击事件
   */
  private notifyTabClick(tabId: number): void {
    this.tabClickCallbacks.forEach(callback => {
      try {
        callback(tabId);
      } catch (error) {
        console.error('Error in tab click callback:', error);
      }
    });
  }

  /**
   * 通知标签页关闭事件
   */
  private notifyTabClose(tabId: number): void {
    this.tabCloseCallbacks.forEach(callback => {
      try {
        callback(tabId);
      } catch (error) {
        console.error('Error in tab close callback:', error);
      }
    });
  }

  /**
   * 通知标签页固定事件
   */
  private notifyTabPin(tabId: number, pinned: boolean): void {
    this.tabPinCallbacks.forEach(callback => {
      try {
        callback(tabId, pinned);
      } catch (error) {
        console.error('Error in tab pin callback:', error);
      }
    });
  }
}