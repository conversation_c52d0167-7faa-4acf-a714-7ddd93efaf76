/**
 * Tab Item - 标签页项目组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { TabInfo } from '../../types/index.js';

export class TabItem implements UIComponent {
  element: HTMLElement;
  private tab: TabInfo;
  private clickCallbacks: (() => void)[] = [];
  private contextMenuCallbacks: ((event: MouseEvent, tab: TabInfo) => void)[] = [];
  private closeCallbacks: (() => void)[] = [];
  private pinCallbacks: ((pinned: boolean) => void)[] = [];
  private visible = true;
  private selected = false;

  constructor(tab: TabInfo) {
    this.tab = tab;
    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';

    // 创建图标
    const icon = this.createIcon();
    this.element.appendChild(icon);

    // 创建文本内容
    const text = this.createText();
    this.element.appendChild(text);

    // 创建操作按钮
    const actions = this.createActions();
    this.element.appendChild(actions);

    // 应用状态样式
    this.applyStateStyles();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    this.clickCallbacks = [];
    this.contextMenuCallbacks = [];
    this.closeCallbacks = [];
    this.pinCallbacks = [];

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    if (data && data.tab) {
      this.tab = data.tab;
      this.render();
    }
  }

  /**
   * 获取标签页数据
   */
  getTab(): TabInfo {
    return this.tab;
  }

  /**
   * 更新标签页数据
   */
  updateTab(tab: TabInfo): void {
    this.tab = tab;
    this.render();
  }

  /**
   * 显示项目
   */
  show(): void {
    this.visible = true;
    this.element.style.display = 'flex';
    this.element.classList.remove('hidden');
  }

  /**
   * 隐藏项目
   */
  hide(): void {
    this.visible = false;
    this.element.style.display = 'none';
    this.element.classList.add('hidden');
  }

  /**
   * 检查是否可见
   */
  isVisible(): boolean {
    return this.visible;
  }

  /**
   * 高亮搜索结果
   */
  highlight(query: string): void {
    const textElement = this.element.querySelector('.sidebar-item-text');
    if (!textElement) return;

    const title = this.tab.title;
    const regex = new RegExp(`(${query})`, 'gi');
    const highlightedTitle = title.replace(regex, '<mark>$1</mark>');
    textElement.innerHTML = highlightedTitle;
  }

  /**
   * 清除高亮
   */
  clearHighlight(): void {
    const textElement = this.element.querySelector('.sidebar-item-text');
    if (textElement) {
      textElement.textContent = this.tab.title;
    }
  }

  /**
   * 设置选中状态
   */
  setSelected(selected: boolean): void {
    this.selected = selected;
    if (selected) {
      this.element.classList.add('selected');
      this.element.setAttribute('aria-selected', 'true');
    } else {
      this.element.classList.remove('selected');
      this.element.setAttribute('aria-selected', 'false');
    }
  }

  /**
   * 获取选中状态
   */
  isSelected(): boolean {
    return this.selected;
  }

  /**
   * 添加点击回调
   */
  onClick(callback: () => void): void {
    this.clickCallbacks.push(callback);
  }

  /**
   * 添加右键菜单回调
   */
  onContextMenu(callback: (event: MouseEvent, tab: TabInfo) => void): void {
    this.contextMenuCallbacks.push(callback);
  }

  /**
   * 添加关闭回调
   */
  onClose(callback: () => void): void {
    this.closeCallbacks.push(callback);
  }

  /**
   * 添加固定回调
   */
  onPin(callback: (pinned: boolean) => void): void {
    this.pinCallbacks.push(callback);
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-item tab-item';
    element.setAttribute('role', 'button');
    element.setAttribute('tabindex', '0');
    element.setAttribute('aria-label', `标签页: ${this.tab.title}`);
    element.setAttribute('data-tab-id', this.tab.id.toString());
    
    // 添加无障碍描述
    if (this.tab.pinned) {
      element.setAttribute('aria-description', '已固定的标签页');
    }
    
    if (this.tab.active) {
      element.setAttribute('aria-current', 'page');
    }
    
    return element;
  }

  /**
   * 创建图标
   */
  private createIcon(): HTMLElement {
    const icon = document.createElement('div');
    icon.className = 'sidebar-item-icon tab-icon';

    // 如果正在加载，显示加载动画
    if (this.tab.status === 'loading') {
      icon.innerHTML = this.getLoadingIcon();
      icon.classList.add('loading');
    } else if (this.tab.audible) {
      // 如果有音频播放，显示音频图标
      icon.innerHTML = this.getAudioIcon();
      icon.classList.add('audible');
    } else if (this.tab.favIconUrl) {
      const img = document.createElement('img');
      img.src = this.tab.favIconUrl;
      img.alt = '';
      img.onerror = () => {
        // 如果图标加载失败，显示默认图标
        img.style.display = 'none';
        icon.innerHTML = this.getDefaultIcon();
      };
      icon.appendChild(img);
    } else {
      icon.innerHTML = this.getDefaultIcon();
    }

    return icon;
  }

  /**
   * 创建文本内容
   */
  private createText(): HTMLElement {
    const text = document.createElement('div');
    text.className = 'sidebar-item-text tab-text';
    text.textContent = this.tab.title || this.tab.url;
    text.title = `${this.tab.title}\n${this.tab.url}`;
    return text;
  }

  /**
   * 创建操作按钮
   */
  private createActions(): HTMLElement {
    const actions = document.createElement('div');
    actions.className = 'sidebar-item-actions tab-actions';

    // 固定按钮
    if (this.tab.pinned) {
      const unpinButton = this.createActionButton('取消固定', this.getUnpinIcon(), () => {
        this.notifyPin(false);
      });
      unpinButton.classList.add('pin-button');
      actions.appendChild(unpinButton);
    } else {
      const pinButton = this.createActionButton('固定标签页', this.getPinIcon(), () => {
        this.notifyPin(true);
      });
      pinButton.classList.add('pin-button');
      actions.appendChild(pinButton);
    }

    // 关闭按钮 - 始终显示，更明显的样式
    const closeButtonTitle = this.tab.pinned ? '关闭固定标签页' : '关闭标签页';
    const closeButton = this.createActionButton(closeButtonTitle, this.getCloseIcon(), () => {
      this.notifyClose();
    });
    closeButton.classList.add('close-button');
    
    // 为固定标签页添加特殊样式
    if (this.tab.pinned) {
      closeButton.classList.add('close-pinned-tab');
    }
    
    actions.appendChild(closeButton);

    return actions;
  }

  /**
   * 创建操作按钮
   */
  private createActionButton(title: string, icon: string, onClick: () => void): HTMLElement {
    const button = document.createElement('button');
    button.className = 'sidebar-item-action';
    button.title = title;
    button.innerHTML = icon;
    button.setAttribute('aria-label', title);
    button.addEventListener('click', (event) => {
      event.stopPropagation();
      event.preventDefault();
      onClick();
    });
    
    // 添加键盘支持
    button.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.stopPropagation();
        event.preventDefault();
        onClick();
      }
    });
    
    return button;
  }

  /**
   * 应用状态样式
   */
  private applyStateStyles(): void {
    // 清除所有状态类
    this.element.classList.remove('active', 'pinned', 'loading', 'muted', 'audible');

    // 应用当前状态
    if (this.tab.active) {
      this.element.classList.add('active');
      this.element.setAttribute('aria-current', 'page');
    } else {
      this.element.removeAttribute('aria-current');
    }

    if (this.tab.pinned) {
      this.element.classList.add('pinned');
      this.element.setAttribute('aria-description', '已固定的标签页');
    } else {
      this.element.removeAttribute('aria-description');
    }

    // 检查是否正在加载
    if (this.tab.status === 'loading') {
      this.element.classList.add('loading');
    }

    // 检查是否为静音状态
    if (this.tab.url.startsWith('chrome://')) {
      this.element.classList.add('muted');
    }

    // 检查是否有音频播放
    if (this.tab.audible) {
      this.element.classList.add('audible');
    }

    // 更新aria-label
    let ariaLabel = `标签页: ${this.tab.title}`;
    if (this.tab.pinned) ariaLabel += ' (已固定)';
    if (this.tab.active) ariaLabel += ' (当前)';
    if (this.tab.audible) ariaLabel += ' (播放音频)';
    this.element.setAttribute('aria-label', ariaLabel);
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 点击事件
    this.element.addEventListener('click', (event) => {
      event.preventDefault();
      this.notifyClick();
    });

    // 右键菜单
    this.element.addEventListener('contextmenu', (event) => {
      this.notifyContextMenu(event);
    });

    // 键盘支持
    this.element.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.notifyClick();
      } else if (event.key === 'Delete' || (event.key === 'w' && (event.ctrlKey || event.metaKey))) {
        // Delete键或Ctrl+W/Cmd+W关闭标签页
        event.preventDefault();
        event.stopPropagation();
        this.notifyClose();
      } else if (event.key === 'p' && (event.ctrlKey || event.metaKey)) {
        // Ctrl+P/Cmd+P切换固定状态
        event.preventDefault();
        event.stopPropagation();
        this.notifyPin(!this.tab.pinned);
      }
    });

    // 鼠标悬停效果
    this.element.addEventListener('mouseenter', () => {
      this.element.classList.add('hover');
    });

    this.element.addEventListener('mouseleave', () => {
      this.element.classList.remove('hover');
    });

    // 拖拽支持（为将来的拖拽排序功能预留）
    this.element.draggable = true;
    this.element.addEventListener('dragstart', (event) => {
      event.dataTransfer?.setData('text/plain', this.tab.id.toString());
      this.element.classList.add('dragging');
    });

    this.element.addEventListener('dragend', () => {
      this.element.classList.remove('dragging');
    });
  }

  /**
   * 获取默认图标
   */
  private getDefaultIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2z"/>
        <path d="M8 4a4 4 0 1 1 0 8 4 4 0 0 1 0-8z"/>
      </svg>
    `;
  }

  /**
   * 获取固定图标
   */
  private getPinIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
        <path d="M4.456.734a1.75 1.75 0 0 1 2.826.504l.613 1.327a3.081 3.081 0 0 0 2.084 1.707l2.454.584c1.332.317 1.8 1.972.832 2.94L11.06 10l3.72 3.72a.75.75 0 1 1-1.061 1.06L10 11.06l-2.204 2.205c-.968.968-2.623.5-2.94-.832l-.584-2.454a3.081 3.081 0 0 0-1.707-2.084l-1.327-.613a1.75 1.75 0 0 1-.504-2.826L4.456.734z"/>
      </svg>
    `;
  }

  /**
   * 获取取消固定图标
   */
  private getUnpinIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
        <path d="M9.586 2.414A2 2 0 0 0 8.172 2H7.828a2 2 0 0 0-1.414.586L5 4H4a1 1 0 0 0 0 2v6a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V6a1 1 0 1 0 0-2h-1l-1.414-1.586z"/>
        <path d="M1 1l14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    `;
  }

  /**
   * 获取关闭图标
   */
  private getCloseIcon(): string {
    return `
      <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
      </svg>
    `;
  }

  /**
   * 获取加载图标
   */
  private getLoadingIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" class="loading-spinner">
        <path d="M8 0a8 8 0 0 1 8 8 .5.5 0 0 1-1 0 7 7 0 0 0-7-7 .5.5 0 0 1 0-1z"/>
      </svg>
    `;
  }

  /**
   * 获取音频图标
   */
  private getAudioIcon(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 3a5 5 0 0 0-5 5v1h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V8a6 6 0 1 1 12 0v5a1 1 0 0 1-1 1h-1a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h1V8a5 5 0 0 0-5-5z"/>
      </svg>
    `;
  }

  /**
   * 通知点击事件
   */
  private notifyClick(): void {
    this.clickCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in click callback:', error);
      }
    });
  }

  /**
   * 通知右键菜单事件
   */
  private notifyContextMenu(event: MouseEvent): void {
    this.contextMenuCallbacks.forEach(callback => {
      try {
        callback(event, this.tab);
      } catch (error) {
        console.error('Error in context menu callback:', error);
      }
    });
  }

  /**
   * 通知关闭事件
   */
  private notifyClose(): void {
    // 如果是固定标签页，显示确认对话框
    if (this.tab.pinned) {
      const confirmed = confirm(`确定要关闭固定的标签页"${this.tab.title}"吗？`);
      if (!confirmed) {
        return;
      }
    }

    // 显示关闭通知
    this.showCloseNotification();

    // 添加关闭动画
    this.element.classList.add('closing');
    
    // 延迟执行关闭回调，让动画完成
    setTimeout(() => {
      this.closeCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('Error in close callback:', error);
        }
      });
    }, 200);
  }

  /**
   * 显示关闭通知
   */
  private showCloseNotification(): void {
    const notification = document.createElement('div');
    notification.className = 'tab-close-notification';
    
    const message = document.createElement('span');
    message.textContent = `已关闭: ${this.tab.title}`;
    notification.appendChild(message);
    
    // 添加撤销按钮
    const undoButton = document.createElement('button');
    undoButton.className = 'undo-button';
    undoButton.textContent = '撤销';
    undoButton.addEventListener('click', () => {
      this.undoClose();
      this.hideNotification(notification);
    });
    notification.appendChild(undoButton);
    
    // 添加到侧边栏容器
    const sidebar = document.querySelector('.vertical-sidebar');
    if (sidebar) {
      sidebar.appendChild(notification);
      
      // 显示动画
      setTimeout(() => notification.classList.add('show'), 10);
      
      // 自动隐藏
      setTimeout(() => {
        this.hideNotification(notification);
      }, 5000);
    }
  }

  /**
   * 隐藏通知
   */
  private hideNotification(notification: HTMLElement): void {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  /**
   * 撤销关闭操作
   */
  private async undoClose(): Promise<void> {
    try {
      // 重新打开标签页
      await chrome.runtime.sendMessage({
        type: 'RESTORE_TAB',
        payload: { 
          url: this.tab.url,
          title: this.tab.title,
          pinned: this.tab.pinned
        },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error restoring tab:', error);
    }
  }

  /**
   * 通知固定事件
   */
  private notifyPin(pinned: boolean): void {
    this.pinCallbacks.forEach(callback => {
      try {
        callback(pinned);
      } catch (error) {
        console.error('Error in pin callback:', error);
      }
    });
  }
}