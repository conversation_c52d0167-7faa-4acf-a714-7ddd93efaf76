/**
 * Tabs Section - 标签页区域组件
 */

import { UIComponent } from '../../../interfaces/content.interface.js';
import { TabInfo, TabGroup, UserSettings } from '../../types/index.js';
import { TabItem } from './TabItem.js';
import { TabGroupComponent } from './TabGroupComponent.js';

export class TabsSection implements UIComponent {
  element: HTMLElement;
  private tabs: TabInfo[] = [];
  private tabGroups: TabGroup[] = [];
  private tabItems: Map<number, TabItem> = new Map();
  private groupComponents: Map<number, TabGroupComponent> = new Map();
  private searchQuery = '';
  private settings: UserSettings | null = null;
  private showTabGroups = true;

  constructor() {
    this.element = this.createElement();
    this.setupEventListeners();
  }

  /**
   * 渲染组件
   */
  render(): void {
    this.element.innerHTML = '';
    
    // 添加区域标题
    const title = this.createSectionTitle();
    this.element.appendChild(title);

    // 添加标签页列表容器
    const listContainer = this.createListContainer();
    this.element.appendChild(listContainer);

    // 渲染标签页
    this.renderTabs();
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 销毁所有标签页项目
    this.tabItems.forEach(item => item.destroy());
    this.tabItems.clear();

    // 销毁所有分组组件
    this.groupComponents.forEach(group => group.destroy());
    this.groupComponents.clear();

    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }

  /**
   * 更新组件
   */
  update(data?: any): void {
    this.renderTabs();
  }

  /**
   * 更新标签页数据
   */
  updateTabs(tabs: TabInfo[]): void {
    this.tabs = tabs;
    this.renderTabs();
  }

  /**
   * 更新标签页分组
   */
  updateTabGroups(groups: TabGroup[]): void {
    console.log('TabsSection: 更新标签分组数据', groups);
    this.tabGroups = groups;
    this.renderTabs();
  }

  /**
   * 更新设置
   */
  updateSettings(settings: UserSettings): void {
    this.settings = settings;
    this.showTabGroups = settings.showTabGroups;
    this.renderTabs();
  }

  /**
   * 搜索标签页
   */
  search(query: string): void {
    this.searchQuery = query.toLowerCase();
    this.filterTabs();
  }

  /**
   * 检查是否有可见项目
   */
  hasVisibleItems(): boolean {
    return Array.from(this.tabItems.values()).some(item => item.isVisible()) ||
           Array.from(this.groupComponents.values()).some(group => group.hasVisibleTabs());
  }

  /**
   * 创建DOM元素
   */
  private createElement(): HTMLElement {
    const element = document.createElement('div');
    element.className = 'sidebar-section tabs-section';
    return element;
  }

  /**
   * 创建区域标题
   */
  private createSectionTitle(): HTMLElement {
    const title = document.createElement('div');
    title.className = 'sidebar-section-title';
    title.textContent = '标签页';
    return title;
  }

  /**
   * 创建列表容器
   */
  private createListContainer(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'tabs-list';
    return container;
  }

  /**
   * 渲染标签页
   */
  private renderTabs(): void {
    const listContainer = this.element.querySelector('.tabs-list');
    if (!listContainer) return;

    // 清空现有内容
    listContainer.innerHTML = '';

    // 清理现有组件
    this.tabItems.forEach(item => item.destroy());
    this.tabItems.clear();
    this.groupComponents.forEach(group => group.destroy());
    this.groupComponents.clear();

    if (this.tabs.length === 0) {
      this.showEmptyState(listContainer);
      return;
    }

    // 按分组组织标签页
    const groupedTabs = this.organizeTabsByGroups();

    // 渲染分组（如果启用了分组显示）
    if (this.showTabGroups) {
      groupedTabs.groups.forEach(group => {
        const groupComponent = new TabGroupComponent(group);
        this.groupComponents.set(group.id, groupComponent);
        
        // 传递标签页数据给分组组件
        groupComponent.updateTabs(this.tabs);
        
        // 设置分组事件监听
        this.setupGroupEventListeners(groupComponent);
        
        listContainer.appendChild(groupComponent.element);
        groupComponent.render();
      });
    }

    // 渲染未分组的标签页，或者如果禁用了分组显示，渲染所有标签页
    const tabsToRender = this.showTabGroups ? groupedTabs.ungroupedTabs : this.tabs;
    tabsToRender.forEach(tab => {
      const tabItem = new TabItem(tab);
      this.tabItems.set(tab.id, tabItem);
      
      // 设置标签页事件监听
      this.setupTabEventListeners(tabItem);
      
      listContainer.appendChild(tabItem.element);
      tabItem.render();
    });

    // 应用搜索过滤
    if (this.searchQuery) {
      this.filterTabs();
    }

    // 更新滚动位置到活动标签页
    this.scrollToActiveTab();
  }

  /**
   * 按分组组织标签页
   */
  private organizeTabsByGroups(): { groups: TabGroup[]; ungroupedTabs: TabInfo[] } {
    const groups: TabGroup[] = [];
    const ungroupedTabs: TabInfo[] = [];

    console.log('TabsSection: 组织标签页数据', {
      tabs: this.tabs.length,
      tabGroups: this.tabGroups.length,
      showTabGroups: this.showTabGroups
    });

    // 创建分组映射
    const groupMap = new Map<number, TabGroup>();
    this.tabGroups.forEach(group => {
      groupMap.set(group.id, { ...group, tabIds: [] });
    });

    // 分配标签页到分组
    this.tabs.forEach(tab => {
      if (tab.groupId && tab.groupId !== -1 && groupMap.has(tab.groupId)) {
        const group = groupMap.get(tab.groupId)!;
        group.tabIds.push(tab.id);
      } else {
        ungroupedTabs.push(tab);
      }
    });

    // 只包含有标签页的分组
    groupMap.forEach(group => {
      if (group.tabIds.length > 0) {
        groups.push(group);
      }
    });

    // 按索引排序
    groups.sort((a, b) => {
      const aFirstTab = this.tabs.find(tab => tab.groupId === a.id);
      const bFirstTab = this.tabs.find(tab => tab.groupId === b.id);
      return (aFirstTab?.index || 0) - (bFirstTab?.index || 0);
    });

    ungroupedTabs.sort((a, b) => a.index - b.index);

    console.log('TabsSection: 组织结果', {
      groups: groups.length,
      ungroupedTabs: ungroupedTabs.length,
      groupDetails: groups.map(g => ({ id: g.id, title: g.title, tabCount: g.tabIds.length }))
    });

    return { groups, ungroupedTabs };
  }

  /**
   * 设置标签页事件监听
   */
  private setupTabEventListeners(tabItem: TabItem): void {
    // 点击切换标签页
    tabItem.onClick(() => {
      this.switchToTab(tabItem.getTab().id);
    });

    // 右键菜单
    tabItem.onContextMenu((event, tab) => {
      this.showTabContextMenu(event, tab);
    });

    // 关闭标签页
    tabItem.onClose(() => {
      this.closeTab(tabItem.getTab().id);
    });

    // 固定/取消固定标签页
    tabItem.onPin((pinned) => {
      this.pinTab(tabItem.getTab().id, pinned);
    });
  }

  /**
   * 设置分组事件监听
   */
  private setupGroupEventListeners(groupComponent: TabGroupComponent): void {
    // 展开/折叠分组
    groupComponent.onToggle((collapsed) => {
      this.toggleTabGroup(groupComponent.getGroup().id, collapsed);
    });

    // 分组右键菜单
    groupComponent.onContextMenu((event, group) => {
      this.showGroupContextMenu(event, group);
    });

    // 标签页事件代理
    groupComponent.onTabClick((tabId) => {
      this.switchToTab(tabId);
    });

    groupComponent.onTabClose((tabId) => {
      this.closeTab(tabId);
    });

    groupComponent.onTabPin((tabId, pinned) => {
      this.pinTab(tabId, pinned);
    });
  }

  /**
   * 过滤标签页
   */
  private filterTabs(): void {
    if (!this.searchQuery) {
      // 显示所有项目
      this.tabItems.forEach(item => item.show());
      this.groupComponents.forEach(group => group.show());
      return;
    }

    // 过滤独立标签页
    this.tabItems.forEach(item => {
      const tab = item.getTab();
      const matches = this.matchesSearch(tab.title, tab.url);
      if (matches) {
        item.show();
        item.highlight(this.searchQuery);
      } else {
        item.hide();
      }
    });

    // 过滤分组标签页
    this.groupComponents.forEach(group => {
      const hasVisibleTabs = group.filterTabs(this.searchQuery);
      if (hasVisibleTabs) {
        group.show();
      } else {
        group.hide();
      }
    });
  }

  /**
   * 检查是否匹配搜索
   */
  private matchesSearch(title: string, url: string): boolean {
    const query = this.searchQuery.toLowerCase();
    return title.toLowerCase().includes(query) || 
           url.toLowerCase().includes(query);
  }

  /**
   * 切换到指定标签页
   */
  private async switchToTab(tabId: number): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'SWITCH_TAB',
        payload: { tabId },
        timestamp: Date.now()
      });

      if (!response.success) {
        console.error('Failed to switch tab:', response.error);
      }
    } catch (error) {
      console.error('Error switching tab:', error);
    }
  }

  /**
   * 关闭标签页
   */
  private async closeTab(tabId: number): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'CLOSE_TAB',
        payload: { tabId },
        timestamp: Date.now()
      });

      if (!response.success) {
        console.error('Failed to close tab:', response.error);
      }
    } catch (error) {
      console.error('Error closing tab:', error);
    }
  }

  /**
   * 固定/取消固定标签页
   */
  private async pinTab(tabId: number, pinned: boolean): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'PIN_TAB',
        payload: { tabId, pinned },
        timestamp: Date.now()
      });

      if (!response.success) {
        console.error('Failed to pin tab:', response.error);
      }
    } catch (error) {
      console.error('Error pinning tab:', error);
    }
  }

  /**
   * 切换标签页分组状态
   */
  private async toggleTabGroup(groupId: number, collapsed: boolean): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_TAB_GROUP',
        payload: { groupId, updates: { collapsed } },
        timestamp: Date.now()
      });

      if (!response.success) {
        console.error('Failed to toggle tab group:', response.error);
      }
    } catch (error) {
      console.error('Error toggling tab group:', error);
    }
  }

  /**
   * 显示标签页右键菜单
   */
  private showTabContextMenu(event: MouseEvent, tab: TabInfo): void {
    event.preventDefault();
    
    // 创建上下文菜单
    const menu = this.createTabContextMenu(tab);
    this.showContextMenu(menu, event.clientX, event.clientY);
  }

  /**
   * 显示分组右键菜单
   */
  private showGroupContextMenu(event: MouseEvent, group: TabGroup): void {
    event.preventDefault();
    
    // 创建上下文菜单
    const menu = this.createGroupContextMenu(group);
    this.showContextMenu(menu, event.clientX, event.clientY);
  }

  /**
   * 创建标签页上下文菜单
   */
  private createTabContextMenu(tab: TabInfo): HTMLElement {
    const menu = document.createElement('div');
    menu.className = 'context-menu';

    const items = [
      { text: '新标签页中打开', action: () => this.openTabInNewTab(tab) },
      { text: '复制标签页', action: () => this.duplicateTab(tab.id) },
      { text: tab.pinned ? '取消固定' : '固定标签页', action: () => this.pinTab(tab.id, !tab.pinned) },
      { text: '关闭标签页', action: () => this.closeTab(tab.id) },
      { text: '关闭其他标签页', action: () => this.closeOtherTabs(tab.id) },
      { text: '复制链接', action: () => this.copyTabUrl(tab.url) }
    ];

    items.forEach(item => {
      const menuItem = document.createElement('div');
      menuItem.className = 'context-menu-item';
      menuItem.textContent = item.text;
      menuItem.addEventListener('click', item.action);
      menu.appendChild(menuItem);
    });

    return menu;
  }

  /**
   * 创建分组上下文菜单
   */
  private createGroupContextMenu(group: TabGroup): HTMLElement {
    const menu = document.createElement('div');
    menu.className = 'context-menu';

    const items = [
      { text: group.collapsed ? '展开分组' : '折叠分组', action: () => this.toggleTabGroup(group.id, !group.collapsed) },
      { text: '重命名分组', action: () => this.renameTabGroup(group) },
      { text: '更改颜色', action: () => this.changeGroupColor(group) },
      { text: '取消分组', action: () => this.ungroupTabs(group.id) },
      { text: '关闭分组', action: () => this.closeTabGroup(group.id) }
    ];

    items.forEach(item => {
      const menuItem = document.createElement('div');
      menuItem.className = 'context-menu-item';
      menuItem.textContent = item.text;
      menuItem.addEventListener('click', item.action);
      menu.appendChild(menuItem);
    });

    return menu;
  }

  /**
   * 显示上下文菜单
   */
  private showContextMenu(menu: HTMLElement, x: number, y: number): void {
    // 移除现有菜单
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
      existingMenu.remove();
    }

    // 设置菜单位置
    menu.style.position = 'fixed';
    menu.style.left = `${x}px`;
    menu.style.top = `${y}px`;
    menu.style.zIndex = '2147483648';

    // 添加到页面
    document.body.appendChild(menu);

    // 点击外部关闭菜单
    const closeMenu = (event: MouseEvent) => {
      if (!menu.contains(event.target as Node)) {
        menu.remove();
        document.removeEventListener('click', closeMenu);
      }
    };

    setTimeout(() => {
      document.addEventListener('click', closeMenu);
    }, 0);
  }

  /**
   * 显示空状态
   */
  private showEmptyState(container: Element): void {
    const emptyElement = document.createElement('div');
    emptyElement.className = 'tabs-empty';
    emptyElement.innerHTML = `
      <div class="empty-icon">📑</div>
      <div class="empty-text">没有标签页</div>
      <div class="empty-hint">打开一些网页来查看标签页</div>
    `;
    container.appendChild(emptyElement);
  }

  /**
   * 滚动到活动标签页
   */
  private scrollToActiveTab(): void {
    const activeTab = this.tabs.find(tab => tab.active);
    if (!activeTab) return;

    const activeItem = this.tabItems.get(activeTab.id);
    if (activeItem) {
      activeItem.element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'nearest' 
      });
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 键盘导航支持
    this.element.addEventListener('keydown', (event) => {
      this.handleKeyboardNavigation(event);
    });
  }

  /**
   * 处理键盘导航
   */
  private handleKeyboardNavigation(event: KeyboardEvent): void {
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        this.navigateUp();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.navigateDown();
        break;
      case 'Enter':
        event.preventDefault();
        this.activateSelected();
        break;
    }
  }

  /**
   * 向上导航
   */
  private navigateUp(): void {
    // 实现向上导航逻辑
  }

  /**
   * 向下导航
   */
  private navigateDown(): void {
    // 实现向下导航逻辑
  }

  /**
   * 激活选中项
   */
  private activateSelected(): void {
    // 实现激活选中项逻辑
  }

  // 其他辅助方法
  private async openTabInNewTab(tab: TabInfo): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'CREATE_TAB',
        payload: { url: tab.url },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error opening tab in new tab:', error);
    }
  }

  private async duplicateTab(tabId: number): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'DUPLICATE_TAB',
        payload: { tabId },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error duplicating tab:', error);
    }
  }

  private async closeOtherTabs(keepTabId: number): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'CLOSE_OTHER_TABS',
        payload: { keepTabId },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error closing other tabs:', error);
    }
  }

  private copyTabUrl(url: string): void {
    navigator.clipboard.writeText(url).catch(error => {
      console.error('Error copying URL:', error);
    });
  }

  private async renameTabGroup(group: TabGroup): Promise<void> {
    const newName = prompt('输入新的分组名称:', group.title);
    if (newName && newName !== group.title) {
      try {
        await chrome.runtime.sendMessage({
          type: 'UPDATE_TAB_GROUP',
          payload: { groupId: group.id, updates: { title: newName } },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error renaming tab group:', error);
      }
    }
  }

  private async changeGroupColor(group: TabGroup): Promise<void> {
    // 实现颜色选择逻辑
  }

  private async ungroupTabs(groupId: number): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'UNGROUP_TABS',
        payload: { groupId },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error ungrouping tabs:', error);
    }
  }

  private async closeTabGroup(groupId: number): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'CLOSE_TAB_GROUP',
        payload: { groupId },
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error closing tab group:', error);
    }
  }
}