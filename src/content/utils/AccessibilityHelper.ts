/**
 * Accessibility Helper - 无障碍访问助手
 */

export interface AriaLiveRegion {
  element: HTMLElement;
  politeness: 'polite' | 'assertive';
  atomic: boolean;
}

export interface AccessibilityOptions {
  enableAriaLive?: boolean;
  enableFocusAnnouncements?: boolean;
  enableNavigationAnnouncements?: boolean;
  enableStatusAnnouncements?: boolean;
  announceDelay?: number;
}

export class AccessibilityHelper {
  private options: AccessibilityOptions;
  private liveRegions: Map<string, AriaLiveRegion> = new Map();
  private announceQueue: string[] = [];
  private announceTimer: number | null = null;
  private lastAnnouncement = '';
  private lastAnnouncementTime = 0;

  constructor(options: AccessibilityOptions = {}) {
    this.options = {
      enableAriaLive: true,
      enableFocusAnnouncements: true,
      enableNavigationAnnouncements: true,
      enableStatusAnnouncements: true,
      announceDelay: 100,
      ...options
    };

    this.setupLiveRegions();
  }

  /**
   * 宣布消息
   */
  announce(message: string, politeness: 'polite' | 'assertive' = 'polite'): void {
    if (!this.options.enableAriaLive || !message.trim()) return;

    // 避免重复宣布相同消息
    const now = Date.now();
    if (message === this.lastAnnouncement && now - this.lastAnnouncementTime < 1000) {
      return;
    }

    this.lastAnnouncement = message;
    this.lastAnnouncementTime = now;

    // 添加到队列
    this.announceQueue.push(message);

    // 清除之前的定时器
    if (this.announceTimer) {
      clearTimeout(this.announceTimer);
    }

    // 延迟宣布以避免过于频繁
    this.announceTimer = window.setTimeout(() => {
      this.processAnnounceQueue(politeness);
      this.announceTimer = null;
    }, this.options.announceDelay);
  }

  /**
   * 宣布焦点变化
   */
  announceFocus(elementType: string, elementText: string, level?: number): void {
    if (!this.options.enableFocusAnnouncements) return;

    let message = `${this.getTypeDescription(elementType)} ${elementText}`;
    
    if (level !== undefined && level > 0) {
      message += `, 层级 ${level}`;
    }

    this.announce(message, 'polite');
  }

  /**
   * 宣布导航动作
   */
  announceNavigation(action: string, elementType: string, elementText: string): void {
    if (!this.options.enableNavigationAnnouncements) return;

    const actionDescription = this.getActionDescription(action);
    const typeDescription = this.getTypeDescription(elementType);
    
    const message = `${actionDescription} ${typeDescription} ${elementText}`;
    this.announce(message, 'polite');
  }

  /**
   * 宣布状态变化
   */
  announceStatus(status: string, context?: string): void {
    if (!this.options.enableStatusAnnouncements) return;

    let message = status;
    if (context) {
      message = `${context}: ${status}`;
    }

    this.announce(message, 'assertive');
  }

  /**
   * 宣布搜索结果
   */
  announceSearchResults(count: number, query: string): void {
    let message: string;
    
    if (count === 0) {
      message = `没有找到与"${query}"相关的结果`;
    } else if (count === 1) {
      message = `找到 1 个与"${query}"相关的结果`;
    } else {
      message = `找到 ${count} 个与"${query}"相关的结果`;
    }

    this.announce(message, 'polite');
  }

  /**
   * 宣布列表变化
   */
  announceListChange(action: 'added' | 'removed' | 'moved', itemType: string, itemText: string, position?: number): void {
    let message: string;
    const typeDescription = this.getTypeDescription(itemType);

    switch (action) {
      case 'added':
        message = `已添加 ${typeDescription} ${itemText}`;
        if (position !== undefined) {
          message += ` 到位置 ${position}`;
        }
        break;
      case 'removed':
        message = `已删除 ${typeDescription} ${itemText}`;
        break;
      case 'moved':
        message = `已移动 ${typeDescription} ${itemText}`;
        if (position !== undefined) {
          message += ` 到位置 ${position}`;
        }
        break;
    }

    this.announce(message, 'polite');
  }

  /**
   * 设置元素的ARIA属性
   */
  setAriaAttributes(element: HTMLElement, attributes: Record<string, string>): void {
    Object.entries(attributes).forEach(([key, value]) => {
      if (key.startsWith('aria-') || key === 'role') {
        element.setAttribute(key, value);
      } else {
        element.setAttribute(`aria-${key}`, value);
      }
    });
  }

  /**
   * 设置元素的描述
   */
  setElementDescription(element: HTMLElement, description: string): void {
    const descId = `desc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建描述元素
    const descElement = document.createElement('div');
    descElement.id = descId;
    descElement.className = 'sr-only';
    descElement.textContent = description;
    descElement.style.position = 'absolute';
    descElement.style.width = '1px';
    descElement.style.height = '1px';
    descElement.style.padding = '0';
    descElement.style.margin = '-1px';
    descElement.style.overflow = 'hidden';
    descElement.style.clip = 'rect(0, 0, 0, 0)';
    descElement.style.whiteSpace = 'nowrap';
    descElement.style.border = '0';

    // 添加到DOM
    document.body.appendChild(descElement);

    // 设置aria-describedby
    const existingDescribedBy = element.getAttribute('aria-describedby');
    const describedBy = existingDescribedBy ? `${existingDescribedBy} ${descId}` : descId;
    element.setAttribute('aria-describedby', describedBy);
  }

  /**
   * 创建键盘快捷键提示
   */
  createShortcutHint(element: HTMLElement, shortcuts: string[]): void {
    if (shortcuts.length === 0) return;

    const hint = shortcuts.length === 1 
      ? `快捷键: ${shortcuts[0]}`
      : `快捷键: ${shortcuts.join(', ')}`;

    this.setElementDescription(element, hint);
  }

  /**
   * 设置加载状态
   */
  setLoadingState(element: HTMLElement, loading: boolean, message?: string): void {
    if (loading) {
      element.setAttribute('aria-busy', 'true');
      if (message) {
        this.announce(message, 'polite');
      }
    } else {
      element.removeAttribute('aria-busy');
      if (message) {
        this.announce(message, 'polite');
      }
    }
  }

  /**
   * 设置错误状态
   */
  setErrorState(element: HTMLElement, hasError: boolean, errorMessage?: string): void {
    if (hasError) {
      element.setAttribute('aria-invalid', 'true');
      if (errorMessage) {
        this.setElementDescription(element, `错误: ${errorMessage}`);
        this.announce(`错误: ${errorMessage}`, 'assertive');
      }
    } else {
      element.removeAttribute('aria-invalid');
    }
  }

  /**
   * 创建进度指示器
   */
  createProgressIndicator(current: number, total: number, label?: string): string {
    const percentage = Math.round((current / total) * 100);
    let message = `进度 ${current} / ${total} (${percentage}%)`;
    
    if (label) {
      message = `${label}: ${message}`;
    }

    return message;
  }

  /**
   * 设置展开/折叠状态
   */
  setExpandedState(element: HTMLElement, expanded: boolean): void {
    element.setAttribute('aria-expanded', expanded.toString());
    
    // 宣布状态变化
    const elementText = element.textContent || element.getAttribute('aria-label') || '项目';
    const state = expanded ? '已展开' : '已折叠';
    this.announce(`${elementText} ${state}`, 'polite');
  }

  /**
   * 设置选中状态
   */
  setSelectedState(element: HTMLElement, selected: boolean): void {
    element.setAttribute('aria-selected', selected.toString());
    
    if (selected) {
      const elementText = element.textContent || element.getAttribute('aria-label') || '项目';
      this.announce(`已选中 ${elementText}`, 'polite');
    }
  }

  /**
   * 创建面包屑导航提示
   */
  createBreadcrumbHint(path: string[]): string {
    if (path.length === 0) return '';
    
    if (path.length === 1) {
      return `当前位置: ${path[0]}`;
    }
    
    return `当前位置: ${path.join(' > ')}`;
  }

  /**
   * 销毁助手
   */
  destroy(): void {
    // 清除定时器
    if (this.announceTimer) {
      clearTimeout(this.announceTimer);
      this.announceTimer = null;
    }

    // 移除live regions
    this.liveRegions.forEach(region => {
      if (region.element.parentNode) {
        region.element.parentNode.removeChild(region.element);
      }
    });
    this.liveRegions.clear();

    // 清除队列
    this.announceQueue = [];
  }

  /**
   * 设置live regions
   */
  private setupLiveRegions(): void {
    if (!this.options.enableAriaLive) return;

    // 创建polite live region
    const politeRegion = this.createLiveRegion('polite', true);
    this.liveRegions.set('polite', politeRegion);

    // 创建assertive live region
    const assertiveRegion = this.createLiveRegion('assertive', true);
    this.liveRegions.set('assertive', assertiveRegion);
  }

  /**
   * 创建live region
   */
  private createLiveRegion(politeness: 'polite' | 'assertive', atomic: boolean): AriaLiveRegion {
    const element = document.createElement('div');
    element.setAttribute('aria-live', politeness);
    element.setAttribute('aria-atomic', atomic.toString());
    element.className = 'sr-only';
    element.style.position = 'absolute';
    element.style.width = '1px';
    element.style.height = '1px';
    element.style.padding = '0';
    element.style.margin = '-1px';
    element.style.overflow = 'hidden';
    element.style.clip = 'rect(0, 0, 0, 0)';
    element.style.whiteSpace = 'nowrap';
    element.style.border = '0';

    document.body.appendChild(element);

    return { element, politeness, atomic };
  }

  /**
   * 处理宣布队列
   */
  private processAnnounceQueue(politeness: 'polite' | 'assertive'): void {
    if (this.announceQueue.length === 0) return;

    const region = this.liveRegions.get(politeness);
    if (!region) return;

    // 合并队列中的消息
    const message = this.announceQueue.join('. ');
    this.announceQueue = [];

    // 清空region然后设置新消息（确保屏幕阅读器读取）
    region.element.textContent = '';
    setTimeout(() => {
      region.element.textContent = message;
    }, 10);
  }

  /**
   * 获取类型描述
   */
  private getTypeDescription(type: string): string {
    const typeMap: Record<string, string> = {
      'tab': '标签页',
      'bookmark': '收藏夹',
      'folder': '文件夹',
      'search': '搜索框',
      'filter': '过滤器',
      'button': '按钮',
      'link': '链接',
      'item': '项目'
    };

    return typeMap[type] || type;
  }

  /**
   * 获取动作描述
   */
  private getActionDescription(action: string): string {
    const actionMap: Record<string, string> = {
      'next': '移动到下一个',
      'previous': '移动到上一个',
      'first': '移动到第一个',
      'last': '移动到最后一个',
      'expand': '展开',
      'collapse': '折叠',
      'toggle': '切换',
      'activate': '激活',
      'select': '选择',
      'open': '打开',
      'close': '关闭',
      'pageUp': '向上翻页到',
      'pageDown': '向下翻页到',
      'typeAhead': '类型提前搜索到'
    };

    return actionMap[action] || action;
  }

  /**
   * 创建屏幕阅读器专用文本
   */
  static createScreenReaderText(text: string): HTMLElement {
    const element = document.createElement('span');
    element.className = 'sr-only';
    element.textContent = text;
    element.style.position = 'absolute';
    element.style.width = '1px';
    element.style.height = '1px';
    element.style.padding = '0';
    element.style.margin = '-1px';
    element.style.overflow = 'hidden';
    element.style.clip = 'rect(0, 0, 0, 0)';
    element.style.whiteSpace = 'nowrap';
    element.style.border = '0';
    
    return element;
  }

  /**
   * 检查是否启用了屏幕阅读器
   */
  static isScreenReaderActive(): boolean {
    // 检查常见的屏幕阅读器指示器
    return !!(
      window.navigator.userAgent.includes('NVDA') ||
      window.navigator.userAgent.includes('JAWS') ||
      window.speechSynthesis ||
      (window as any).speechSynthesis ||
      document.body.classList.contains('screen-reader-active')
    );
  }

  /**
   * 设置高对比度模式
   */
  static setHighContrastMode(enabled: boolean): void {
    if (enabled) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }
  }

  /**
   * 检查是否启用了高对比度模式
   */
  static isHighContrastMode(): boolean {
    return window.matchMedia('(prefers-contrast: high)').matches ||
           document.body.classList.contains('high-contrast');
  }

  /**
   * 检查是否启用了减少动画
   */
  static isPrefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }
}