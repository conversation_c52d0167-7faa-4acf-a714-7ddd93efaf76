/**
 * Animation Manager - 动画管理器
 */

import { AnimationManager as IAnimationManager } from '../../interfaces/content.interface.js';
import { SIDEBAR_CONFIG } from '../types/index.js';

export class AnimationManager implements IAnimationManager {
  private activeAnimations: Map<HTMLElement, Animation> = new Map();
  private initialized = false;

  /**
   * 初始化动画管理器
   */
  initialize(): void {
    this.initialized = true;
    console.log('Animation Manager initialized');
  }

  /**
   * 展开侧边栏动画
   */
  async expandSidebar(duration?: number): Promise<void> {
    const sidebar = document.getElementById('chrome-vertical-sidebar');
    if (!sidebar) return;

    const animationDuration = duration || SIDEBAR_CONFIG.animations.expandDuration;
    const easing = SIDEBAR_CONFIG.animations.easing;

    // 取消现有动画
    this.cancelAnimation(sidebar);

    // 获取当前和目标宽度
    const currentWidth = sidebar.offsetWidth;
    const targetWidth = SIDEBAR_CONFIG.dimensions.expandedWidth;

    if (currentWidth === targetWidth) return;

    // 创建动画
    const animation = sidebar.animate([
      { width: `${currentWidth}px` },
      { width: `${targetWidth}px` }
    ], {
      duration: animationDuration,
      easing: easing,
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(sidebar, animation);

    // 等待动画完成
    try {
      await animation.finished;
      sidebar.style.width = `${targetWidth}px`;
    } catch (error) {
      // 动画被取消
      console.debug('Expand animation cancelled');
    } finally {
      this.activeAnimations.delete(sidebar);
    }
  }

  /**
   * 收起侧边栏动画
   */
  async collapseSidebar(duration?: number): Promise<void> {
    const sidebar = document.getElementById('chrome-vertical-sidebar');
    if (!sidebar) return;

    const animationDuration = duration || SIDEBAR_CONFIG.animations.collapseDuration;
    const easing = SIDEBAR_CONFIG.animations.easing;

    // 取消现有动画
    this.cancelAnimation(sidebar);

    // 获取当前和目标宽度
    const currentWidth = sidebar.offsetWidth;
    const targetWidth = SIDEBAR_CONFIG.dimensions.collapsedWidth;

    if (currentWidth === targetWidth) return;

    // 创建动画
    const animation = sidebar.animate([
      { width: `${currentWidth}px` },
      { width: `${targetWidth}px` }
    ], {
      duration: animationDuration,
      easing: easing,
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(sidebar, animation);

    // 等待动画完成
    try {
      await animation.finished;
      sidebar.style.width = `${targetWidth}px`;
    } catch (error) {
      // 动画被取消
      console.debug('Collapse animation cancelled');
    } finally {
      this.activeAnimations.delete(sidebar);
    }
  }

  /**
   * 淡入动画
   */
  async fadeIn(element: HTMLElement, duration?: number): Promise<void> {
    const animationDuration = duration || 200;

    // 取消现有动画
    this.cancelAnimation(element);

    // 设置初始状态
    element.style.opacity = '0';
    element.style.display = 'block';

    // 创建动画
    const animation = element.animate([
      { opacity: 0 },
      { opacity: 1 }
    ], {
      duration: animationDuration,
      easing: 'ease-out',
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
      element.style.opacity = '1';
    } catch (error) {
      console.debug('Fade in animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 淡出动画
   */
  async fadeOut(element: HTMLElement, duration?: number): Promise<void> {
    const animationDuration = duration || 200;

    // 取消现有动画
    this.cancelAnimation(element);

    // 创建动画
    const animation = element.animate([
      { opacity: 1 },
      { opacity: 0 }
    ], {
      duration: animationDuration,
      easing: 'ease-in',
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
      element.style.opacity = '0';
      element.style.display = 'none';
    } catch (error) {
      console.debug('Fade out animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 滑入动画
   */
  async slideIn(element: HTMLElement, direction: 'left' | 'right', duration?: number): Promise<void> {
    const animationDuration = duration || 300;
    const translateX = direction === 'left' ? '-100%' : '100%';

    // 取消现有动画
    this.cancelAnimation(element);

    // 设置初始状态
    element.style.transform = `translateX(${translateX})`;
    element.style.display = 'block';

    // 创建动画
    const animation = element.animate([
      { transform: `translateX(${translateX})` },
      { transform: 'translateX(0)' }
    ], {
      duration: animationDuration,
      easing: SIDEBAR_CONFIG.animations.easing,
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
      element.style.transform = 'translateX(0)';
    } catch (error) {
      console.debug('Slide in animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 滑出动画
   */
  async slideOut(element: HTMLElement, direction: 'left' | 'right', duration?: number): Promise<void> {
    const animationDuration = duration || 300;
    const translateX = direction === 'left' ? '-100%' : '100%';

    // 取消现有动画
    this.cancelAnimation(element);

    // 创建动画
    const animation = element.animate([
      { transform: 'translateX(0)' },
      { transform: `translateX(${translateX})` }
    ], {
      duration: animationDuration,
      easing: SIDEBAR_CONFIG.animations.easing,
      fill: 'forwards'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
      element.style.transform = `translateX(${translateX})`;
      element.style.display = 'none';
    } catch (error) {
      console.debug('Slide out animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 弹性缩放动画
   */
  async bounceScale(element: HTMLElement, scale = 1.1, duration = 200): Promise<void> {
    // 取消现有动画
    this.cancelAnimation(element);

    // 创建动画
    const animation = element.animate([
      { transform: 'scale(1)' },
      { transform: `scale(${scale})` },
      { transform: 'scale(1)' }
    ], {
      duration: duration,
      easing: 'ease-out'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
    } catch (error) {
      console.debug('Bounce scale animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 脉冲动画
   */
  async pulse(element: HTMLElement, intensity = 0.1, duration = 1000): Promise<void> {
    // 取消现有动画
    this.cancelAnimation(element);

    // 创建动画
    const animation = element.animate([
      { opacity: 1 },
      { opacity: 1 - intensity },
      { opacity: 1 }
    ], {
      duration: duration,
      easing: 'ease-in-out',
      iterations: Infinity
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);
  }

  /**
   * 停止脉冲动画
   */
  stopPulse(element: HTMLElement): void {
    this.cancelAnimation(element);
    element.style.opacity = '1';
  }

  /**
   * 摇摆动画
   */
  async shake(element: HTMLElement, intensity = 10, duration = 500): Promise<void> {
    // 取消现有动画
    this.cancelAnimation(element);

    // 创建动画
    const animation = element.animate([
      { transform: 'translateX(0)' },
      { transform: `translateX(-${intensity}px)` },
      { transform: `translateX(${intensity}px)` },
      { transform: `translateX(-${intensity}px)` },
      { transform: `translateX(${intensity}px)` },
      { transform: 'translateX(0)' }
    ], {
      duration: duration,
      easing: 'ease-in-out'
    });

    // 保存动画引用
    this.activeAnimations.set(element, animation);

    // 等待动画完成
    try {
      await animation.finished;
    } catch (error) {
      console.debug('Shake animation cancelled');
    } finally {
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 取消元素的动画
   */
  cancelAnimation(element: HTMLElement): void {
    const animation = this.activeAnimations.get(element);
    if (animation) {
      animation.cancel();
      this.activeAnimations.delete(element);
    }
  }

  /**
   * 取消所有动画
   */
  cancelAllAnimations(): void {
    this.activeAnimations.forEach(animation => {
      animation.cancel();
    });
    this.activeAnimations.clear();
  }

  /**
   * 检查元素是否正在动画
   */
  isAnimating(element: HTMLElement): boolean {
    return this.activeAnimations.has(element);
  }

  /**
   * 获取活动动画数量
   */
  getActiveAnimationCount(): number {
    return this.activeAnimations.size;
  }

  /**
   * 设置动画性能偏好
   */
  setPerformanceMode(mode: 'smooth' | 'fast' | 'disabled'): void {
    const root = document.documentElement;
    
    switch (mode) {
      case 'smooth':
        root.style.setProperty('--animation-duration-multiplier', '1');
        break;
      case 'fast':
        root.style.setProperty('--animation-duration-multiplier', '0.5');
        break;
      case 'disabled':
        root.style.setProperty('--animation-duration-multiplier', '0');
        break;
    }
  }

  /**
   * 检查是否支持动画
   */
  supportsAnimations(): boolean {
    return typeof Element.prototype.animate === 'function';
  }

  /**
   * 检查用户是否偏好减少动画
   */
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * 销毁动画管理器
   */
  destroy(): void {
    this.cancelAllAnimations();
    this.initialized = false;
    console.log('Animation Manager destroyed');
  }
}