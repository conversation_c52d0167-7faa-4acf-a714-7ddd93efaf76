/**
 * Bookmark Drag Handler - 收藏夹拖拽处理器
 */

import { BookmarkNode } from '../types/index.js';
import { DragManager, DragData } from './DragManager.js';

export class BookmarkDragHandler {
  private dragManager: DragManager;
  private dragPreview: HTMLElement | null = null;

  constructor(dragManager: DragManager) {
    this.dragManager = dragManager;
  }

  /**
   * 处理收藏夹拖拽开始
   */
  handleBookmarkDragStart(element: HTMLElement, bookmark: BookmarkNode, event: DragEvent): void {
    const dragData: DragData = {
      type: 'bookmark',
      id: bookmark.id,
      sourceIndex: 0, // 将在实际使用时设置
      data: bookmark
    };

    // 创建拖拽预览
    this.createBookmarkDragPreview(bookmark);

    // 开始拖拽
    this.dragManager.startDrag(element, dragData, event);

    console.log('Bookmark drag started:', bookmark.title);
  }

  /**
   * 处理收藏夹拖拽到文件夹
   */
  async handleBookmarkDropOnFolder(dragData: DragData, targetFolder: BookmarkNode, dropIndex: number): Promise<void> {
    if (dragData.type !== 'bookmark') return;

    try {
      const bookmarkId = dragData.id as string;
      const sourceBookmark = dragData.data as BookmarkNode;

      // 检查是否拖拽到自己或子文件夹
      if (this.isDropOnSelfOrChild(sourceBookmark, targetFolder)) {
        console.warn('Cannot drop bookmark on itself or child folder');
        return;
      }

      // 移动收藏夹
      await this.moveBookmark(bookmarkId, targetFolder.id, dropIndex);

      console.log(`Moved bookmark "${sourceBookmark.title}" to folder "${targetFolder.title}"`);
    } catch (error) {
      console.error('Error dropping bookmark on folder:', error);
    }
  }

  /**
   * 处理收藏夹拖拽排序
   */
  async handleBookmarkReorder(dragData: DragData, targetBookmark: BookmarkNode, dropIndex: number): Promise<void> {
    if (dragData.type !== 'bookmark') return;

    try {
      const sourceBookmarkId = dragData.id as string;
      const sourceBookmark = dragData.data as BookmarkNode;

      // 如果是同一个收藏夹，不处理
      if (sourceBookmarkId === targetBookmark.id) {
        return;
      }

      // 获取目标收藏夹的父文件夹
      const targetParentId = targetBookmark.parentId || '1'; // 默认为书签栏

      // 移动收藏夹
      await this.moveBookmark(sourceBookmarkId, targetParentId, dropIndex);

      console.log(`Reordered bookmark "${sourceBookmark.title}" to position ${dropIndex}`);
    } catch (error) {
      console.error('Error reordering bookmark:', error);
    }
  }

  /**
   * 处理文件夹拖拽
   */
  handleFolderDragStart(element: HTMLElement, folder: BookmarkNode, event: DragEvent): void {
    const dragData: DragData = {
      type: 'bookmark', // 文件夹也是收藏夹的一种
      id: folder.id,
      sourceIndex: 0,
      data: folder
    };

    // 创建文件夹拖拽预览
    this.createFolderDragPreview(folder);

    // 开始拖拽
    this.dragManager.startDrag(element, dragData, event);

    console.log('Folder drag started:', folder.title);
  }

  /**
   * 处理文件夹拖拽到文件夹
   */
  async handleFolderDropOnFolder(dragData: DragData, targetFolder: BookmarkNode, dropIndex: number): Promise<void> {
    if (dragData.type !== 'bookmark') return;

    try {
      const sourceFolderId = dragData.id as string;
      const sourceFolder = dragData.data as BookmarkNode;

      // 检查是否拖拽到自己或子文件夹
      if (this.isDropOnSelfOrChild(sourceFolder, targetFolder)) {
        console.warn('Cannot drop folder on itself or child folder');
        return;
      }

      // 移动文件夹
      await this.moveBookmark(sourceFolderId, targetFolder.id, dropIndex);

      console.log(`Moved folder "${sourceFolder.title}" to folder "${targetFolder.title}"`);
    } catch (error) {
      console.error('Error dropping folder on folder:', error);
    }
  }

  /**
   * 检查是否可以放置
   */
  canDropOnTarget(dragData: DragData, targetBookmark: BookmarkNode): boolean {
    if (dragData.type !== 'bookmark') return false;

    const sourceBookmark = dragData.data as BookmarkNode;

    // 不能拖拽到自己
    if (sourceBookmark.id === targetBookmark.id) {
      return false;
    }

    // 如果目标是文件夹，检查是否拖拽到子文件夹
    if (targetBookmark.children) {
      return !this.isDropOnSelfOrChild(sourceBookmark, targetBookmark);
    }

    return true;
  }

  /**
   * 获取拖拽预览元素
   */
  getDragPreview(): HTMLElement | null {
    return this.dragPreview;
  }

  /**
   * 创建收藏夹拖拽预览
   */
  private createBookmarkDragPreview(bookmark: BookmarkNode): void {
    this.dragPreview = document.createElement('div');
    this.dragPreview.className = 'bookmark-drag-preview';
    this.dragPreview.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      padding: 8px 12px;
      background: #ffffff;
      border: 1px solid #d0d7de;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      white-space: nowrap;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      z-index: 9999;
      pointer-events: none;
    `;

    // 添加图标和文本
    const icon = this.createBookmarkIcon(bookmark);
    const text = document.createElement('span');
    text.textContent = bookmark.title;
    text.style.marginLeft = '8px';

    this.dragPreview.appendChild(icon);
    this.dragPreview.appendChild(text);

    document.body.appendChild(this.dragPreview);
  }

  /**
   * 创建文件夹拖拽预览
   */
  private createFolderDragPreview(folder: BookmarkNode): void {
    this.dragPreview = document.createElement('div');
    this.dragPreview.className = 'folder-drag-preview';
    this.dragPreview.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      padding: 8px 12px;
      background: #f6f8fa;
      border: 1px solid #d0d7de;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      white-space: nowrap;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      z-index: 9999;
      pointer-events: none;
    `;

    // 添加文件夹图标和文本
    const icon = this.createFolderIcon();
    const text = document.createElement('span');
    text.textContent = folder.title;
    text.style.marginLeft = '8px';

    const count = document.createElement('span');
    count.textContent = ` (${folder.children?.length || 0})`;
    count.style.opacity = '0.7';
    count.style.fontSize = '12px';

    this.dragPreview.appendChild(icon);
    this.dragPreview.appendChild(text);
    this.dragPreview.appendChild(count);

    document.body.appendChild(this.dragPreview);
  }

  /**
   * 创建收藏夹图标
   */
  private createBookmarkIcon(bookmark: BookmarkNode): HTMLElement {
    const icon = document.createElement('div');
    icon.style.cssText = `
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
    `;

    if (bookmark.url) {
      // 尝试获取网站图标
      const domain = this.extractDomain(bookmark.url);
      if (domain) {
        const img = document.createElement('img');
        img.src = `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
        img.style.cssText = 'width: 16px; height: 16px;';
        img.onerror = () => {
          icon.innerHTML = this.getDefaultBookmarkIconSVG();
        };
        icon.appendChild(img);
      } else {
        icon.innerHTML = this.getDefaultBookmarkIconSVG();
      }
    } else {
      icon.innerHTML = this.getDefaultBookmarkIconSVG();
    }

    return icon;
  }

  /**
   * 创建文件夹图标
   */
  private createFolderIcon(): HTMLElement {
    const icon = document.createElement('div');
    icon.style.cssText = `
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      color: #0969da;
    `;
    icon.innerHTML = this.getFolderIconSVG();
    return icon;
  }

  /**
   * 检查是否拖拽到自己或子文件夹
   */
  private isDropOnSelfOrChild(source: BookmarkNode, target: BookmarkNode): boolean {
    // 不能拖拽到自己
    if (source.id === target.id) {
      return true;
    }

    // 如果源是文件夹，检查目标是否是其子文件夹
    if (source.children) {
      return this.isChildFolder(source, target);
    }

    return false;
  }

  /**
   * 检查是否为子文件夹
   */
  private isChildFolder(parent: BookmarkNode, target: BookmarkNode): boolean {
    if (!parent.children) return false;

    for (const child of parent.children) {
      if (child.id === target.id) {
        return true;
      }
      if (child.children && this.isChildFolder(child, target)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 移动收藏夹
   */
  private async moveBookmark(bookmarkId: string, parentId: string, index?: number): Promise<void> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'MOVE_BOOKMARK',
        payload: { bookmarkId, parentId, index },
        timestamp: Date.now()
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to move bookmark');
      }
    } catch (error) {
      console.error('Error moving bookmark:', error);
      throw error;
    }
  }

  /**
   * 提取域名
   */
  private extractDomain(url: string): string | null {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取默认收藏夹图标SVG
   */
  private getDefaultBookmarkIconSVG(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M2 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v13.5a.5.5 0 0 1-.777.416L8 13.101l-5.223 2.815A.5.5 0 0 1 2 15.5V2zm2-1a1 1 0 0 0-1 1v12.566l4.723-2.482a.5.5 0 0 1 .554 0L13 14.566V2a1 1 0 0 0-1-1H4z"/>
      </svg>
    `;
  }

  /**
   * 获取文件夹图标SVG
   */
  private getFolderIconSVG(): string {
    return `
      <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M.5 3l.04.87a1.99 1.99 0 0 0-.342 1.311l.637 7A2 2 0 0 0 2.826 14h10.348a2 2 0 0 0 1.991-1.819l.637-7A1.99 1.99 0 0 0 15.46 3.87L15.5 3H.5zM2.19 4h11.62a1 1 0 0 1 .996 1.09L14.17 12.1a1 1 0 0 1-.996.9H2.826a1 1 0 0 1-.995-.9L1.194 5.09A1 1 0 0 1 2.19 4z"/>
        <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h3A1.5 1.5 0 0 1 6 1.5V3H1.5A1.5 1.5 0 0 1 0 1.5z"/>
      </svg>
    `;
  }

  /**
   * 清理拖拽预览
   */
  cleanup(): void {
    if (this.dragPreview && this.dragPreview.parentNode) {
      this.dragPreview.parentNode.removeChild(this.dragPreview);
      this.dragPreview = null;
    }
  }
}