/**
 * Bookmark Editor - 收藏夹编辑器
 */

import { BookmarkNode } from '../types/index.js';

export interface BookmarkEditData {
  title: string;
  url?: string;
}

export class BookmarkEditor {
  private modal: HTMLElement | null = null;
  private currentBookmark: BookmarkNode | null = null;
  private onSaveCallback: ((data: BookmarkEditData) => void) | null = null;
  private onCancelCallback: (() => void) | null = null;

  /**
   * 编辑收藏夹
   */
  editBookmark(bookmark: BookmarkNode, onSave: (data: BookmarkEditData) => void, onCancel?: () => void): void {
    this.currentBookmark = bookmark;
    this.onSaveCallback = onSave;
    this.onCancelCallback = onCancel || null;

    this.showEditModal(bookmark);
  }

  /**
   * 创建新收藏夹
   */
  createBookmark(parentFolder?: BookmarkNode, onSave?: (data: BookmarkEditData) => void, onCancel?: () => void): void {
    this.currentBookmark = null;
    this.onSaveCallback = onSave || null;
    this.onCancelCallback = onCancel || null;

    const newBookmark: BookmarkNode = {
      id: '',
      title: '',
      url: '',
      parentId: parentFolder?.id || '1'
    };

    this.showEditModal(newBookmark, true);
  }

  /**
   * 创建新文件夹
   */
  createFolder(parentFolder?: BookmarkNode, onSave?: (data: BookmarkEditData) => void, onCancel?: () => void): void {
    this.currentBookmark = null;
    this.onSaveCallback = onSave || null;
    this.onCancelCallback = onCancel || null;

    const newFolder: BookmarkNode = {
      id: '',
      title: '',
      parentId: parentFolder?.id || '1',
      children: []
    };

    this.showEditModal(newFolder, true);
  }

  /**
   * 隐藏编辑器
   */
  hide(): void {
    if (this.modal) {
      this.modal.remove();
      this.modal = null;
    }
    this.currentBookmark = null;
    this.onSaveCallback = null;
    this.onCancelCallback = null;
  }

  /**
   * 显示编辑模态框
   */
  private showEditModal(bookmark: BookmarkNode, isNew = false): void {
    // 移除现有模态框
    this.hide();

    // 创建模态框
    this.modal = this.createModal();
    
    // 创建表单
    const form = this.createForm(bookmark, isNew);
    this.modal.appendChild(form);

    // 添加到页面
    document.body.appendChild(this.modal);

    // 聚焦第一个输入框
    const firstInput = this.modal.querySelector('input') as HTMLInputElement;
    if (firstInput) {
      firstInput.focus();
      firstInput.select();
    }

    // 设置事件监听
    this.setupModalEventListeners();
  }

  /**
   * 创建模态框
   */
  private createModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'bookmark-editor-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2147483647;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    return modal;
  }

  /**
   * 创建表单
   */
  private createForm(bookmark: BookmarkNode, isNew: boolean): HTMLElement {
    const form = document.createElement('div');
    form.className = 'bookmark-editor-form';
    form.style.cssText = `
      background: #ffffff;
      border-radius: 8px;
      padding: 24px;
      width: 400px;
      max-width: 90vw;
      box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
    `;

    // 标题
    const title = document.createElement('h3');
    title.style.cssText = `
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      color: #24292f;
    `;
    
    if (bookmark.children !== undefined) {
      title.textContent = isNew ? '新建文件夹' : '编辑文件夹';
    } else {
      title.textContent = isNew ? '新建收藏夹' : '编辑收藏夹';
    }
    
    form.appendChild(title);

    // 名称输入框
    const nameGroup = this.createInputGroup('名称', 'text', bookmark.title, 'bookmark-title');
    form.appendChild(nameGroup);

    // URL输入框（仅收藏夹需要）
    if (bookmark.children === undefined) {
      const urlGroup = this.createInputGroup('网址', 'url', bookmark.url || '', 'bookmark-url');
      form.appendChild(urlGroup);
    }

    // 按钮组
    const buttonGroup = this.createButtonGroup();
    form.appendChild(buttonGroup);

    return form;
  }

  /**
   * 创建输入组
   */
  private createInputGroup(label: string, type: string, value: string, id: string): HTMLElement {
    const group = document.createElement('div');
    group.style.cssText = 'margin-bottom: 16px;';

    const labelElement = document.createElement('label');
    labelElement.textContent = label;
    labelElement.setAttribute('for', id);
    labelElement.style.cssText = `
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      font-weight: 500;
      color: #24292f;
    `;

    const input = document.createElement('input');
    input.type = type;
    input.id = id;
    input.value = value;
    input.style.cssText = `
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d0d7de;
      border-radius: 6px;
      font-size: 14px;
      line-height: 1.4;
      color: #24292f;
      background: #ffffff;
      box-sizing: border-box;
    `;

    input.addEventListener('focus', () => {
      input.style.borderColor = '#0969da';
      input.style.boxShadow = '0 0 0 3px rgba(9, 105, 218, 0.1)';
    });

    input.addEventListener('blur', () => {
      input.style.borderColor = '#d0d7de';
      input.style.boxShadow = 'none';
    });

    group.appendChild(labelElement);
    group.appendChild(input);

    return group;
  }

  /**
   * 创建按钮组
   */
  private createButtonGroup(): HTMLElement {
    const group = document.createElement('div');
    group.style.cssText = `
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 24px;
    `;

    // 取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.style.cssText = `
      padding: 8px 16px;
      border: 1px solid #d0d7de;
      border-radius: 6px;
      background: #ffffff;
      color: #24292f;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.15s ease;
    `;

    cancelButton.addEventListener('mouseenter', () => {
      cancelButton.style.backgroundColor = '#f6f8fa';
    });

    cancelButton.addEventListener('mouseleave', () => {
      cancelButton.style.backgroundColor = '#ffffff';
    });

    cancelButton.addEventListener('click', () => {
      this.handleCancel();
    });

    // 保存按钮
    const saveButton = document.createElement('button');
    saveButton.textContent = '保存';
    saveButton.style.cssText = `
      padding: 8px 16px;
      border: 1px solid #0969da;
      border-radius: 6px;
      background: #0969da;
      color: #ffffff;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.15s ease;
    `;

    saveButton.addEventListener('mouseenter', () => {
      saveButton.style.backgroundColor = '#0860ca';
    });

    saveButton.addEventListener('mouseleave', () => {
      saveButton.style.backgroundColor = '#0969da';
    });

    saveButton.addEventListener('click', () => {
      this.handleSave();
    });

    group.appendChild(cancelButton);
    group.appendChild(saveButton);

    return group;
  }

  /**
   * 设置模态框事件监听
   */
  private setupModalEventListeners(): void {
    if (!this.modal) return;

    // 点击背景关闭
    this.modal.addEventListener('click', (event) => {
      if (event.target === this.modal) {
        this.handleCancel();
      }
    });

    // ESC键关闭
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        this.handleCancel();
        document.removeEventListener('keydown', handleKeydown);
      } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
        this.handleSave();
        document.removeEventListener('keydown', handleKeydown);
      }
    };

    document.addEventListener('keydown', handleKeydown);

    // 表单提交
    const form = this.modal.querySelector('.bookmark-editor-form');
    if (form) {
      form.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault();
          this.handleSave();
        }
      });
    }
  }

  /**
   * 处理保存
   */
  private handleSave(): void {
    if (!this.modal) return;

    const titleInput = this.modal.querySelector('#bookmark-title') as HTMLInputElement;
    const urlInput = this.modal.querySelector('#bookmark-url') as HTMLInputElement;

    if (!titleInput) return;

    const title = titleInput.value.trim();
    if (!title) {
      titleInput.focus();
      titleInput.style.borderColor = '#d1242f';
      return;
    }

    const data: BookmarkEditData = {
      title
    };

    if (urlInput) {
      const url = urlInput.value.trim();
      if (url && !this.isValidUrl(url)) {
        urlInput.focus();
        urlInput.style.borderColor = '#d1242f';
        return;
      }
      data.url = url;
    }

    if (this.onSaveCallback) {
      this.onSaveCallback(data);
    }

    this.hide();
  }

  /**
   * 处理取消
   */
  private handleCancel(): void {
    if (this.onCancelCallback) {
      this.onCancelCallback();
    }
    this.hide();
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      // 尝试添加协议
      try {
        new URL('https://' + url);
        return true;
      } catch {
        return false;
      }
    }
  }

  /**
   * 规范化URL
   */
  normalizeUrl(url: string): string {
    if (!url) return '';
    
    try {
      new URL(url);
      return url;
    } catch {
      // 尝试添加协议
      try {
        new URL('https://' + url);
        return 'https://' + url;
      } catch {
        return url;
      }
    }
  }
}