/**
 * Context Menu Manager - 上下文菜单管理器
 */

export interface ContextMenuItem {
  text: string;
  icon?: string;
  action: () => void;
  disabled?: boolean;
  separator?: boolean;
  submenu?: ContextMenuItem[];
}

export class ContextMenuManager {
  private currentMenu: HTMLElement | null = null;
  private menuId = 0;

  /**
   * 显示上下文菜单
   */
  showMenu(items: ContextMenuItem[], x: number, y: number): void {
    // 隐藏现有菜单
    this.hideMenu();

    // 创建菜单
    this.currentMenu = this.createMenu(items);
    
    // 设置位置
    this.positionMenu(this.currentMenu, x, y);
    
    // 添加到页面
    document.body.appendChild(this.currentMenu);
    
    // 设置事件监听
    this.setupMenuEventListeners();
    
    // 聚焦第一个可用项目
    this.focusFirstItem();
  }

  /**
   * 隐藏上下文菜单
   */
  hideMenu(): void {
    if (this.currentMenu) {
      if (this.currentMenu.parentNode) {
        this.currentMenu.parentNode.removeChild(this.currentMenu);
      }
      this.currentMenu = null;
    }
  }

  /**
   * 检查菜单是否显示
   */
  isMenuVisible(): boolean {
    return this.currentMenu !== null;
  }

  /**
   * 创建菜单元素
   */
  private createMenu(items: ContextMenuItem[]): HTMLElement {
    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.id = `context-menu-${++this.menuId}`;
    menu.setAttribute('role', 'menu');
    menu.setAttribute('tabindex', '-1');

    // 应用样式
    this.applyMenuStyles(menu);

    // 创建菜单项
    items.forEach((item, index) => {
      if (item.separator) {
        const separator = this.createSeparator();
        menu.appendChild(separator);
      } else {
        const menuItem = this.createMenuItem(item, index);
        menu.appendChild(menuItem);
      }
    });

    return menu;
  }

  /**
   * 创建菜单项
   */
  private createMenuItem(item: ContextMenuItem, index: number): HTMLElement {
    const menuItem = document.createElement('div');
    menuItem.className = 'context-menu-item';
    menuItem.setAttribute('role', 'menuitem');
    menuItem.setAttribute('tabindex', '0');
    menuItem.setAttribute('data-index', index.toString());

    if (item.disabled) {
      menuItem.classList.add('disabled');
      menuItem.setAttribute('aria-disabled', 'true');
    }

    // 创建图标
    if (item.icon) {
      const icon = document.createElement('span');
      icon.className = 'context-menu-icon';
      icon.innerHTML = item.icon;
      menuItem.appendChild(icon);
    }

    // 创建文本
    const text = document.createElement('span');
    text.className = 'context-menu-text';
    text.textContent = item.text;
    menuItem.appendChild(text);

    // 创建子菜单指示器
    if (item.submenu && item.submenu.length > 0) {
      const arrow = document.createElement('span');
      arrow.className = 'context-menu-arrow';
      arrow.innerHTML = '▶';
      menuItem.appendChild(arrow);
      menuItem.classList.add('has-submenu');
    }

    // 设置点击事件
    if (!item.disabled) {
      menuItem.addEventListener('click', (event) => {
        event.stopPropagation();
        if (!item.submenu) {
          item.action();
          this.hideMenu();
        }
      });

      // 键盘支持
      menuItem.addEventListener('keydown', (event) => {
        this.handleMenuItemKeydown(event, item);
      });
    }

    return menuItem;
  }

  /**
   * 创建分隔符
   */
  private createSeparator(): HTMLElement {
    const separator = document.createElement('div');
    separator.className = 'context-menu-separator';
    separator.setAttribute('role', 'separator');
    return separator;
  }

  /**
   * 应用菜单样式
   */
  private applyMenuStyles(menu: HTMLElement): void {
    menu.style.position = 'fixed';
    menu.style.zIndex = '2147483648';
    menu.style.backgroundColor = '#ffffff';
    menu.style.border = '1px solid #d0d7de';
    menu.style.borderRadius = '6px';
    menu.style.boxShadow = '0 8px 24px rgba(140, 149, 159, 0.2)';
    menu.style.padding = '4px 0';
    menu.style.minWidth = '160px';
    menu.style.maxWidth = '320px';
    menu.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    menu.style.fontSize = '14px';
    menu.style.lineHeight = '1.4';
    menu.style.color = '#24292f';
    menu.style.userSelect = 'none';

    // 暗色主题支持
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      menu.style.backgroundColor = '#21262d';
      menu.style.borderColor = '#30363d';
      menu.style.color = '#f0f6fc';
    }

    // 添加CSS样式
    if (!document.getElementById('context-menu-styles')) {
      const styles = document.createElement('style');
      styles.id = 'context-menu-styles';
      styles.textContent = this.getMenuCSS();
      document.head.appendChild(styles);
    }
  }

  /**
   * 获取菜单CSS样式
   */
  private getMenuCSS(): string {
    return `
      .context-menu-item {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        cursor: pointer;
        transition: background-color 0.1s ease;
      }

      .context-menu-item:hover:not(.disabled) {
        background-color: #f6f8fa;
      }

      .context-menu-item:focus:not(.disabled) {
        background-color: #f6f8fa;
        outline: none;
      }

      .context-menu-item.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .context-menu-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .context-menu-text {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .context-menu-arrow {
        margin-left: 8px;
        font-size: 10px;
        opacity: 0.7;
      }

      .context-menu-separator {
        height: 1px;
        background-color: #d0d7de;
        margin: 4px 0;
      }

      @media (prefers-color-scheme: dark) {
        .context-menu-item:hover:not(.disabled),
        .context-menu-item:focus:not(.disabled) {
          background-color: #30363d;
        }

        .context-menu-separator {
          background-color: #30363d;
        }
      }
    `;
  }

  /**
   * 定位菜单
   */
  private positionMenu(menu: HTMLElement, x: number, y: number): void {
    // 临时添加到页面以获取尺寸
    menu.style.visibility = 'hidden';
    document.body.appendChild(menu);
    
    const menuRect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 计算最佳位置
    let left = x;
    let top = y;

    // 防止菜单超出右边界
    if (left + menuRect.width > viewportWidth) {
      left = viewportWidth - menuRect.width - 10;
    }

    // 防止菜单超出下边界
    if (top + menuRect.height > viewportHeight) {
      top = viewportHeight - menuRect.height - 10;
    }

    // 防止菜单超出左边界
    if (left < 10) {
      left = 10;
    }

    // 防止菜单超出上边界
    if (top < 10) {
      top = 10;
    }

    // 应用位置
    menu.style.left = `${left}px`;
    menu.style.top = `${top}px`;
    menu.style.visibility = 'visible';
  }

  /**
   * 设置菜单事件监听
   */
  private setupMenuEventListeners(): void {
    if (!this.currentMenu) return;

    // 点击外部关闭菜单
    const closeOnClickOutside = (event: MouseEvent) => {
      if (this.currentMenu && !this.currentMenu.contains(event.target as Node)) {
        this.hideMenu();
        document.removeEventListener('click', closeOnClickOutside);
      }
    };

    // 延迟添加事件监听，避免立即触发
    setTimeout(() => {
      document.addEventListener('click', closeOnClickOutside);
    }, 0);

    // ESC键关闭菜单
    const closeOnEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        this.hideMenu();
        document.removeEventListener('keydown', closeOnEscape);
      }
    };

    document.addEventListener('keydown', closeOnEscape);

    // 菜单键盘导航
    this.currentMenu.addEventListener('keydown', (event) => {
      this.handleMenuKeydown(event);
    });
  }

  /**
   * 处理菜单键盘事件
   */
  private handleMenuKeydown(event: KeyboardEvent): void {
    if (!this.currentMenu) return;

    const items = Array.from(this.currentMenu.querySelectorAll('.context-menu-item:not(.disabled)'));
    const currentIndex = items.findIndex(item => item === document.activeElement);

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = (currentIndex + 1) % items.length;
        (items[nextIndex] as HTMLElement).focus();
        break;

      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = currentIndex <= 0 ? items.length - 1 : currentIndex - 1;
        (items[prevIndex] as HTMLElement).focus();
        break;

      case 'Home':
        event.preventDefault();
        (items[0] as HTMLElement).focus();
        break;

      case 'End':
        event.preventDefault();
        (items[items.length - 1] as HTMLElement).focus();
        break;
    }
  }

  /**
   * 处理菜单项键盘事件
   */
  private handleMenuItemKeydown(event: KeyboardEvent, item: ContextMenuItem): void {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (!item.submenu) {
          item.action();
          this.hideMenu();
        }
        break;
    }
  }

  /**
   * 聚焦第一个项目
   */
  private focusFirstItem(): void {
    if (!this.currentMenu) return;

    const firstItem = this.currentMenu.querySelector('.context-menu-item:not(.disabled)') as HTMLElement;
    if (firstItem) {
      firstItem.focus();
    }
  }

  /**
   * 销毁上下文菜单管理器
   */
  destroy(): void {
    this.hideMenu();
    
    // 移除样式
    const styles = document.getElementById('context-menu-styles');
    if (styles) {
      styles.remove();
    }
  }
}