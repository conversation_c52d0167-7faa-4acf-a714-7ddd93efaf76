/**
 * Drag Manager - 拖拽管理器
 * 处理标签页和收藏夹的拖拽排序功能
 */

export interface DragData {
  type: 'tab' | 'bookmark' | 'group';
  id: string | number;
  sourceIndex: number;
  sourceGroupId?: number;
  data: any;
}

export interface DropZone {
  element: HTMLElement;
  type: 'tab' | 'bookmark' | 'group';
  groupId?: number;
  index?: number;
  onDrop: (dragData: DragData, dropIndex: number) => void;
}

export class DragManager {
  private dragData: DragData | null = null;
  private dropZones: DropZone[] = [];
  private dragPreview: HTMLElement | null = null;
  private dragOverlay: HTMLElement | null = null;
  private isDragging = false;

  /**
   * 初始化拖拽管理器
   */
  initialize(): void {
    this.setupGlobalEventListeners();
    console.log('Drag Manager initialized');
  }

  /**
   * 开始拖拽
   */
  startDrag(element: HTMLElement, dragData: DragData, event: DragEvent): void {
    this.dragData = dragData;
    this.isDragging = true;

    // 创建拖拽预览
    this.createDragPreview(element);

    // 设置拖拽数据
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('application/json', JSON.stringify(dragData));
      
      // 设置自定义拖拽图像
      if (this.dragPreview) {
        event.dataTransfer.setDragImage(this.dragPreview, 0, 0);
      }
    }

    // 添加拖拽样式
    element.classList.add('dragging');
    document.body.classList.add('drag-active');

    // 显示拖拽覆盖层
    this.showDragOverlay();

    console.log('Drag started:', dragData);
  }

  /**
   * 结束拖拽
   */
  endDrag(element: HTMLElement): void {
    if (!this.isDragging) return;

    // 清理拖拽状态
    this.isDragging = false;
    this.dragData = null;

    // 移除样式
    element.classList.remove('dragging');
    document.body.classList.remove('drag-active');

    // 清理拖拽预览和覆盖层
    this.cleanupDragPreview();
    this.hideDragOverlay();

    // 清理所有放置区域的样式
    this.dropZones.forEach(zone => {
      zone.element.classList.remove('drag-over', 'drag-valid', 'drag-invalid');
    });

    console.log('Drag ended');
  }

  /**
   * 注册放置区域
   */
  registerDropZone(dropZone: DropZone): void {
    this.dropZones.push(dropZone);
    this.setupDropZoneEventListeners(dropZone);
  }

  /**
   * 取消注册放置区域
   */
  unregisterDropZone(dropZone: DropZone): void {
    const index = this.dropZones.indexOf(dropZone);
    if (index > -1) {
      this.dropZones.splice(index, 1);
      this.cleanupDropZoneEventListeners(dropZone);
    }
  }

  /**
   * 清理所有放置区域
   */
  clearDropZones(): void {
    this.dropZones.forEach(zone => {
      this.cleanupDropZoneEventListeners(zone);
    });
    this.dropZones = [];
  }

  /**
   * 检查是否正在拖拽
   */
  isDragActive(): boolean {
    return this.isDragging;
  }

  /**
   * 获取当前拖拽数据
   */
  getCurrentDragData(): DragData | null {
    return this.dragData;
  }

  /**
   * 创建拖拽预览
   */
  private createDragPreview(element: HTMLElement): void {
    this.dragPreview = element.cloneNode(true) as HTMLElement;
    this.dragPreview.style.position = 'absolute';
    this.dragPreview.style.top = '-1000px';
    this.dragPreview.style.left = '-1000px';
    this.dragPreview.style.width = `${element.offsetWidth}px`;
    this.dragPreview.style.height = `${element.offsetHeight}px`;
    this.dragPreview.style.opacity = '0.8';
    this.dragPreview.style.transform = 'rotate(5deg)';
    this.dragPreview.style.pointerEvents = 'none';
    this.dragPreview.style.zIndex = '9999';
    
    document.body.appendChild(this.dragPreview);
  }

  /**
   * 清理拖拽预览
   */
  private cleanupDragPreview(): void {
    if (this.dragPreview && this.dragPreview.parentNode) {
      this.dragPreview.parentNode.removeChild(this.dragPreview);
      this.dragPreview = null;
    }
  }

  /**
   * 显示拖拽覆盖层
   */
  private showDragOverlay(): void {
    this.dragOverlay = document.createElement('div');
    this.dragOverlay.className = 'drag-overlay';
    this.dragOverlay.style.position = 'fixed';
    this.dragOverlay.style.top = '0';
    this.dragOverlay.style.left = '0';
    this.dragOverlay.style.width = '100%';
    this.dragOverlay.style.height = '100%';
    this.dragOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
    this.dragOverlay.style.pointerEvents = 'none';
    this.dragOverlay.style.zIndex = '2147483646';
    
    document.body.appendChild(this.dragOverlay);
  }

  /**
   * 隐藏拖拽覆盖层
   */
  private hideDragOverlay(): void {
    if (this.dragOverlay && this.dragOverlay.parentNode) {
      this.dragOverlay.parentNode.removeChild(this.dragOverlay);
      this.dragOverlay = null;
    }
  }

  /**
   * 设置全局事件监听
   */
  private setupGlobalEventListeners(): void {
    // 监听拖拽结束事件
    document.addEventListener('dragend', (event) => {
      if (this.isDragging) {
        this.endDrag(event.target as HTMLElement);
      }
    });

    // 监听ESC键取消拖拽
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.isDragging) {
        this.endDrag(event.target as HTMLElement);
      }
    });
  }

  /**
   * 设置放置区域事件监听
   */
  private setupDropZoneEventListeners(dropZone: DropZone): void {
    const element = dropZone.element;

    // 拖拽进入
    const dragEnterHandler = (event: DragEvent) => {
      event.preventDefault();
      if (this.canDrop(dropZone)) {
        element.classList.add('drag-over', 'drag-valid');
      } else {
        element.classList.add('drag-over', 'drag-invalid');
      }
    };

    // 拖拽悬停
    const dragOverHandler = (event: DragEvent) => {
      event.preventDefault();
      if (this.canDrop(dropZone)) {
        event.dataTransfer!.dropEffect = 'move';
      } else {
        event.dataTransfer!.dropEffect = 'none';
      }
    };

    // 拖拽离开
    const dragLeaveHandler = (event: DragEvent) => {
      // 检查是否真的离开了元素（而不是进入子元素）
      if (!element.contains(event.relatedTarget as Node)) {
        element.classList.remove('drag-over', 'drag-valid', 'drag-invalid');
      }
    };

    // 放置
    const dropHandler = (event: DragEvent) => {
      event.preventDefault();
      element.classList.remove('drag-over', 'drag-valid', 'drag-invalid');

      if (!this.canDrop(dropZone)) {
        return;
      }

      try {
        const dragDataStr = event.dataTransfer?.getData('application/json');
        if (dragDataStr) {
          const dragData = JSON.parse(dragDataStr) as DragData;
          const dropIndex = this.calculateDropIndex(dropZone, event);
          dropZone.onDrop(dragData, dropIndex);
        }
      } catch (error) {
        console.error('Error handling drop:', error);
      }
    };

    // 保存事件处理器引用以便后续清理
    (element as any)._dragHandlers = {
      dragEnterHandler,
      dragOverHandler,
      dragLeaveHandler,
      dropHandler
    };

    // 添加事件监听
    element.addEventListener('dragenter', dragEnterHandler);
    element.addEventListener('dragover', dragOverHandler);
    element.addEventListener('dragleave', dragLeaveHandler);
    element.addEventListener('drop', dropHandler);
  }

  /**
   * 清理放置区域事件监听
   */
  private cleanupDropZoneEventListeners(dropZone: DropZone): void {
    const element = dropZone.element;
    const handlers = (element as any)._dragHandlers;

    if (handlers) {
      element.removeEventListener('dragenter', handlers.dragEnterHandler);
      element.removeEventListener('dragover', handlers.dragOverHandler);
      element.removeEventListener('dragleave', handlers.dragLeaveHandler);
      element.removeEventListener('drop', handlers.dropHandler);
      delete (element as any)._dragHandlers;
    }
  }

  /**
   * 检查是否可以放置
   */
  private canDrop(dropZone: DropZone): boolean {
    if (!this.dragData) return false;

    // 检查类型兼容性
    if (this.dragData.type !== dropZone.type) {
      return false;
    }

    // 检查是否拖拽到自己
    if (this.dragData.type === 'tab' && dropZone.type === 'tab') {
      // 如果是同一个标签页，不允许放置
      if (this.dragData.id === dropZone.element.getAttribute('data-tab-id')) {
        return false;
      }
    }

    return true;
  }

  /**
   * 计算放置索引
   */
  private calculateDropIndex(dropZone: DropZone, event: DragEvent): number {
    if (dropZone.index !== undefined) {
      return dropZone.index;
    }

    // 根据鼠标位置计算插入位置
    const rect = dropZone.element.getBoundingClientRect();
    const mouseY = event.clientY;
    const elementCenter = rect.top + rect.height / 2;

    // 如果鼠标在元素上半部分，插入到前面；否则插入到后面
    return mouseY < elementCenter ? 0 : 1;
  }

  /**
   * 销毁拖拽管理器
   */
  destroy(): void {
    this.clearDropZones();
    this.cleanupDragPreview();
    this.hideDragOverlay();
    
    if (this.isDragging) {
      document.body.classList.remove('drag-active');
    }

    this.dragData = null;
    this.isDragging = false;
    
    console.log('Drag Manager destroyed');
  }
}