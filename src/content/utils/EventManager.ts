/**
 * Event Manager - 事件管理器
 */

import { EventManager as IEventManager } from '../../interfaces/content.interface.js';

interface EventListenerInfo {
  element: HTMLElement | Window | Document;
  event: string;
  handler: EventListener;
  options?: boolean | AddEventListenerOptions;
}

export class EventManager implements IEventManager {
  private listeners: EventListenerInfo[] = [];
  private delegatedListeners: Map<HTMLElement, Map<string, Map<string, EventListener>>> = new Map();

  /**
   * 添加事件监听器
   */
  addEventListener(element: HTMLElement | Window | Document, event: string, handler: EventListener, options?: boolean | AddEventListenerOptions): void {
    try {
      element.addEventListener(event, handler, options);
      
      // 保存监听器信息以便后续移除
      this.listeners.push({
        element: element as HTMLElement,
        event,
        handler,
        options
      });
    } catch (error) {
      console.error('Error adding event listener:', error);
    }
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(element: HTMLElement | Window | Document, event: string, handler: EventListener): void {
    try {
      element.removeEventListener(event, handler);
      
      // 从保存的监听器列表中移除
      const index = this.listeners.findIndex(listener => 
        listener.element === element && 
        listener.event === event && 
        listener.handler === handler
      );
      
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    } catch (error) {
      console.error('Error removing event listener:', error);
    }
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(): void {
    try {
      // 移除所有普通监听器
      this.listeners.forEach(({ element, event, handler }) => {
        try {
          element.removeEventListener(event, handler);
        } catch (error) {
          console.error('Error removing listener:', error);
        }
      });
      
      // 移除所有委托监听器
      this.delegatedListeners.forEach((eventMap, parent) => {
        eventMap.forEach((selectorMap, event) => {
          selectorMap.forEach((handler, selector) => {
            try {
              parent.removeEventListener(event, handler);
            } catch (error) {
              console.error('Error removing delegated listener:', error);
            }
          });
        });
      });

      // 清空所有记录
      this.listeners = [];
      this.delegatedListeners.clear();
      
      console.log('All event listeners removed');
    } catch (error) {
      console.error('Error removing all listeners:', error);
    }
  }

  /**
   * 事件委托
   */
  delegateEvent(parent: HTMLElement, selector: string, event: string, handler: EventListener): void {
    try {
      // 创建委托处理器
      const delegatedHandler = (e: Event) => {
        const target = e.target as HTMLElement;
        const matchedElement = target.closest(selector);
        
        if (matchedElement && parent.contains(matchedElement)) {
          // 修改事件对象的currentTarget
          Object.defineProperty(e, 'currentTarget', {
            value: matchedElement,
            configurable: true
          });
          
          handler.call(matchedElement, e);
        }
      };

      // 添加事件监听器
      parent.addEventListener(event, delegatedHandler);

      // 保存委托监听器信息
      if (!this.delegatedListeners.has(parent)) {
        this.delegatedListeners.set(parent, new Map());
      }
      
      const eventMap = this.delegatedListeners.get(parent)!;
      if (!eventMap.has(event)) {
        eventMap.set(event, new Map());
      }
      
      const selectorMap = eventMap.get(event)!;
      selectorMap.set(selector, delegatedHandler);

    } catch (error) {
      console.error('Error setting up event delegation:', error);
    }
  }

  /**
   * 移除委托事件
   */
  removeDelegatedEvent(parent: HTMLElement, selector: string, event: string): void {
    try {
      const eventMap = this.delegatedListeners.get(parent);
      if (!eventMap) return;

      const selectorMap = eventMap.get(event);
      if (!selectorMap) return;

      const handler = selectorMap.get(selector);
      if (!handler) return;

      // 移除事件监听器
      parent.removeEventListener(event, handler);

      // 清理记录
      selectorMap.delete(selector);
      if (selectorMap.size === 0) {
        eventMap.delete(event);
      }
      if (eventMap.size === 0) {
        this.delegatedListeners.delete(parent);
      }

    } catch (error) {
      console.error('Error removing delegated event:', error);
    }
  }

  /**
   * 添加一次性事件监听器
   */
  addEventListenerOnce(element: HTMLElement | Window | Document, event: string, handler: EventListener): void {
    const onceHandler = (e: Event) => {
      handler(e);
      this.removeEventListener(element, event, onceHandler);
    };
    
    this.addEventListener(element, event, onceHandler);
  }

  /**
   * 添加防抖事件监听器
   */
  addDebouncedEventListener(element: HTMLElement | Window | Document, event: string, handler: EventListener, delay = 300): void {
    let timeoutId: NodeJS.Timeout;
    
    const debouncedHandler = (e: Event) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => handler(e), delay);
    };
    
    this.addEventListener(element, event, debouncedHandler);
  }

  /**
   * 添加节流事件监听器
   */
  addThrottledEventListener(element: HTMLElement | Window | Document, event: string, handler: EventListener, delay = 100): void {
    let lastCall = 0;
    
    const throttledHandler = (e: Event) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        handler(e);
      }
    };
    
    this.addEventListener(element, event, throttledHandler);
  }

  /**
   * 创建自定义事件
   */
  createCustomEvent(name: string, detail?: any): CustomEvent {
    return new CustomEvent(name, {
      detail,
      bubbles: true,
      cancelable: true
    });
  }

  /**
   * 触发自定义事件
   */
  dispatchCustomEvent(element: HTMLElement, name: string, detail?: any): boolean {
    const event = this.createCustomEvent(name, detail);
    return element.dispatchEvent(event);
  }

  /**
   * 等待事件发生
   */
  waitForEvent(element: HTMLElement | Window | Document, event: string, timeout?: number): Promise<Event> {
    return new Promise((resolve, reject) => {
      let timeoutId: NodeJS.Timeout;
      
      const handler = (e: Event) => {
        if (timeoutId) clearTimeout(timeoutId);
        this.removeEventListener(element, event, handler);
        resolve(e);
      };
      
      this.addEventListener(element, event, handler);
      
      if (timeout) {
        timeoutId = setTimeout(() => {
          this.removeEventListener(element, event, handler);
          reject(new Error(`Event '${event}' timeout after ${timeout}ms`));
        }, timeout);
      }
    });
  }

  /**
   * 检查元素是否支持某个事件
   */
  supportsEvent(element: HTMLElement, event: string): boolean {
    return `on${event}` in element;
  }

  /**
   * 获取事件路径（事件冒泡路径）
   */
  getEventPath(event: Event): EventTarget[] {
    if (event.composedPath) {
      return event.composedPath();
    }
    
    // 降级实现
    const path: EventTarget[] = [];
    let target = event.target as Node;
    
    while (target) {
      path.push(target);
      target = target.parentNode!;
    }
    
    return path;
  }

  /**
   * 阻止事件冒泡和默认行为
   */
  stopEvent(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
  }

  /**
   * 检查事件是否来自键盘
   */
  isKeyboardEvent(event: Event): event is KeyboardEvent {
    return event instanceof KeyboardEvent;
  }

  /**
   * 检查事件是否来自鼠标
   */
  isMouseEvent(event: Event): event is MouseEvent {
    return event instanceof MouseEvent;
  }

  /**
   * 检查事件是否来自触摸
   */
  isTouchEvent(event: Event): event is TouchEvent {
    return event instanceof TouchEvent;
  }

  /**
   * 获取鼠标/触摸位置
   */
  getEventPosition(event: MouseEvent | TouchEvent): { x: number; y: number } {
    if (this.isTouchEvent(event)) {
      const touch = event.touches[0] || event.changedTouches[0];
      return { x: touch.clientX, y: touch.clientY };
    } else {
      return { x: event.clientX, y: event.clientY };
    }
  }

  /**
   * 检查是否按下了修饰键
   */
  hasModifierKey(event: KeyboardEvent | MouseEvent): boolean {
    return event.ctrlKey || event.shiftKey || event.altKey || event.metaKey;
  }

  /**
   * 获取当前监听器数量
   */
  getListenerCount(): number {
    let count = this.listeners.length;
    
    this.delegatedListeners.forEach(eventMap => {
      eventMap.forEach(selectorMap => {
        count += selectorMap.size;
      });
    });
    
    return count;
  }

  /**
   * 获取监听器统计信息
   */
  getListenerStats(): { total: number; direct: number; delegated: number } {
    let delegatedCount = 0;
    
    this.delegatedListeners.forEach(eventMap => {
      eventMap.forEach(selectorMap => {
        delegatedCount += selectorMap.size;
      });
    });
    
    return {
      total: this.listeners.length + delegatedCount,
      direct: this.listeners.length,
      delegated: delegatedCount
    };
  }
}