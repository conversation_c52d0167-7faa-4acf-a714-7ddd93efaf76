/**
 * Focus Manager - 焦点管理器
 */

export interface FocusableElement {
  element: HTMLElement;
  priority: number;
  group?: string;
}

export interface FocusTrapOptions {
  initialFocus?: HTMLElement | string;
  returnFocus?: HTMLElement;
  allowOutsideClick?: boolean;
  escapeDeactivates?: boolean;
}

export class FocusManager {
  private focusStack: HTMLElement[] = [];
  private activeTrap: FocusTrap | null = null;
  private focusableSelectors = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');

  /**
   * 保存当前焦点
   */
  saveFocus(): void {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement && activeElement !== document.body) {
      this.focusStack.push(activeElement);
    }
  }

  /**
   * 恢复焦点
   */
  restoreFocus(): boolean {
    const lastFocused = this.focusStack.pop();
    if (lastFocused && this.isElementFocusable(lastFocused)) {
      lastFocused.focus();
      return true;
    }
    return false;
  }

  /**
   * 清除焦点栈
   */
  clearFocusStack(): void {
    this.focusStack = [];
  }

  /**
   * 创建焦点陷阱
   */
  createFocusTrap(container: HTMLElement, options: FocusTrapOptions = {}): FocusTrap {
    // 如果已有活动的焦点陷阱，先停用
    if (this.activeTrap) {
      this.activeTrap.deactivate();
    }

    const trap = new FocusTrap(container, options, this);
    this.activeTrap = trap;
    return trap;
  }

  /**
   * 停用当前焦点陷阱
   */
  deactivateCurrentTrap(): void {
    if (this.activeTrap) {
      this.activeTrap.deactivate();
      this.activeTrap = null;
    }
  }

  /**
   * 获取容器内的可聚焦元素
   */
  getFocusableElements(container: HTMLElement = document.body): HTMLElement[] {
    const elements = Array.from(container.querySelectorAll(this.focusableSelectors)) as HTMLElement[];
    return elements.filter(el => this.isElementFocusable(el));
  }

  /**
   * 获取第一个可聚焦元素
   */
  getFirstFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements[0] || null;
  }

  /**
   * 获取最后一个可聚焦元素
   */
  getLastFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements[focusableElements.length - 1] || null;
  }

  /**
   * 检查元素是否可聚焦
   */
  isElementFocusable(element: HTMLElement): boolean {
    // 检查元素是否可见
    if (!this.isElementVisible(element)) {
      return false;
    }

    // 检查元素是否被禁用
    if (element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true') {
      return false;
    }

    // 检查tabindex
    const tabindex = element.getAttribute('tabindex');
    if (tabindex === '-1') {
      return false;
    }

    return true;
  }

  /**
   * 检查元素是否可见
   */
  private isElementVisible(element: HTMLElement): boolean {
    if (element.offsetParent === null) {
      return false;
    }

    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0';
  }

  /**
   * 查找下一个可聚焦元素
   */
  findNextFocusableElement(current: HTMLElement, container: HTMLElement = document.body): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    const currentIndex = focusableElements.indexOf(current);
    
    if (currentIndex === -1) return null;
    
    return focusableElements[currentIndex + 1] || null;
  }

  /**
   * 查找上一个可聚焦元素
   */
  findPreviousFocusableElement(current: HTMLElement, container: HTMLElement = document.body): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    const currentIndex = focusableElements.indexOf(current);
    
    if (currentIndex === -1) return null;
    
    return focusableElements[currentIndex - 1] || null;
  }

  /**
   * 设置焦点到最合适的元素
   */
  setInitialFocus(container: HTMLElement, preferred?: HTMLElement | string): boolean {
    let targetElement: HTMLElement | null = null;

    // 尝试使用首选元素
    if (preferred) {
      if (typeof preferred === 'string') {
        targetElement = container.querySelector(preferred) as HTMLElement;
      } else {
        targetElement = preferred;
      }
    }

    // 如果首选元素不可用，查找第一个可聚焦元素
    if (!targetElement || !this.isElementFocusable(targetElement)) {
      targetElement = this.getFirstFocusableElement(container);
    }

    if (targetElement) {
      targetElement.focus();
      return true;
    }

    return false;
  }

  /**
   * 创建焦点指示器
   */
  createFocusIndicator(): HTMLElement {
    const indicator = document.createElement('div');
    indicator.className = 'focus-indicator';
    indicator.style.position = 'absolute';
    indicator.style.pointerEvents = 'none';
    indicator.style.border = '2px solid #0969da';
    indicator.style.borderRadius = '4px';
    indicator.style.zIndex = '9999';
    indicator.style.transition = 'all 0.15s ease';
    indicator.style.display = 'none';
    
    document.body.appendChild(indicator);
    return indicator;
  }

  /**
   * 更新焦点指示器位置
   */
  updateFocusIndicator(indicator: HTMLElement, target: HTMLElement): void {
    const rect = target.getBoundingClientRect();
    indicator.style.display = 'block';
    indicator.style.left = `${rect.left - 2}px`;
    indicator.style.top = `${rect.top - 2}px`;
    indicator.style.width = `${rect.width + 4}px`;
    indicator.style.height = `${rect.height + 4}px`;
  }

  /**
   * 隐藏焦点指示器
   */
  hideFocusIndicator(indicator: HTMLElement): void {
    indicator.style.display = 'none';
  }
}

/**
 * 焦点陷阱类
 */
export class FocusTrap {
  private container: HTMLElement;
  private options: FocusTrapOptions;
  private focusManager: FocusManager;
  private active = false;
  private previouslyFocused: HTMLElement | null = null;

  constructor(container: HTMLElement, options: FocusTrapOptions, focusManager: FocusManager) {
    this.container = container;
    this.options = options;
    this.focusManager = focusManager;
  }

  /**
   * 激活焦点陷阱
   */
  activate(): void {
    if (this.active) return;

    this.active = true;
    this.previouslyFocused = document.activeElement as HTMLElement;

    // 设置初始焦点
    this.setInitialFocus();

    // 添加事件监听
    document.addEventListener('keydown', this.handleKeydown);
    document.addEventListener('focusin', this.handleFocusIn);
    
    if (this.options.allowOutsideClick) {
      document.addEventListener('click', this.handleClick);
    }
  }

  /**
   * 停用焦点陷阱
   */
  deactivate(): void {
    if (!this.active) return;

    this.active = false;

    // 移除事件监听
    document.removeEventListener('keydown', this.handleKeydown);
    document.removeEventListener('focusin', this.handleFocusIn);
    document.removeEventListener('click', this.handleClick);

    // 恢复焦点
    if (this.options.returnFocus !== false && this.previouslyFocused) {
      this.previouslyFocused.focus();
    }
  }

  /**
   * 检查是否激活
   */
  isActive(): boolean {
    return this.active;
  }

  /**
   * 设置初始焦点
   */
  private setInitialFocus(): void {
    let initialElement: HTMLElement | null = null;

    if (this.options.initialFocus) {
      if (typeof this.options.initialFocus === 'string') {
        initialElement = this.container.querySelector(this.options.initialFocus) as HTMLElement;
      } else {
        initialElement = this.options.initialFocus;
      }
    }

    if (!initialElement || !this.focusManager.isElementFocusable(initialElement)) {
      initialElement = this.focusManager.getFirstFocusableElement(this.container);
    }

    if (initialElement) {
      initialElement.focus();
    }
  }

  /**
   * 处理键盘事件
   */
  private handleKeydown = (event: KeyboardEvent): void => {
    if (!this.active) return;

    if (event.key === 'Escape' && this.options.escapeDeactivates !== false) {
      event.preventDefault();
      this.deactivate();
      return;
    }

    if (event.key === 'Tab') {
      this.handleTabKey(event);
    }
  };

  /**
   * 处理Tab键
   */
  private handleTabKey(event: KeyboardEvent): void {
    const focusableElements = this.focusManager.getFocusableElements(this.container);
    
    if (focusableElements.length === 0) {
      event.preventDefault();
      return;
    }

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    const activeElement = document.activeElement as HTMLElement;

    if (event.shiftKey) {
      // Shift+Tab: 向后导航
      if (activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab: 向前导航
      if (activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * 处理焦点进入事件
   */
  private handleFocusIn = (event: FocusEvent): void => {
    if (!this.active) return;

    const target = event.target as HTMLElement;
    
    // 如果焦点移到容器外，重新聚焦到容器内
    if (!this.container.contains(target)) {
      event.preventDefault();
      const firstFocusable = this.focusManager.getFirstFocusableElement(this.container);
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }
  };

  /**
   * 处理点击事件
   */
  private handleClick = (event: MouseEvent): void => {
    if (!this.active) return;

    const target = event.target as HTMLElement;
    
    // 如果点击在容器外，停用焦点陷阱
    if (!this.container.contains(target)) {
      this.deactivate();
    }
  };
}

/**
 * 全局焦点管理器实例
 */
export const globalFocusManager = new FocusManager();