/**
 * Global Keyboard Manager - 全局键盘管理器
 */

export interface GlobalShortcut {
  id: string;
  key: string;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  description: string;
  category: string;
  enabled: boolean;
  global: boolean;
  preventDefault?: boolean;
  stopPropagation?: boolean;
}

export interface KeyboardManagerOptions {
  enableGlobalShortcuts?: boolean;
  enableContextualShortcuts?: boolean;
  preventDefaultOnMatch?: boolean;
  stopPropagationOnMatch?: boolean;
  debugMode?: boolean;
}

export class GlobalKeyboardManager {
  private options: KeyboardManagerOptions;
  private shortcuts: Map<string, GlobalShortcut> = new Map();
  private actionHandlers: Map<string, (() => void | Promise<void>)[]> = new Map();
  private contextStack: string[] = [];
  private enabled = true;
  private globalListenerAttached = false;
  private debugMode = false;

  constructor(options: KeyboardManagerOptions = {}) {
    this.options = {
      enableGlobalShortcuts: true,
      enableContextualShortcuts: true,
      preventDefaultOnMatch: true,
      stopPropagationOnMatch: true,
      debugMode: false,
      ...options
    };

    this.debugMode = this.options.debugMode || false;
    this.setupDefaultShortcuts();
    
    if (this.options.enableGlobalShortcuts) {
      this.attachGlobalListener();
    }
  }

  /**
   * 注册快捷键
   */
  registerShortcut(shortcut: Omit<GlobalShortcut, 'id'>): string {
    const id = this.generateShortcutId(shortcut);
    const fullShortcut: GlobalShortcut = {
      id,
      ...shortcut
    };

    this.shortcuts.set(id, fullShortcut);
    
    if (this.debugMode) {
      console.log(`Registered shortcut: ${id}`, fullShortcut);
    }

    return id;
  }

  /**
   * 注销快捷键
   */
  unregisterShortcut(id: string): boolean {
    const removed = this.shortcuts.delete(id);
    
    if (this.debugMode && removed) {
      console.log(`Unregistered shortcut: ${id}`);
    }

    return removed;
  }

  /**
   * 更新快捷键
   */
  updateShortcut(id: string, updates: Partial<GlobalShortcut>): boolean {
    const shortcut = this.shortcuts.get(id);
    if (!shortcut) return false;

    const updatedShortcut = { ...shortcut, ...updates };
    this.shortcuts.set(id, updatedShortcut);
    
    if (this.debugMode) {
      console.log(`Updated shortcut: ${id}`, updatedShortcut);
    }

    return true;
  }

  /**
   * 启用/禁用快捷键
   */
  toggleShortcut(id: string, enabled?: boolean): boolean {
    const shortcut = this.shortcuts.get(id);
    if (!shortcut) return false;

    shortcut.enabled = enabled !== undefined ? enabled : !shortcut.enabled;
    
    if (this.debugMode) {
      console.log(`Toggled shortcut: ${id}, enabled: ${shortcut.enabled}`);
    }

    return true;
  }

  /**
   * 添加动作处理器
   */
  onAction(shortcutId: string, handler: () => void | Promise<void>): void {
    if (!this.actionHandlers.has(shortcutId)) {
      this.actionHandlers.set(shortcutId, []);
    }
    this.actionHandlers.get(shortcutId)!.push(handler);
  }

  /**
   * 移除动作处理器
   */
  offAction(shortcutId: string, handler?: () => void | Promise<void>): void {
    const handlers = this.actionHandlers.get(shortcutId);
    if (!handlers) return;

    if (handler) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      this.actionHandlers.delete(shortcutId);
    }
  }

  /**
   * 推入上下文
   */
  pushContext(context: string): void {
    this.contextStack.push(context);
    
    if (this.debugMode) {
      console.log(`Pushed context: ${context}, stack:`, this.contextStack);
    }
  }

  /**
   * 弹出上下文
   */
  popContext(): string | undefined {
    const context = this.contextStack.pop();
    
    if (this.debugMode) {
      console.log(`Popped context: ${context}, stack:`, this.contextStack);
    }

    return context;
  }

  /**
   * 获取当前上下文
   */
  getCurrentContext(): string | undefined {
    return this.contextStack[this.contextStack.length - 1];
  }

  /**
   * 清除上下文栈
   */
  clearContextStack(): void {
    this.contextStack = [];
    
    if (this.debugMode) {
      console.log('Cleared context stack');
    }
  }

  /**
   * 启用键盘管理器
   */
  enable(): void {
    this.enabled = true;
    
    if (this.debugMode) {
      console.log('Keyboard manager enabled');
    }
  }

  /**
   * 禁用键盘管理器
   */
  disable(): void {
    this.enabled = false;
    
    if (this.debugMode) {
      console.log('Keyboard manager disabled');
    }
  }

  /**
   * 检查是否启用
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 手动处理键盘事件
   */
  handleKeyboardEvent(event: KeyboardEvent): boolean {
    if (!this.enabled) return false;

    const matchedShortcuts = this.findMatchingShortcuts(event);
    
    if (matchedShortcuts.length === 0) return false;

    // 按优先级排序（上下文相关的优先）
    const sortedShortcuts = this.sortShortcutsByPriority(matchedShortcuts);
    const shortcut = sortedShortcuts[0];

    if (this.debugMode) {
      console.log('Matched shortcut:', shortcut, 'Event:', event);
    }

    // 执行快捷键动作
    this.executeShortcut(shortcut, event);

    // 阻止默认行为和事件冒泡
    if (shortcut.preventDefault !== false && this.options.preventDefaultOnMatch) {
      event.preventDefault();
    }
    
    if (shortcut.stopPropagation !== false && this.options.stopPropagationOnMatch) {
      event.stopPropagation();
    }

    return true;
  }

  /**
   * 获取所有快捷键
   */
  getAllShortcuts(): GlobalShortcut[] {
    return Array.from(this.shortcuts.values());
  }

  /**
   * 按类别获取快捷键
   */
  getShortcutsByCategory(category: string): GlobalShortcut[] {
    return Array.from(this.shortcuts.values()).filter(s => s.category === category);
  }

  /**
   * 获取启用的快捷键
   */
  getEnabledShortcuts(): GlobalShortcut[] {
    return Array.from(this.shortcuts.values()).filter(s => s.enabled);
  }

  /**
   * 检查快捷键冲突
   */
  checkConflicts(): { shortcut1: GlobalShortcut; shortcut2: GlobalShortcut }[] {
    const conflicts: { shortcut1: GlobalShortcut; shortcut2: GlobalShortcut }[] = [];
    const shortcuts = this.getEnabledShortcuts();

    for (let i = 0; i < shortcuts.length; i++) {
      for (let j = i + 1; j < shortcuts.length; j++) {
        if (this.shortcutsConflict(shortcuts[i], shortcuts[j])) {
          conflicts.push({ shortcut1: shortcuts[i], shortcut2: shortcuts[j] });
        }
      }
    }

    return conflicts;
  }

  /**
   * 导出快捷键配置
   */
  exportConfig(): string {
    const config = {
      shortcuts: Array.from(this.shortcuts.values()),
      options: this.options
    };
    return JSON.stringify(config, null, 2);
  }

  /**
   * 导入快捷键配置
   */
  importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson);
      
      if (config.shortcuts && Array.isArray(config.shortcuts)) {
        this.shortcuts.clear();
        config.shortcuts.forEach((shortcut: GlobalShortcut) => {
          this.shortcuts.set(shortcut.id, shortcut);
        });
      }

      if (config.options) {
        this.options = { ...this.options, ...config.options };
      }

      if (this.debugMode) {
        console.log('Imported keyboard config:', config);
      }

      return true;
    } catch (error) {
      console.error('Failed to import keyboard config:', error);
      return false;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.detachGlobalListener();
    this.shortcuts.clear();
    this.actionHandlers.clear();
    this.contextStack = [];
    this.enabled = false;
    
    if (this.debugMode) {
      console.log('Keyboard manager destroyed');
    }
  }

  /**
   * 生成快捷键ID
   */
  private generateShortcutId(shortcut: Omit<GlobalShortcut, 'id'>): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('ctrl');
    if (shortcut.metaKey) parts.push('meta');
    if (shortcut.shiftKey) parts.push('shift');
    if (shortcut.altKey) parts.push('alt');
    parts.push(shortcut.key.toLowerCase());
    
    return parts.join('+');
  }

  /**
   * 设置默认快捷键
   */
  private setupDefaultShortcuts(): void {
    const defaultShortcuts: Omit<GlobalShortcut, 'id'>[] = [
      // 侧边栏控制
      {
        key: 'b',
        ctrlKey: true,
        shiftKey: true,
        description: '切换侧边栏显示',
        category: 'sidebar',
        enabled: true,
        global: true
      },
      {
        key: 'b',
        metaKey: true,
        shiftKey: true,
        description: '切换侧边栏显示 (Mac)',
        category: 'sidebar',
        enabled: true,
        global: true
      },

      // 搜索快捷键
      {
        key: 'f',
        ctrlKey: true,
        description: '聚焦搜索框',
        category: 'search',
        enabled: true,
        global: true
      },
      {
        key: 'k',
        ctrlKey: true,
        description: '快速搜索',
        category: 'search',
        enabled: true,
        global: true
      },
      {
        key: 'f',
        metaKey: true,
        description: '聚焦搜索框 (Mac)',
        category: 'search',
        enabled: true,
        global: true
      },
      {
        key: 'k',
        metaKey: true,
        description: '快速搜索 (Mac)',
        category: 'search',
        enabled: true,
        global: true
      },

      // 导航快捷键
      {
        key: 'j',
        ctrlKey: true,
        description: '下一个项目',
        category: 'navigation',
        enabled: true,
        global: false
      },
      {
        key: 'k',
        ctrlKey: true,
        description: '上一个项目',
        category: 'navigation',
        enabled: true,
        global: false
      },
      {
        key: 'g',
        ctrlKey: true,
        description: '跳转到顶部',
        category: 'navigation',
        enabled: true,
        global: false
      },
      {
        key: 'G',
        ctrlKey: true,
        shiftKey: true,
        description: '跳转到底部',
        category: 'navigation',
        enabled: true,
        global: false
      },

      // 标签页操作
      {
        key: 't',
        ctrlKey: true,
        description: '新建标签页',
        category: 'tabs',
        enabled: true,
        global: true
      },
      {
        key: 'w',
        ctrlKey: true,
        description: '关闭当前标签页',
        category: 'tabs',
        enabled: true,
        global: true
      },
      {
        key: 'Tab',
        ctrlKey: true,
        description: '切换到下一个标签页',
        category: 'tabs',
        enabled: true,
        global: true
      },
      {
        key: 'Tab',
        ctrlKey: true,
        shiftKey: true,
        description: '切换到上一个标签页',
        category: 'tabs',
        enabled: true,
        global: true
      },

      // 收藏夹操作
      {
        key: 'd',
        ctrlKey: true,
        description: '添加到收藏夹',
        category: 'bookmarks',
        enabled: true,
        global: true
      },
      {
        key: 'd',
        ctrlKey: true,
        shiftKey: true,
        description: '管理收藏夹',
        category: 'bookmarks',
        enabled: true,
        global: true
      },

      // 通用快捷键
      {
        key: 'Escape',
        description: '取消/关闭',
        category: 'general',
        enabled: true,
        global: false
      },
      {
        key: 'Enter',
        description: '确认/选择',
        category: 'general',
        enabled: true,
        global: false
      },
      {
        key: '?',
        shiftKey: true,
        description: '显示快捷键帮助',
        category: 'general',
        enabled: true,
        global: true
      }
    ];

    defaultShortcuts.forEach(shortcut => {
      this.registerShortcut(shortcut);
    });
  }

  /**
   * 附加全局监听器
   */
  private attachGlobalListener(): void {
    if (this.globalListenerAttached) return;

    document.addEventListener('keydown', this.handleGlobalKeydown, true);
    this.globalListenerAttached = true;
    
    if (this.debugMode) {
      console.log('Global keyboard listener attached');
    }
  }

  /**
   * 分离全局监听器
   */
  private detachGlobalListener(): void {
    if (!this.globalListenerAttached) return;

    document.removeEventListener('keydown', this.handleGlobalKeydown, true);
    this.globalListenerAttached = false;
    
    if (this.debugMode) {
      console.log('Global keyboard listener detached');
    }
  }

  /**
   * 处理全局键盘事件
   */
  private handleGlobalKeydown = (event: KeyboardEvent): void => {
    // 检查是否应该忽略此事件
    if (this.shouldIgnoreEvent(event)) {
      return;
    }

    this.handleKeyboardEvent(event);
  };

  /**
   * 检查是否应该忽略事件
   */
  private shouldIgnoreEvent(event: KeyboardEvent): boolean {
    const target = event.target as HTMLElement;
    
    // 忽略在输入元素中的事件（除非是全局快捷键）
    const isInputElement = target.tagName === 'INPUT' || 
                          target.tagName === 'TEXTAREA' || 
                          target.contentEditable === 'true';

    if (isInputElement) {
      const matchedShortcuts = this.findMatchingShortcuts(event);
      const hasGlobalShortcut = matchedShortcuts.some(s => s.global);
      return !hasGlobalShortcut;
    }

    return false;
  }

  /**
   * 查找匹配的快捷键
   */
  private findMatchingShortcuts(event: KeyboardEvent): GlobalShortcut[] {
    const matches: GlobalShortcut[] = [];

    this.shortcuts.forEach(shortcut => {
      if (!shortcut.enabled) return;

      if (this.shortcutMatches(shortcut, event)) {
        matches.push(shortcut);
      }
    });

    return matches;
  }

  /**
   * 检查快捷键是否匹配事件
   */
  private shortcutMatches(shortcut: GlobalShortcut, event: KeyboardEvent): boolean {
    return shortcut.key.toLowerCase() === event.key.toLowerCase() &&
           !!shortcut.ctrlKey === event.ctrlKey &&
           !!shortcut.metaKey === event.metaKey &&
           !!shortcut.shiftKey === event.shiftKey &&
           !!shortcut.altKey === event.altKey;
  }

  /**
   * 按优先级排序快捷键
   */
  private sortShortcutsByPriority(shortcuts: GlobalShortcut[]): GlobalShortcut[] {
    return shortcuts.sort((a, b) => {
      // 上下文相关的快捷键优先级更高
      const currentContext = this.getCurrentContext();
      if (currentContext) {
        const aContextMatch = a.category === currentContext;
        const bContextMatch = b.category === currentContext;
        
        if (aContextMatch && !bContextMatch) return -1;
        if (!aContextMatch && bContextMatch) return 1;
      }

      // 非全局快捷键优先级更高
      if (!a.global && b.global) return -1;
      if (a.global && !b.global) return 1;

      // 修饰键更多的优先级更高
      const aModifiers = (a.ctrlKey ? 1 : 0) + (a.metaKey ? 1 : 0) + 
                        (a.shiftKey ? 1 : 0) + (a.altKey ? 1 : 0);
      const bModifiers = (b.ctrlKey ? 1 : 0) + (b.metaKey ? 1 : 0) + 
                        (b.shiftKey ? 1 : 0) + (b.altKey ? 1 : 0);

      return bModifiers - aModifiers;
    });
  }

  /**
   * 执行快捷键
   */
  private async executeShortcut(shortcut: GlobalShortcut, event: KeyboardEvent): Promise<void> {
    const handlers = this.actionHandlers.get(shortcut.id);
    
    if (!handlers || handlers.length === 0) {
      if (this.debugMode) {
        console.warn(`No handlers for shortcut: ${shortcut.id}`);
      }
      return;
    }

    if (this.debugMode) {
      console.log(`Executing shortcut: ${shortcut.id}`, shortcut);
    }

    // 执行所有处理器
    for (const handler of handlers) {
      try {
        await handler();
      } catch (error) {
        console.error(`Error executing shortcut handler for ${shortcut.id}:`, error);
      }
    }
  }

  /**
   * 检查快捷键是否冲突
   */
  private shortcutsConflict(shortcut1: GlobalShortcut, shortcut2: GlobalShortcut): boolean {
    return shortcut1.key.toLowerCase() === shortcut2.key.toLowerCase() &&
           shortcut1.ctrlKey === shortcut2.ctrlKey &&
           shortcut1.metaKey === shortcut2.metaKey &&
           shortcut1.shiftKey === shortcut2.shiftKey &&
           shortcut1.altKey === shortcut2.altKey &&
           shortcut1.global === shortcut2.global;
  }

  /**
   * 格式化快捷键显示
   */
  static formatShortcut(shortcut: GlobalShortcut): string {
    const parts = [];
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    
    if (shortcut.ctrlKey) {
      parts.push(isMac ? '⌃' : 'Ctrl');
    }
    if (shortcut.metaKey) {
      parts.push(isMac ? '⌘' : 'Meta');
    }
    if (shortcut.altKey) {
      parts.push(isMac ? '⌥' : 'Alt');
    }
    if (shortcut.shiftKey) {
      parts.push(isMac ? '⇧' : 'Shift');
    }
    
    let keyName = shortcut.key;
    const keyMappings: Record<string, string> = {
      'ArrowUp': '↑',
      'ArrowDown': '↓',
      'ArrowLeft': '←',
      'ArrowRight': '→',
      'Enter': '⏎',
      'Escape': 'Esc',
      'Delete': 'Del',
      'Backspace': '⌫',
      'Tab': '⇥',
      ' ': 'Space'
    };
    
    if (keyMappings[keyName]) {
      keyName = keyMappings[keyName];
    } else if (keyName.length === 1) {
      keyName = keyName.toUpperCase();
    }
    
    parts.push(keyName);
    
    return parts.join(isMac ? '' : '+');
  }
}