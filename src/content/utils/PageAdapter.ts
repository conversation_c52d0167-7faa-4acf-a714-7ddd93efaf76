/**
 * Page Adapter - 页面适配器
 * 处理页面注入、兼容性检测和布局调整
 */

import { PageAdapter as IPageAdapter } from '../../interfaces/content.interface.js';
import { ErrorHandler } from '../../background/utils/ErrorHandler.js';
import { StyleManager } from './StyleManager.js';

export class PageAdapter implements IPageAdapter {
  private originalBodyStyle: Partial<CSSStyleDeclaration> = {};
  private originalDocumentStyle: Partial<CSSStyleDeclaration> = {};
  private injectionAttempts = 0;
  private readonly MAX_INJECTION_ATTEMPTS = 3;
  private readonly INJECTION_RETRY_DELAY = 1000;
  private styleManager: StyleManager | null = null;
  private spaceReserved = false;
  private reservedWidth = 0;
  private reservedPosition: 'left' | 'right' = 'left';
  private earlyReservationApplied = false;

  constructor(styleManager?: StyleManager) {
    this.styleManager = styleManager || null;
  }

  /**
   * 提前预留侧边栏空间（页面加载完成时调用）
   */
  reserveSpaceEarly(width: number, position: 'left' | 'right'): void {
    try {
      console.log(`Early space reservation: ${width}px on ${position}`);
      
      if (this.earlyReservationApplied) {
        console.log('Early reservation already applied, updating...');
        this.updateReservedSpace(width, position);
        return;
      }

      this.reservedWidth = width;
      this.reservedPosition = position;
      this.earlyReservationApplied = true;

      // 等待DOM完全加载
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          this.applyEarlyReservation();
        });
      } else {
        // DOM已加载，直接应用
        this.applyEarlyReservation();
      }

    } catch (error) {
      console.error('Error in early space reservation:', error);
      ErrorHandler.handleError(error, 'PageAdapter.reserveSpaceEarly');
    }
  }

  /**
   * 应用提前预留的空间
   */
  private applyEarlyReservation(): void {
    if (!this.earlyReservationApplied || this.spaceReserved) {
      return;
    }

    try {
      // 添加预留空间的样式
      this.addReservationStyles();
      
      // 应用基础布局调整（不使用动画）
      this.adjustBodyLayout(this.reservedWidth, this.reservedPosition);
      
      // 启用页面适配样式类
      if (this.styleManager) {
        this.styleManager.enablePageAdjustment(this.reservedPosition);
      }

      this.spaceReserved = true;
      console.log('Early space reservation applied successfully');

    } catch (error) {
      console.error('Error applying early reservation:', error);
      this.earlyReservationApplied = false;
    }
  }

  /**
   * 更新已预留的空间
   */
  updateReservedSpace(width: number, position: 'left' | 'right'): void {
    if (!this.spaceReserved) {
      return;
    }

    try {
      const widthChanged = this.reservedWidth !== width;
      const positionChanged = this.reservedPosition !== position;

      if (widthChanged || positionChanged) {
        console.log(`Updating reserved space: ${this.reservedWidth}px → ${width}px, ${this.reservedPosition} → ${position}`);
        
        // 暂时恢复布局
        if (positionChanged) {
          this.restoreBodyStyles();
        }

        // 更新参数
        this.reservedWidth = width;
        this.reservedPosition = position;

        // 重新应用布局
        this.adjustBodyLayout(width, position);

        // 更新样式管理器
        if (this.styleManager) {
          this.styleManager.disablePageAdjustment();
          this.styleManager.enablePageAdjustment(position);
        }
      }
    } catch (error) {
      console.error('Error updating reserved space:', error);
    }
  }

  /**
   * 添加空间预留的CSS样式
   */
  private addReservationStyles(): void {
    const existingStyle = document.getElementById('sidebar-space-reservation');
    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = 'sidebar-space-reservation';
    style.textContent = `
      /* 侧边栏空间预留样式 */
      .sidebar-space-reserved {
        box-sizing: border-box !important;
        transition: none !important; /* 预留阶段不要动画 */
      }

      .sidebar-space-reserved.sidebar-position-left {
        margin-left: ${this.reservedWidth}px !important;
        padding-left: 0 !important;
      }

      .sidebar-space-reserved.sidebar-position-right {
        margin-right: ${this.reservedWidth}px !important;
        padding-right: 0 !important;
      }

      /* 防止内容渗透到预留区域 */
      .sidebar-space-reserved::before {
        content: '';
        position: fixed;
        top: 0;
        ${this.reservedPosition}: 0;
        width: ${this.reservedWidth}px;
        height: 100vh;
        background: transparent;
        pointer-events: none;
        z-index: 1000000; /* 很高的z-index防止内容渗透 */
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * 为预留空间启用过渡动画
   */
  private enableTransitionsForReservedSpace(): void {
    try {
      const body = document.body;
      const html = document.documentElement;

      // 临时启用动画
      body.style.transition = 'margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
      html.style.transition = 'margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';

      // 3秒后移除过渡（避免影响其他操作）
      setTimeout(() => {
        if (body.style.transition.includes('margin 0.3s')) {
          body.style.transition = body.style.transition.replace('margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)', '').replace(/^,\s*|,\s*$/g, '').trim();
          if (!body.style.transition) {
            body.style.transition = '';
          }
        }
        if (html.style.transition.includes('margin 0.3s')) {
          html.style.transition = '';
        }
      }, 350);
    } catch (error) {
      console.error('Error enabling transitions for reserved space:', error);
    }
  }

  /**
   * 清除空间预留
   */
  clearSpaceReservation(): void {
    try {
      console.log('Clearing space reservation');

      // 移除预留样式
      const reservationStyle = document.getElementById('sidebar-space-reservation');
      if (reservationStyle) {
        reservationStyle.remove();
      }

      // 移除body类
      document.body.classList.remove('sidebar-space-reserved', 'sidebar-position-left', 'sidebar-position-right');

      // 重置状态
      this.spaceReserved = false;
      this.earlyReservationApplied = false;
      this.reservedWidth = 0;

      console.log('Space reservation cleared');
    } catch (error) {
      console.error('Error clearing space reservation:', error);
    }
  }

  /**
   * 检查是否可以注入侧边栏
   */
  canInject(): boolean {
    try {
      // 检查基本DOM可用性
      if (!document || !document.body || !document.documentElement) {
        console.log('DOM not ready for injection');
        return false;
      }

      // 检查URL类型
      if (!this.isValidURL()) {
        console.log('Invalid URL for injection:', window.location.href);
        return false;
      }

      // 检查页面类型
      if (!this.isValidPageType()) {
        console.log('Invalid page type for injection');
        return false;
      }

      // 检查CSP限制
      if (!this.checkCSPCompatibility()) {
        console.log('CSP restrictions detected');
        return false;
      }

      // 检查页面布局兼容性
      if (!this.checkLayoutCompatibility()) {
        console.log('Layout compatibility issues detected');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking injection compatibility:', error);
      return false;
    }
  }

  /**
   * 调整页面布局以适应侧边栏
   */
  adjustPageLayout(sidebarWidth: number, position: 'left' | 'right', animated: boolean = true): void {
    try {
      console.log(`Adjusting page layout: ${sidebarWidth}px on ${position}, animated: ${animated}`);

      // 如果已有预留空间，优先使用更新机制
      if (this.spaceReserved) {
        this.updateReservedSpace(sidebarWidth, position);
        
        // 启用动画过渡
        if (animated) {
          this.enableTransitionsForReservedSpace();
        }
      } else {
        // 传统调整方式
        // 保存原始样式
        this.saveOriginalStyles();

        // 添加页面内容过渡动画
        if (animated) {
          this.addPageTransitions();
        }

        // 启用页面适配样式
        if (this.styleManager) {
          this.styleManager.enablePageAdjustment(position);
        }

        // 调整body样式
        this.adjustBodyLayout(sidebarWidth, position);
      }

      // 调整特殊元素（总是需要）
      this.adjustSpecialElements(sidebarWidth, position);

      // 处理固定定位元素
      this.adjustFixedElements(sidebarWidth, position);

      // 处理视口meta标签
      this.adjustViewportMeta();

      // 触发resize事件通知页面布局变化（延迟触发以确保CSS变化生效）
      setTimeout(() => {
        this.triggerResizeEvent();
      }, animated ? 320 : 0); // 稍长于CSS过渡时间

      console.log('Page layout adjusted successfully');
    } catch (error) {
      console.error('Error adjusting page layout:', error);
      ErrorHandler.handleError(error, 'PageAdapter.adjustPageLayout');
    }
  }

  /**
   * 恢复页面原始布局
   */
  restorePageLayout(animated: boolean = true): void {
    try {
      console.log('Restoring original page layout, animated:', animated);

      // 添加过渡动画
      if (animated) {
        this.addPageTransitions();
      }

      // 恢复body样式
      this.restoreBodyStyles();

      // 恢复特殊元素样式
      this.restoreSpecialElements();

      // 恢复固定定位元素
      this.restoreFixedElements();

      // 恢复视口设置
      this.restoreViewportMeta();

      // 延迟触发resize事件和移除过渡
      setTimeout(() => {
        this.triggerResizeEvent();
        if (animated) {
          this.removePageTransitions();
        }
      }, animated ? 320 : 0);

      console.log('Page layout restored successfully');
    } catch (error) {
      console.error('Error restoring page layout:', error);
      ErrorHandler.handleError(error, 'PageAdapter.restorePageLayout');
    }
  }

  /**
   * 检测页面冲突
   */
  detectConflicts(): string[] {
    const conflicts: string[] = [];

    try {
      // 检测左侧固定元素冲突
      const leftFixedElements = this.findFixedElements('left');
      if (leftFixedElements.length > 0) {
        conflicts.push(`Found ${leftFixedElements.length} left-positioned fixed elements`);
      }

      // 检测全宽布局冲突
      const fullWidthElements = this.findFullWidthElements();
      if (fullWidthElements.length > 0) {
        conflicts.push(`Found ${fullWidthElements.length} full-width elements`);
      }

      // 检测z-index冲突
      const highZIndexElements = this.findHighZIndexElements();
      if (highZIndexElements.length > 0) {
        conflicts.push(`Found ${highZIndexElements.length} high z-index elements`);
      }

      // 检测CSS框架冲突
      const frameworkConflicts = this.detectFrameworkConflicts();
      conflicts.push(...frameworkConflicts);

      // 检测自定义滚动条冲突
      if (this.hasCustomScrollbar()) {
        conflicts.push('Custom scrollbar detected');
      }

    } catch (error) {
      console.error('Error detecting conflicts:', error);
      conflicts.push('Error during conflict detection');
    }

    return conflicts;
  }

  /**
   * 处理检测到的冲突
   */
  handleConflicts(conflicts: string[]): void {
    try {
      console.log('Handling conflicts:', conflicts);

      for (const conflict of conflicts) {
        if (conflict.includes('left-positioned fixed elements')) {
          this.handleFixedElementConflicts();
        } else if (conflict.includes('full-width elements')) {
          this.handleFullWidthConflicts();
        } else if (conflict.includes('high z-index elements')) {
          this.handleZIndexConflicts();
        } else if (conflict.includes('Custom scrollbar')) {
          this.handleScrollbarConflicts();
        }
      }

      console.log('Conflicts handled successfully');
    } catch (error) {
      console.error('Error handling conflicts:', error);
      ErrorHandler.handleError(error, 'PageAdapter.handleConflicts');
    }
  }

  /**
   * 检查URL是否有效
   */
  private isValidURL(): boolean {
    const url = window.location.href;
    
    // 排除特殊协议
    const invalidProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'about:', 'file:'];
    for (const protocol of invalidProtocols) {
      if (url.startsWith(protocol)) {
        return false;
      }
    }

    // 排除特殊域名
    const invalidDomains = ['chrome.google.com'];
    for (const domain of invalidDomains) {
      if (url.includes(domain)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查页面类型是否有效
   */
  private isValidPageType(): boolean {
    // 检查是否为iframe
    if (window !== window.top) {
      console.log('Skipping injection in iframe');
      return false;
    }

    // 检查页面内容类型
    const contentType = document.contentType || document.mimeType;
    if (contentType && !contentType.includes('text/html')) {
      console.log('Non-HTML content type:', contentType);
      return false;
    }

    // 检查是否为PDF或其他特殊内容
    if (document.querySelector('embed[type="application/pdf"]') ||
        document.querySelector('object[type="application/pdf"]')) {
      console.log('PDF content detected');
      return false;
    }

    return true;
  }

  /**
   * 检查CSP兼容性
   */
  private checkCSPCompatibility(): boolean {
    try {
      // 检查CSP meta标签
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (cspMeta) {
        const content = cspMeta.getAttribute('content');
        if (content) {
          // 检查是否允许内联样式
          if (content.includes('style-src') && !content.includes('unsafe-inline')) {
            console.warn('CSP may block inline styles');
          }
          
          // 检查是否允许脚本执行
          if (content.includes('script-src') && !content.includes('unsafe-inline')) {
            console.warn('CSP may block inline scripts');
          }
        }
      }

      // 尝试创建测试元素来验证CSP
      const testElement = document.createElement('div');
      testElement.style.display = 'none';
      document.body.appendChild(testElement);
      document.body.removeChild(testElement);

      return true;
    } catch (error) {
      console.error('CSP compatibility check failed:', error);
      return false;
    }
  }

  /**
   * 检查布局兼容性
   */
  private checkLayoutCompatibility(): boolean {
    try {
      // 检查body是否可修改
      if (!document.body || document.body.style === null) {
        return false;
      }

      // 检查是否存在阻止修改的样式
      const bodyStyle = window.getComputedStyle(document.body);
      if (bodyStyle.position === 'fixed' || bodyStyle.position === 'absolute') {
        console.warn('Body has fixed/absolute positioning');
      }

      // 检查viewport设置
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        const content = viewport.getAttribute('content');
        if (content && content.includes('user-scalable=no')) {
          console.warn('Viewport scaling disabled');
        }
      }

      return true;
    } catch (error) {
      console.error('Layout compatibility check failed:', error);
      return false;
    }
  }

  /**
   * 保存原始样式
   */
  private saveOriginalStyles(): void {
    const body = document.body;
    const documentElement = document.documentElement;

    // 保存body样式
    this.originalBodyStyle = {
      marginLeft: body.style.marginLeft,
      marginRight: body.style.marginRight,
      paddingLeft: body.style.paddingLeft,
      paddingRight: body.style.paddingRight,
      width: body.style.width,
      maxWidth: body.style.maxWidth,
      transform: body.style.transform,
      transition: body.style.transition
    };

    // 保存document样式
    this.originalDocumentStyle = {
      marginLeft: documentElement.style.marginLeft,
      marginRight: documentElement.style.marginRight,
      paddingLeft: documentElement.style.paddingLeft,
      paddingRight: documentElement.style.paddingRight
    };
  }

  /**
   * 调整body布局
   */
  private adjustBodyLayout(sidebarWidth: number, position: 'left' | 'right'): void {
    const body = document.body;
    const documentElement = document.documentElement;

    // 检查body当前定位方式
    const bodyStyle = window.getComputedStyle(body);
    const isFixedOrAbsolute = bodyStyle.position === 'fixed' || bodyStyle.position === 'absolute';

    if (position === 'left') {
      if (!isFixedOrAbsolute) {
        body.style.marginLeft = `${sidebarWidth}px`;
        documentElement.style.marginLeft = `${sidebarWidth}px`;
      } else {
        // 对于固定或绝对定位的body，使用transform
        body.style.transform = `translateX(${sidebarWidth}px)`;
        body.style.width = `calc(100% - ${sidebarWidth}px)`;
      }
    } else {
      if (!isFixedOrAbsolute) {
        body.style.marginRight = `${sidebarWidth}px`;
        documentElement.style.marginRight = `${sidebarWidth}px`;
      } else {
        body.style.transform = `translateX(-${sidebarWidth}px)`;
        body.style.width = `calc(100% - ${sidebarWidth}px)`;
      }
    }

    // 设置最小宽度防止内容被过度压缩
    body.style.minWidth = `${Math.max(320, window.innerWidth - sidebarWidth)}px`;
  }

  /**
   * 调整特殊元素
   */
  private adjustSpecialElements(sidebarWidth: number, position: 'left' | 'right'): void {
    // 调整全宽元素
    const fullWidthElements = this.findFullWidthElements();
    fullWidthElements.forEach(element => {
      const originalWidth = element.style.width;
      element.setAttribute('data-original-width', originalWidth || '');
      
      if (position === 'left') {
        element.style.marginLeft = `${sidebarWidth}px`;
      } else {
        element.style.marginRight = `${sidebarWidth}px`;
      }
    });
  }

  /**
   * 调整固定定位元素
   */
  private adjustFixedElements(sidebarWidth: number, position: 'left' | 'right'): void {
    const fixedElements = this.findFixedElements(position);
    
    fixedElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const currentLeft = parseInt(computedStyle.left) || 0;
      const currentRight = parseInt(computedStyle.right) || 0;

      // 保存原始位置
      element.setAttribute('data-original-left', computedStyle.left);
      element.setAttribute('data-original-right', computedStyle.right);

      if (position === 'left' && currentLeft < sidebarWidth) {
        element.style.left = `${currentLeft + sidebarWidth}px`;
      } else if (position === 'right' && currentRight < sidebarWidth) {
        element.style.right = `${currentRight + sidebarWidth}px`;
      }
    });
  }

  /**
   * 恢复body样式
   */
  private restoreBodyStyles(): void {
    const body = document.body;
    const documentElement = document.documentElement;

    // 恢复body样式
    Object.keys(this.originalBodyStyle).forEach(key => {
      const value = this.originalBodyStyle[key as keyof CSSStyleDeclaration];
      if (value !== undefined) {
        (body.style as any)[key] = value;
      } else {
        (body.style as any)[key] = '';
      }
    });

    // 恢复document样式
    Object.keys(this.originalDocumentStyle).forEach(key => {
      const value = this.originalDocumentStyle[key as keyof CSSStyleDeclaration];
      if (value !== undefined) {
        (documentElement.style as any)[key] = value;
      } else {
        (documentElement.style as any)[key] = '';
      }
    });
  }

  /**
   * 恢复特殊元素
   */
  private restoreSpecialElements(): void {
    const elements = document.querySelectorAll('[data-original-width]');
    elements.forEach(element => {
      const originalWidth = element.getAttribute('data-original-width');
      if (originalWidth) {
        (element as HTMLElement).style.width = originalWidth;
      } else {
        (element as HTMLElement).style.width = '';
      }
      element.removeAttribute('data-original-width');
      (element as HTMLElement).style.marginLeft = '';
      (element as HTMLElement).style.marginRight = '';
    });
  }

  /**
   * 恢复固定定位元素
   */
  private restoreFixedElements(): void {
    const elements = document.querySelectorAll('[data-original-left], [data-original-right]');
    elements.forEach(element => {
      const originalLeft = element.getAttribute('data-original-left');
      const originalRight = element.getAttribute('data-original-right');

      if (originalLeft) {
        (element as HTMLElement).style.left = originalLeft;
        element.removeAttribute('data-original-left');
      }

      if (originalRight) {
        (element as HTMLElement).style.right = originalRight;
        element.removeAttribute('data-original-right');
      }
    });
  }

  /**
   * 查找固定定位元素
   */
  private findFixedElements(position: 'left' | 'right'): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const allElements = document.querySelectorAll('*');

    allElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      if (computedStyle.position === 'fixed') {
        const left = parseInt(computedStyle.left) || 0;
        const right = parseInt(computedStyle.right) || 0;

        if ((position === 'left' && left >= 0 && left < 100) ||
            (position === 'right' && right >= 0 && right < 100)) {
          elements.push(element as HTMLElement);
        }
      }
    });

    return elements;
  }

  /**
   * 查找全宽元素
   */
  private findFullWidthElements(): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const candidates = document.querySelectorAll('div, section, header, footer, nav');

    candidates.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const width = computedStyle.width;
      
      if (width === '100vw' || width === '100%') {
        elements.push(element as HTMLElement);
      }
    });

    return elements;
  }

  /**
   * 查找高z-index元素
   */
  private findHighZIndexElements(): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const allElements = document.querySelectorAll('*');
    const SIDEBAR_Z_INDEX = 2147483647;

    allElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      const zIndex = parseInt(computedStyle.zIndex);
      
      if (zIndex >= SIDEBAR_Z_INDEX) {
        elements.push(element as HTMLElement);
      }
    });

    return elements;
  }

  /**
   * 检测CSS框架冲突
   */
  private detectFrameworkConflicts(): string[] {
    const conflicts: string[] = [];

    // 检测Bootstrap
    if (document.querySelector('.container-fluid, .container') ||
        document.querySelector('link[href*="bootstrap"]')) {
      conflicts.push('Bootstrap framework detected');
    }

    // 检测Tailwind CSS
    if (document.querySelector('[class*="w-full"], [class*="w-screen"]') ||
        document.querySelector('link[href*="tailwind"]')) {
      conflicts.push('Tailwind CSS framework detected');
    }

    // 检测Material-UI
    if (document.querySelector('[class*="MuiContainer"], [class*="makeStyles"]')) {
      conflicts.push('Material-UI framework detected');
    }

    return conflicts;
  }

  /**
   * 检查是否有自定义滚动条
   */
  private hasCustomScrollbar(): boolean {
    const bodyStyle = window.getComputedStyle(document.body);
    return bodyStyle.getPropertyValue('scrollbar-width') !== 'auto' ||
           bodyStyle.getPropertyValue('-webkit-scrollbar-width') !== '';
  }

  /**
   * 处理固定元素冲突
   */
  private handleFixedElementConflicts(): void {
    console.log('Handling fixed element conflicts');
    
    try {
      // 查找所有可能冲突的固定元素
      const conflictElements = this.findConflictingFixedElements();
      
      conflictElements.forEach(element => {
        const el = element as HTMLElement;
        const computedStyle = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        
        // 保存原始样式
        if (!el.hasAttribute('data-original-z-index')) {
          el.setAttribute('data-original-z-index', computedStyle.zIndex);
          el.setAttribute('data-original-left', computedStyle.left);
          el.setAttribute('data-original-right', computedStyle.right);
          el.setAttribute('data-original-position', computedStyle.position);
        }
        
        // 检查是否与侧边栏重叠
        const sidebarZone = 300; // 假设侧边栏最大宽度
        const isLeftConflict = rect.left < sidebarZone;
        const isRightConflict = rect.right > (window.innerWidth - sidebarZone);
        
        if (isLeftConflict || isRightConflict) {
          // 降低z-index以避免覆盖侧边栏
          const currentZIndex = parseInt(computedStyle.zIndex) || 0;
          if (currentZIndex >= 2147483647) {
            el.style.zIndex = '2147483646'; // 略低于侧边栏
          }
          
          // 添加警告标识
          el.setAttribute('data-sidebar-conflict', 'true');
          el.title = (el.title || '') + ' [侧边栏冲突检测]';
          
          console.warn('Fixed element conflict detected and handled:', el);
        }
      });
      
    } catch (error) {
      console.error('Error handling fixed element conflicts:', error);
    }
  }

  /**
   * 处理全宽元素冲突
   */
  private handleFullWidthConflicts(): void {
    console.log('Handling full-width element conflicts');
    
    try {
      const fullWidthElements = this.findFullWidthElements();
      
      fullWidthElements.forEach(element => {
        const el = element as HTMLElement;
        const computedStyle = window.getComputedStyle(el);
        
        // 保存原始样式
        if (!el.hasAttribute('data-original-width')) {
          el.setAttribute('data-original-width', el.style.width || computedStyle.width);
          el.setAttribute('data-original-margin-left', el.style.marginLeft || computedStyle.marginLeft);
          el.setAttribute('data-original-margin-right', el.style.marginRight || computedStyle.marginRight);
          el.setAttribute('data-original-padding-left', el.style.paddingLeft || computedStyle.paddingLeft);
          el.setAttribute('data-original-padding-right', el.style.paddingRight || computedStyle.paddingRight);
        }
        
        // 检查元素类型并应用相应处理
        const tagName = el.tagName.toLowerCase();
        const className = el.className;
        
        if (tagName === 'header' || className.includes('header')) {
          this.adjustFullWidthHeader(el);
        } else if (tagName === 'footer' || className.includes('footer')) {
          this.adjustFullWidthFooter(el);
        } else if (className.includes('banner') || className.includes('hero')) {
          this.adjustFullWidthBanner(el);
        } else {
          this.adjustGenericFullWidthElement(el);
        }
        
        console.log('Full-width element adjusted:', el);
      });
      
    } catch (error) {
      console.error('Error handling full-width conflicts:', error);
    }
  }

  /**
   * 处理z-index冲突
   */
  private handleZIndexConflicts(): void {
    console.log('Handling z-index conflicts');
    
    try {
      const highZIndexElements = this.findHighZIndexElements();
      const SIDEBAR_Z_INDEX = 2147483647;
      
      highZIndexElements.forEach(element => {
        const el = element as HTMLElement;
        const computedStyle = window.getComputedStyle(el);
        const currentZIndex = parseInt(computedStyle.zIndex);
        
        // 保存原始z-index
        if (!el.hasAttribute('data-original-z-index')) {
          el.setAttribute('data-original-z-index', currentZIndex.toString());
        }
        
        // 检查是否真的需要这么高的z-index
        const rect = el.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0;
        const isInteractive = el.onclick || el.addEventListener || 
                             el.tagName.toLowerCase() === 'button' ||
                             el.tagName.toLowerCase() === 'a' ||
                             el.hasAttribute('role');
        
        if (currentZIndex >= SIDEBAR_Z_INDEX) {
          if (isVisible && isInteractive) {
            // 保持较高但低于侧边栏的z-index
            el.style.zIndex = (SIDEBAR_Z_INDEX - 1).toString();
            el.setAttribute('data-sidebar-z-adjusted', 'true');
            console.warn('High z-index element adjusted to avoid sidebar conflict:', el);
          } else {
            // 降低到更合理的层级
            el.style.zIndex = '1000';
            el.setAttribute('data-sidebar-z-reduced', 'true');
            console.log('Unnecessary high z-index reduced:', el);
          }
        }
      });
      
    } catch (error) {
      console.error('Error handling z-index conflicts:', error);
    }
  }

  /**
   * 处理滚动条冲突
   */
  private handleScrollbarConflicts(): void {
    console.log('Handling scrollbar conflicts');
    
    try {
      const body = document.body;
      const html = document.documentElement;
      
      // 检测自定义滚动条样式
      const hasCustomScrollbar = this.hasCustomScrollbar();
      
      if (hasCustomScrollbar) {
        // 保存原始滚动条样式
        const bodyStyle = window.getComputedStyle(body);
        const htmlStyle = window.getComputedStyle(html);
        
        body.setAttribute('data-original-scrollbar-width', 
          bodyStyle.getPropertyValue('scrollbar-width'));
        body.setAttribute('data-original-webkit-scrollbar', 
          bodyStyle.getPropertyValue('-webkit-scrollbar-width'));
        
        // 调整滚动条以避免与侧边栏冲突
        const scrollbarStyle = `
          body::-webkit-scrollbar {
            width: 8px !important;
          }
          body::-webkit-scrollbar-track {
            background: transparent !important;
          }
          body::-webkit-scrollbar-thumb {
            background: rgba(128, 128, 128, 0.5) !important;
            border-radius: 4px !important;
          }
          body::-webkit-scrollbar-thumb:hover {
            background: rgba(128, 128, 128, 0.7) !important;
          }
        `;
        
        // 注入调整后的滚动条样式
        let scrollbarStyleElement = document.getElementById('sidebar-scrollbar-fix');
        if (!scrollbarStyleElement) {
          scrollbarStyleElement = document.createElement('style');
          scrollbarStyleElement.id = 'sidebar-scrollbar-fix';
          document.head.appendChild(scrollbarStyleElement);
        }
        scrollbarStyleElement.textContent = scrollbarStyle;
        
        console.log('Custom scrollbar adjusted for sidebar compatibility');
      }
      
    } catch (error) {
      console.error('Error handling scrollbar conflicts:', error);
    }
  }

  /**
   * 触发resize事件
   */
  private triggerResizeEvent(): void {
    const resizeEvent = new Event('resize');
    window.dispatchEvent(resizeEvent);
  }

  /**
   * 添加页面内容过渡动画
   */
  private addPageTransitions(): void {
    const body = document.body;
    const documentElement = document.documentElement;
    
    const transitionStyle = 'margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
    
    body.style.transition = transitionStyle;
    documentElement.style.transition = 'margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';

    // 为特殊元素添加过渡
    const specialElements = document.querySelectorAll('[data-original-width], [data-original-left], [data-original-right]');
    specialElements.forEach(element => {
      const el = element as HTMLElement;
      const existingTransition = el.style.transition || '';
      if (!existingTransition.includes('margin') && !existingTransition.includes('left') && !existingTransition.includes('right')) {
        el.style.transition = existingTransition + (existingTransition ? ', ' : '') + 'margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), left 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), right 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)';
      }
    });
  }

  /**
   * 移除页面内容过渡动画
   */
  private removePageTransitions(): void {
    const body = document.body;
    const documentElement = document.documentElement;
    
    // 恢复原始过渡或清除
    body.style.transition = this.originalBodyStyle.transition || '';
    documentElement.style.transition = '';

    // 清理特殊元素的过渡
    const specialElements = document.querySelectorAll('[data-original-transition]');
    specialElements.forEach(element => {
      const el = element as HTMLElement;
      const originalTransition = el.getAttribute('data-original-transition');
      el.style.transition = originalTransition || '';
      el.removeAttribute('data-original-transition');
    });
  }

  /**
   * 调整视口meta标签
   */
  private adjustViewportMeta(): void {
    const viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
    if (viewport) {
      // 保存原始viewport设置
      if (!viewport.hasAttribute('data-original-content')) {
        viewport.setAttribute('data-original-content', viewport.content);
      }
      
      // 禁用用户缩放以避免布局问题
      const currentContent = viewport.content;
      if (!currentContent.includes('user-scalable=no')) {
        const newContent = currentContent + (currentContent ? ', ' : '') + 'user-scalable=no';
        viewport.content = newContent;
      }
    }
  }

  /**
   * 恢复视口meta标签
   */
  private restoreViewportMeta(): void {
    const viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
    if (viewport && viewport.hasAttribute('data-original-content')) {
      viewport.content = viewport.getAttribute('data-original-content') || '';
      viewport.removeAttribute('data-original-content');
    }
  }

  /**
   * 智能检测并调整页面布局
   */
  smartAdjustLayout(sidebarWidth: number, position: 'left' | 'right'): void {
    try {
      // 检测页面类型并应用相应策略
      const pageType = this.detectPageType();
      console.log('Detected page type:', pageType);

      // 根据页面类型调整策略
      switch (pageType) {
        case 'bootstrap':
          this.adjustBootstrapLayout(sidebarWidth, position);
          break;
        case 'tailwind':
          this.adjustTailwindLayout(sidebarWidth, position);
          break;
        case 'fixed-header':
          this.adjustFixedHeaderLayout(sidebarWidth, position);
          break;
        default:
          this.adjustPageLayout(sidebarWidth, position);
      }
    } catch (error) {
      console.error('Error in smart layout adjustment:', error);
      // 降级到标准调整
      this.adjustPageLayout(sidebarWidth, position);
    }
  }

  /**
   * 检测页面类型
   */
  private detectPageType(): string {
    // Bootstrap检测
    if (document.querySelector('.container-fluid, .container, .row, .col') ||
        document.querySelector('link[href*="bootstrap"]') ||
        document.querySelector('script[src*="bootstrap"]')) {
      return 'bootstrap';
    }
    
    // Tailwind检测
    if (document.querySelector('[class*="w-full"], [class*="w-screen"], [class*="container"], [class*="mx-auto"]') ||
        document.querySelector('link[href*="tailwind"]') ||
        document.documentElement.className.includes('tw-')) {
      return 'tailwind';
    }
    
    // 固定头部检测
    const header = document.querySelector('header, .header, #header');
    if (header) {
      const headerStyle = window.getComputedStyle(header);
      if (headerStyle.position === 'fixed' || headerStyle.position === 'sticky') {
        return 'fixed-header';
      }
    }

    return 'default';
  }

  /**
   * Bootstrap特殊处理
   */
  private adjustBootstrapLayout(sidebarWidth: number, position: 'left' | 'right'): void {
    this.adjustPageLayout(sidebarWidth, position);
    
    // 特殊处理Bootstrap容器
    const containers = document.querySelectorAll('.container-fluid, .container');
    containers.forEach(container => {
      const el = container as HTMLElement;
      el.setAttribute('data-original-width', el.style.width || '');
      el.setAttribute('data-original-margin', el.style.marginLeft + '|' + el.style.marginRight);
      
      if (position === 'left') {
        el.style.marginLeft = `${sidebarWidth}px`;
      } else {
        el.style.marginRight = `${sidebarWidth}px`;
      }
    });
  }

  /**
   * Tailwind特殊处理
   */
  private adjustTailwindLayout(sidebarWidth: number, position: 'left' | 'right'): void {
    this.adjustPageLayout(sidebarWidth, position);
    
    // 处理Tailwind的全宽元素
    const fullWidthElements = document.querySelectorAll('[class*="w-screen"], [class*="w-full"]');
    fullWidthElements.forEach(element => {
      const el = element as HTMLElement;
      el.setAttribute('data-original-classes', el.className);
      
      if (position === 'left') {
        el.style.marginLeft = `${sidebarWidth}px`;
        el.style.width = `calc(100% - ${sidebarWidth}px)`;
      } else {
        el.style.marginRight = `${sidebarWidth}px`;
        el.style.width = `calc(100% - ${sidebarWidth}px)`;
      }
    });
  }

  /**
   * 固定头部特殊处理
   */
  private adjustFixedHeaderLayout(sidebarWidth: number, position: 'left' | 'right'): void {
    this.adjustPageLayout(sidebarWidth, position);
    
    // 额外处理固定头部
    const headers = document.querySelectorAll('header, .header, #header, nav, .navbar');
    headers.forEach(header => {
      const headerStyle = window.getComputedStyle(header);
      if (headerStyle.position === 'fixed' || headerStyle.position === 'sticky') {
        const el = header as HTMLElement;
        const currentLeft = parseInt(headerStyle.left) || 0;
        const currentRight = parseInt(headerStyle.right) || 0;
        
        el.setAttribute('data-original-left', headerStyle.left);
        el.setAttribute('data-original-right', headerStyle.right);
        el.setAttribute('data-original-width', headerStyle.width);
        
        if (position === 'left' && currentLeft === 0) {
          el.style.left = `${sidebarWidth}px`;
          el.style.width = `calc(100% - ${sidebarWidth}px)`;
        } else if (position === 'right' && currentRight === 0) {
          el.style.right = `${sidebarWidth}px`;
          el.style.width = `calc(100% - ${sidebarWidth}px)`;
        }
      }
    });
  }

  /**
   * 查找可能冲突的固定元素
   */
  private findConflictingFixedElements(): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const allElements = document.querySelectorAll('*');
    
    allElements.forEach(element => {
      const computedStyle = window.getComputedStyle(element);
      if (computedStyle.position === 'fixed' || computedStyle.position === 'sticky') {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0;
        
        if (isVisible) {
          elements.push(element as HTMLElement);
        }
      }
    });
    
    return elements;
  }

  /**
   * 调整全宽头部元素
   */
  private adjustFullWidthHeader(element: HTMLElement): void {
    element.style.transition = 'width 0.3s ease, margin 0.3s ease';
    // 头部通常需要完整显示，只调整左右边距
    element.style.width = 'calc(100% - 300px)';
    element.style.marginLeft = '300px';
  }

  /**
   * 调整全宽底部元素
   */
  private adjustFullWidthFooter(element: HTMLElement): void {
    element.style.transition = 'width 0.3s ease, margin 0.3s ease';
    // 底部元素通常可以缩小宽度
    element.style.width = 'calc(100% - 300px)';
    element.style.marginLeft = '300px';
  }

  /**
   * 调整全宽横幅元素
   */
  private adjustFullWidthBanner(element: HTMLElement): void {
    element.style.transition = 'width 0.3s ease, margin 0.3s ease';
    // 横幅可能需要保持视觉冲击力，使用padding而不是margin
    element.style.paddingLeft = '300px';
  }

  /**
   * 调整通用全宽元素
   */
  private adjustGenericFullWidthElement(element: HTMLElement): void {
    element.style.transition = 'width 0.3s ease, margin 0.3s ease';
    
    // 检查元素内容类型决定调整策略
    const hasImportantContent = element.querySelector('img, video, canvas, svg');
    const hasText = element.textContent && element.textContent.trim().length > 0;
    
    if (hasImportantContent && !hasText) {
      // 主要是媒体内容，使用margin
      element.style.marginLeft = '300px';
      element.style.width = 'calc(100% - 300px)';
    } else {
      // 有文本或混合内容，使用padding保持可读性
      element.style.paddingLeft = '300px';
    }
  }

  /**
   * 恢复冲突处理的更改
   */
  restoreConflictHandling(): void {
    try {
      // 恢复固定元素
      const conflictElements = document.querySelectorAll('[data-sidebar-conflict="true"]');
      conflictElements.forEach(element => {
        const el = element as HTMLElement;
        const originalZIndex = el.getAttribute('data-original-z-index');
        const originalLeft = el.getAttribute('data-original-left');
        const originalRight = el.getAttribute('data-original-right');
        
        if (originalZIndex) el.style.zIndex = originalZIndex;
        if (originalLeft) el.style.left = originalLeft;
        if (originalRight) el.style.right = originalRight;
        
        el.removeAttribute('data-sidebar-conflict');
        el.removeAttribute('data-original-z-index');
        el.removeAttribute('data-original-left');
        el.removeAttribute('data-original-right');
        
        // 清理title中的警告信息
        if (el.title && el.title.includes('[侧边栏冲突检测]')) {
          el.title = el.title.replace(' [侧边栏冲突检测]', '');
        }
      });
      
      // 恢复z-index调整
      const zAdjustedElements = document.querySelectorAll('[data-sidebar-z-adjusted="true"], [data-sidebar-z-reduced="true"]');
      zAdjustedElements.forEach(element => {
        const el = element as HTMLElement;
        const originalZIndex = el.getAttribute('data-original-z-index');
        
        if (originalZIndex) {
          el.style.zIndex = originalZIndex;
        }
        
        el.removeAttribute('data-sidebar-z-adjusted');
        el.removeAttribute('data-sidebar-z-reduced');
        el.removeAttribute('data-original-z-index');
      });
      
      // 恢复全宽元素
      const fullWidthElements = document.querySelectorAll('[data-original-width]');
      fullWidthElements.forEach(element => {
        const el = element as HTMLElement;
        const originalWidth = el.getAttribute('data-original-width');
        const originalMarginLeft = el.getAttribute('data-original-margin-left');
        const originalMarginRight = el.getAttribute('data-original-margin-right');
        const originalPaddingLeft = el.getAttribute('data-original-padding-left');
        const originalPaddingRight = el.getAttribute('data-original-padding-right');
        
        if (originalWidth) el.style.width = originalWidth;
        if (originalMarginLeft) el.style.marginLeft = originalMarginLeft;
        if (originalMarginRight) el.style.marginRight = originalMarginRight;
        if (originalPaddingLeft) el.style.paddingLeft = originalPaddingLeft;
        if (originalPaddingRight) el.style.paddingRight = originalPaddingRight;
        
        el.removeAttribute('data-original-width');
        el.removeAttribute('data-original-margin-left');
        el.removeAttribute('data-original-margin-right');
        el.removeAttribute('data-original-padding-left');
        el.removeAttribute('data-original-padding-right');
        el.style.transition = '';
      });
      
      // 恢复滚动条
      const scrollbarStyleElement = document.getElementById('sidebar-scrollbar-fix');
      if (scrollbarStyleElement) {
        scrollbarStyleElement.remove();
      }
      
      const body = document.body;
      const originalScrollbarWidth = body.getAttribute('data-original-scrollbar-width');
      const originalWebkitScrollbar = body.getAttribute('data-original-webkit-scrollbar');
      
      if (originalScrollbarWidth) {
        body.style.setProperty('scrollbar-width', originalScrollbarWidth);
        body.removeAttribute('data-original-scrollbar-width');
      }
      
      if (originalWebkitScrollbar) {
        body.style.setProperty('-webkit-scrollbar-width', originalWebkitScrollbar);
        body.removeAttribute('data-original-webkit-scrollbar');
      }
      
      console.log('Conflict handling restored successfully');
    } catch (error) {
      console.error('Error restoring conflict handling:', error);
    }
  }

  // 添加空间保护观察器属性
  private spaceProtectionObserver: MutationObserver | null = null;

  /**
   * 强化空间保证策略 - 多重保护确保空间不被占用
   */
  reinforceSpaceProtection(width: number, position: 'left' | 'right'): void {
    console.log(`Reinforcing space protection: ${width}px on ${position}`);
    
    try {
      // 1. 添加强化保护样式
      this.addReinforcementStyles(width, position);
      
      // 2. 设置DOM变化监听器
      this.setupSpaceProtectionWatcher(width, position);
      
      // 3. 添加周期性验证
      this.startSpaceProtectionValidation(width, position);
      
      // 4. 强化固定元素位置锁定
      this.lockFixedElementPositions(width, position);
      
      // 5. 添加页面滚动保护
      this.setupScrollProtection(position);
      
      // 6. 标记为已保护状态
      document.body.classList.add('sidebar-space-protected');
      
      console.log('Space protection reinforcement applied');
    } catch (error) {
      console.error('Error reinforcing space protection:', error);
    }
  }

  /**
   * 添加强化保护样式
   */
  private addReinforcementStyles(width: number, position: 'left' | 'right'): void {
    const existingStyle = document.getElementById('sidebar-space-reinforcement');
    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = 'sidebar-space-reinforcement';
    
    const oppositePosition = position === 'left' ? 'right' : 'left';
    
    style.textContent = `
      /* 强化空间保护样式 */
      .sidebar-space-protected {
        --sidebar-protected-width: ${width}px;
        --sidebar-protected-position: ${position};
      }

      /* 多重保护屏障 */
      .sidebar-space-protected::after {
        content: '';
        position: fixed !important;
        top: 0 !important;
        ${position}: 0 !important;
        width: ${width}px !important;
        height: 100vh !important;
        z-index: 2147483646 !important;
        pointer-events: none !important;
        background: transparent !important;
        border-${oppositePosition}: 1px solid transparent !important;
      }

      /* 强制所有子元素避开保护区域 */
      .sidebar-space-protected * {
        max-width: calc(100vw - ${width}px) !important;
      }

      /* 固定元素强制重定位 */
      .sidebar-space-protected [style*="position: fixed"],
      .sidebar-space-protected [style*="position:fixed"] {
        ${position}: ${width}px !important;
        width: calc(100% - ${width}px) !important;
        max-width: calc(100vw - ${width}px) !important;
      }

      /* 全宽元素强制限制 */
      .sidebar-space-protected [style*="width: 100vw"],
      .sidebar-space-protected [style*="width:100vw"],
      .sidebar-space-protected .w-screen,
      .sidebar-space-protected .w-full,
      .sidebar-space-protected .container-fluid {
        width: calc(100vw - ${width}px) !important;
        margin-${position}: ${width}px !important;
        max-width: calc(100vw - ${width}px) !important;
      }

      /* 防止内容溢出到保护区域 */
      .sidebar-space-protected {
        overflow-x: hidden !important;
      }

      /* 强化根容器保护 */
      html.sidebar-space-protected,
      body.sidebar-space-protected {
        margin-${position}: ${width}px !important;
        width: calc(100% - ${width}px) !important;
        max-width: calc(100vw - ${width}px) !important;
        box-sizing: border-box !important;
      }

      /* 特殊框架元素强制调整 */
      .sidebar-space-protected .container,
      .sidebar-space-protected .container-xl,
      .sidebar-space-protected .container-lg,
      .sidebar-space-protected .container-md,
      .sidebar-space-protected .container-sm,
      .sidebar-space-protected [class*="container"] {
        margin-${position}: ${width}px !important;
        width: calc(100% - ${width}px) !important;
        max-width: calc(100vw - ${width}px) !important;
      }

      /* 媒体查询保护 - 小屏幕下的额外保护 */
      @media (max-width: 768px) {
        .sidebar-space-protected {
          --sidebar-protected-width: 60px;
        }
        
        .sidebar-space-protected::after {
          width: 60px !important;
        }
      }

      /* 阻止JavaScript动态修改保护区域 */
      .sidebar-space-protected [data-sidebar-protected="true"] {
        ${position}: ${width}px !important;
        width: calc(100% - ${width}px) !important;
      }
    `;

    document.head.appendChild(style);
    console.log('Reinforcement styles added');
  }

  /**
   * 设置空间保护监听器
   */
  private setupSpaceProtectionWatcher(width: number, position: 'left' | 'right'): void {
    if (this.spaceProtectionObserver) {
      this.spaceProtectionObserver.disconnect();
    }

    // 创建MutationObserver监听DOM变化
    this.spaceProtectionObserver = new MutationObserver((mutations) => {
      let needsReinforcement = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查新添加的元素是否侵犯空间
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              if (this.isElementInvadingSpace(element, width, position)) {
                this.enforceSpaceCompliance(element, width, position);
                needsReinforcement = true;
              }
            }
          });
        } else if (mutation.type === 'attributes') {
          // 检查样式属性变化是否影响空间
          const element = mutation.target as HTMLElement;
          if (element && (
            mutation.attributeName === 'style' ||
            mutation.attributeName === 'class'
          )) {
            if (this.isElementInvadingSpace(element, width, position)) {
              this.enforceSpaceCompliance(element, width, position);
              needsReinforcement = true;
            }
          }
        }
      });

      // 如果发现侵犯，延迟重新强化
      if (needsReinforcement) {
        setTimeout(() => {
          this.validateSpaceIntegrity(width, position);
        }, 100);
      }
    });

    // 开始监听
    this.spaceProtectionObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    console.log('Space protection watcher set up');
  }

  /**
   * 检查元素是否侵犯保护空间
   */
  private isElementInvadingSpace(element: HTMLElement, width: number, position: 'left' | 'right'): boolean {
    try {
      const rect = element.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(element);
      
      // 检查固定或绝对定位元素
      if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
        if (position === 'left' && rect.left < width) {
          return true;
        }
        if (position === 'right' && rect.right > (window.innerWidth - width)) {
          return true;
        }
      }

      // 检查全宽元素
      if (rect.width >= window.innerWidth - 50) { // 允许50px的容错
        return true;
      }

      // 检查z-index过高的元素
      const zIndex = parseInt(computedStyle.zIndex) || 0;
      if (zIndex >= 2147483647) {
        return true;
      }

      return false;
    } catch (error) {
      console.warn('Error checking element invasion:', error);
      return false;
    }
  }

  /**
   * 强制元素遵守空间规则
   */
  private enforceSpaceCompliance(element: HTMLElement, width: number, position: 'left' | 'right'): void {
    try {
      console.log('Enforcing space compliance for element:', element);
      
      // 添加保护标记
      element.setAttribute('data-sidebar-protected', 'true');
      
      const computedStyle = window.getComputedStyle(element);
      
      // 处理固定定位元素
      if (computedStyle.position === 'fixed') {
        const currentLeft = parseInt(computedStyle.left) || 0;
        const currentRight = parseInt(computedStyle.right) || 0;
        
        if (position === 'left' && currentLeft < width) {
          element.style.left = `${width}px`;
          element.style.width = `calc(100% - ${width}px)`;
        } else if (position === 'right' && currentRight < width) {
          element.style.right = `${width}px`;
          element.style.width = `calc(100% - ${width}px)`;
        }
      }

      // 限制z-index
      const zIndex = parseInt(computedStyle.zIndex) || 0;
      if (zIndex >= 2147483647) {
        element.style.zIndex = '2147483646';
      }

      // 限制宽度
      const rect = element.getBoundingClientRect();
      if (rect.width >= window.innerWidth - 50) {
        element.style.maxWidth = `calc(100vw - ${width}px)`;
        element.style.width = `calc(100% - ${width}px)`;
        
        if (position === 'left') {
          element.style.marginLeft = `${width}px`;
        } else {
          element.style.marginRight = `${width}px`;
        }
      }

    } catch (error) {
      console.warn('Error enforcing space compliance:', error);
    }
  }

  /**
   * 开始周期性空间保护验证
   */
  private startSpaceProtectionValidation(width: number, position: 'left' | 'right'): void {
    // 每5秒验证一次空间完整性
    setInterval(() => {
      this.validateSpaceIntegrity(width, position);
    }, 5000);
  }

  /**
   * 验证空间完整性
   */
  private validateSpaceIntegrity(width: number, position: 'left' | 'right'): void {
    try {
      console.log('Validating space integrity...');
      
      let violationsFound = 0;
      
      // 检查所有可能的侵犯元素
      const allElements = document.querySelectorAll('*');
      allElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (this.isElementInvadingSpace(htmlElement, width, position)) {
          this.enforceSpaceCompliance(htmlElement, width, position);
          violationsFound++;
        }
      });

      // 强化根元素保护
      this.reinforceRootElementsProtection(width, position);
      
      if (violationsFound > 0) {
        console.warn(`Space integrity validation found ${violationsFound} violations, corrected`);
      } else {
        console.log('Space integrity validation passed');
      }
      
    } catch (error) {
      console.error('Error during space integrity validation:', error);
    }
  }

  /**
   * 锁定固定元素位置
   */
  private lockFixedElementPositions(width: number, position: 'left' | 'right'): void {
    const fixedElements = document.querySelectorAll('[style*="position: fixed"], [style*="position:fixed"]');
    
    fixedElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      const computedStyle = window.getComputedStyle(htmlElement);
      
      // 保存并锁定位置
      const originalLeft = computedStyle.left;
      const originalRight = computedStyle.right;
      const originalWidth = computedStyle.width;
      
      htmlElement.setAttribute('data-original-left', originalLeft);
      htmlElement.setAttribute('data-original-right', originalRight);
      htmlElement.setAttribute('data-original-width', originalWidth);
      htmlElement.setAttribute('data-position-locked', 'true');
      
      // 应用保护位置
      if (position === 'left') {
        const currentLeft = parseInt(originalLeft) || 0;
        if (currentLeft < width) {
          htmlElement.style.left = `${Math.max(currentLeft + width, width)}px`;
          htmlElement.style.width = `calc(100% - ${width}px)`;
        }
      } else {
        const currentRight = parseInt(originalRight) || 0;
        if (currentRight < width) {
          htmlElement.style.right = `${Math.max(currentRight + width, width)}px`;
          htmlElement.style.width = `calc(100% - ${width}px)`;
        }
      }
    });

    console.log(`Locked ${fixedElements.length} fixed elements positions`);
  }

  /**
   * 设置滚动保护
   */
  private setupScrollProtection(position: 'left' | 'right'): void {
    // 防止水平滚动影响空间保护
    document.addEventListener('scroll', () => {
      const protectionElement = document.querySelector('.sidebar-space-protected::after') as HTMLElement;
      if (protectionElement) {
        protectionElement.style.position = 'fixed';
        protectionElement.style[position] = '0px';
      }
    }, { passive: true });
  }

  /**
   * 强化根元素保护
   */
  private reinforceRootElementsProtection(width: number, position: 'left' | 'right'): void {
    // 确保html和body始终保持正确的边距
    const html = document.documentElement;
    const body = document.body;
    
    // 强化HTML元素
    if (!html.style.getPropertyValue(`margin-${position}`) || 
        parseInt(html.style.getPropertyValue(`margin-${position}`)) < width) {
      html.style.setProperty(`margin-${position}`, `${width}px`, 'important');
    }
    
    // 强化BODY元素
    if (!body.style.getPropertyValue(`margin-${position}`) || 
        parseInt(body.style.getPropertyValue(`margin-${position}`)) < width) {
      body.style.setProperty(`margin-${position}`, `${width}px`, 'important');
      body.style.setProperty('width', `calc(100% - ${width}px)`, 'important');
      body.style.setProperty('max-width', `calc(100vw - ${width}px)`, 'important');
    }
  }
}