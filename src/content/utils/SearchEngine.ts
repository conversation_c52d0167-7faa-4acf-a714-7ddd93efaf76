/**
 * Search Engine - 搜索引擎工具类
 */

import { BookmarkNode } from '../../types/index.js';
import { TabInfo } from '../../types/index.js';

export interface SearchResult<T> {
  item: T;
  score: number;
  matches: SearchMatch[];
  type: 'bookmark' | 'tab' | 'folder';
}

export interface SearchMatch {
  field: string;
  indices: [number, number][];
  value: string;
}

export interface SearchOptions {
  threshold: number;
  includeScore: boolean;
  includeMatches: boolean;
  minMatchCharLength: number;
  maxResults: number;
  searchFields: string[];
}

export class SearchEngine {
  private options: SearchOptions;

  constructor(options: Partial<SearchOptions> = {}) {
    this.options = {
      threshold: 0.3,
      includeScore: true,
      includeMatches: true,
      minMatchCharLength: 1,
      maxResults: 50,
      searchFields: ['title', 'url'],
      ...options
    };
  }

  /**
   * 搜索收藏夹
   */
  searchBookmarks(bookmarks: BookmarkNode[], query: string): SearchResult<BookmarkNode>[] {
    if (!query.trim()) return [];

    const results: SearchResult<BookmarkNode>[] = [];
    const normalizedQuery = this.normalizeQuery(query);

    this.searchBookmarksRecursive(bookmarks, normalizedQuery, results);

    // 按分数排序并限制结果数量
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, this.options.maxResults);
  }

  /**
   * 搜索标签页
   */
  searchTabs(tabs: TabInfo[], query: string): SearchResult<TabInfo>[] {
    if (!query.trim()) return [];

    const results: SearchResult<TabInfo>[] = [];
    const normalizedQuery = this.normalizeQuery(query);

    tabs.forEach(tab => {
      const result = this.searchItem(tab, normalizedQuery, 'tab');
      if (result && result.score >= this.options.threshold) {
        results.push(result);
      }
    });

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, this.options.maxResults);
  }

  /**
   * 高亮搜索结果
   */
  highlightMatches(text: string, matches: SearchMatch[], className = 'search-highlight'): string {
    if (!matches.length) return text;

    let highlightedText = text;
    let offset = 0;

    // 按索引位置排序
    const sortedIndices = matches
      .flatMap(match => match.indices)
      .sort((a, b) => a[0] - b[0]);

    // 合并重叠的匹配区间
    const mergedIndices = this.mergeOverlappingIndices(sortedIndices);

    // 从后往前插入高亮标签，避免索引偏移
    mergedIndices.reverse().forEach(([start, end]) => {
      const before = highlightedText.slice(0, start + offset);
      const match = highlightedText.slice(start + offset, end + offset + 1);
      const after = highlightedText.slice(end + offset + 1);
      
      highlightedText = before + `<mark class="${className}">${match}</mark>` + after;
      offset += `<mark class="${className}"></mark>`.length;
    });

    return highlightedText;
  }

  /**
   * 创建搜索建议
   */
  createSuggestions(query: string, items: (BookmarkNode | TabInfo)[]): string[] {
    const suggestions = new Set<string>();
    const normalizedQuery = this.normalizeQuery(query);

    items.forEach(item => {
      const title = item.title.toLowerCase();
      const url = (item as any).url?.toLowerCase() || '';

      // 基于标题的建议
      if (title.includes(normalizedQuery)) {
        const words = title.split(/\s+/);
        words.forEach(word => {
          if (word.length >= 3 && word.startsWith(normalizedQuery)) {
            suggestions.add(word);
          }
        });
      }

      // 基于URL的建议
      if (url.includes(normalizedQuery)) {
        try {
          const urlObj = new URL(url);
          const domain = urlObj.hostname.replace('www.', '');
          if (domain.startsWith(normalizedQuery)) {
            suggestions.add(domain);
          }
        } catch (error) {
          // 忽略无效URL
        }
      }
    });

    return Array.from(suggestions).slice(0, 5);
  }

  /**
   * 获取搜索统计信息
   */
  getSearchStats(results: SearchResult<any>[]): {
    total: number;
    bookmarks: number;
    tabs: number;
    folders: number;
    avgScore: number;
  } {
    const stats = {
      total: results.length,
      bookmarks: 0,
      tabs: 0,
      folders: 0,
      avgScore: 0
    };

    let totalScore = 0;

    results.forEach(result => {
      totalScore += result.score;
      stats[result.type]++;
    });

    stats.avgScore = results.length > 0 ? totalScore / results.length : 0;

    return stats;
  }

  /**
   * 递归搜索收藏夹
   */
  private searchBookmarksRecursive(
    bookmarks: BookmarkNode[], 
    query: string, 
    results: SearchResult<BookmarkNode>[]
  ): void {
    bookmarks.forEach(bookmark => {
      const type = bookmark.children ? 'folder' : 'bookmark';
      const result = this.searchItem(bookmark, query, type as 'bookmark' | 'folder');
      
      if (result && result.score >= this.options.threshold) {
        results.push(result);
      }

      // 递归搜索子项目
      if (bookmark.children) {
        this.searchBookmarksRecursive(bookmark.children, query, results);
      }
    });
  }

  /**
   * 搜索单个项目
   */
  private searchItem<T extends BookmarkNode | TabInfo>(
    item: T, 
    query: string, 
    type: 'bookmark' | 'tab' | 'folder'
  ): SearchResult<T> | null {
    const matches: SearchMatch[] = [];
    let totalScore = 0;
    let matchCount = 0;

    this.options.searchFields.forEach(field => {
      const value = (item as any)[field];
      if (typeof value === 'string') {
        const fieldMatches = this.findMatches(value, query);
        if (fieldMatches.length > 0) {
          matches.push({
            field,
            indices: fieldMatches,
            value
          });
          
          // 计算字段分数
          const fieldScore = this.calculateFieldScore(value, query, fieldMatches);
          totalScore += fieldScore;
          matchCount++;
        }
      }
    });

    if (matchCount === 0) return null;

    // 计算最终分数
    const score = totalScore / matchCount;

    return {
      item,
      score,
      matches,
      type
    };
  }

  /**
   * 查找匹配项
   */
  private findMatches(text: string, query: string): [number, number][] {
    const matches: [number, number][] = [];
    const normalizedText = text.toLowerCase();
    const queryLength = query.length;

    if (queryLength < this.options.minMatchCharLength) {
      return matches;
    }

    // 精确匹配
    let index = normalizedText.indexOf(query);
    while (index !== -1) {
      matches.push([index, index + queryLength - 1]);
      index = normalizedText.indexOf(query, index + 1);
    }

    // 模糊匹配（如果没有精确匹配）
    if (matches.length === 0) {
      const fuzzyMatches = this.findFuzzyMatches(normalizedText, query);
      matches.push(...fuzzyMatches);
    }

    return matches;
  }

  /**
   * 查找模糊匹配
   */
  private findFuzzyMatches(text: string, query: string): [number, number][] {
    const matches: [number, number][] = [];
    const textLength = text.length;
    const queryLength = query.length;

    if (queryLength > textLength) return matches;

    let textIndex = 0;
    let queryIndex = 0;
    let matchStart = -1;

    while (textIndex < textLength && queryIndex < queryLength) {
      if (text[textIndex] === query[queryIndex]) {
        if (matchStart === -1) {
          matchStart = textIndex;
        }
        queryIndex++;
      } else if (matchStart !== -1) {
        // 如果已经开始匹配但当前字符不匹配，结束当前匹配
        matches.push([matchStart, textIndex - 1]);
        matchStart = -1;
        queryIndex = 0;
      }
      textIndex++;
    }

    // 如果查询完全匹配
    if (queryIndex === queryLength && matchStart !== -1) {
      matches.push([matchStart, textIndex - 1]);
    }

    return matches;
  }

  /**
   * 计算字段分数
   */
  private calculateFieldScore(text: string, query: string, matches: [number, number][]): number {
    if (matches.length === 0) return 0;

    const textLength = text.length;
    const queryLength = query.length;
    let score = 0;

    matches.forEach(([start, end]) => {
      const matchLength = end - start + 1;
      
      // 基础分数：匹配长度 / 查询长度
      let matchScore = matchLength / queryLength;
      
      // 位置加权：开头匹配得分更高
      const positionWeight = 1 - (start / textLength) * 0.5;
      matchScore *= positionWeight;
      
      // 连续匹配加权
      if (matchLength === queryLength) {
        matchScore *= 1.5; // 完全匹配加权
      }
      
      score += matchScore;
    });

    // 归一化分数
    return Math.min(score, 1);
  }

  /**
   * 合并重叠的索引区间
   */
  private mergeOverlappingIndices(indices: [number, number][]): [number, number][] {
    if (indices.length <= 1) return indices;

    const merged: [number, number][] = [];
    let current = indices[0];

    for (let i = 1; i < indices.length; i++) {
      const next = indices[i];
      
      if (current[1] >= next[0] - 1) {
        // 重叠或相邻，合并
        current = [current[0], Math.max(current[1], next[1])];
      } else {
        // 不重叠，添加当前区间并更新
        merged.push(current);
        current = next;
      }
    }
    
    merged.push(current);
    return merged;
  }

  /**
   * 规范化查询字符串
   */
  private normalizeQuery(query: string): string {
    return query.toLowerCase().trim();
  }

  /**
   * 更新搜索选项
   */
  updateOptions(options: Partial<SearchOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * 获取当前搜索选项
   */
  getOptions(): SearchOptions {
    return { ...this.options };
  }
}