/**
 * Search Keyboard Handler - 搜索键盘处理器
 */

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: string;
  description: string;
}

export interface SearchKeyboardHandlerOptions {
  enableGlobalShortcuts?: boolean;
  enableNavigationKeys?: boolean;
  enableActionKeys?: boolean;
}

export class SearchKeyboardHandler {
  private options: SearchKeyboardHandlerOptions;
  private shortcuts: Map<string, KeyboardShortcut> = new Map();
  private actionCallbacks: Map<string, (() => void)[]> = new Map();
  private globalListenerAttached = false;

  constructor(options: SearchKeyboardHandlerOptions = {}) {
    this.options = {
      enableGlobalShortcuts: true,
      enableNavigationKeys: true,
      enableActionKeys: true,
      ...options
    };

    this.setupDefaultShortcuts();
  }

  /**
   * 注册快捷键
   */
  registerShortcut(shortcut: KeyboardShortcut): void {
    const key = this.createShortcutKey(shortcut);
    this.shortcuts.set(key, shortcut);
  }

  /**
   * 注销快捷键
   */
  unregisterShortcut(key: string, ctrlKey = false, metaKey = false, shiftKey = false, altKey = false): void {
    const shortcutKey = this.createShortcutKey({ key, ctrlKey, metaKey, shiftKey, altKey } as KeyboardShortcut);
    this.shortcuts.delete(shortcutKey);
  }

  /**
   * 添加动作回调
   */
  onAction(action: string, callback: () => void): void {
    if (!this.actionCallbacks.has(action)) {
      this.actionCallbacks.set(action, []);
    }
    this.actionCallbacks.get(action)!.push(callback);
  }

  /**
   * 移除动作回调
   */
  offAction(action: string, callback?: () => void): void {
    if (!this.actionCallbacks.has(action)) return;

    if (callback) {
      const callbacks = this.actionCallbacks.get(action)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.actionCallbacks.delete(action);
    }
  }

  /**
   * 启用全局快捷键监听
   */
  enableGlobalShortcuts(): void {
    if (this.globalListenerAttached) return;

    document.addEventListener('keydown', this.handleGlobalKeydown);
    this.globalListenerAttached = true;
  }

  /**
   * 禁用全局快捷键监听
   */
  disableGlobalShortcuts(): void {
    if (!this.globalListenerAttached) return;

    document.removeEventListener('keydown', this.handleGlobalKeydown);
    this.globalListenerAttached = false;
  }

  /**
   * 处理搜索框键盘事件
   */
  handleSearchBoxKeydown(event: KeyboardEvent): boolean {
    const action = this.getActionFromEvent(event);
    if (action) {
      this.executeAction(action);
      return true;
    }
    return false;
  }

  /**
   * 处理搜索结果键盘事件
   */
  handleSearchResultsKeydown(event: KeyboardEvent): boolean {
    if (!this.options.enableNavigationKeys) return false;

    const action = this.getActionFromEvent(event);
    if (action && this.isNavigationAction(action)) {
      this.executeAction(action);
      return true;
    }
    return false;
  }

  /**
   * 处理建议框键盘事件
   */
  handleSuggestionsKeydown(event: KeyboardEvent): boolean {
    const action = this.getActionFromEvent(event);
    if (action && this.isSuggestionAction(action)) {
      this.executeAction(action);
      return true;
    }
    return false;
  }

  /**
   * 获取所有快捷键
   */
  getAllShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values());
  }

  /**
   * 获取快捷键帮助信息
   */
  getShortcutHelp(): { category: string; shortcuts: KeyboardShortcut[] }[] {
    const categories = {
      search: { name: '搜索', shortcuts: [] as KeyboardShortcut[] },
      navigation: { name: '导航', shortcuts: [] as KeyboardShortcut[] },
      actions: { name: '操作', shortcuts: [] as KeyboardShortcut[] }
    };

    this.shortcuts.forEach(shortcut => {
      if (shortcut.action.startsWith('search.')) {
        categories.search.shortcuts.push(shortcut);
      } else if (shortcut.action.startsWith('navigate.')) {
        categories.navigation.shortcuts.push(shortcut);
      } else {
        categories.actions.shortcuts.push(shortcut);
      }
    });

    return Object.entries(categories).map(([key, category]) => ({
      category: category.name,
      shortcuts: category.shortcuts
    }));
  }

  /**
   * 销毁处理器
   */
  destroy(): void {
    this.disableGlobalShortcuts();
    this.shortcuts.clear();
    this.actionCallbacks.clear();
  }

  /**
   * 设置默认快捷键
   */
  private setupDefaultShortcuts(): void {
    const defaultShortcuts: KeyboardShortcut[] = [
      // 搜索快捷键
      {
        key: 'f',
        ctrlKey: true,
        action: 'search.focus',
        description: '聚焦搜索框'
      },
      {
        key: 'k',
        ctrlKey: true,
        action: 'search.focus',
        description: '快速搜索'
      },
      {
        key: 'f',
        metaKey: true,
        action: 'search.focus',
        description: '聚焦搜索框 (Mac)'
      },
      {
        key: 'k',
        metaKey: true,
        action: 'search.focus',
        description: '快速搜索 (Mac)'
      },
      {
        key: 'Escape',
        action: 'search.clear',
        description: '清除搜索或失焦'
      },
      {
        key: 'Enter',
        action: 'search.submit',
        description: '提交搜索'
      },

      // 导航快捷键
      {
        key: 'ArrowDown',
        action: 'navigate.next',
        description: '下一个结果'
      },
      {
        key: 'ArrowUp',
        action: 'navigate.previous',
        description: '上一个结果'
      },
      {
        key: 'Home',
        action: 'navigate.first',
        description: '第一个结果'
      },
      {
        key: 'End',
        action: 'navigate.last',
        description: '最后一个结果'
      },
      {
        key: 'PageDown',
        action: 'navigate.pageDown',
        description: '向下翻页'
      },
      {
        key: 'PageUp',
        action: 'navigate.pageUp',
        description: '向上翻页'
      },

      // 操作快捷键
      {
        key: 'Enter',
        action: 'action.select',
        description: '选择当前项目'
      },
      {
        key: ' ',
        action: 'action.select',
        description: '选择当前项目'
      },
      {
        key: 'Enter',
        ctrlKey: true,
        action: 'action.openInNewTab',
        description: '在新标签页中打开'
      },
      {
        key: 'Enter',
        metaKey: true,
        action: 'action.openInNewTab',
        description: '在新标签页中打开 (Mac)'
      },
      {
        key: 'Delete',
        action: 'action.delete',
        description: '删除项目'
      },
      {
        key: 'Backspace',
        action: 'action.delete',
        description: '删除项目'
      },

      // 过滤器快捷键
      {
        key: '1',
        altKey: true,
        action: 'filter.bookmarks',
        description: '过滤收藏夹'
      },
      {
        key: '2',
        altKey: true,
        action: 'filter.folders',
        description: '过滤文件夹'
      },
      {
        key: '3',
        altKey: true,
        action: 'filter.tabs',
        description: '过滤标签页'
      },
      {
        key: 'c',
        altKey: true,
        action: 'filter.clear',
        description: '清除所有过滤器'
      }
    ];

    defaultShortcuts.forEach(shortcut => {
      this.registerShortcut(shortcut);
    });
  }

  /**
   * 创建快捷键标识
   */
  private createShortcutKey(shortcut: Partial<KeyboardShortcut>): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('ctrl');
    if (shortcut.metaKey) parts.push('meta');
    if (shortcut.shiftKey) parts.push('shift');
    if (shortcut.altKey) parts.push('alt');
    parts.push(shortcut.key?.toLowerCase() || '');
    return parts.join('+');
  }

  /**
   * 从事件获取动作
   */
  private getActionFromEvent(event: KeyboardEvent): string | null {
    const key = this.createShortcutKey({
      key: event.key,
      ctrlKey: event.ctrlKey,
      metaKey: event.metaKey,
      shiftKey: event.shiftKey,
      altKey: event.altKey
    });

    const shortcut = this.shortcuts.get(key);
    return shortcut ? shortcut.action : null;
  }

  /**
   * 执行动作
   */
  private executeAction(action: string): void {
    const callbacks = this.actionCallbacks.get(action);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error(`Error executing action ${action}:`, error);
        }
      });
    }
  }

  /**
   * 检查是否为导航动作
   */
  private isNavigationAction(action: string): boolean {
    return action.startsWith('navigate.');
  }

  /**
   * 检查是否为建议动作
   */
  private isSuggestionAction(action: string): boolean {
    return ['navigate.next', 'navigate.previous', 'action.select', 'search.clear'].includes(action);
  }

  /**
   * 处理全局键盘事件
   */
  private handleGlobalKeydown = (event: KeyboardEvent): void => {
    if (!this.options.enableGlobalShortcuts) return;

    // 忽略在输入框中的快捷键（除了特定的搜索快捷键）
    const target = event.target as HTMLElement;
    const isInputElement = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true';
    
    if (isInputElement) {
      const action = this.getActionFromEvent(event);
      if (!action || !['search.focus', 'search.clear', 'search.submit'].includes(action)) {
        return;
      }
    }

    const action = this.getActionFromEvent(event);
    if (action) {
      event.preventDefault();
      event.stopPropagation();
      this.executeAction(action);
    }
  };

  /**
   * 格式化快捷键显示
   */
  static formatShortcut(shortcut: KeyboardShortcut): string {
    const parts = [];
    
    // 检测操作系统
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    
    if (shortcut.ctrlKey) {
      parts.push(isMac ? '⌃' : 'Ctrl');
    }
    if (shortcut.metaKey) {
      parts.push(isMac ? '⌘' : 'Meta');
    }
    if (shortcut.altKey) {
      parts.push(isMac ? '⌥' : 'Alt');
    }
    if (shortcut.shiftKey) {
      parts.push(isMac ? '⇧' : 'Shift');
    }
    
    // 格式化按键名称
    let keyName = shortcut.key;
    const keyMappings: Record<string, string> = {
      'ArrowUp': '↑',
      'ArrowDown': '↓',
      'ArrowLeft': '←',
      'ArrowRight': '→',
      'Enter': '⏎',
      'Escape': 'Esc',
      'Delete': 'Del',
      'Backspace': '⌫',
      'PageUp': 'PgUp',
      'PageDown': 'PgDn',
      ' ': 'Space'
    };
    
    if (keyMappings[keyName]) {
      keyName = keyMappings[keyName];
    } else if (keyName.length === 1) {
      keyName = keyName.toUpperCase();
    }
    
    parts.push(keyName);
    
    return parts.join(isMac ? '' : '+');
  }

  /**
   * 创建快捷键帮助HTML
   */
  static createShortcutHelpHTML(shortcuts: KeyboardShortcut[]): string {
    const html = shortcuts.map(shortcut => `
      <div class="shortcut-item">
        <span class="shortcut-keys">${SearchKeyboardHandler.formatShortcut(shortcut)}</span>
        <span class="shortcut-description">${shortcut.description}</span>
      </div>
    `).join('');

    return `
      <div class="shortcut-help">
        <style>
          .shortcut-help {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 14px;
          }
          .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid #e1e4e8;
          }
          .shortcut-item:last-child {
            border-bottom: none;
          }
          .shortcut-keys {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 12px;
            background-color: #f6f8fa;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #d0d7de;
            white-space: nowrap;
          }
          .shortcut-description {
            color: #656d76;
            margin-left: 12px;
          }
        </style>
        ${html}
      </div>
    `;
  }
}