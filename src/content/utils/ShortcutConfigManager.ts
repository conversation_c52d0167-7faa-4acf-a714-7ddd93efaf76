/**
 * Shortcut Config Manager - 快捷键配置管理器
 */

import { GlobalShortcut } from './GlobalKeyboardManager.js';

export interface ShortcutConfig {
  version: string;
  shortcuts: GlobalShortcut[];
  customShortcuts: GlobalShortcut[];
  disabledShortcuts: string[];
  lastModified: number;
}

export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export class ShortcutConfigManager {
  private static readonly STORAGE_KEY = 'sidebar-shortcut-config';
  private static readonly CONFIG_VERSION = '1.0.0';
  private static readonly MAX_CUSTOM_SHORTCUTS = 50;

  private config: ShortcutConfig;
  private changeCallbacks: ((config: ShortcutConfig) => void)[] = [];

  constructor() {
    this.config = this.getDefaultConfig();
    this.loadConfig();
  }

  /**
   * 获取当前配置
   */
  getConfig(): ShortcutConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<ShortcutConfig>): boolean {
    try {
      const newConfig = { ...this.config, ...updates };
      const validation = this.validateConfig(newConfig);
      
      if (!validation.valid) {
        console.error('Invalid shortcut config:', validation.errors);
        return false;
      }

      this.config = newConfig;
      this.config.lastModified = Date.now();
      
      this.saveConfig();
      this.notifyChange();
      
      return true;
    } catch (error) {
      console.error('Error updating shortcut config:', error);
      return false;
    }
  }

  /**
   * 添加自定义快捷键
   */
  addCustomShortcut(shortcut: Omit<GlobalShortcut, 'id'>): string | null {
    if (this.config.customShortcuts.length >= ShortcutConfigManager.MAX_CUSTOM_SHORTCUTS) {
      console.warn('Maximum custom shortcuts limit reached');
      return null;
    }

    const id = this.generateShortcutId(shortcut);
    const fullShortcut: GlobalShortcut = {
      id,
      ...shortcut,
      enabled: true
    };

    // 检查冲突
    if (this.hasConflict(fullShortcut)) {
      console.warn('Shortcut conflicts with existing shortcut:', id);
      return null;
    }

    this.config.customShortcuts.push(fullShortcut);
    this.config.lastModified = Date.now();
    
    this.saveConfig();
    this.notifyChange();
    
    return id;
  }

  /**
   * 移除自定义快捷键
   */
  removeCustomShortcut(id: string): boolean {
    const index = this.config.customShortcuts.findIndex(s => s.id === id);
    if (index === -1) return false;

    this.config.customShortcuts.splice(index, 1);
    this.config.lastModified = Date.now();
    
    this.saveConfig();
    this.notifyChange();
    
    return true;
  }

  /**
   * 更新自定义快捷键
   */
  updateCustomShortcut(id: string, updates: Partial<GlobalShortcut>): boolean {
    const shortcut = this.config.customShortcuts.find(s => s.id === id);
    if (!shortcut) return false;

    const updatedShortcut = { ...shortcut, ...updates };
    
    // 检查冲突（排除自己）
    if (this.hasConflict(updatedShortcut, id)) {
      console.warn('Updated shortcut conflicts with existing shortcut:', id);
      return false;
    }

    Object.assign(shortcut, updates);
    this.config.lastModified = Date.now();
    
    this.saveConfig();
    this.notifyChange();
    
    return true;
  }

  /**
   * 禁用快捷键
   */
  disableShortcut(id: string): boolean {
    if (this.config.disabledShortcuts.includes(id)) {
      return true; // 已经禁用
    }

    this.config.disabledShortcuts.push(id);
    this.config.lastModified = Date.now();
    
    this.saveConfig();
    this.notifyChange();
    
    return true;
  }

  /**
   * 启用快捷键
   */
  enableShortcut(id: string): boolean {
    const index = this.config.disabledShortcuts.indexOf(id);
    if (index === -1) {
      return true; // 已经启用
    }

    this.config.disabledShortcuts.splice(index, 1);
    this.config.lastModified = Date.now();
    
    this.saveConfig();
    this.notifyChange();
    
    return true;
  }

  /**
   * 检查快捷键是否被禁用
   */
  isShortcutDisabled(id: string): boolean {
    return this.config.disabledShortcuts.includes(id);
  }

  /**
   * 获取所有快捷键（包括自定义）
   */
  getAllShortcuts(): GlobalShortcut[] {
    return [
      ...this.config.shortcuts.map(s => ({
        ...s,
        enabled: !this.isShortcutDisabled(s.id)
      })),
      ...this.config.customShortcuts.map(s => ({
        ...s,
        enabled: !this.isShortcutDisabled(s.id)
      }))
    ];
  }

  /**
   * 按类别获取快捷键
   */
  getShortcutsByCategory(category: string): GlobalShortcut[] {
    return this.getAllShortcuts().filter(s => s.category === category);
  }

  /**
   * 获取启用的快捷键
   */
  getEnabledShortcuts(): GlobalShortcut[] {
    return this.getAllShortcuts().filter(s => s.enabled);
  }

  /**
   * 获取自定义快捷键
   */
  getCustomShortcuts(): GlobalShortcut[] {
    return [...this.config.customShortcuts];
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): boolean {
    try {
      this.config = this.getDefaultConfig();
      this.saveConfig();
      this.notifyChange();
      return true;
    } catch (error) {
      console.error('Error resetting shortcut config:', error);
      return false;
    }
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): boolean {
    try {
      const importedConfig = JSON.parse(configJson) as ShortcutConfig;
      const validation = this.validateConfig(importedConfig);
      
      if (!validation.valid) {
        console.error('Invalid imported config:', validation.errors);
        return false;
      }

      // 合并配置（保留现有的自定义快捷键）
      const mergedConfig: ShortcutConfig = {
        ...importedConfig,
        customShortcuts: [
          ...this.config.customShortcuts,
          ...importedConfig.customShortcuts.filter(imported => 
            !this.config.customShortcuts.some(existing => existing.id === imported.id)
          )
        ],
        lastModified: Date.now()
      };

      this.config = mergedConfig;
      this.saveConfig();
      this.notifyChange();
      
      return true;
    } catch (error) {
      console.error('Error importing shortcut config:', error);
      return false;
    }
  }

  /**
   * 检查配置冲突
   */
  checkConflicts(): { shortcut1: GlobalShortcut; shortcut2: GlobalShortcut }[] {
    const conflicts: { shortcut1: GlobalShortcut; shortcut2: GlobalShortcut }[] = [];
    const allShortcuts = this.getEnabledShortcuts();

    for (let i = 0; i < allShortcuts.length; i++) {
      for (let j = i + 1; j < allShortcuts.length; j++) {
        if (this.shortcutsConflict(allShortcuts[i], allShortcuts[j])) {
          conflicts.push({ 
            shortcut1: allShortcuts[i], 
            shortcut2: allShortcuts[j] 
          });
        }
      }
    }

    return conflicts;
  }

  /**
   * 自动解决冲突
   */
  resolveConflicts(): number {
    const conflicts = this.checkConflicts();
    let resolvedCount = 0;

    conflicts.forEach(({ shortcut1, shortcut2 }) => {
      // 优先保留默认快捷键，禁用自定义快捷键
      const isShortcut1Custom = this.config.customShortcuts.some(s => s.id === shortcut1.id);
      const isShortcut2Custom = this.config.customShortcuts.some(s => s.id === shortcut2.id);

      if (isShortcut1Custom && !isShortcut2Custom) {
        this.disableShortcut(shortcut1.id);
        resolvedCount++;
      } else if (!isShortcut1Custom && isShortcut2Custom) {
        this.disableShortcut(shortcut2.id);
        resolvedCount++;
      } else if (isShortcut1Custom && isShortcut2Custom) {
        // 两个都是自定义，禁用后添加的
        const shortcut1Index = this.config.customShortcuts.findIndex(s => s.id === shortcut1.id);
        const shortcut2Index = this.config.customShortcuts.findIndex(s => s.id === shortcut2.id);
        
        if (shortcut1Index > shortcut2Index) {
          this.disableShortcut(shortcut1.id);
        } else {
          this.disableShortcut(shortcut2.id);
        }
        resolvedCount++;
      }
    });

    if (resolvedCount > 0) {
      this.saveConfig();
      this.notifyChange();
    }

    return resolvedCount;
  }

  /**
   * 添加配置变更回调
   */
  onChange(callback: (config: ShortcutConfig) => void): void {
    this.changeCallbacks.push(callback);
  }

  /**
   * 移除配置变更回调
   */
  offChange(callback: (config: ShortcutConfig) => void): void {
    const index = this.changeCallbacks.indexOf(callback);
    if (index > -1) {
      this.changeCallbacks.splice(index, 1);
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ShortcutConfig {
    return {
      version: ShortcutConfigManager.CONFIG_VERSION,
      shortcuts: [], // 将由GlobalKeyboardManager填充
      customShortcuts: [],
      disabledShortcuts: [],
      lastModified: Date.now()
    };
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const stored = localStorage.getItem(ShortcutConfigManager.STORAGE_KEY);
      if (!stored) return;

      const storedConfig = JSON.parse(stored) as ShortcutConfig;
      const validation = this.validateConfig(storedConfig);
      
      if (validation.valid) {
        this.config = storedConfig;
      } else {
        console.warn('Invalid stored shortcut config, using defaults:', validation.errors);
        this.saveConfig(); // 保存默认配置
      }
    } catch (error) {
      console.error('Error loading shortcut config:', error);
      this.saveConfig(); // 保存默认配置
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(
        ShortcutConfigManager.STORAGE_KEY, 
        JSON.stringify(this.config)
      );
    } catch (error) {
      console.error('Error saving shortcut config:', error);
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(config: any): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查必需字段
    if (!config.version) {
      errors.push('Missing version field');
    }

    if (!Array.isArray(config.shortcuts)) {
      errors.push('shortcuts must be an array');
    }

    if (!Array.isArray(config.customShortcuts)) {
      errors.push('customShortcuts must be an array');
    }

    if (!Array.isArray(config.disabledShortcuts)) {
      errors.push('disabledShortcuts must be an array');
    }

    // 检查快捷键格式
    if (config.shortcuts) {
      config.shortcuts.forEach((shortcut: any, index: number) => {
        const shortcutErrors = this.validateShortcut(shortcut);
        shortcutErrors.forEach(error => {
          errors.push(`shortcuts[${index}]: ${error}`);
        });
      });
    }

    if (config.customShortcuts) {
      config.customShortcuts.forEach((shortcut: any, index: number) => {
        const shortcutErrors = this.validateShortcut(shortcut);
        shortcutErrors.forEach(error => {
          errors.push(`customShortcuts[${index}]: ${error}`);
        });
      });

      if (config.customShortcuts.length > ShortcutConfigManager.MAX_CUSTOM_SHORTCUTS) {
        warnings.push(`Too many custom shortcuts (${config.customShortcuts.length}), maximum is ${ShortcutConfigManager.MAX_CUSTOM_SHORTCUTS}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证单个快捷键
   */
  private validateShortcut(shortcut: any): string[] {
    const errors: string[] = [];

    if (!shortcut.id || typeof shortcut.id !== 'string') {
      errors.push('Missing or invalid id');
    }

    if (!shortcut.key || typeof shortcut.key !== 'string') {
      errors.push('Missing or invalid key');
    }

    if (!shortcut.description || typeof shortcut.description !== 'string') {
      errors.push('Missing or invalid description');
    }

    if (!shortcut.category || typeof shortcut.category !== 'string') {
      errors.push('Missing or invalid category');
    }

    if (typeof shortcut.enabled !== 'boolean') {
      errors.push('enabled must be a boolean');
    }

    if (typeof shortcut.global !== 'boolean') {
      errors.push('global must be a boolean');
    }

    return errors;
  }

  /**
   * 生成快捷键ID
   */
  private generateShortcutId(shortcut: Omit<GlobalShortcut, 'id'>): string {
    const parts = [];
    if (shortcut.ctrlKey) parts.push('ctrl');
    if (shortcut.metaKey) parts.push('meta');
    if (shortcut.shiftKey) parts.push('shift');
    if (shortcut.altKey) parts.push('alt');
    parts.push(shortcut.key.toLowerCase());
    
    return parts.join('+');
  }

  /**
   * 检查快捷键冲突
   */
  private hasConflict(shortcut: GlobalShortcut, excludeId?: string): boolean {
    const allShortcuts = this.getAllShortcuts();
    
    return allShortcuts.some(existing => 
      existing.id !== excludeId && 
      this.shortcutsConflict(shortcut, existing)
    );
  }

  /**
   * 检查两个快捷键是否冲突
   */
  private shortcutsConflict(shortcut1: GlobalShortcut, shortcut2: GlobalShortcut): boolean {
    return shortcut1.key.toLowerCase() === shortcut2.key.toLowerCase() &&
           !!shortcut1.ctrlKey === !!shortcut2.ctrlKey &&
           !!shortcut1.metaKey === !!shortcut2.metaKey &&
           !!shortcut1.shiftKey === !!shortcut2.shiftKey &&
           !!shortcut1.altKey === !!shortcut2.altKey &&
           shortcut1.global === shortcut2.global;
  }

  /**
   * 通知配置变更
   */
  private notifyChange(): void {
    this.changeCallbacks.forEach(callback => {
      try {
        callback(this.config);
      } catch (error) {
        console.error('Error in config change callback:', error);
      }
    });
  }

  /**
   * 获取配置统计信息
   */
  getStats(): {
    totalShortcuts: number;
    customShortcuts: number;
    disabledShortcuts: number;
    enabledShortcuts: number;
    conflicts: number;
  } {
    const allShortcuts = this.getAllShortcuts();
    const conflicts = this.checkConflicts();

    return {
      totalShortcuts: allShortcuts.length,
      customShortcuts: this.config.customShortcuts.length,
      disabledShortcuts: this.config.disabledShortcuts.length,
      enabledShortcuts: allShortcuts.filter(s => s.enabled).length,
      conflicts: conflicts.length
    };
  }
}