/**
 * Sidebar Navigation Manager - 侧边栏导航管理器
 */

export interface NavigableElement {
  element: HTMLElement;
  id: string;
  type: 'tab' | 'bookmark' | 'folder' | 'search' | 'filter' | 'button';
  level: number;
  parent?: string;
  children?: string[];
  focusable: boolean;
  visible: boolean;
}

export interface NavigationOptions {
  enableArrowKeys?: boolean;
  enableTabNavigation?: boolean;
  enableHomeEndKeys?: boolean;
  enablePageUpDown?: boolean;
  enableTypeAhead?: boolean;
  wrapAround?: boolean;
  skipDisabled?: boolean;
}

export class SidebarNavigationManager {
  private options: NavigationOptions;
  private elements: Map<string, NavigableElement> = new Map();
  private focusedElementId: string | null = null;
  private typeAheadBuffer = '';
  private typeAheadTimer: number | null = null;
  private navigationCallbacks: ((elementId: string, action: string) => void)[] = [];
  private focusCallbacks: ((elementId: string | null, previousId: string | null) => void)[] = [];

  constructor(options: NavigationOptions = {}) {
    this.options = {
      enableArrowKeys: true,
      enableTabNavigation: true,
      enableHomeEndKeys: true,
      enablePageUpDown: true,
      enableTypeAhead: true,
      wrapAround: true,
      skipDisabled: true,
      ...options
    };

    this.setupEventListeners();
  }

  /**
   * 注册可导航元素
   */
  registerElement(element: NavigableElement): void {
    this.elements.set(element.id, element);
    this.setupElementAttributes(element);
    
    // 如果是第一个元素，设置为初始焦点
    if (this.elements.size === 1 && element.focusable && element.visible) {
      this.setFocus(element.id);
    }
  }

  /**
   * 注销可导航元素
   */
  unregisterElement(elementId: string): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    // 如果当前焦点在此元素上，移动焦点
    if (this.focusedElementId === elementId) {
      const nextElement = this.findNextFocusableElement(elementId);
      this.setFocus(nextElement?.id || null);
    }

    this.elements.delete(elementId);
  }

  /**
   * 更新元素状态
   */
  updateElement(elementId: string, updates: Partial<NavigableElement>): void {
    const element = this.elements.get(elementId);
    if (!element) return;

    Object.assign(element, updates);
    this.setupElementAttributes(element);

    // 如果元素变为不可见或不可聚焦，且当前有焦点，移动焦点
    if (this.focusedElementId === elementId && (!element.visible || !element.focusable)) {
      const nextElement = this.findNextFocusableElement(elementId);
      this.setFocus(nextElement?.id || null);
    }
  }

  /**
   * 设置焦点
   */
  setFocus(elementId: string | null): void {
    const previousId = this.focusedElementId;
    
    // 清除之前的焦点
    if (previousId) {
      const previousElement = this.elements.get(previousId);
      if (previousElement) {
        previousElement.element.setAttribute('tabindex', '-1');
        previousElement.element.classList.remove('keyboard-focused');
        previousElement.element.blur();
      }
    }

    this.focusedElementId = elementId;

    // 设置新焦点
    if (elementId) {
      const element = this.elements.get(elementId);
      if (element && element.focusable && element.visible) {
        element.element.setAttribute('tabindex', '0');
        element.element.classList.add('keyboard-focused');
        element.element.focus();
      }
    }

    this.notifyFocusChange(elementId, previousId);
  }

  /**
   * 获取当前焦点元素
   */
  getFocusedElement(): NavigableElement | null {
    if (!this.focusedElementId) return null;
    return this.elements.get(this.focusedElementId) || null;
  }

  /**
   * 移动到下一个元素
   */
  moveNext(): void {
    const current = this.getFocusedElement();
    if (!current) {
      this.focusFirstElement();
      return;
    }

    const next = this.findNextFocusableElement(current.id);
    if (next) {
      this.setFocus(next.id);
      this.notifyNavigation(next.id, 'next');
    } else if (this.options.wrapAround) {
      this.focusFirstElement();
    }
  }

  /**
   * 移动到上一个元素
   */
  movePrevious(): void {
    const current = this.getFocusedElement();
    if (!current) {
      this.focusLastElement();
      return;
    }

    const previous = this.findPreviousFocusableElement(current.id);
    if (previous) {
      this.setFocus(previous.id);
      this.notifyNavigation(previous.id, 'previous');
    } else if (this.options.wrapAround) {
      this.focusLastElement();
    }
  }

  /**
   * 移动到第一个元素
   */
  focusFirstElement(): void {
    const first = this.findFirstFocusableElement();
    if (first) {
      this.setFocus(first.id);
      this.notifyNavigation(first.id, 'first');
    }
  }

  /**
   * 移动到最后一个元素
   */
  focusLastElement(): void {
    const last = this.findLastFocusableElement();
    if (last) {
      this.setFocus(last.id);
      this.notifyNavigation(last.id, 'last');
    }
  }

  /**
   * 展开/折叠当前元素
   */
  toggleCurrentElement(): void {
    const current = this.getFocusedElement();
    if (!current) return;

    if (current.type === 'folder') {
      this.notifyNavigation(current.id, 'toggle');
    }
  }

  /**
   * 激活当前元素
   */
  activateCurrentElement(): void {
    const current = this.getFocusedElement();
    if (!current) return;

    this.notifyNavigation(current.id, 'activate');
  }

  /**
   * 向上翻页
   */
  pageUp(): void {
    const current = this.getFocusedElement();
    if (!current) return;

    const pageSize = this.calculatePageSize();
    let target = current;

    for (let i = 0; i < pageSize; i++) {
      const previous = this.findPreviousFocusableElement(target.id);
      if (!previous) break;
      target = previous;
    }

    if (target.id !== current.id) {
      this.setFocus(target.id);
      this.notifyNavigation(target.id, 'pageUp');
    }
  }

  /**
   * 向下翻页
   */
  pageDown(): void {
    const current = this.getFocusedElement();
    if (!current) return;

    const pageSize = this.calculatePageSize();
    let target = current;

    for (let i = 0; i < pageSize; i++) {
      const next = this.findNextFocusableElement(target.id);
      if (!next) break;
      target = next;
    }

    if (target.id !== current.id) {
      this.setFocus(target.id);
      this.notifyNavigation(target.id, 'pageDown');
    }
  }

  /**
   * 类型提前搜索
   */
  typeAheadSearch(char: string): void {
    if (!this.options.enableTypeAhead) return;

    // 清除之前的定时器
    if (this.typeAheadTimer) {
      clearTimeout(this.typeAheadTimer);
    }

    // 添加字符到缓冲区
    this.typeAheadBuffer += char.toLowerCase();

    // 查找匹配的元素
    const matches = this.findElementsByTypeAhead(this.typeAheadBuffer);
    if (matches.length > 0) {
      // 如果当前焦点已经在匹配项中，移动到下一个匹配项
      const currentIndex = matches.findIndex(m => m.id === this.focusedElementId);
      const nextIndex = currentIndex >= 0 ? (currentIndex + 1) % matches.length : 0;
      
      this.setFocus(matches[nextIndex].id);
      this.notifyNavigation(matches[nextIndex].id, 'typeAhead');
    }

    // 设置清除缓冲区的定时器
    this.typeAheadTimer = window.setTimeout(() => {
      this.typeAheadBuffer = '';
      this.typeAheadTimer = null;
    }, 1000);
  }

  /**
   * 获取所有可导航元素
   */
  getAllElements(): NavigableElement[] {
    return Array.from(this.elements.values());
  }

  /**
   * 获取可见的可聚焦元素
   */
  getFocusableElements(): NavigableElement[] {
    return Array.from(this.elements.values())
      .filter(el => el.focusable && el.visible)
      .sort((a, b) => this.compareElementOrder(a, b));
  }

  /**
   * 添加导航回调
   */
  onNavigation(callback: (elementId: string, action: string) => void): void {
    this.navigationCallbacks.push(callback);
  }

  /**
   * 添加焦点变化回调
   */
  onFocusChange(callback: (elementId: string | null, previousId: string | null) => void): void {
    this.focusCallbacks.push(callback);
  }

  /**
   * 清除所有元素
   */
  clear(): void {
    this.elements.clear();
    this.focusedElementId = null;
    this.typeAheadBuffer = '';
    
    if (this.typeAheadTimer) {
      clearTimeout(this.typeAheadTimer);
      this.typeAheadTimer = null;
    }
  }

  /**
   * 销毁导航管理器
   */
  destroy(): void {
    this.clear();
    this.navigationCallbacks = [];
    this.focusCallbacks = [];
    document.removeEventListener('keydown', this.handleKeydown);
  }

  /**
   * 设置元素属性
   */
  private setupElementAttributes(element: NavigableElement): void {
    const el = element.element;
    
    // 设置基本属性
    el.setAttribute('role', this.getElementRole(element.type));
    el.setAttribute('data-nav-id', element.id);
    el.setAttribute('data-nav-type', element.type);
    el.setAttribute('data-nav-level', element.level.toString());

    // 设置tabindex
    if (element.focusable && element.visible) {
      el.setAttribute('tabindex', element.id === this.focusedElementId ? '0' : '-1');
    } else {
      el.removeAttribute('tabindex');
    }

    // 设置ARIA属性
    if (element.type === 'folder') {
      el.setAttribute('aria-expanded', 'false'); // 默认折叠，实际状态由外部管理
    }

    if (element.parent) {
      el.setAttribute('aria-parent', element.parent);
    }

    if (element.level > 0) {
      el.setAttribute('aria-level', element.level.toString());
    }
  }

  /**
   * 获取元素角色
   */
  private getElementRole(type: string): string {
    const roleMap: Record<string, string> = {
      'tab': 'tab',
      'bookmark': 'button',
      'folder': 'button',
      'search': 'searchbox',
      'filter': 'button',
      'button': 'button'
    };

    return roleMap[type] || 'button';
  }

  /**
   * 查找下一个可聚焦元素
   */
  private findNextFocusableElement(currentId: string): NavigableElement | null {
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.findIndex(el => el.id === currentId);
    
    if (currentIndex === -1) return null;
    
    for (let i = currentIndex + 1; i < focusableElements.length; i++) {
      const element = focusableElements[i];
      if (this.isElementNavigable(element)) {
        return element;
      }
    }

    return null;
  }

  /**
   * 查找上一个可聚焦元素
   */
  private findPreviousFocusableElement(currentId: string): NavigableElement | null {
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.findIndex(el => el.id === currentId);
    
    if (currentIndex === -1) return null;
    
    for (let i = currentIndex - 1; i >= 0; i--) {
      const element = focusableElements[i];
      if (this.isElementNavigable(element)) {
        return element;
      }
    }

    return null;
  }

  /**
   * 查找第一个可聚焦元素
   */
  private findFirstFocusableElement(): NavigableElement | null {
    const focusableElements = this.getFocusableElements();
    return focusableElements.find(el => this.isElementNavigable(el)) || null;
  }

  /**
   * 查找最后一个可聚焦元素
   */
  private findLastFocusableElement(): NavigableElement | null {
    const focusableElements = this.getFocusableElements();
    for (let i = focusableElements.length - 1; i >= 0; i--) {
      const element = focusableElements[i];
      if (this.isElementNavigable(element)) {
        return element;
      }
    }
    return null;
  }

  /**
   * 检查元素是否可导航
   */
  private isElementNavigable(element: NavigableElement): boolean {
    if (!element.focusable || !element.visible) return false;
    
    if (this.options.skipDisabled) {
      const el = element.element;
      if (el.hasAttribute('disabled') || el.getAttribute('aria-disabled') === 'true') {
        return false;
      }
    }

    return true;
  }

  /**
   * 比较元素顺序
   */
  private compareElementOrder(a: NavigableElement, b: NavigableElement): number {
    // 首先按层级排序
    if (a.level !== b.level) {
      return a.level - b.level;
    }

    // 然后按DOM顺序排序
    const position = a.element.compareDocumentPosition(b.element);
    if (position & Node.DOCUMENT_POSITION_FOLLOWING) {
      return -1;
    } else if (position & Node.DOCUMENT_POSITION_PRECEDING) {
      return 1;
    }

    return 0;
  }

  /**
   * 计算页面大小
   */
  private calculatePageSize(): number {
    // 简单实现：返回固定值，实际可以根据容器高度计算
    return 10;
  }

  /**
   * 根据类型提前搜索查找元素
   */
  private findElementsByTypeAhead(query: string): NavigableElement[] {
    return this.getFocusableElements().filter(element => {
      const text = this.getElementText(element);
      return text.toLowerCase().startsWith(query);
    });
  }

  /**
   * 获取元素文本
   */
  private getElementText(element: NavigableElement): string {
    // 尝试从不同属性获取文本
    const el = element.element;
    return el.textContent || 
           el.getAttribute('aria-label') || 
           el.getAttribute('title') || 
           el.getAttribute('alt') || 
           '';
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    document.addEventListener('keydown', this.handleKeydown);
  }

  /**
   * 处理键盘事件
   */
  private handleKeydown = (event: KeyboardEvent): void => {
    // 只处理侧边栏内的键盘事件
    const target = event.target as HTMLElement;
    if (!this.isElementInSidebar(target)) return;

    // 检查是否有焦点元素
    if (!this.focusedElementId) return;

    let handled = false;

    switch (event.key) {
      case 'ArrowDown':
        if (this.options.enableArrowKeys) {
          event.preventDefault();
          this.moveNext();
          handled = true;
        }
        break;

      case 'ArrowUp':
        if (this.options.enableArrowKeys) {
          event.preventDefault();
          this.movePrevious();
          handled = true;
        }
        break;

      case 'ArrowRight':
        if (this.options.enableArrowKeys) {
          event.preventDefault();
          const current = this.getFocusedElement();
          if (current?.type === 'folder') {
            this.notifyNavigation(current.id, 'expand');
          } else {
            this.moveNext();
          }
          handled = true;
        }
        break;

      case 'ArrowLeft':
        if (this.options.enableArrowKeys) {
          event.preventDefault();
          const current = this.getFocusedElement();
          if (current?.type === 'folder') {
            this.notifyNavigation(current.id, 'collapse');
          } else {
            this.movePrevious();
          }
          handled = true;
        }
        break;

      case 'Home':
        if (this.options.enableHomeEndKeys) {
          event.preventDefault();
          this.focusFirstElement();
          handled = true;
        }
        break;

      case 'End':
        if (this.options.enableHomeEndKeys) {
          event.preventDefault();
          this.focusLastElement();
          handled = true;
        }
        break;

      case 'PageUp':
        if (this.options.enablePageUpDown) {
          event.preventDefault();
          this.pageUp();
          handled = true;
        }
        break;

      case 'PageDown':
        if (this.options.enablePageUpDown) {
          event.preventDefault();
          this.pageDown();
          handled = true;
        }
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        this.activateCurrentElement();
        handled = true;
        break;

      case 'Tab':
        if (this.options.enableTabNavigation) {
          // Tab导航由浏览器处理，但我们需要更新焦点跟踪
          setTimeout(() => {
            const activeElement = document.activeElement as HTMLElement;
            const navId = activeElement?.getAttribute('data-nav-id');
            if (navId && this.elements.has(navId)) {
              this.focusedElementId = navId;
            }
          }, 0);
        }
        break;

      default:
        // 类型提前搜索
        if (this.options.enableTypeAhead && event.key.length === 1 && !event.ctrlKey && !event.metaKey && !event.altKey) {
          this.typeAheadSearch(event.key);
          handled = true;
        }
        break;
    }

    if (handled) {
      event.stopPropagation();
    }
  };

  /**
   * 检查元素是否在侧边栏内
   */
  private isElementInSidebar(element: HTMLElement): boolean {
    // 检查元素是否在侧边栏容器内
    return element.closest('.sidebar-container') !== null ||
           element.hasAttribute('data-nav-id');
  }

  /**
   * 通知导航事件
   */
  private notifyNavigation(elementId: string, action: string): void {
    this.navigationCallbacks.forEach(callback => {
      try {
        callback(elementId, action);
      } catch (error) {
        console.error('Error in navigation callback:', error);
      }
    });
  }

  /**
   * 通知焦点变化事件
   */
  private notifyFocusChange(elementId: string | null, previousId: string | null): void {
    this.focusCallbacks.forEach(callback => {
      try {
        callback(elementId, previousId);
      } catch (error) {
        console.error('Error in focus change callback:', error);
      }
    });
  }
}