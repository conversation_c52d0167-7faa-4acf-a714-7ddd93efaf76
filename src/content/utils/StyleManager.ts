/**
 * Style Manager - 样式管理器
 */

import { StyleManager as IStyleManager } from '../../interfaces/content.interface.js';

export class StyleManager implements IStyleManager {
  private styleElement: HTMLStyleElement | null = null;
  private readonly STYLE_ID = 'chrome-vertical-sidebar-styles';
  private injected = false;

  /**
   * 注入样式
   */
  injectStyles(): void {
    if (this.injected) return;

    try {
      // 创建样式元素
      this.styleElement = document.createElement('style');
      this.styleElement.id = this.STYLE_ID;
      this.styleElement.type = 'text/css';

      // 添加基础样式
      this.styleElement.textContent = this.getBaseStyles();

      // 插入到head中
      document.head.appendChild(this.styleElement);

      this.injected = true;
      console.log('Sidebar styles injected');

    } catch (error) {
      console.error('Failed to inject styles:', error);
    }
  }

  /**
   * 移除样式
   */
  removeStyles(): void {
    if (!this.injected || !this.styleElement) return;

    try {
      if (this.styleElement.parentNode) {
        this.styleElement.parentNode.removeChild(this.styleElement);
      }
      
      this.styleElement = null;
      this.injected = false;
      console.log('Sidebar styles removed');

    } catch (error) {
      console.error('Failed to remove styles:', error);
    }
  }

  /**
   * 更新主题
   */
  updateTheme(theme: 'light' | 'dark' | 'auto'): void {
    if (!this.styleElement) return;

    try {
      // 移除现有主题类
      const sidebar = document.getElementById('chrome-vertical-sidebar');
      if (sidebar) {
        sidebar.classList.remove('theme-light', 'theme-dark', 'theme-auto');
        sidebar.classList.add(`theme-${theme}`);

        // 如果是auto主题，检测系统主题
        if (theme === 'auto') {
          const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          sidebar.classList.add(isDark ? 'theme-dark' : 'theme-light');
        }
      }

      // 更新CSS变量
      this.updateThemeVariables(theme);

    } catch (error) {
      console.error('Failed to update theme:', error);
    }
  }

  /**
   * 更新位置
   */
  updatePosition(position: 'left' | 'right'): void {
    if (!this.styleElement) return;

    try {
      const sidebar = document.getElementById('chrome-vertical-sidebar');
      if (sidebar) {
        sidebar.classList.remove('position-left', 'position-right');
        sidebar.classList.add(`position-${position}`);

        // 更新位置样式
        if (position === 'left') {
          sidebar.style.left = '0';
          sidebar.style.right = 'auto';
        } else {
          sidebar.style.left = 'auto';
          sidebar.style.right = '0';
        }
      }

    } catch (error) {
      console.error('Failed to update position:', error);
    }
  }

  /**
   * 更新尺寸
   */
  updateDimensions(collapsed: number, expanded: number): void {
    if (!this.styleElement) return;

    try {
      // 更新CSS变量
      document.documentElement.style.setProperty('--sidebar-collapsed-width', `${collapsed}px`);
      document.documentElement.style.setProperty('--sidebar-expanded-width', `${expanded}px`);

      // 更新侧边栏当前宽度
      const sidebar = document.getElementById('chrome-vertical-sidebar');
      if (sidebar) {
        const isExpanded = sidebar.classList.contains('expanded');
        sidebar.style.width = `${isExpanded ? expanded : collapsed}px`;
      }

    } catch (error) {
      console.error('Failed to update dimensions:', error);
    }
  }

  /**
   * 获取基础样式
   */
  private getBaseStyles(): string {
    return `
      /* CSS变量定义 */
      :root {
        --sidebar-collapsed-width: 60px;
        --sidebar-expanded-width: 300px;
        --sidebar-z-index: 2147483647;
        --sidebar-transition: width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        
        /* 明亮主题颜色 */
        --sidebar-bg-light: #ffffff;
        --sidebar-border-light: #e0e0e0;
        --sidebar-text-light: #333333;
        --sidebar-text-secondary-light: #656d76;
        --sidebar-hover-light: #f6f8fa;
        --sidebar-active-light: #e7f3ff;
        --sidebar-header-bg-light: #f8f9fa;
        
        /* 暗黑主题颜色 */
        --sidebar-bg-dark: #1e1e1e;
        --sidebar-border-dark: #333333;
        --sidebar-text-dark: #ffffff;
        --sidebar-text-secondary-dark: #8b949e;
        --sidebar-hover-dark: #333333;
        --sidebar-active-dark: #1f2937;
        --sidebar-header-bg-dark: #2d2d2d;
      }

      /* 侧边栏容器 */
      #chrome-vertical-sidebar {
        position: fixed !important;
        top: 0 !important;
        height: 100vh !important;
        width: var(--sidebar-collapsed-width) !important;
        z-index: var(--sidebar-z-index) !important;
        display: flex !important;
        flex-direction: column !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        transition: var(--sidebar-transition) !important;
        overflow: hidden !important;
        box-sizing: border-box !important;
      }

      /* 位置样式 */
      #chrome-vertical-sidebar.position-left {
        left: 0 !important;
        right: auto !important;
      }

      #chrome-vertical-sidebar.position-right {
        left: auto !important;
        right: 0 !important;
      }

      /* 展开状态 */
      #chrome-vertical-sidebar.expanded {
        width: var(--sidebar-expanded-width) !important;
      }

      /* 固定状态 */
      #chrome-vertical-sidebar.pinned {
        position: relative !important;
      }

      /* 明亮主题 */
      #chrome-vertical-sidebar.theme-light {
        background: var(--sidebar-bg-light) !important;
        color: var(--sidebar-text-light) !important;
        border-right: 1px solid var(--sidebar-border-light) !important;
      }

      #chrome-vertical-sidebar.theme-light.position-right {
        border-right: none !important;
        border-left: 1px solid var(--sidebar-border-light) !important;
      }

      /* 暗黑主题 */
      #chrome-vertical-sidebar.theme-dark {
        background: var(--sidebar-bg-dark) !important;
        color: var(--sidebar-text-dark) !important;
        border-right: 1px solid var(--sidebar-border-dark) !important;
      }

      #chrome-vertical-sidebar.theme-dark.position-right {
        border-right: none !important;
        border-left: 1px solid var(--sidebar-border-dark) !important;
      }

      /* 头部样式 */
      #chrome-vertical-sidebar .sidebar-header {
        padding: 12px !important;
        min-height: 60px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-header {
        background: var(--sidebar-header-bg-light) !important;
        border-bottom: 1px solid var(--sidebar-border-light) !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-header {
        background: var(--sidebar-header-bg-dark) !important;
        border-bottom: 1px solid var(--sidebar-border-dark) !important;
      }

      /* 搜索框样式 */
      #chrome-vertical-sidebar .sidebar-search {
        width: 100% !important;
        padding: 8px 12px !important;
        border: 1px solid var(--sidebar-border-light) !important;
        border-radius: 6px !important;
        font-size: 14px !important;
        outline: none !important;
        opacity: 0 !important;
        transition: opacity 0.2s ease !important;
        box-sizing: border-box !important;
      }

      #chrome-vertical-sidebar.expanded .sidebar-search {
        opacity: 1 !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-search {
        background: var(--sidebar-bg-light) !important;
        color: var(--sidebar-text-light) !important;
        border-color: var(--sidebar-border-light) !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-search {
        background: var(--sidebar-bg-dark) !important;
        color: var(--sidebar-text-dark) !important;
        border-color: var(--sidebar-border-dark) !important;
      }

      #chrome-vertical-sidebar .sidebar-search:focus {
        border-color: #0969da !important;
        box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1) !important;
      }

      /* 内容区域样式 */
      #chrome-vertical-sidebar .sidebar-content {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
      }

      /* 滚动条样式 */
      #chrome-vertical-sidebar .sidebar-content::-webkit-scrollbar {
        width: 6px !important;
      }

      #chrome-vertical-sidebar .sidebar-content::-webkit-scrollbar-track {
        background: transparent !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--sidebar-border-light) !important;
        border-radius: 3px !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--sidebar-border-dark) !important;
        border-radius: 3px !important;
      }

      /* 底部样式 */
      #chrome-vertical-sidebar .sidebar-footer {
        padding: 12px !important;
        display: flex !important;
        justify-content: center !important;
        gap: 8px !important;
        box-sizing: border-box !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-footer {
        background: var(--sidebar-header-bg-light) !important;
        border-top: 1px solid var(--sidebar-border-light) !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-footer {
        background: var(--sidebar-header-bg-dark) !important;
        border-top: 1px solid var(--sidebar-border-dark) !important;
      }

      /* 底部按钮样式 */
      #chrome-vertical-sidebar .sidebar-footer-button {
        width: 36px !important;
        height: 36px !important;
        border: none !important;
        background: transparent !important;
        border-radius: 6px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.15s ease !important;
        box-sizing: border-box !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-footer-button {
        color: var(--sidebar-text-secondary-light) !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-footer-button {
        color: var(--sidebar-text-secondary-dark) !important;
      }

      #chrome-vertical-sidebar.theme-light .sidebar-footer-button:hover {
        background: var(--sidebar-border-light) !important;
        color: var(--sidebar-text-light) !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-footer-button:hover {
        background: var(--sidebar-border-dark) !important;
        color: var(--sidebar-text-dark) !important;
      }

      #chrome-vertical-sidebar .sidebar-footer-button.active {
        background: #0969da !important;
        color: #ffffff !important;
      }

      #chrome-vertical-sidebar.theme-dark .sidebar-footer-button.active {
        background: #3b82f6 !important;
      }

      /* 页面内容适配样式 */
      body.sidebar-page-adjusted {
        transition: margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), 
                   transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), 
                   width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
        min-width: 320px !important;
      }

      html.sidebar-page-adjusted {
        transition: margin 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
      }

      /* 页面内容收缩保护 */
      body.sidebar-page-adjusted {
        overflow-x: auto !important;
      }

      /* 固定元素适配指示 */
      .sidebar-conflict-element {
        outline: 2px dashed rgba(255, 165, 0, 0.5) !important;
        outline-offset: 2px !important;
      }

      /* 媒体查询 - 页面适配增强 */
      @media (max-width: 1200px) {
        body.sidebar-page-adjusted {
          min-width: 280px !important;
        }
      }

      @media (max-width: 768px) {
        #chrome-vertical-sidebar.expanded {
          width: 280px !important;
        }
        
        body.sidebar-page-adjusted {
          min-width: 240px !important;
        }
        
        /* 小屏幕下优化页面内容适配 */
        body.sidebar-page-adjusted.sidebar-position-left {
          margin-left: 60px !important;
        }
        
        body.sidebar-page-adjusted.sidebar-position-right {
          margin-right: 60px !important;
        }
      }

      @media (max-width: 480px) {
        /* 极小屏幕下侧边栏覆盖模式 */
        #chrome-vertical-sidebar {
          position: fixed !important;
          z-index: var(--sidebar-z-index) !important;
        }
        
        body.sidebar-page-adjusted {
          margin-left: 0 !important;
          margin-right: 0 !important;
          transform: none !important;
        }
      }

      /* 高对比度模式支持 */
      @media (prefers-contrast: high) {
        #chrome-vertical-sidebar {
          border-width: 2px !important;
        }
        
        .sidebar-conflict-element {
          outline-width: 3px !important;
          outline-color: orange !important;
        }
      }

      /* 减少动画模式支持 */
      @media (prefers-reduced-motion: reduce) {
        #chrome-vertical-sidebar,
        #chrome-vertical-sidebar .sidebar-search,
        #chrome-vertical-sidebar .sidebar-footer-button,
        body.sidebar-page-adjusted,
        html.sidebar-page-adjusted {
          transition: none !important;
        }
      }

      /* 深色模式下的页面适配样式 */
      @media (prefers-color-scheme: dark) {
        .sidebar-conflict-element {
          outline-color: rgba(255, 193, 7, 0.7) !important;
        }
      }

      /* 确保侧边栏不被其他元素覆盖 */
      #chrome-vertical-sidebar * {
        box-sizing: border-box !important;
      }

      /* 防止文本选择 */
      #chrome-vertical-sidebar {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
      }

      /* 搜索框允许文本选择 */
      #chrome-vertical-sidebar .sidebar-search {
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
      }

      /* 页面内容保护样式 */
      .sidebar-protected-element {
        transition: margin 0.3s ease, padding 0.3s ease, width 0.3s ease !important;
      }

      /* 全宽元素特殊处理 */
      .sidebar-fullwidth-adjusted {
        box-sizing: border-box !important;
        transition: width 0.3s ease, margin 0.3s ease, padding 0.3s ease !important;
      }
    `;
  }

  /**
   * 更新主题变量
   */
  private updateThemeVariables(theme: 'light' | 'dark' | 'auto'): void {
    const root = document.documentElement;
    
    if (theme === 'auto') {
      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const updateAutoTheme = () => {
        const isDark = mediaQuery.matches;
        root.setAttribute('data-sidebar-theme', isDark ? 'dark' : 'light');
      };
      
      updateAutoTheme();
      mediaQuery.addEventListener('change', updateAutoTheme);
    } else {
      root.setAttribute('data-sidebar-theme', theme);
    }
  }

  /**
   * 启用页面内容适配样式
   */
  enablePageAdjustment(position: 'left' | 'right'): void {
    try {
      const body = document.body;
      const html = document.documentElement;
      
      // 添加适配类
      body.classList.add('sidebar-page-adjusted');
      body.classList.add(`sidebar-position-${position}`);
      html.classList.add('sidebar-page-adjusted');
      
      console.log('Page adjustment styles enabled');
    } catch (error) {
      console.error('Failed to enable page adjustment:', error);
    }
  }

  /**
   * 禁用页面内容适配样式
   */
  disablePageAdjustment(): void {
    try {
      const body = document.body;
      const html = document.documentElement;
      
      // 移除适配类
      body.classList.remove('sidebar-page-adjusted');
      body.classList.remove('sidebar-position-left', 'sidebar-position-right');
      html.classList.remove('sidebar-page-adjusted');
      
      console.log('Page adjustment styles disabled');
    } catch (error) {
      console.error('Failed to disable page adjustment:', error);
    }
  }

  /**
   * 标记冲突元素
   */
  markConflictElement(element: HTMLElement): void {
    try {
      element.classList.add('sidebar-conflict-element');
    } catch (error) {
      console.error('Failed to mark conflict element:', error);
    }
  }

  /**
   * 取消标记冲突元素
   */
  unmarkConflictElement(element: HTMLElement): void {
    try {
      element.classList.remove('sidebar-conflict-element');
    } catch (error) {
      console.error('Failed to unmark conflict element:', error);
    }
  }

  /**
   * 标记受保护的元素
   */
  markProtectedElement(element: HTMLElement): void {
    try {
      element.classList.add('sidebar-protected-element');
    } catch (error) {
      console.error('Failed to mark protected element:', error);
    }
  }

  /**
   * 取消标记受保护的元素
   */
  unmarkProtectedElement(element: HTMLElement): void {
    try {
      element.classList.remove('sidebar-protected-element');
    } catch (error) {
      console.error('Failed to unmark protected element:', error);
    }
  }

  /**
   * 标记全宽调整元素
   */
  markFullwidthAdjusted(element: HTMLElement): void {
    try {
      element.classList.add('sidebar-fullwidth-adjusted');
    } catch (error) {
      console.error('Failed to mark fullwidth adjusted element:', error);
    }
  }

  /**
   * 取消标记全宽调整元素
   */
  unmarkFullwidthAdjusted(element: HTMLElement): void {
    try {
      element.classList.remove('sidebar-fullwidth-adjusted');
    } catch (error) {
      console.error('Failed to unmark fullwidth adjusted element:', error);
    }
  }

  /**
   * 清理所有页面适配标记
   */
  cleanupPageAdjustment(): void {
    try {
      // 清理body和html类
      this.disablePageAdjustment();
      
      // 清理所有特殊标记的元素
      const conflictElements = document.querySelectorAll('.sidebar-conflict-element');
      conflictElements.forEach(el => el.classList.remove('sidebar-conflict-element'));
      
      const protectedElements = document.querySelectorAll('.sidebar-protected-element');
      protectedElements.forEach(el => el.classList.remove('sidebar-protected-element'));
      
      const fullwidthElements = document.querySelectorAll('.sidebar-fullwidth-adjusted');
      fullwidthElements.forEach(el => el.classList.remove('sidebar-fullwidth-adjusted'));
      
      console.log('Page adjustment cleanup completed');
    } catch (error) {
      console.error('Failed to cleanup page adjustment:', error);
    }
  }
}