/**
 * Universal Layout Adapter - Handles sidebar layout compatibility across all websites
 * Provides multiple adaptation strategies for different layout patterns
 */

import { ErrorHandler } from '../../background/utils/ErrorHandler.js';

export interface LayoutStrategy {
  name: string;
  priority: number;
  detect: () => boolean;
  apply: (sidebarWidth: number, position: 'left' | 'right') => boolean;
  restore: () => void;
}

export interface LayoutDetectionResult {
  strategies: LayoutStrategy[];
  primaryStrategy: LayoutStrategy;
  fallbackStrategies: LayoutStrategy[];
  websiteType: string;
  confidence: number;
}

export class UniversalLayoutAdapter {
  private strategies: Map<string, LayoutStrategy> = new Map();
  private appliedStrategies: Set<string> = new Set();
  private originalStyles: Map<Element, Map<string, string>> = new Map();
  private customPropertiesInjected = false;
  private layoutObserver: MutationObserver | null = null;
  private resizeObserver: ResizeObserver | null = null;
  private currentSidebarWidth = 0;
  private currentPosition: 'left' | 'right' = 'left';

  constructor() {
    this.initializeStrategies();
    this.setupLayoutMonitoring();
  }

  /**
   * Apply sidebar layout adaptation
   */
  async adaptLayout(sidebarWidth: number, position: 'left' | 'right'): Promise<boolean> {
    try {
      console.log(`🎯 Starting universal layout adaptation: ${sidebarWidth}px on ${position}`);

      this.currentSidebarWidth = sidebarWidth;
      this.currentPosition = position;

      // Inject CSS custom properties first
      this.injectCustomProperties(sidebarWidth, position);

      // Detect optimal strategies
      const detection = this.detectLayoutStrategies();
      console.log(`📊 Detected layout: ${detection.websiteType} (confidence: ${detection.confidence}%)`);

      // For stubborn websites, try DOM manipulation first
      let success = false;
      if (this.detectStubbornWebsite()) {
        console.log('🚨 Stubborn website detected, trying DOM manipulation first');
        try {
          success = this.applyDOMManipulationStrategy(sidebarWidth, position);
          if (success) {
            this.appliedStrategies.add('dom-manipulation');
            console.log(`💥 DOM Manipulation strategy successful`);
          }
        } catch (error) {
          console.warn(`⚠️ DOM Manipulation strategy failed:`, error);
        }
      }

      // Apply primary strategy if DOM manipulation didn't work
      if (!success) {
        try {
          success = detection.primaryStrategy.apply(sidebarWidth, position);
          if (success) {
            this.appliedStrategies.add(detection.primaryStrategy.name);
            console.log(`✅ Primary strategy applied: ${detection.primaryStrategy.name}`);
          }
        } catch (error) {
          console.warn(`⚠️ Primary strategy failed: ${detection.primaryStrategy.name}`, error);
        }
      }

      // Apply fallback strategies if needed
      if (!success || detection.confidence < 80) {
        for (const strategy of detection.fallbackStrategies) {
          try {
            const fallbackSuccess = strategy.apply(sidebarWidth, position);
            if (fallbackSuccess) {
              this.appliedStrategies.add(strategy.name);
              console.log(`✅ Fallback strategy applied: ${strategy.name}`);
              success = true;
            }
          } catch (error) {
            console.warn(`⚠️ Fallback strategy failed: ${strategy.name}`, error);
          }
        }
      }

      // Apply universal CSS overrides as final layer
      this.applyUniversalOverrides(sidebarWidth, position);

      // For really stubborn cases, apply nuclear option
      if (!success || this.detectStubbornWebsite()) {
        this.applyNuclearOption(sidebarWidth, position);
        success = true;
      }

      // Trigger layout recalculation
      this.triggerLayoutRecalculation();

      console.log(`🎉 Layout adaptation completed. Applied strategies: ${Array.from(this.appliedStrategies).join(', ')}`);
      return success;

    } catch (error) {
      console.error('❌ Universal layout adaptation failed:', error);
      ErrorHandler.handleError(error, 'UniversalLayoutAdapter.adaptLayout');
      return false;
    }
  }

  /**
   * Restore original layout
   */
  async restoreLayout(): Promise<void> {
    try {
      console.log('🔄 Restoring original layout');

      // Restore all applied strategies
      for (const strategyName of this.appliedStrategies) {
        const strategy = this.strategies.get(strategyName);
        if (strategy) {
          try {
            strategy.restore();
          } catch (error) {
            console.warn(`⚠️ Failed to restore strategy: ${strategyName}`, error);
          }
        }
      }

      // Restore original styles
      this.restoreOriginalStyles();

      // Remove custom properties
      this.removeCustomProperties();

      // Clear applied strategies
      this.appliedStrategies.clear();

      // Trigger layout recalculation
      this.triggerLayoutRecalculation();

      console.log('✅ Layout restoration completed');

    } catch (error) {
      console.error('❌ Layout restoration failed:', error);
      ErrorHandler.handleError(error, 'UniversalLayoutAdapter.restoreLayout');
    }
  }

  /**
   * Initialize all layout strategies
   */
  private initializeStrategies(): void {
    // CSS Custom Properties Strategy (Universal)
    this.strategies.set('css-custom-properties', {
      name: 'CSS Custom Properties',
      priority: 10,
      detect: () => true, // Always available
      apply: (width, position) => this.applyCSSCustomPropertiesStrategy(width, position),
      restore: () => this.restoreCSSCustomPropertiesStrategy()
    });

    // Viewport Units Strategy (Modern browsers)
    this.strategies.set('viewport-units', {
      name: 'Viewport Units',
      priority: 9,
      detect: () => this.supportsViewportUnits(),
      apply: (width, position) => this.applyViewportUnitsStrategy(width, position),
      restore: () => this.restoreViewportUnitsStrategy()
    });

    // CSS Grid Strategy
    this.strategies.set('css-grid', {
      name: 'CSS Grid',
      priority: 8,
      detect: () => this.detectCSSGrid(),
      apply: (width, position) => this.applyCSSGridStrategy(width, position),
      restore: () => this.restoreCSSGridStrategy()
    });

    // Flexbox Strategy
    this.strategies.set('flexbox', {
      name: 'Flexbox',
      priority: 7,
      detect: () => this.detectFlexbox(),
      apply: (width, position) => this.applyFlexboxStrategy(width, position),
      restore: () => this.restoreFlexboxStrategy()
    });

    // Absolute/Fixed Positioning Strategy
    this.strategies.set('absolute-positioning', {
      name: 'Absolute Positioning',
      priority: 6,
      detect: () => this.detectAbsolutePositioning(),
      apply: (width, position) => this.applyAbsolutePositioningStrategy(width, position),
      restore: () => this.restoreAbsolutePositioningStrategy()
    });

    // Traditional Margin Strategy
    this.strategies.set('traditional-margin', {
      name: 'Traditional Margin',
      priority: 5,
      detect: () => true, // Always available as fallback
      apply: (width, position) => this.applyTraditionalMarginStrategy(width, position),
      restore: () => this.restoreTraditionalMarginStrategy()
    });

    // DOM Manipulation Strategy (Nuclear option for stubborn websites)
    this.strategies.set('dom-manipulation', {
      name: 'DOM Manipulation',
      priority: 4,
      detect: () => this.detectStubbornWebsite(),
      apply: (width, position) => this.applyDOMManipulationStrategy(width, position),
      restore: () => this.restoreDOMManipulationStrategy()
    });

    console.log(`📋 Initialized ${this.strategies.size} layout strategies`);
  }

  /**
   * Detect optimal layout strategies for current website
   */
  private detectLayoutStrategies(): LayoutDetectionResult {
    const availableStrategies: LayoutStrategy[] = [];
    let websiteType = 'unknown';
    let confidence = 0;

    // Test all strategies
    for (const strategy of this.strategies.values()) {
      if (strategy.detect()) {
        availableStrategies.push(strategy);
      }
    }

    // Sort by priority
    availableStrategies.sort((a, b) => b.priority - a.priority);

    // Determine website type and confidence
    const detectionResults = this.analyzeWebsiteLayout();
    websiteType = detectionResults.type;
    confidence = detectionResults.confidence;

    // Select primary and fallback strategies
    const primaryStrategy = availableStrategies[0];
    const fallbackStrategies = availableStrategies.slice(1, 4); // Top 3 fallbacks

    return {
      strategies: availableStrategies,
      primaryStrategy,
      fallbackStrategies,
      websiteType,
      confidence
    };
  }

  /**
   * Analyze website layout patterns
   */
  private analyzeWebsiteLayout(): { type: string; confidence: number } {
    let type = 'traditional';
    let confidence = 50;

    // Check for modern frameworks
    if (this.detectReactApp()) {
      type = 'react-spa';
      confidence = 85;
    } else if (this.detectVueApp()) {
      type = 'vue-spa';
      confidence = 85;
    } else if (this.detectAngularApp()) {
      type = 'angular-spa';
      confidence = 85;
    } else if (this.detectCSSGrid()) {
      type = 'css-grid';
      confidence = 80;
    } else if (this.detectFlexbox()) {
      type = 'flexbox';
      confidence = 75;
    } else if (this.detectAbsolutePositioning()) {
      type = 'absolute-positioning';
      confidence = 70;
    }

    // Adjust confidence based on layout complexity
    const layoutComplexity = this.calculateLayoutComplexity();
    confidence = Math.max(30, confidence - layoutComplexity * 10);

    return { type, confidence };
  }

  /**
   * Setup layout monitoring for dynamic changes
   */
  private setupLayoutMonitoring(): void {
    // Monitor DOM changes
    this.layoutObserver = new MutationObserver((mutations) => {
      let shouldReapply = false;
      
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if significant layout elements were added
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (this.isSignificantLayoutElement(element)) {
                shouldReapply = true;
                break;
              }
            }
          }
        }
      }

      if (shouldReapply && this.appliedStrategies.size > 0) {
        console.log('🔄 Layout change detected, reapplying adaptation');
        this.adaptLayout(this.currentSidebarWidth, this.currentPosition);
      }
    });

    // Monitor resize changes
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver((entries) => {
        if (this.appliedStrategies.size > 0) {
          // Debounce resize events
          clearTimeout((this as any).resizeTimeout);
          (this as any).resizeTimeout = setTimeout(() => {
            console.log('🔄 Resize detected, reapplying adaptation');
            this.adaptLayout(this.currentSidebarWidth, this.currentPosition);
          }, 250);
        }
      });

      this.resizeObserver.observe(document.body);
    }

    // Start monitoring
    this.layoutObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  /**
   * Inject CSS custom properties for universal layout adaptation
   */
  private injectCustomProperties(sidebarWidth: number, position: 'left' | 'right'): void {
    if (this.customPropertiesInjected) {
      this.updateCustomProperties(sidebarWidth, position);
      return;
    }

    const style = document.createElement('style');
    style.id = 'universal-sidebar-properties';
    style.textContent = this.generateCustomPropertiesCSS(sidebarWidth, position);
    document.head.appendChild(style);

    this.customPropertiesInjected = true;
    console.log('📐 CSS custom properties injected');
  }

  /**
   * Update CSS custom properties
   */
  private updateCustomProperties(sidebarWidth: number, position: 'left' | 'right'): void {
    const style = document.getElementById('universal-sidebar-properties');
    if (style) {
      style.textContent = this.generateCustomPropertiesCSS(sidebarWidth, position);
      console.log('📐 CSS custom properties updated');
    }
  }

  /**
   * Generate CSS custom properties
   */
  private generateCustomPropertiesCSS(sidebarWidth: number, position: 'left' | 'right'): string {
    const offsetProperty = position === 'left' ? 'margin-left' : 'margin-right';
    const oppositeProperty = position === 'left' ? 'margin-right' : 'margin-left';

    return `
      :root {
        --sidebar-width: ${sidebarWidth}px;
        --sidebar-position: ${position};
        --content-width: calc(100vw - var(--sidebar-width));
        --content-max-width: calc(100% - var(--sidebar-width));
      }

      /* Universal viewport-based adaptation */
      html.sidebar-adapted,
      body.sidebar-adapted {
        ${offsetProperty}: var(--sidebar-width) !important;
        ${oppositeProperty}: 0 !important;
        width: var(--content-width) !important;
        max-width: var(--content-width) !important;
        box-sizing: border-box !important;
        transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
      }

      /* Prevent horizontal overflow */
      html.sidebar-adapted,
      body.sidebar-adapted {
        overflow-x: hidden !important;
      }

      /* Full-width element protection */
      .sidebar-adapted [style*="width: 100vw"],
      .sidebar-adapted [style*="width:100vw"],
      .sidebar-adapted .w-screen,
      .sidebar-adapted .w-full {
        width: var(--content-width) !important;
        max-width: var(--content-width) !important;
        ${offsetProperty}: var(--sidebar-width) !important;
      }
    `;
  }

  /**
   * Remove CSS custom properties
   */
  private removeCustomProperties(): void {
    const style = document.getElementById('universal-sidebar-properties');
    if (style) {
      style.remove();
      this.customPropertiesInjected = false;
      console.log('🧹 CSS custom properties removed');
    }

    // Remove adaptation classes
    document.documentElement.classList.remove('sidebar-adapted');
    document.body.classList.remove('sidebar-adapted');
  }

  /**
   * Apply universal CSS overrides as final layer
   */
  private applyUniversalOverrides(sidebarWidth: number, position: 'left' | 'right'): void {
    const style = document.createElement('style');
    style.id = 'universal-sidebar-overrides';
    style.textContent = this.generateUniversalOverridesCSS(sidebarWidth, position);
    document.head.appendChild(style);

    // Add adaptation classes
    document.documentElement.classList.add('sidebar-adapted');
    document.body.classList.add('sidebar-adapted');

    console.log('🛡️ Universal CSS overrides applied');
  }

  /**
   * Generate universal CSS overrides with aggressive targeting
   */
  private generateUniversalOverridesCSS(sidebarWidth: number, position: 'left' | 'right'): string {
    const offsetProperty = position === 'left' ? 'left' : 'right';
    const marginProperty = position === 'left' ? 'margin-left' : 'margin-right';

    return `
      /* Ultra-high specificity overrides - Force layout changes */
      html.sidebar-adapted body.sidebar-adapted,
      html.sidebar-adapted body.sidebar-adapted > *:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${marginProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        box-sizing: border-box !important;
      }

      /* Force all direct body children to respect sidebar */
      html.sidebar-adapted body.sidebar-adapted > div:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted body.sidebar-adapted > main:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted body.sidebar-adapted > section:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted body.sidebar-adapted > article:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${marginProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
        box-sizing: border-box !important;
        transform: translateX(0) !important;
      }

      /* Aggressive viewport unit targeting */
      html.sidebar-adapted [style*="width: 100vw"],
      html.sidebar-adapted [style*="width:100vw"],
      html.sidebar-adapted [style*="width: 100%"],
      html.sidebar-adapted [style*="width:100%"] {
        width: calc(100vw - ${sidebarWidth}px) !important;
        ${marginProperty}: ${sidebarWidth}px !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
      }

      /* Force fixed/absolute elements */
      html.sidebar-adapted [style*="position: fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="position:fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="position: absolute"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="position:absolute"]:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${offsetProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
      }

      /* Framework-specific aggressive targeting */
      html.sidebar-adapted .container-fluid:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted .el-container:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted #app:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted #root:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted .app:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted .application:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${marginProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
        box-sizing: border-box !important;
      }

      /* CSS Grid and Flexbox aggressive override */
      html.sidebar-adapted [style*="display: grid"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="display:grid"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="display: flex"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html.sidebar-adapted [style*="display:flex"]:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${marginProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
        box-sizing: border-box !important;
      }

      /* Force layout recalculation */
      html.sidebar-adapted * {
        transition: width 0.3s ease, margin 0.3s ease, transform 0.3s ease !important;
      }

      /* Prevent horizontal overflow */
      html.sidebar-adapted,
      html.sidebar-adapted body {
        overflow-x: hidden !important;
      }
    `;
  }

  /**
   * CSS Custom Properties Strategy Implementation
   */
  private applyCSSCustomPropertiesStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      // Already applied in injectCustomProperties
      return true;
    } catch (error) {
      console.error('CSS Custom Properties strategy failed:', error);
      return false;
    }
  }

  private restoreCSSCustomPropertiesStrategy(): void {
    // Handled in removeCustomProperties
  }

  /**
   * Viewport Units Strategy Implementation
   */
  private applyViewportUnitsStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      const elements = this.findViewportBasedElements();

      for (const element of elements) {
        this.saveOriginalStyles(element, ['width', 'height', 'margin-left', 'margin-right']);

        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.width.includes('100vw')) {
          (element as HTMLElement).style.width = `calc(100vw - ${sidebarWidth}px)`;
          (element as HTMLElement).style[position === 'left' ? 'marginLeft' : 'marginRight'] = `${sidebarWidth}px`;
        }
      }

      return elements.length > 0;
    } catch (error) {
      console.error('Viewport Units strategy failed:', error);
      return false;
    }
  }

  private restoreViewportUnitsStrategy(): void {
    const elements = this.findViewportBasedElements();
    for (const element of elements) {
      this.restoreElementStyles(element);
    }
  }

  /**
   * Support detection methods
   */
  private supportsViewportUnits(): boolean {
    try {
      const testElement = document.createElement('div');
      testElement.style.width = '100vw';
      return testElement.style.width === '100vw';
    } catch {
      return false;
    }
  }

  private detectCSSGrid(): boolean {
    const gridElements = document.querySelectorAll('[style*="display: grid"], [style*="display:grid"]');
    return gridElements.length > 0 || this.hasGridInStylesheets();
  }

  private detectFlexbox(): boolean {
    const flexElements = document.querySelectorAll('[style*="display: flex"], [style*="display:flex"]');
    return flexElements.length > 0 || this.hasFlexInStylesheets();
  }

  private detectAbsolutePositioning(): boolean {
    const absoluteElements = document.querySelectorAll('[style*="position: absolute"], [style*="position:absolute"], [style*="position: fixed"], [style*="position:fixed"]');
    return absoluteElements.length > 0;
  }

  /**
   * CSS Grid Strategy Implementation
   */
  private applyCSSGridStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      const gridContainers = this.findGridContainers();

      for (const container of gridContainers) {
        this.saveOriginalStyles(container, ['grid-template-columns', 'margin-left', 'margin-right', 'width']);

        const element = container as HTMLElement;
        const computedStyle = window.getComputedStyle(element);

        // Adjust grid template columns if using fr units
        if (computedStyle.gridTemplateColumns.includes('fr')) {
          const marginProperty = position === 'left' ? 'marginLeft' : 'marginRight';
          element.style[marginProperty] = `${sidebarWidth}px`;
          element.style.width = `calc(100% - ${sidebarWidth}px)`;
        }
      }

      return gridContainers.length > 0;
    } catch (error) {
      console.error('CSS Grid strategy failed:', error);
      return false;
    }
  }

  private restoreCSSGridStrategy(): void {
    const gridContainers = this.findGridContainers();
    for (const container of gridContainers) {
      this.restoreElementStyles(container);
    }
  }

  /**
   * Flexbox Strategy Implementation
   */
  private applyFlexboxStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      const flexContainers = this.findFlexContainers();

      for (const container of flexContainers) {
        this.saveOriginalStyles(container, ['margin-left', 'margin-right', 'width', 'flex-basis']);

        const element = container as HTMLElement;
        const marginProperty = position === 'left' ? 'marginLeft' : 'marginRight';

        element.style[marginProperty] = `${sidebarWidth}px`;
        element.style.width = `calc(100% - ${sidebarWidth}px)`;

        // Adjust flex children if needed
        const flexChildren = element.children;
        for (let i = 0; i < flexChildren.length; i++) {
          const child = flexChildren[i] as HTMLElement;
          const childStyle = window.getComputedStyle(child);

          if (childStyle.flexBasis === '100%' || childStyle.width === '100%') {
            this.saveOriginalStyles(child, ['flex-basis', 'width']);
            child.style.flexBasis = `calc(100% - ${sidebarWidth}px)`;
            child.style.width = `calc(100% - ${sidebarWidth}px)`;
          }
        }
      }

      return flexContainers.length > 0;
    } catch (error) {
      console.error('Flexbox strategy failed:', error);
      return false;
    }
  }

  private restoreFlexboxStrategy(): void {
    const flexContainers = this.findFlexContainers();
    for (const container of flexContainers) {
      this.restoreElementStyles(container);

      // Restore flex children
      const flexChildren = container.children;
      for (let i = 0; i < flexChildren.length; i++) {
        this.restoreElementStyles(flexChildren[i] as HTMLElement);
      }
    }
  }

  /**
   * Absolute Positioning Strategy Implementation
   */
  private applyAbsolutePositioningStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      const absoluteElements = this.findAbsoluteElements();

      for (const element of absoluteElements) {
        this.saveOriginalStyles(element, ['left', 'right', 'width', 'margin-left', 'margin-right']);

        const htmlElement = element as HTMLElement;
        const computedStyle = window.getComputedStyle(htmlElement);

        if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
          if (position === 'left') {
            const currentLeft = parseInt(computedStyle.left) || 0;
            htmlElement.style.left = `${currentLeft + sidebarWidth}px`;
          } else {
            const currentRight = parseInt(computedStyle.right) || 0;
            htmlElement.style.right = `${currentRight + sidebarWidth}px`;
          }

          // Adjust width if it's full width
          if (computedStyle.width === '100%' || computedStyle.width.includes('100vw')) {
            htmlElement.style.width = `calc(100% - ${sidebarWidth}px)`;
          }
        }
      }

      return absoluteElements.length > 0;
    } catch (error) {
      console.error('Absolute Positioning strategy failed:', error);
      return false;
    }
  }

  private restoreAbsolutePositioningStrategy(): void {
    const absoluteElements = this.findAbsoluteElements();
    for (const element of absoluteElements) {
      this.restoreElementStyles(element);
    }
  }

  /**
   * Traditional Margin Strategy Implementation
   */
  private applyTraditionalMarginStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      this.saveOriginalStyles(document.body, ['margin-left', 'margin-right', 'width']);
      this.saveOriginalStyles(document.documentElement, ['margin-left', 'margin-right']);

      const marginProperty = position === 'left' ? 'marginLeft' : 'marginRight';

      document.body.style[marginProperty] = `${sidebarWidth}px`;
      document.documentElement.style[marginProperty] = `${sidebarWidth}px`;
      document.body.style.width = `calc(100% - ${sidebarWidth}px)`;

      return true;
    } catch (error) {
      console.error('Traditional Margin strategy failed:', error);
      return false;
    }
  }

  private restoreTraditionalMarginStrategy(): void {
    this.restoreElementStyles(document.body);
    this.restoreElementStyles(document.documentElement);
  }

  /**
   * DOM Manipulation Strategy Implementation (Nuclear option)
   */
  private applyDOMManipulationStrategy(sidebarWidth: number, position: 'left' | 'right'): boolean {
    try {
      console.log('🚨 Applying DOM Manipulation Strategy (Nuclear option)');

      // Find the main content container
      const mainContainer = this.findMainContentContainer();
      if (!mainContainer) {
        console.warn('No main container found for DOM manipulation');
        return false;
      }

      // Save original styles
      this.saveOriginalStyles(mainContainer, ['width', 'margin-left', 'margin-right', 'transform', 'position']);

      // Force the container to respect sidebar
      const htmlElement = mainContainer as HTMLElement;
      const marginProperty = position === 'left' ? 'marginLeft' : 'marginRight';

      // Apply aggressive styling
      htmlElement.style.setProperty('width', `calc(100vw - ${sidebarWidth}px)`, 'important');
      htmlElement.style.setProperty(marginProperty, `${sidebarWidth}px`, 'important');
      htmlElement.style.setProperty('max-width', `calc(100vw - ${sidebarWidth}px)`, 'important');
      htmlElement.style.setProperty('min-width', `calc(100vw - ${sidebarWidth}px)`, 'important');
      htmlElement.style.setProperty('box-sizing', 'border-box', 'important');

      // Force layout recalculation
      htmlElement.offsetHeight;

      // Also apply to all direct children
      const children = Array.from(htmlElement.children) as HTMLElement[];
      children.forEach(child => {
        if (!this.isSidebarElement(child)) {
          this.saveOriginalStyles(child, ['width', 'margin-left', 'margin-right', 'max-width']);
          child.style.setProperty('width', '100%', 'important');
          child.style.setProperty('max-width', '100%', 'important');
          child.style.setProperty('box-sizing', 'border-box', 'important');
        }
      });

      console.log('💥 DOM Manipulation applied to main container:', mainContainer);
      return true;

    } catch (error) {
      console.error('DOM Manipulation strategy failed:', error);
      return false;
    }
  }

  private restoreDOMManipulationStrategy(): void {
    const mainContainer = this.findMainContentContainer();
    if (mainContainer) {
      this.restoreElementStyles(mainContainer);

      // Restore children
      const children = Array.from(mainContainer.children) as HTMLElement[];
      children.forEach(child => {
        this.restoreElementStyles(child);
      });
    }
  }

  /**
   * Detect stubborn websites that need DOM manipulation
   */
  private detectStubbornWebsite(): boolean {
    const hostname = window.location.hostname;

    // Known stubborn websites
    const stubbornSites = [
      'gaccode.com',
      'claude.ai',
      'chat.openai.com',
      'github.com'
    ];

    if (stubbornSites.some(site => hostname.includes(site))) {
      return true;
    }

    // Detect websites with complex layout that might need DOM manipulation
    const hasComplexLayout = this.hasComplexLayoutStructure();
    const hasViewportUnits = this.findViewportBasedElements().length > 0;
    const hasFixedPositioning = this.findAbsoluteElements().length > 3;

    return hasComplexLayout && (hasViewportUnits || hasFixedPositioning);
  }

  /**
   * Find the main content container for DOM manipulation
   */
  private findMainContentContainer(): Element | null {
    // Try common main container selectors
    const selectors = [
      'main',
      '#main',
      '.main',
      '#app',
      '#root',
      '.app',
      '.application',
      'body > div:first-child',
      'body > div:not([id*="sidebar"]):not([class*="sidebar"]):first-child'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && this.isValidMainContainer(element)) {
        return element;
      }
    }

    // If no specific container found, use body's first significant child
    const bodyChildren = Array.from(document.body.children);
    for (const child of bodyChildren) {
      if (this.isValidMainContainer(child) && !this.isSidebarElement(child)) {
        return child;
      }
    }

    return null;
  }

  /**
   * Check if element is a valid main container
   */
  private isValidMainContainer(element: Element): boolean {
    const htmlElement = element as HTMLElement;
    const computedStyle = window.getComputedStyle(htmlElement);

    // Must be visible and have significant size
    if (computedStyle.display === 'none' ||
        htmlElement.offsetWidth < 100 ||
        htmlElement.offsetHeight < 100) {
      return false;
    }

    // Should not be a sidebar element
    if (this.isSidebarElement(element)) {
      return false;
    }

    return true;
  }

  /**
   * Check if element is part of sidebar
   */
  private isSidebarElement(element: Element): boolean {
    const id = element.id.toLowerCase();
    const className = element.className.toLowerCase();

    return id.includes('sidebar') ||
           className.includes('sidebar') ||
           id.includes('chrome-vertical') ||
           id.includes('edge-vertical');
  }

  /**
   * Detect complex layout structure
   */
  private hasComplexLayoutStructure(): boolean {
    const gridElements = document.querySelectorAll('[style*="display: grid"], [style*="display:grid"]');
    const flexElements = document.querySelectorAll('[style*="display: flex"], [style*="display:flex"]');
    const absoluteElements = document.querySelectorAll('[style*="position: absolute"], [style*="position:absolute"]');

    return gridElements.length > 2 || flexElements.length > 5 || absoluteElements.length > 3;
  }

  /**
   * Helper methods for finding elements
   */
  private findViewportBasedElements(): Element[] {
    return Array.from(document.querySelectorAll('[style*="100vw"], [style*="100vh"]'));
  }

  private findGridContainers(): Element[] {
    return Array.from(document.querySelectorAll('[style*="display: grid"], [style*="display:grid"]'));
  }

  private findFlexContainers(): Element[] {
    return Array.from(document.querySelectorAll('[style*="display: flex"], [style*="display:flex"]'));
  }

  private findAbsoluteElements(): Element[] {
    return Array.from(document.querySelectorAll('[style*="position: absolute"], [style*="position:absolute"], [style*="position: fixed"], [style*="position:fixed"]'));
  }

  /**
   * Framework detection methods
   */
  private detectReactApp(): boolean {
    return !!(window as any).React ||
           document.querySelector('[data-reactroot]') !== null ||
           document.querySelector('#root') !== null;
  }

  private detectVueApp(): boolean {
    return !!(window as any).Vue ||
           document.querySelector('[data-v-]') !== null ||
           document.querySelector('.v-application') !== null;
  }

  private detectAngularApp(): boolean {
    return !!(window as any).ng ||
           document.querySelector('[ng-app]') !== null ||
           document.querySelector('app-root') !== null;
  }

  /**
   * Stylesheet analysis methods
   */
  private hasGridInStylesheets(): boolean {
    try {
      for (const stylesheet of document.styleSheets) {
        try {
          for (const rule of stylesheet.cssRules) {
            if (rule instanceof CSSStyleRule && rule.style.display === 'grid') {
              return true;
            }
          }
        } catch (e) {
          // Cross-origin stylesheet, skip
        }
      }
    } catch (e) {
      // Error accessing stylesheets
    }
    return false;
  }

  private hasFlexInStylesheets(): boolean {
    try {
      for (const stylesheet of document.styleSheets) {
        try {
          for (const rule of stylesheet.cssRules) {
            if (rule instanceof CSSStyleRule && rule.style.display === 'flex') {
              return true;
            }
          }
        } catch (e) {
          // Cross-origin stylesheet, skip
        }
      }
    } catch (e) {
      // Error accessing stylesheets
    }
    return false;
  }

  /**
   * Style management methods
   */
  private saveOriginalStyles(element: Element, properties: string[]): void {
    if (!this.originalStyles.has(element)) {
      this.originalStyles.set(element, new Map());
    }

    const styleMap = this.originalStyles.get(element)!;
    const htmlElement = element as HTMLElement;

    for (const property of properties) {
      if (!styleMap.has(property)) {
        styleMap.set(property, htmlElement.style.getPropertyValue(property) || '');
      }
    }
  }

  private restoreElementStyles(element: Element): void {
    const styleMap = this.originalStyles.get(element);
    if (!styleMap) return;

    const htmlElement = element as HTMLElement;

    for (const [property, value] of styleMap) {
      if (value) {
        htmlElement.style.setProperty(property, value);
      } else {
        htmlElement.style.removeProperty(property);
      }
    }

    this.originalStyles.delete(element);
  }

  private restoreOriginalStyles(): void {
    for (const [element, styleMap] of this.originalStyles) {
      const htmlElement = element as HTMLElement;

      for (const [property, value] of styleMap) {
        if (value) {
          htmlElement.style.setProperty(property, value);
        } else {
          htmlElement.style.removeProperty(property);
        }
      }
    }

    this.originalStyles.clear();
  }

  /**
   * Layout analysis methods
   */
  private calculateLayoutComplexity(): number {
    let complexity = 0;

    // Count different layout types
    const gridElements = document.querySelectorAll('[style*="display: grid"], [style*="display:grid"]');
    const flexElements = document.querySelectorAll('[style*="display: flex"], [style*="display:flex"]');
    const absoluteElements = document.querySelectorAll('[style*="position: absolute"], [style*="position:absolute"], [style*="position: fixed"], [style*="position:fixed"]');
    const viewportElements = document.querySelectorAll('[style*="100vw"], [style*="100vh"]');

    complexity += gridElements.length * 0.3;
    complexity += flexElements.length * 0.2;
    complexity += absoluteElements.length * 0.4;
    complexity += viewportElements.length * 0.5;

    // Check for nested layouts
    const nestedLayouts = document.querySelectorAll('[style*="display: grid"] [style*="display: flex"], [style*="display: flex"] [style*="display: grid"]');
    complexity += nestedLayouts.length * 0.6;

    return Math.min(complexity, 5); // Cap at 5
  }

  private isSignificantLayoutElement(element: Element): boolean {
    const tagName = element.tagName.toLowerCase();
    const className = element.className;

    // Check for layout-significant elements
    if (['main', 'section', 'article', 'aside', 'header', 'footer', 'nav'].includes(tagName)) {
      return true;
    }

    // Check for layout-significant classes
    const layoutClasses = ['container', 'wrapper', 'layout', 'grid', 'flex', 'app', 'root'];
    for (const layoutClass of layoutClasses) {
      if (className.includes(layoutClass)) {
        return true;
      }
    }

    // Check for layout-significant styles
    const style = (element as HTMLElement).style;
    if (style.display === 'grid' || style.display === 'flex' ||
        style.position === 'absolute' || style.position === 'fixed') {
      return true;
    }

    return false;
  }

  /**
   * Layout recalculation and cleanup
   */
  private triggerLayoutRecalculation(): void {
    // Force layout recalculation
    document.body.offsetHeight;

    // Trigger resize event
    window.dispatchEvent(new Event('resize'));

    // Trigger custom layout event for frameworks
    window.dispatchEvent(new CustomEvent('sidebar-layout-change', {
      detail: {
        sidebarWidth: this.currentSidebarWidth,
        position: this.currentPosition
      }
    }));
  }

  /**
   * Nuclear option: Force layout using viewport manipulation
   */
  private applyNuclearOption(sidebarWidth: number, position: 'left' | 'right'): void {
    console.log('☢️ Applying Nuclear Option - Viewport manipulation');

    try {
      // Create a super high-priority style that overrides everything
      const nuclearStyle = document.createElement('style');
      nuclearStyle.id = 'nuclear-layout-override';
      nuclearStyle.textContent = this.generateNuclearCSS(sidebarWidth, position);

      // Insert at the very end of head to ensure highest priority
      document.head.appendChild(nuclearStyle);

      // Force immediate layout recalculation
      document.body.offsetHeight;

      // Use MutationObserver to fight back against any CSS changes
      this.setupNuclearProtection(sidebarWidth, position);

      console.log('☢️ Nuclear option applied successfully');

    } catch (error) {
      console.error('☢️ Nuclear option failed:', error);
    }
  }

  /**
   * Generate nuclear-level CSS overrides
   */
  private generateNuclearCSS(sidebarWidth: number, position: 'left' | 'right'): string {
    const marginProperty = position === 'left' ? 'margin-left' : 'margin-right';
    const offsetProperty = position === 'left' ? 'left' : 'right';

    return `
      /* NUCLEAR OPTION: Override everything with maximum specificity */
      html html html html body body body body,
      html html html html body body body body *:not([id*="sidebar"]):not([class*="sidebar"]):not([id*="chrome-vertical"]):not([id*="edge-vertical"]) {
        ${marginProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
        box-sizing: border-box !important;
      }

      /* Force viewport elements */
      html html html html [style*="width: 100vw"],
      html html html html [style*="width:100vw"],
      html html html html [style*="width: 100%"],
      html html html html [style*="width:100%"] {
        width: calc(100vw - ${sidebarWidth}px) !important;
        ${marginProperty}: ${sidebarWidth}px !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
      }

      /* Force positioned elements */
      html html html html [style*="position: fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html html html html [style*="position:fixed"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html html html html [style*="position: absolute"]:not([id*="sidebar"]):not([class*="sidebar"]),
      html html html html [style*="position:absolute"]:not([id*="sidebar"]):not([class*="sidebar"]) {
        ${offsetProperty}: ${sidebarWidth}px !important;
        width: calc(100vw - ${sidebarWidth}px) !important;
        max-width: calc(100vw - ${sidebarWidth}px) !important;
        min-width: calc(100vw - ${sidebarWidth}px) !important;
      }

      /* Prevent any element from exceeding the adjusted viewport */
      html html html html * {
        max-width: calc(100vw - ${sidebarWidth}px) !important;
      }

      /* Force layout recalculation */
      html html html html body {
        transform: translateZ(0) !important;
      }
    `;
  }

  /**
   * Setup nuclear protection against CSS changes
   */
  private setupNuclearProtection(sidebarWidth: number, position: 'left' | 'right'): void {
    // Create a super-aggressive observer that fights back
    const nuclearObserver = new MutationObserver((mutations) => {
      let needsReapplication = false;

      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const target = mutation.target as HTMLElement;
          if (!this.isSidebarElement(target)) {
            // Check if the element is trying to use full width
            const style = target.style;
            if (style.width === '100vw' || style.width === '100%') {
              needsReapplication = true;
              break;
            }
          }
        }
      }

      if (needsReapplication) {
        // Reapply nuclear option immediately
        setTimeout(() => {
          this.applyNuclearOption(sidebarWidth, position);
        }, 0);
      }
    });

    // Observe the entire document
    nuclearObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style'],
      subtree: true
    });

    // Store observer for cleanup
    (this as any).nuclearObserver = nuclearObserver;
  }

  /**
   * Cleanup and destroy
   */
  destroy(): void {
    // Stop monitoring
    if (this.layoutObserver) {
      this.layoutObserver.disconnect();
      this.layoutObserver = null;
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // Stop nuclear protection
    if ((this as any).nuclearObserver) {
      (this as any).nuclearObserver.disconnect();
      (this as any).nuclearObserver = null;
    }

    // Remove nuclear styles
    const nuclearStyle = document.getElementById('nuclear-layout-override');
    if (nuclearStyle) {
      nuclearStyle.remove();
    }

    // Restore layout
    this.restoreLayout();

    console.log('🧹 UniversalLayoutAdapter destroyed');
  }
}
