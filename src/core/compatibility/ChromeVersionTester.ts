/**
 * Chrome Version Tester - Chrome版本兼容性检测
 */

import { Logger } from '../logging/Logger';

export interface ChromeVersionInfo {
  version: string;
  majorVersion: number;
  minorVersion: number;
  buildVersion: number;
  patchVersion: number;
  isSupported: boolean;
  supportLevel: 'full' | 'partial' | 'limited' | 'unsupported';
  missingFeatures: string[];
  deprecatedFeatures: string[];
  newFeatures: string[];
}

export interface ChromeFeatureSupport {
  feature: string;
  minVersion: number;
  maxVersion?: number;
  status: 'stable' | 'experimental' | 'deprecated' | 'removed';
  description: string;
  alternatives?: string[];
}

export interface ChromeAPISupport {
  api: string;
  methods: Record<string, {
    minVersion: number;
    maxVersion?: number;
    status: 'stable' | 'experimental' | 'deprecated' | 'removed';
  }>;
}

/**
 * Chrome Version Tester
 */
export class ChromeVersionTester {
  private static instance: ChromeVersionTester;
  private logger: Logger;
  private versionInfo: ChromeVersionInfo;
  private featureSupport: Map<string, ChromeFeatureSupport> = new Map();
  private apiSupport: Map<string, ChromeAPISupport> = new Map();

  private constructor() {
    this.logger = Logger.getInstance();
    this.versionInfo = this.detectChromeVersion();
    this.initializeFeatureSupport();
    this.initializeAPISupport();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ChromeVersionTester {
    if (!ChromeVersionTester.instance) {
      ChromeVersionTester.instance = new ChromeVersionTester();
    }
    return ChromeVersionTester.instance;
  }

  /**
   * Get Chrome version info
   */
  getVersionInfo(): ChromeVersionInfo {
    return { ...this.versionInfo };
  }

  /**
   * Check if feature is supported
   */
  isFeatureSupported(feature: string): boolean {
    const featureInfo = this.featureSupport.get(feature);
    if (!featureInfo) {
      return false;
    }

    const currentVersion = this.versionInfo.majorVersion;
    
    if (currentVersion < featureInfo.minVersion) {
      return false;
    }

    if (featureInfo.maxVersion && currentVersion > featureInfo.maxVersion) {
      return false;
    }

    return featureInfo.status !== 'removed';
  }

  /**
   * Check if API method is supported
   */
  isAPIMethodSupported(api: string, method: string): boolean {
    const apiInfo = this.apiSupport.get(api);
    if (!apiInfo) {
      return false;
    }

    const methodInfo = apiInfo.methods[method];
    if (!methodInfo) {
      return false;
    }

    const currentVersion = this.versionInfo.majorVersion;
    
    if (currentVersion < methodInfo.minVersion) {
      return false;
    }

    if (methodInfo.maxVersion && currentVersion > methodInfo.maxVersion) {
      return false;
    }

    return methodInfo.status !== 'removed';
  }

  /**
   * Get missing features for current version
   */
  getMissingFeatures(): string[] {
    const missing: string[] = [];
    
    this.featureSupport.forEach((featureInfo, feature) => {
      if (!this.isFeatureSupported(feature)) {
        missing.push(feature);
      }
    });

    return missing;
  }

  /**
   * Get deprecated features for current version
   */
  getDeprecatedFeatures(): string[] {
    const deprecated: string[] = [];
    
    this.featureSupport.forEach((featureInfo, feature) => {
      if (featureInfo.status === 'deprecated' && this.isFeatureSupported(feature)) {
        deprecated.push(feature);
      }
    });

    return deprecated;
  }

  /**
   * Get new features for current version
   */
  getNewFeatures(): string[] {
    const newFeatures: string[] = [];
    
    this.featureSupport.forEach((featureInfo, feature) => {
      if (this.versionInfo.majorVersion === featureInfo.minVersion) {
        newFeatures.push(feature);
      }
    });

    return newFeatures;
  }

  /**
   * Get compatibility recommendations
   */
  getCompatibilityRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.versionInfo.majorVersion < 88) {
      recommendations.push('Chrome version is quite old. Consider updating for better performance and security.');
    }

    if (this.versionInfo.majorVersion < 90) {
      recommendations.push('Some modern web APIs may not be available. Consider using polyfills.');
    }

    const missingFeatures = this.getMissingFeatures();
    if (missingFeatures.length > 0) {
      recommendations.push(`Missing features: ${missingFeatures.slice(0, 3).join(', ')}${missingFeatures.length > 3 ? '...' : ''}`);
    }

    const deprecatedFeatures = this.getDeprecatedFeatures();
    if (deprecatedFeatures.length > 0) {
      recommendations.push(`Deprecated features in use: ${deprecatedFeatures.slice(0, 3).join(', ')}${deprecatedFeatures.length > 3 ? '...' : ''}`);
    }

    return recommendations;
  }

  /**
   * Test Chrome extension manifest version support
   */
  testManifestVersionSupport(): {
    v2: boolean;
    v3: boolean;
    recommended: 'v2' | 'v3';
    migrationRequired: boolean;
  } {
    const currentVersion = this.versionInfo.majorVersion;
    
    // Manifest V2 support
    const v2Supported = currentVersion >= 18; // Very old versions
    
    // Manifest V3 support (Chrome 88+)
    const v3Supported = currentVersion >= 88;
    
    // Chrome 127+ will phase out Manifest V2
    const migrationRequired = currentVersion >= 127;
    
    return {
      v2: v2Supported && !migrationRequired,
      v3: v3Supported,
      recommended: v3Supported ? 'v3' : 'v2',
      migrationRequired
    };
  }

  /**
   * Test service worker support
   */
  testServiceWorkerSupport(): {
    supported: boolean;
    version: string;
    features: {
      registration: boolean;
      updateOnReload: boolean;
      navigationPreload: boolean;
      backgroundSync: boolean;
      pushMessaging: boolean;
    };
  } {
    const currentVersion = this.versionInfo.majorVersion;
    
    return {
      supported: currentVersion >= 40,
      version: currentVersion >= 40 ? 'supported' : 'not supported',
      features: {
        registration: currentVersion >= 40,
        updateOnReload: currentVersion >= 68,
        navigationPreload: currentVersion >= 59,
        backgroundSync: currentVersion >= 49,
        pushMessaging: currentVersion >= 42
      }
    };
  }

  /**
   * Test extension API support
   */
  testExtensionAPISupport(): Record<string, boolean> {
    const currentVersion = this.versionInfo.majorVersion;
    
    return {
      'chrome.tabs': currentVersion >= 4,
      'chrome.bookmarks': currentVersion >= 5,
      'chrome.storage': currentVersion >= 20,
      'chrome.runtime': currentVersion >= 22,
      'chrome.permissions': currentVersion >= 16,
      'chrome.contextMenus': currentVersion >= 6,
      'chrome.notifications': currentVersion >= 28,
      'chrome.alarms': currentVersion >= 22,
      'chrome.identity': currentVersion >= 29,
      'chrome.management': currentVersion >= 8,
      'chrome.webNavigation': currentVersion >= 16,
      'chrome.webRequest': currentVersion >= 17,
      'chrome.declarativeNetRequest': currentVersion >= 84,
      'chrome.scripting': currentVersion >= 88,
      'chrome.action': currentVersion >= 88, // Manifest V3
      'chrome.sidePanel': currentVersion >= 114
    };
  }

  /**
   * Detect Chrome version
   */
  private detectChromeVersion(): ChromeVersionInfo {
    const userAgent = navigator.userAgent;
    const match = userAgent.match(/Chrome\/([0-9.]+)/);
    
    if (!match) {
      return {
        version: 'unknown',
        majorVersion: 0,
        minorVersion: 0,
        buildVersion: 0,
        patchVersion: 0,
        isSupported: false,
        supportLevel: 'unsupported',
        missingFeatures: [],
        deprecatedFeatures: [],
        newFeatures: []
      };
    }

    const version = match[1];
    const parts = version.split('.').map(Number);
    const majorVersion = parts[0] || 0;
    const minorVersion = parts[1] || 0;
    const buildVersion = parts[2] || 0;
    const patchVersion = parts[3] || 0;

    // Determine support level
    let supportLevel: ChromeVersionInfo['supportLevel'];
    let isSupported: boolean;

    if (majorVersion >= 100) {
      supportLevel = 'full';
      isSupported = true;
    } else if (majorVersion >= 88) {
      supportLevel = 'partial';
      isSupported = true;
    } else if (majorVersion >= 70) {
      supportLevel = 'limited';
      isSupported = true;
    } else {
      supportLevel = 'unsupported';
      isSupported = false;
    }

    return {
      version,
      majorVersion,
      minorVersion,
      buildVersion,
      patchVersion,
      isSupported,
      supportLevel,
      missingFeatures: [],
      deprecatedFeatures: [],
      newFeatures: []
    };
  }

  /**
   * Initialize feature support data
   */
  private initializeFeatureSupport(): void {
    // CSS Features
    this.featureSupport.set('css-grid', {
      feature: 'css-grid',
      minVersion: 57,
      status: 'stable',
      description: 'CSS Grid Layout support'
    });

    this.featureSupport.set('css-flexbox', {
      feature: 'css-flexbox',
      minVersion: 21,
      status: 'stable',
      description: 'CSS Flexbox Layout support'
    });

    this.featureSupport.set('css-custom-properties', {
      feature: 'css-custom-properties',
      minVersion: 49,
      status: 'stable',
      description: 'CSS Custom Properties (Variables) support'
    });

    this.featureSupport.set('css-container-queries', {
      feature: 'css-container-queries',
      minVersion: 105,
      status: 'stable',
      description: 'CSS Container Queries support'
    });

    // JavaScript Features
    this.featureSupport.set('es6-modules', {
      feature: 'es6-modules',
      minVersion: 61,
      status: 'stable',
      description: 'ES6 Modules support'
    });

    this.featureSupport.set('async-await', {
      feature: 'async-await',
      minVersion: 55,
      status: 'stable',
      description: 'Async/Await syntax support'
    });

    this.featureSupport.set('dynamic-imports', {
      feature: 'dynamic-imports',
      minVersion: 63,
      status: 'stable',
      description: 'Dynamic import() support'
    });

    this.featureSupport.set('optional-chaining', {
      feature: 'optional-chaining',
      minVersion: 80,
      status: 'stable',
      description: 'Optional chaining operator (?.) support'
    });

    this.featureSupport.set('nullish-coalescing', {
      feature: 'nullish-coalescing',
      minVersion: 80,
      status: 'stable',
      description: 'Nullish coalescing operator (??) support'
    });

    // Web APIs
    this.featureSupport.set('intersection-observer', {
      feature: 'intersection-observer',
      minVersion: 51,
      status: 'stable',
      description: 'Intersection Observer API support'
    });

    this.featureSupport.set('resize-observer', {
      feature: 'resize-observer',
      minVersion: 64,
      status: 'stable',
      description: 'Resize Observer API support'
    });

    this.featureSupport.set('performance-observer', {
      feature: 'performance-observer',
      minVersion: 52,
      status: 'stable',
      description: 'Performance Observer API support'
    });

    this.featureSupport.set('web-components', {
      feature: 'web-components',
      minVersion: 54,
      status: 'stable',
      description: 'Web Components (Custom Elements, Shadow DOM) support'
    });

    this.featureSupport.set('service-workers', {
      feature: 'service-workers',
      minVersion: 40,
      status: 'stable',
      description: 'Service Workers support'
    });

    // Extension-specific features
    this.featureSupport.set('manifest-v3', {
      feature: 'manifest-v3',
      minVersion: 88,
      status: 'stable',
      description: 'Manifest V3 support'
    });

    this.featureSupport.set('declarative-net-request', {
      feature: 'declarative-net-request',
      minVersion: 84,
      status: 'stable',
      description: 'Declarative Net Request API support'
    });

    this.featureSupport.set('scripting-api', {
      feature: 'scripting-api',
      minVersion: 88,
      status: 'stable',
      description: 'chrome.scripting API support'
    });

    this.featureSupport.set('side-panel-api', {
      feature: 'side-panel-api',
      minVersion: 114,
      status: 'stable',
      description: 'chrome.sidePanel API support'
    });

    // Deprecated features
    this.featureSupport.set('background-pages', {
      feature: 'background-pages',
      minVersion: 4,
      maxVersion: 127,
      status: 'deprecated',
      description: 'Background pages (deprecated in favor of service workers)',
      alternatives: ['service-workers']
    });

    this.featureSupport.set('web-request-blocking', {
      feature: 'web-request-blocking',
      minVersion: 17,
      maxVersion: 127,
      status: 'deprecated',
      description: 'Blocking webRequest API (deprecated in favor of declarativeNetRequest)',
      alternatives: ['declarative-net-request']
    });
  }

  /**
   * Initialize API support data
   */
  private initializeAPISupport(): void {
    // chrome.tabs API
    this.apiSupport.set('chrome.tabs', {
      api: 'chrome.tabs',
      methods: {
        'query': { minVersion: 4, status: 'stable' },
        'get': { minVersion: 4, status: 'stable' },
        'create': { minVersion: 4, status: 'stable' },
        'update': { minVersion: 4, status: 'stable' },
        'remove': { minVersion: 4, status: 'stable' },
        'duplicate': { minVersion: 23, status: 'stable' },
        'group': { minVersion: 89, status: 'stable' },
        'ungroup': { minVersion: 89, status: 'stable' },
        'highlight': { minVersion: 16, status: 'stable' },
        'move': { minVersion: 4, status: 'stable' },
        'reload': { minVersion: 4, status: 'stable' },
        'captureVisibleTab': { minVersion: 5, status: 'stable' },
        'executeScript': { minVersion: 4, maxVersion: 88, status: 'deprecated' },
        'insertCSS': { minVersion: 4, maxVersion: 88, status: 'deprecated' }
      }
    });

    // chrome.bookmarks API
    this.apiSupport.set('chrome.bookmarks', {
      api: 'chrome.bookmarks',
      methods: {
        'get': { minVersion: 5, status: 'stable' },
        'getChildren': { minVersion: 5, status: 'stable' },
        'getTree': { minVersion: 5, status: 'stable' },
        'getSubTree': { minVersion: 5, status: 'stable' },
        'search': { minVersion: 5, status: 'stable' },
        'create': { minVersion: 5, status: 'stable' },
        'move': { minVersion: 5, status: 'stable' },
        'update': { minVersion: 5, status: 'stable' },
        'remove': { minVersion: 5, status: 'stable' },
        'removeTree': { minVersion: 5, status: 'stable' }
      }
    });

    // chrome.storage API
    this.apiSupport.set('chrome.storage', {
      api: 'chrome.storage',
      methods: {
        'local.get': { minVersion: 20, status: 'stable' },
        'local.set': { minVersion: 20, status: 'stable' },
        'local.remove': { minVersion: 20, status: 'stable' },
        'local.clear': { minVersion: 20, status: 'stable' },
        'sync.get': { minVersion: 20, status: 'stable' },
        'sync.set': { minVersion: 20, status: 'stable' },
        'sync.remove': { minVersion: 20, status: 'stable' },
        'sync.clear': { minVersion: 20, status: 'stable' },
        'session.get': { minVersion: 102, status: 'stable' },
        'session.set': { minVersion: 102, status: 'stable' },
        'session.remove': { minVersion: 102, status: 'stable' },
        'session.clear': { minVersion: 102, status: 'stable' }
      }
    });

    // chrome.runtime API
    this.apiSupport.set('chrome.runtime', {
      api: 'chrome.runtime',
      methods: {
        'sendMessage': { minVersion: 22, status: 'stable' },
        'connect': { minVersion: 22, status: 'stable' },
        'getManifest': { minVersion: 22, status: 'stable' },
        'getURL': { minVersion: 22, status: 'stable' },
        'reload': { minVersion: 25, status: 'stable' },
        'requestUpdateCheck': { minVersion: 25, status: 'stable' },
        'restart': { minVersion: 32, status: 'stable' },
        'setUninstallURL': { minVersion: 41, status: 'stable' },
        'openOptionsPage': { minVersion: 42, status: 'stable' }
      }
    });

    // chrome.scripting API (Manifest V3)
    this.apiSupport.set('chrome.scripting', {
      api: 'chrome.scripting',
      methods: {
        'executeScript': { minVersion: 88, status: 'stable' },
        'insertCSS': { minVersion: 88, status: 'stable' },
        'removeCSS': { minVersion: 88, status: 'stable' },
        'registerContentScripts': { minVersion: 96, status: 'stable' },
        'unregisterContentScripts': { minVersion: 96, status: 'stable' },
        'getRegisteredContentScripts': { minVersion: 96, status: 'stable' },
        'updateContentScripts': { minVersion: 96, status: 'stable' }
      }
    });

    // chrome.action API (Manifest V3)
    this.apiSupport.set('chrome.action', {
      api: 'chrome.action',
      methods: {
        'setTitle': { minVersion: 88, status: 'stable' },
        'getTitle': { minVersion: 88, status: 'stable' },
        'setIcon': { minVersion: 88, status: 'stable' },
        'setPopup': { minVersion: 88, status: 'stable' },
        'getPopup': { minVersion: 88, status: 'stable' },
        'setBadgeText': { minVersion: 88, status: 'stable' },
        'getBadgeText': { minVersion: 88, status: 'stable' },
        'setBadgeBackgroundColor': { minVersion: 88, status: 'stable' },
        'getBadgeBackgroundColor': { minVersion: 88, status: 'stable' },
        'enable': { minVersion: 88, status: 'stable' },
        'disable': { minVersion: 88, status: 'stable' }
      }
    });

    // chrome.sidePanel API
    this.apiSupport.set('chrome.sidePanel', {
      api: 'chrome.sidePanel',
      methods: {
        'open': { minVersion: 114, status: 'stable' },
        'close': { minVersion: 114, status: 'stable' },
        'setOptions': { minVersion: 114, status: 'stable' },
        'getOptions': { minVersion: 114, status: 'stable' },
        'setPanelBehavior': { minVersion: 116, status: 'stable' }
      }
    });
  }
}

// Export convenience functions
export const chromeVersionTester = ChromeVersionTester.getInstance();

export function getChromeVersionInfo(): ChromeVersionInfo {
  return chromeVersionTester.getVersionInfo();
}

export function isChromeFeatureSupported(feature: string): boolean {
  return chromeVersionTester.isFeatureSupported(feature);
}

export function isChromeAPISupported(api: string, method: string): boolean {
  return chromeVersionTester.isAPIMethodSupported(api, method);
}

export function getChromeCompatibilityRecommendations(): string[] {
  return chromeVersionTester.getCompatibilityRecommendations();
}

export function testManifestVersionSupport() {
  return chromeVersionTester.testManifestVersionSupport();
}

export function testExtensionAPISupport(): Record<string, boolean> {
  return chromeVersionTester.testExtensionAPISupport();
}