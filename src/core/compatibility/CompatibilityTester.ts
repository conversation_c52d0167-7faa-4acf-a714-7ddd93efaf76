/**
 * Compatibility Tester - 兼容性检测和测试框架
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';

export interface CompatibilityTest {
  id: string;
  name: string;
  description: string;
  category: 'browser' | 'api' | 'css' | 'javascript' | 'extension' | 'website';
  priority: 'low' | 'medium' | 'high' | 'critical';
  test: () => Promise<CompatibilityResult> | CompatibilityResult;
  requirements?: string[];
  fallback?: () => void;
}

export interface CompatibilityResult {
  success: boolean;
  supported: boolean;
  version?: string;
  message: string;
  details?: Record<string, any>;
  workaround?: string;
  impact: 'none' | 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
}

export interface BrowserInfo {
  name: string;
  version: string;
  engine: string;
  engineVersion: string;
  platform: string;
  mobile: boolean;
  userAgent: string;
}

export interface CompatibilityReport {
  browserInfo: BrowserInfo;
  testResults: Map<string, CompatibilityResult>;
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
    critical: number;
  };
  recommendations: string[];
  timestamp: number;
}

export interface WebsiteCompatibility {
  url: string;
  domain: string;
  title: string;
  issues: CompatibilityIssue[];
  score: number;
  tested: boolean;
  timestamp: number;
}

export interface CompatibilityIssue {
  type: 'layout' | 'functionality' | 'performance' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  element?: string;
  fix?: string;
  workaround?: string;
}

/**
 * Compatibility Tester
 */
export class CompatibilityTester {
  private static instance: CompatibilityTester;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private tests: Map<string, CompatibilityTest> = new Map();
  private results: Map<string, CompatibilityResult> = new Map();
  private browserInfo: BrowserInfo;
  private websiteCompatibility: Map<string, WebsiteCompatibility> = new Map();

  private constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.browserInfo = this.detectBrowserInfo();
    this.registerDefaultTests();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): CompatibilityTester {
    if (!CompatibilityTester.instance) {
      CompatibilityTester.instance = new CompatibilityTester();
    }
    return CompatibilityTester.instance;
  }
}  /*
*
   * Register compatibility test
   */
  registerTest(test: CompatibilityTest): void {
    this.tests.set(test.id, test);
    this.logger.debug('compatibility', `Registered test: ${test.name}`);
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<CompatibilityReport> {
    this.logger.info('compatibility', 'Starting compatibility tests');

    const results = new Map<string, CompatibilityResult>();
    
    for (const [id, test] of this.tests) {
      try {
        this.performanceMonitor.startTiming(`compatibility.${id}`, 'compatibility');
        
        const result = await test.test();
        result.timestamp = Date.now();
        
        results.set(id, result);
        this.results.set(id, result);
        
        this.performanceMonitor.endTiming(`compatibility.${id}`, 'compatibility');
        
        this.logger.debug('compatibility', `Test completed: ${test.name}`, {
          success: result.success,
          supported: result.supported,
          impact: result.impact
        });
        
      } catch (error) {
        const errorResult: CompatibilityResult = {
          success: false,
          supported: false,
          message: `Test failed: ${error instanceof Error ? error.message : String(error)}`,
          impact: 'high',
          timestamp: Date.now()
        };
        
        results.set(id, errorResult);
        this.results.set(id, errorResult);
        
        this.logger.error('compatibility', `Test failed: ${test.name}`, error);
      }
    }

    const report = this.generateReport(results);
    
    this.logger.info('compatibility', 'Compatibility tests completed', {
      total: report.summary.total,
      passed: report.summary.passed,
      failed: report.summary.failed,
      critical: report.summary.critical
    });

    return report;
  }

  /**
   * Run specific test
   */
  async runTest(testId: string): Promise<CompatibilityResult | null> {
    const test = this.tests.get(testId);
    if (!test) {
      this.logger.warn('compatibility', `Test not found: ${testId}`);
      return null;
    }

    try {
      const result = await test.test();
      result.timestamp = Date.now();
      
      this.results.set(testId, result);
      
      this.logger.debug('compatibility', `Test completed: ${test.name}`, result);
      
      return result;
    } catch (error) {
      const errorResult: CompatibilityResult = {
        success: false,
        supported: false,
        message: `Test failed: ${error instanceof Error ? error.message : String(error)}`,
        impact: 'high',
        timestamp: Date.now()
      };
      
      this.results.set(testId, errorResult);
      this.logger.error('compatibility', `Test failed: ${test.name}`, error);
      
      return errorResult;
    }
  }

  /**
   * Test website compatibility
   */
  async testWebsiteCompatibility(url: string): Promise<WebsiteCompatibility> {
    this.logger.info('compatibility', `Testing website compatibility: ${url}`);

    const domain = new URL(url).hostname;
    const issues: CompatibilityIssue[] = [];

    try {
      // Test basic functionality
      const basicTests = await this.runBasicWebsiteTests(url);
      issues.push(...basicTests);

      // Test layout compatibility
      const layoutTests = await this.runLayoutTests(url);
      issues.push(...layoutTests);

      // Test performance impact
      const performanceTests = await this.runPerformanceTests(url);
      issues.push(...performanceTests);

      // Calculate compatibility score
      const score = this.calculateCompatibilityScore(issues);

      const compatibility: WebsiteCompatibility = {
        url,
        domain,
        title: document.title || 'Unknown',
        issues,
        score,
        tested: true,
        timestamp: Date.now()
      };

      this.websiteCompatibility.set(domain, compatibility);
      
      this.logger.info('compatibility', `Website compatibility test completed: ${url}`, {
        score,
        issues: issues.length
      });

      return compatibility;
      
    } catch (error) {
      this.logger.error('compatibility', `Website compatibility test failed: ${url}`, error);
      
      return {
        url,
        domain,
        title: 'Error',
        issues: [{
          type: 'functionality',
          severity: 'high',
          description: `Failed to test website: ${error instanceof Error ? error.message : String(error)}`
        }],
        score: 0,
        tested: false,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get browser info
   */
  getBrowserInfo(): BrowserInfo {
    return { ...this.browserInfo };
  }

  /**
   * Get test results
   */
  getTestResults(): Map<string, CompatibilityResult> {
    return new Map(this.results);
  }

  /**
   * Get website compatibility
   */
  getWebsiteCompatibility(domain?: string): WebsiteCompatibility[] {
    if (domain) {
      const result = this.websiteCompatibility.get(domain);
      return result ? [result] : [];
    }
    
    return Array.from(this.websiteCompatibility.values());
  }

  /**
   * Check feature support
   */
  checkFeatureSupport(feature: string): boolean {
    const testResult = this.results.get(feature);
    return testResult ? testResult.supported : false;
  }

  /**
   * Get compatibility recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    
    this.results.forEach((result, testId) => {
      if (!result.supported || result.impact === 'high' || result.impact === 'critical') {
        if (result.workaround) {
          recommendations.push(result.workaround);
        }
        
        const test = this.tests.get(testId);
        if (test?.fallback) {
          recommendations.push(`Consider using fallback for ${test.name}`);
        }
      }
    });

    return [...new Set(recommendations)];
  }

  /**
   * Detect browser info
   */
  private detectBrowserInfo(): BrowserInfo {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    
    // Detect browser name and version
    let name = 'Unknown';
    let version = 'Unknown';
    let engine = 'Unknown';
    let engineVersion = 'Unknown';
    
    if (userAgent.includes('Chrome')) {
      name = 'Chrome';
      const match = userAgent.match(/Chrome\/([0-9.]+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Blink';
      
      const blinkMatch = userAgent.match(/Chrome\/([0-9.]+)/);
      engineVersion = blinkMatch ? blinkMatch[1] : 'Unknown';
    } else if (userAgent.includes('Firefox')) {
      name = 'Firefox';
      const match = userAgent.match(/Firefox\/([0-9.]+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'Gecko';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      name = 'Safari';
      const match = userAgent.match(/Version\/([0-9.]+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'WebKit';
    } else if (userAgent.includes('Edge')) {
      name = 'Edge';
      const match = userAgent.match(/Edge\/([0-9.]+)/);
      version = match ? match[1] : 'Unknown';
      engine = 'EdgeHTML';
    }

    const mobile = /Mobile|Android|iPhone|iPad/.test(userAgent);

    return {
      name,
      version,
      engine,
      engineVersion,
      platform,
      mobile,
      userAgent
    };
  }

  /**
   * Register default tests
   */
  private registerDefaultTests(): void {
    // Chrome Extension API tests
    this.registerTest({
      id: 'chrome_tabs_api',
      name: 'Chrome Tabs API',
      description: 'Test Chrome tabs API availability',
      category: 'api',
      priority: 'critical',
      test: () => {
        const supported = !!(chrome && chrome.tabs);
        return {
          success: true,
          supported,
          message: supported ? 'Chrome tabs API is available' : 'Chrome tabs API is not available',
          impact: supported ? 'none' : 'critical',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'chrome_bookmarks_api',
      name: 'Chrome Bookmarks API',
      description: 'Test Chrome bookmarks API availability',
      category: 'api',
      priority: 'critical',
      test: () => {
        const supported = !!(chrome && chrome.bookmarks);
        return {
          success: true,
          supported,
          message: supported ? 'Chrome bookmarks API is available' : 'Chrome bookmarks API is not available',
          impact: supported ? 'none' : 'critical',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'chrome_storage_api',
      name: 'Chrome Storage API',
      description: 'Test Chrome storage API availability',
      category: 'api',
      priority: 'critical',
      test: () => {
        const supported = !!(chrome && chrome.storage);
        return {
          success: true,
          supported,
          message: supported ? 'Chrome storage API is available' : 'Chrome storage API is not available',
          impact: supported ? 'none' : 'critical',
          timestamp: Date.now()
        };
      }
    });

    // CSS Feature tests
    this.registerTest({
      id: 'css_flexbox',
      name: 'CSS Flexbox',
      description: 'Test CSS Flexbox support',
      category: 'css',
      priority: 'high',
      test: () => {
        const testElement = document.createElement('div');
        testElement.style.display = 'flex';
        const supported = testElement.style.display === 'flex';
        
        return {
          success: true,
          supported,
          message: supported ? 'CSS Flexbox is supported' : 'CSS Flexbox is not supported',
          impact: supported ? 'none' : 'high',
          workaround: supported ? undefined : 'Use float-based layouts as fallback',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'css_grid',
      name: 'CSS Grid',
      description: 'Test CSS Grid support',
      category: 'css',
      priority: 'medium',
      test: () => {
        const testElement = document.createElement('div');
        testElement.style.display = 'grid';
        const supported = testElement.style.display === 'grid';
        
        return {
          success: true,
          supported,
          message: supported ? 'CSS Grid is supported' : 'CSS Grid is not supported',
          impact: supported ? 'none' : 'medium',
          workaround: supported ? undefined : 'Use flexbox or float layouts as fallback',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'css_custom_properties',
      name: 'CSS Custom Properties',
      description: 'Test CSS custom properties (variables) support',
      category: 'css',
      priority: 'medium',
      test: () => {
        const supported = window.CSS && CSS.supports && CSS.supports('color', 'var(--test)');
        
        return {
          success: true,
          supported,
          message: supported ? 'CSS custom properties are supported' : 'CSS custom properties are not supported',
          impact: supported ? 'none' : 'medium',
          workaround: supported ? undefined : 'Use preprocessor variables or JavaScript for theming',
          timestamp: Date.now()
        };
      }
    });

    // JavaScript Feature tests
    this.registerTest({
      id: 'es6_modules',
      name: 'ES6 Modules',
      description: 'Test ES6 modules support',
      category: 'javascript',
      priority: 'high',
      test: () => {
        const supported = 'noModule' in document.createElement('script');
        
        return {
          success: true,
          supported,
          message: supported ? 'ES6 modules are supported' : 'ES6 modules are not supported',
          impact: supported ? 'none' : 'high',
          workaround: supported ? undefined : 'Use bundled JavaScript files',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'async_await',
      name: 'Async/Await',
      description: 'Test async/await support',
      category: 'javascript',
      priority: 'high',
      test: async () => {
        let supported = false;
        
        try {
          // Test if async/await syntax is supported
          eval('(async function() { await Promise.resolve(); })');
          supported = true;
        } catch (error) {
          supported = false;
        }
        
        return {
          success: true,
          supported,
          message: supported ? 'Async/await is supported' : 'Async/await is not supported',
          impact: supported ? 'none' : 'high',
          workaround: supported ? undefined : 'Use Promise.then() chains as fallback',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'intersection_observer',
      name: 'Intersection Observer',
      description: 'Test Intersection Observer API support',
      category: 'api',
      priority: 'medium',
      test: () => {
        const supported = 'IntersectionObserver' in window;
        
        return {
          success: true,
          supported,
          message: supported ? 'Intersection Observer is supported' : 'Intersection Observer is not supported',
          impact: supported ? 'none' : 'medium',
          workaround: supported ? undefined : 'Use scroll event listeners as fallback',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'resize_observer',
      name: 'Resize Observer',
      description: 'Test Resize Observer API support',
      category: 'api',
      priority: 'low',
      test: () => {
        const supported = 'ResizeObserver' in window;
        
        return {
          success: true,
          supported,
          message: supported ? 'Resize Observer is supported' : 'Resize Observer is not supported',
          impact: supported ? 'none' : 'low',
          workaround: supported ? undefined : 'Use window resize events as fallback',
          timestamp: Date.now()
        };
      }
    });

    // Performance API tests
    this.registerTest({
      id: 'performance_observer',
      name: 'Performance Observer',
      description: 'Test Performance Observer API support',
      category: 'api',
      priority: 'low',
      test: () => {
        const supported = 'PerformanceObserver' in window;
        
        return {
          success: true,
          supported,
          message: supported ? 'Performance Observer is supported' : 'Performance Observer is not supported',
          impact: supported ? 'none' : 'low',
          workaround: supported ? undefined : 'Use manual performance tracking',
          timestamp: Date.now()
        };
      }
    });

    this.registerTest({
      id: 'performance_memory',
      name: 'Performance Memory',
      description: 'Test Performance Memory API support',
      category: 'api',
      priority: 'low',
      test: () => {
        const supported = 'memory' in performance;
        
        return {
          success: true,
          supported,
          message: supported ? 'Performance Memory is supported' : 'Performance Memory is not supported',
          impact: supported ? 'none' : 'low',
          workaround: supported ? undefined : 'Memory monitoring will be limited',
          timestamp: Date.now()
        };
      }
    });
  }

  /**
   * Generate compatibility report
   */
  private generateReport(results: Map<string, CompatibilityResult>): CompatibilityReport {
    let passed = 0;
    let failed = 0;
    let warnings = 0;
    let critical = 0;

    results.forEach(result => {
      if (result.success && result.supported) {
        passed++;
      } else {
        failed++;
      }

      if (result.impact === 'critical') {
        critical++;
      } else if (result.impact === 'high' || result.impact === 'medium') {
        warnings++;
      }
    });

    const recommendations = this.getRecommendations();

    return {
      browserInfo: this.browserInfo,
      testResults: results,
      summary: {
        total: results.size,
        passed,
        failed,
        warnings,
        critical
      },
      recommendations,
      timestamp: Date.now()
    };
  }

  /**
   * Run basic website tests
   */
  private async runBasicWebsiteTests(url: string): Promise<CompatibilityIssue[]> {
    const issues: CompatibilityIssue[] = [];

    try {
      // Test if sidebar can be injected
      const canInject = this.testSidebarInjection();
      if (!canInject) {
        issues.push({
          type: 'functionality',
          severity: 'critical',
          description: 'Cannot inject sidebar into this website',
          fix: 'Check Content Security Policy and iframe restrictions'
        });
      }

      // Test for conflicting CSS
      const cssConflicts = this.detectCSSConflicts();
      if (cssConflicts.length > 0) {
        issues.push({
          type: 'layout',
          severity: 'medium',
          description: `CSS conflicts detected: ${cssConflicts.join(', ')}`,
          fix: 'Use more specific CSS selectors or CSS-in-JS'
        });
      }

      // Test for JavaScript conflicts
      const jsConflicts = this.detectJavaScriptConflicts();
      if (jsConflicts.length > 0) {
        issues.push({
          type: 'functionality',
          severity: 'high',
          description: `JavaScript conflicts detected: ${jsConflicts.join(', ')}`,
          fix: 'Use namespacing or module pattern to avoid conflicts'
        });
      }

    } catch (error) {
      issues.push({
        type: 'functionality',
        severity: 'high',
        description: `Basic functionality test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    return issues;
  }

  /**
   * Run layout tests
   */
  private async runLayoutTests(url: string): Promise<CompatibilityIssue[]> {
    const issues: CompatibilityIssue[] = [];

    try {
      // Test viewport impact
      const viewportImpact = this.testViewportImpact();
      if (viewportImpact > 0.1) { // More than 10% impact
        issues.push({
          type: 'layout',
          severity: 'medium',
          description: `Sidebar significantly impacts page layout (${Math.round(viewportImpact * 100)}%)`,
          fix: 'Adjust sidebar width or use overlay mode'
        });
      }

      // Test responsive design impact
      const responsiveIssues = this.testResponsiveDesign();
      issues.push(...responsiveIssues);

      // Test z-index conflicts
      const zIndexConflicts = this.detectZIndexConflicts();
      if (zIndexConflicts.length > 0) {
        issues.push({
          type: 'layout',
          severity: 'low',
          description: `Z-index conflicts detected with elements: ${zIndexConflicts.join(', ')}`,
          fix: 'Adjust sidebar z-index or use CSS isolation'
        });
      }

    } catch (error) {
      issues.push({
        type: 'layout',
        severity: 'medium',
        description: `Layout test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    return issues;
  }

  /**
   * Run performance tests
   */
  private async runPerformanceTests(url: string): Promise<CompatibilityIssue[]> {
    const issues: CompatibilityIssue[] = [];

    try {
      // Test performance impact
      const performanceImpact = await this.measurePerformanceImpact();
      
      if (performanceImpact.renderTime > 100) {
        issues.push({
          type: 'performance',
          severity: 'medium',
          description: `Sidebar injection increases render time by ${performanceImpact.renderTime}ms`,
          fix: 'Optimize sidebar initialization and reduce DOM complexity'
        });
      }

      if (performanceImpact.memoryUsage > 10 * 1024 * 1024) { // 10MB
        issues.push({
          type: 'performance',
          severity: 'high',
          description: `Sidebar uses significant memory: ${Math.round(performanceImpact.memoryUsage / 1024 / 1024)}MB`,
          fix: 'Optimize memory usage and implement cleanup'
        });
      }

    } catch (error) {
      issues.push({
        type: 'performance',
        severity: 'low',
        description: `Performance test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    return issues;
  }

  /**
   * Test sidebar injection
   */
  private testSidebarInjection(): boolean {
    try {
      // Test if we can create and inject elements
      const testElement = document.createElement('div');
      testElement.style.position = 'fixed';
      testElement.style.top = '0';
      testElement.style.left = '0';
      testElement.style.width = '1px';
      testElement.style.height = '1px';
      testElement.style.zIndex = '999999';
      
      document.body.appendChild(testElement);
      const injected = document.body.contains(testElement);
      
      if (injected) {
        document.body.removeChild(testElement);
      }
      
      return injected;
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect CSS conflicts
   */
  private detectCSSConflicts(): string[] {
    const conflicts: string[] = [];
    
    try {
      // Check for global CSS resets that might affect sidebar
      const computedStyle = window.getComputedStyle(document.body);
      
      if (computedStyle.boxSizing !== 'border-box') {
        conflicts.push('box-sizing');
      }
      
      if (computedStyle.position === 'fixed' || computedStyle.position === 'absolute') {
        conflicts.push('body-positioning');
      }
      
      // Check for CSS frameworks that might conflict
      const stylesheets = Array.from(document.styleSheets);
      stylesheets.forEach(stylesheet => {
        try {
          const href = stylesheet.href;
          if (href) {
            if (href.includes('bootstrap')) conflicts.push('bootstrap');
            if (href.includes('foundation')) conflicts.push('foundation');
            if (href.includes('bulma')) conflicts.push('bulma');
          }
        } catch (error) {
          // Cross-origin stylesheet, ignore
        }
      });
      
    } catch (error) {
      // Ignore errors in CSS conflict detection
    }
    
    return conflicts;
  }

  /**
   * Detect JavaScript conflicts
   */
  private detectJavaScriptConflicts(): string[] {
    const conflicts: string[] = [];
    
    try {
      // Check for global variables that might conflict
      const globalVars = ['$', 'jQuery', '_', 'React', 'Vue', 'Angular'];
      
      globalVars.forEach(varName => {
        if (varName in window) {
          conflicts.push(varName);
        }
      });
      
      // Check for event listener conflicts
      if (document.onclick || document.onkeydown || document.onscroll) {
        conflicts.push('global-event-handlers');
      }
      
    } catch (error) {
      // Ignore errors in JavaScript conflict detection
    }
    
    return conflicts;
  }

  /**
   * Test viewport impact
   */
  private testViewportImpact(): number {
    const originalWidth = window.innerWidth;
    const sidebarWidth = 300; // Assumed sidebar width
    
    return sidebarWidth / originalWidth;
  }

  /**
   * Test responsive design
   */
  private testResponsiveDesign(): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    
    try {
      const viewportWidth = window.innerWidth;
      
      // Check if viewport is too narrow for sidebar
      if (viewportWidth < 768) {
        issues.push({
          type: 'layout',
          severity: 'high',
          description: 'Viewport too narrow for sidebar on mobile devices',
          fix: 'Use overlay mode or hide sidebar on mobile',
          workaround: 'Implement responsive breakpoints'
        });
      }
      
      // Check for media queries that might conflict
      const mediaQueries = this.detectMediaQueries();
      if (mediaQueries.length > 10) {
        issues.push({
          type: 'layout',
          severity: 'low',
          description: 'Many media queries detected, potential responsive conflicts',
          fix: 'Test sidebar at different viewport sizes'
        });
      }
      
    } catch (error) {
      issues.push({
        type: 'layout',
        severity: 'low',
        description: `Responsive design test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    }
    
    return issues;
  }

  /**
   * Detect z-index conflicts
   */
  private detectZIndexConflicts(): string[] {
    const conflicts: string[] = [];
    
    try {
      const elements = document.querySelectorAll('*');
      const highZIndexElements: string[] = [];
      
      elements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        const zIndex = parseInt(computedStyle.zIndex);
        
        if (zIndex > 999999) { // Higher than typical sidebar z-index
          const identifier = element.id || element.className || element.tagName;
          highZIndexElements.push(identifier);
        }
      });
      
      return highZIndexElements.slice(0, 5); // Limit to first 5
      
    } catch (error) {
      return conflicts;
    }
  }

  /**
   * Detect media queries
   */
  private detectMediaQueries(): string[] {
    const mediaQueries: string[] = [];
    
    try {
      const stylesheets = Array.from(document.styleSheets);
      
      stylesheets.forEach(stylesheet => {
        try {
          const rules = Array.from(stylesheet.cssRules || []);
          
          rules.forEach(rule => {
            if (rule instanceof CSSMediaRule) {
              mediaQueries.push(rule.conditionText);
            }
          });
        } catch (error) {
          // Cross-origin stylesheet, ignore
        }
      });
      
    } catch (error) {
      // Ignore errors
    }
    
    return mediaQueries;
  }

  /**
   * Measure performance impact
   */
  private async measurePerformanceImpact(): Promise<{
    renderTime: number;
    memoryUsage: number;
  }> {
    const startTime = performance.now();
    const startMemory = 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0;
    
    // Simulate sidebar injection
    const testSidebar = document.createElement('div');
    testSidebar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 300px;
      height: 100vh;
      background: white;
      z-index: 999999;
      overflow: auto;
    `;
    
    // Add some content to simulate real sidebar
    for (let i = 0; i < 100; i++) {
      const item = document.createElement('div');
      item.textContent = `Test item ${i}`;
      item.style.padding = '10px';
      item.style.borderBottom = '1px solid #eee';
      testSidebar.appendChild(item);
    }
    
    document.body.appendChild(testSidebar);
    
    // Force reflow
    testSidebar.offsetHeight;
    
    const endTime = performance.now();
    const endMemory = 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0;
    
    // Clean up
    document.body.removeChild(testSidebar);
    
    return {
      renderTime: endTime - startTime,
      memoryUsage: endMemory - startMemory
    };
  }

  /**
   * Calculate compatibility score
   */
  private calculateCompatibilityScore(issues: CompatibilityIssue[]): number {
    let score = 100;
    
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical':
          score -= 25;
          break;
        case 'high':
          score -= 15;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });
    
    return Math.max(0, score);
  }
}

// Export convenience functions
export const compatibilityTester = CompatibilityTester.getInstance();

export function runCompatibilityTests(): Promise<CompatibilityReport> {
  return compatibilityTester.runAllTests();
}

export function testWebsiteCompatibility(url: string): Promise<WebsiteCompatibility> {
  return compatibilityTester.testWebsiteCompatibility(url);
}

export function checkFeatureSupport(feature: string): boolean {
  return compatibilityTester.checkFeatureSupport(feature);
}

export function getBrowserInfo(): BrowserInfo {
  return compatibilityTester.getBrowserInfo();
}