/**
 * Continuous Integration - 持续集成测试流程
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';
import { CompatibilityTester, CompatibilityReport } from './CompatibilityTester';
import { ChromeVersionTester } from './ChromeVersionTester';
import { WebsiteAdapter, WebsiteProfile } from './WebsiteAdapter';

export interface CIConfig {
  enabled: boolean;
  schedule: 'daily' | 'weekly' | 'monthly' | 'on-demand';
  testSuites: string[];
  browsers: string[];
  websites: string[];
  notifications: {
    email?: string[];
    webhook?: string;
    slack?: string;
  };
  thresholds: {
    compatibilityScore: number;
    performanceScore: number;
    errorRate: number;
  };
  retryAttempts: number;
  timeout: number;
}

export interface CITestRun {
  id: string;
  timestamp: number;
  duration: number;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  results: {
    compatibility: CompatibilityReport[];
    websites: WebsiteProfile[];
    performance: any[];
    summary: CISummary;
  };
  errors: string[];
  warnings: string[];
}

export interface CISummary {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  compatibilityScore: number;
  performanceScore: number;
  regressions: CIRegression[];
  improvements: CIImprovement[];
}

export interface CIRegression {
  type: 'compatibility' | 'performance' | 'functionality';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  previousValue: number;
  currentValue: number;
  change: number;
  affectedBrowsers?: string[];
  affectedWebsites?: string[];
}

export interface CIImprovement {
  type: 'compatibility' | 'performance' | 'functionality';
  description: string;
  previousValue: number;
  currentValue: number;
  improvement: number;
}

export interface CINotification {
  type: 'success' | 'failure' | 'warning' | 'info';
  title: string;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * Continuous Integration Manager
 */
export class ContinuousIntegration {
  private static instance: ContinuousIntegration;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private compatibilityTester: CompatibilityTester;
  private chromeVersionTester: ChromeVersionTester;
  private websiteAdapter: WebsiteAdapter;
  private config: CIConfig;
  private testRuns: Map<string, CITestRun> = new Map();
  private currentRun?: CITestRun;
  private scheduledTimer?: NodeJS.Timeout;
  private isRunning = false;

  private constructor(config: Partial<CIConfig> = {}) {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.compatibilityTester = CompatibilityTester.getInstance();
    this.chromeVersionTester = ChromeVersionTester.getInstance();
    this.websiteAdapter = WebsiteAdapter.getInstance();

    this.config = {
      enabled: true,
      schedule: 'daily',
      testSuites: ['compatibility', 'performance', 'websites'],
      browsers: ['chrome'],
      websites: [],
      notifications: {},
      thresholds: {
        compatibilityScore: 80,
        performanceScore: 70,
        errorRate: 5
      },
      retryAttempts: 3,
      timeout: 300000, // 5 minutes
      ...config
    };

    if (this.config.enabled) {
      this.scheduleTests();
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<CIConfig>): ContinuousIntegration {
    if (!ContinuousIntegration.instance) {
      ContinuousIntegration.instance = new ContinuousIntegration(config);
    }
    return ContinuousIntegration.instance;
  }

  /**
   * Start CI test run
   */
  async startTestRun(): Promise<string> {
    if (this.isRunning) {
      throw new Error('CI test run is already in progress');
    }

    const runId = `ci_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentRun = {
      id: runId,
      timestamp: Date.now(),
      duration: 0,
      status: 'running',
      results: {
        compatibility: [],
        websites: [],
        performance: [],
        summary: {
          totalTests: 0,
          passedTests: 0,
          failedTests: 0,
          skippedTests: 0,
          compatibilityScore: 0,
          performanceScore: 0,
          regressions: [],
          improvements: []
        }
      },
      errors: [],
      warnings: []
    };

    this.testRuns.set(runId, this.currentRun);
    this.isRunning = true;

    this.logger.info('ci', `Starting CI test run: ${runId}`);

    try {
      await this.executeTestRun(this.currentRun);
      
      this.currentRun.status = 'completed';
      this.currentRun.duration = Date.now() - this.currentRun.timestamp;

      await this.analyzeResults(this.currentRun);
      await this.sendNotifications(this.currentRun);

      this.logger.info('ci', `CI test run completed: ${runId}`, {
        duration: this.currentRun.duration,
        status: this.currentRun.status,
        totalTests: this.currentRun.results.summary.totalTests,
        passedTests: this.currentRun.results.summary.passedTests
      });

    } catch (error) {
      this.currentRun.status = 'failed';
      this.currentRun.duration = Date.now() - this.currentRun.timestamp;
      this.currentRun.errors.push(error instanceof Error ? error.message : String(error));

      this.logger.error('ci', `CI test run failed: ${runId}`, error);
      
      await this.sendNotifications(this.currentRun);
    } finally {
      this.isRunning = false;
      this.currentRun = undefined;
    }

    return runId;
  }

  /**
   * Cancel current test run
   */
  cancelTestRun(): boolean {
    if (!this.currentRun || !this.isRunning) {
      return false;
    }

    this.currentRun.status = 'cancelled';
    this.currentRun.duration = Date.now() - this.currentRun.timestamp;
    this.isRunning = false;

    this.logger.info('ci', `CI test run cancelled: ${this.currentRun.id}`);
    return true;
  }

  /**
   * Get test run results
   */
  getTestRun(runId: string): CITestRun | null {
    return this.testRuns.get(runId) || null;
  }

  /**
   * Get all test runs
   */
  getAllTestRuns(): CITestRun[] {
    return Array.from(this.testRuns.values()).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get current test run
   */
  getCurrentTestRun(): CITestRun | null {
    return this.currentRun || null;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<CIConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (this.config.enabled) {
      this.scheduleTests();
    } else {
      this.unscheduleTests();
    }

    this.logger.info('ci', 'CI configuration updated', this.config);
  }

  /**
   * Get configuration
   */
  getConfig(): CIConfig {
    return { ...this.config };
  }

  /**
   * Schedule tests
   */
  private scheduleTests(): void {
    this.unscheduleTests();

    if (!this.config.enabled || this.config.schedule === 'on-demand') {
      return;
    }

    let interval: number;
    
    switch (this.config.schedule) {
      case 'daily':
        interval = 24 * 60 * 60 * 1000; // 24 hours
        break;
      case 'weekly':
        interval = 7 * 24 * 60 * 60 * 1000; // 7 days
        break;
      case 'monthly':
        interval = 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      default:
        return;
    }

    this.scheduledTimer = setInterval(() => {
      if (!this.isRunning) {
        this.startTestRun().catch(error => {
          this.logger.error('ci', 'Scheduled test run failed', error);
        });
      }
    }, interval);

    this.logger.info('ci', `CI tests scheduled: ${this.config.schedule}`);
  }

  /**
   * Unschedule tests
   */
  private unscheduleTests(): void {
    if (this.scheduledTimer) {
      clearInterval(this.scheduledTimer);
      this.scheduledTimer = undefined;
    }
  }

  /**
   * Execute test run
   */
  private async executeTestRun(testRun: CITestRun): Promise<void> {
    const startTime = Date.now();

    // Run compatibility tests
    if (this.config.testSuites.includes('compatibility')) {
      try {
        this.logger.info('ci', 'Running compatibility tests');
        const compatibilityReport = await this.compatibilityTester.runAllTests();
        testRun.results.compatibility.push(compatibilityReport);
        
        testRun.results.summary.totalTests += compatibilityReport.summary.total;
        testRun.results.summary.passedTests += compatibilityReport.summary.passed;
        testRun.results.summary.failedTests += compatibilityReport.summary.failed;
        
      } catch (error) {
        testRun.errors.push(`Compatibility tests failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Run website tests
    if (this.config.testSuites.includes('websites') && this.config.websites.length > 0) {
      try {
        this.logger.info('ci', 'Running website tests');
        
        for (const website of this.config.websites) {
          try {
            const profile = await this.websiteAdapter.analyzeWebsite(website);
            testRun.results.websites.push(profile);
            
            testRun.results.summary.totalTests++;
            if (profile.score >= this.config.thresholds.compatibilityScore) {
              testRun.results.summary.passedTests++;
            } else {
              testRun.results.summary.failedTests++;
            }
            
          } catch (error) {
            testRun.errors.push(`Website test failed for ${website}: ${error instanceof Error ? error.message : String(error)}`);
            testRun.results.summary.totalTests++;
            testRun.results.summary.failedTests++;
          }
        }
        
      } catch (error) {
        testRun.errors.push(`Website tests failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Run performance tests
    if (this.config.testSuites.includes('performance')) {
      try {
        this.logger.info('ci', 'Running performance tests');
        
        const performanceData = this.performanceMonitor.getPerformanceSummary();
        testRun.results.performance.push(performanceData);
        
        testRun.results.summary.totalTests++;
        if (performanceData.totalMetrics > 0) {
          testRun.results.summary.passedTests++;
        } else {
          testRun.results.summary.failedTests++;
        }
        
      } catch (error) {
        testRun.errors.push(`Performance tests failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Calculate scores
    testRun.results.summary.compatibilityScore = this.calculateCompatibilityScore(testRun);
    testRun.results.summary.performanceScore = this.calculatePerformanceScore(testRun);

    this.logger.info('ci', 'Test run execution completed', {
      duration: Date.now() - startTime,
      totalTests: testRun.results.summary.totalTests,
      passedTests: testRun.results.summary.passedTests,
      failedTests: testRun.results.summary.failedTests
    });
  }

  /**
   * Analyze results
   */
  private async analyzeResults(testRun: CITestRun): Promise<void> {
    try {
      // Compare with previous runs
      const previousRuns = this.getAllTestRuns().slice(1, 4); // Get last 3 runs
      
      if (previousRuns.length > 0) {
        const regressions = this.detectRegressions(testRun, previousRuns);
        const improvements = this.detectImprovements(testRun, previousRuns);
        
        testRun.results.summary.regressions = regressions;
        testRun.results.summary.improvements = improvements;
      }

      // Check thresholds
      if (testRun.results.summary.compatibilityScore < this.config.thresholds.compatibilityScore) {
        testRun.warnings.push(`Compatibility score ${testRun.results.summary.compatibilityScore} is below threshold ${this.config.thresholds.compatibilityScore}`);
      }

      if (testRun.results.summary.performanceScore < this.config.thresholds.performanceScore) {
        testRun.warnings.push(`Performance score ${testRun.results.summary.performanceScore} is below threshold ${this.config.thresholds.performanceScore}`);
      }

      const errorRate = (testRun.results.summary.failedTests / testRun.results.summary.totalTests) * 100;
      if (errorRate > this.config.thresholds.errorRate) {
        testRun.warnings.push(`Error rate ${errorRate.toFixed(1)}% is above threshold ${this.config.thresholds.errorRate}%`);
      }

    } catch (error) {
      this.logger.error('ci', 'Failed to analyze results', error);
    }
  }

  /**
   * Detect regressions
   */
  private detectRegressions(currentRun: CITestRun, previousRuns: CITestRun[]): CIRegression[] {
    const regressions: CIRegression[] = [];
    
    if (previousRuns.length === 0) return regressions;
    
    const previousRun = previousRuns[0];
    
    // Check compatibility score regression
    if (currentRun.results.summary.compatibilityScore < previousRun.results.summary.compatibilityScore - 5) {
      regressions.push({
        type: 'compatibility',
        description: 'Compatibility score decreased',
        severity: 'medium',
        previousValue: previousRun.results.summary.compatibilityScore,
        currentValue: currentRun.results.summary.compatibilityScore,
        change: currentRun.results.summary.compatibilityScore - previousRun.results.summary.compatibilityScore
      });
    }

    // Check performance score regression
    if (currentRun.results.summary.performanceScore < previousRun.results.summary.performanceScore - 5) {
      regressions.push({
        type: 'performance',
        description: 'Performance score decreased',
        severity: 'medium',
        previousValue: previousRun.results.summary.performanceScore,
        currentValue: currentRun.results.summary.performanceScore,
        change: currentRun.results.summary.performanceScore - previousRun.results.summary.performanceScore
      });
    }

    // Check test failure rate regression
    const currentFailureRate = (currentRun.results.summary.failedTests / currentRun.results.summary.totalTests) * 100;
    const previousFailureRate = (previousRun.results.summary.failedTests / previousRun.results.summary.totalTests) * 100;
    
    if (currentFailureRate > previousFailureRate + 2) {
      regressions.push({
        type: 'functionality',
        description: 'Test failure rate increased',
        severity: 'high',
        previousValue: previousFailureRate,
        currentValue: currentFailureRate,
        change: currentFailureRate - previousFailureRate
      });
    }

    return regressions;
  }

  /**
   * Detect improvements
   */
  private detectImprovements(currentRun: CITestRun, previousRuns: CITestRun[]): CIImprovement[] {
    const improvements: CIImprovement[] = [];
    
    if (previousRuns.length === 0) return improvements;
    
    const previousRun = previousRuns[0];
    
    // Check compatibility score improvement
    if (currentRun.results.summary.compatibilityScore > previousRun.results.summary.compatibilityScore + 5) {
      improvements.push({
        type: 'compatibility',
        description: 'Compatibility score improved',
        previousValue: previousRun.results.summary.compatibilityScore,
        currentValue: currentRun.results.summary.compatibilityScore,
        improvement: currentRun.results.summary.compatibilityScore - previousRun.results.summary.compatibilityScore
      });
    }

    // Check performance score improvement
    if (currentRun.results.summary.performanceScore > previousRun.results.summary.performanceScore + 5) {
      improvements.push({
        type: 'performance',
        description: 'Performance score improved',
        previousValue: previousRun.results.summary.performanceScore,
        currentValue: currentRun.results.summary.performanceScore,
        improvement: currentRun.results.summary.performanceScore - previousRun.results.summary.performanceScore
      });
    }

    return improvements;
  }

  /**
   * Calculate compatibility score
   */
  private calculateCompatibilityScore(testRun: CITestRun): number {
    if (testRun.results.compatibility.length === 0) return 0;
    
    const totalTests = testRun.results.compatibility.reduce((sum, report) => sum + report.summary.total, 0);
    const passedTests = testRun.results.compatibility.reduce((sum, report) => sum + report.summary.passed, 0);
    
    return totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(testRun: CITestRun): number {
    if (testRun.results.performance.length === 0) return 0;
    
    // Simple performance score based on metrics availability
    const performanceData = testRun.results.performance[0];
    if (!performanceData) return 0;
    
    let score = 100;
    
    // Deduct points for active alerts
    if (performanceData.activeAlerts > 0) {
      score -= performanceData.activeAlerts * 10;
    }
    
    // Deduct points for high memory usage
    if (performanceData.memoryUsage && performanceData.memoryUsage.percentage > 80) {
      score -= 20;
    }
    
    return Math.max(0, score);
  }

  /**
   * Send notifications
   */
  private async sendNotifications(testRun: CITestRun): Promise<void> {
    try {
      const notification: CINotification = {
        type: testRun.status === 'completed' ? 
          (testRun.results.summary.regressions.length > 0 ? 'warning' : 'success') : 'failure',
        title: `CI Test Run ${testRun.status}`,
        message: this.generateNotificationMessage(testRun),
        details: {
          runId: testRun.id,
          duration: testRun.duration,
          summary: testRun.results.summary,
          errors: testRun.errors,
          warnings: testRun.warnings
        },
        timestamp: Date.now()
      };

      // Send email notifications
      if (this.config.notifications.email && this.config.notifications.email.length > 0) {
        await this.sendEmailNotification(notification);
      }

      // Send webhook notifications
      if (this.config.notifications.webhook) {
        await this.sendWebhookNotification(notification);
      }

      // Send Slack notifications
      if (this.config.notifications.slack) {
        await this.sendSlackNotification(notification);
      }

    } catch (error) {
      this.logger.error('ci', 'Failed to send notifications', error);
    }
  }

  /**
   * Generate notification message
   */
  private generateNotificationMessage(testRun: CITestRun): string {
    const { summary } = testRun.results;
    
    let message = `Test Run ${testRun.id} ${testRun.status}\n\n`;
    message += `📊 Summary:\n`;
    message += `• Total Tests: ${summary.totalTests}\n`;
    message += `• Passed: ${summary.passedTests}\n`;
    message += `• Failed: ${summary.failedTests}\n`;
    message += `• Compatibility Score: ${summary.compatibilityScore}%\n`;
    message += `• Performance Score: ${summary.performanceScore}%\n`;
    
    if (summary.regressions.length > 0) {
      message += `\n⚠️ Regressions:\n`;
      summary.regressions.forEach(regression => {
        message += `• ${regression.description} (${regression.change > 0 ? '+' : ''}${regression.change})\n`;
      });
    }
    
    if (summary.improvements.length > 0) {
      message += `\n✅ Improvements:\n`;
      summary.improvements.forEach(improvement => {
        message += `• ${improvement.description} (+${improvement.improvement})\n`;
      });
    }
    
    if (testRun.errors.length > 0) {
      message += `\n❌ Errors:\n`;
      testRun.errors.slice(0, 3).forEach(error => {
        message += `• ${error}\n`;
      });
      if (testRun.errors.length > 3) {
        message += `• ... and ${testRun.errors.length - 3} more\n`;
      }
    }
    
    return message;
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: CINotification): Promise<void> {
    // Email notification implementation would go here
    // This is a placeholder for actual email service integration
    this.logger.info('ci', 'Email notification sent', { 
      type: notification.type, 
      title: notification.title 
    });
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(notification: CINotification): Promise<void> {
    try {
      if (!this.config.notifications.webhook) return;
      
      const response = await fetch(this.config.notifications.webhook, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notification)
      });

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.status}`);
      }

      this.logger.info('ci', 'Webhook notification sent', { 
        type: notification.type, 
        title: notification.title 
      });

    } catch (error) {
      this.logger.error('ci', 'Failed to send webhook notification', error);
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(notification: CINotification): Promise<void> {
    try {
      if (!this.config.notifications.slack) return;
      
      const slackPayload = {
        text: notification.title,
        attachments: [{
          color: notification.type === 'success' ? 'good' : 
                notification.type === 'warning' ? 'warning' : 'danger',
          text: notification.message,
          ts: Math.floor(notification.timestamp / 1000)
        }]
      };

      const response = await fetch(this.config.notifications.slack, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(slackPayload)
      });

      if (!response.ok) {
        throw new Error(`Slack request failed: ${response.status}`);
      }

      this.logger.info('ci', 'Slack notification sent', { 
        type: notification.type, 
        title: notification.title 
      });

    } catch (error) {
      this.logger.error('ci', 'Failed to send Slack notification', error);
    }
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.unscheduleTests();
    
    if (this.isRunning) {
      this.cancelTestRun();
    }
  }
}

// Export convenience functions
export const continuousIntegration = ContinuousIntegration.getInstance();

export function startCITestRun(): Promise<string> {
  return continuousIntegration.startTestRun();
}

export function getCITestRun(runId: string): CITestRun | null {
  return continuousIntegration.getTestRun(runId);
}

export function getAllCITestRuns(): CITestRun[] {
  return continuousIntegration.getAllTestRuns();
}

export function updateCIConfig(config: Partial<CIConfig>): void {
  continuousIntegration.updateConfig(config);
}