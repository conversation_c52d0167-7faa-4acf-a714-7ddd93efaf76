/**
 * Website Adapter - 网站适配效果验证
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';

export interface WebsiteProfile {
  domain: string;
  url: string;
  title: string;
  framework: string[];
  cssFrameworks: string[];
  jsLibraries: string[];
  contentType: 'static' | 'spa' | 'dynamic' | 'hybrid';
  layout: 'fixed' | 'fluid' | 'responsive' | 'adaptive';
  complexity: 'low' | 'medium' | 'high' | 'very-high';
  issues: AdaptationIssue[];
  score: number;
  lastTested: number;
}

export interface AdaptationIssue {
  type: 'layout' | 'functionality' | 'performance' | 'visual' | 'interaction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  element?: string;
  selector?: string;
  screenshot?: string;
  fix?: AdaptationFix;
  workaround?: string;
  impact: string;
}

export interface AdaptationFix {
  type: 'css' | 'javascript' | 'config' | 'manual';
  code?: string;
  description: string;
  tested: boolean;
  effectiveness: number; // 0-100
}

export interface AdaptationRule {
  id: string;
  name: string;
  description: string;
  selector: string;
  condition?: (element: Element) => boolean;
  fix: AdaptationFix;
  priority: number;
  category: 'layout' | 'styling' | 'interaction' | 'performance';
}

export interface TestScenario {
  name: string;
  description: string;
  steps: TestStep[];
  expectedResult: string;
  timeout: number;
}

export interface TestStep {
  action: 'navigate' | 'click' | 'scroll' | 'wait' | 'check' | 'measure';
  target?: string;
  value?: any;
  timeout?: number;
}

export interface TestResult {
  scenario: string;
  success: boolean;
  duration: number;
  issues: AdaptationIssue[];
  screenshots: string[];
  metrics: Record<string, number>;
  timestamp: number;
}

/**
 * Website Adapter
 */
export class WebsiteAdapter {
  private static instance: WebsiteAdapter;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private profiles: Map<string, WebsiteProfile> = new Map();
  private adaptationRules: Map<string, AdaptationRule> = new Map();
  private testScenarios: Map<string, TestScenario> = new Map();
  private appliedFixes: Map<string, AdaptationFix[]> = new Map();

  private constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.initializeDefaultRules();
    this.initializeTestScenarios();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): WebsiteAdapter {
    if (!WebsiteAdapter.instance) {
      WebsiteAdapter.instance = new WebsiteAdapter();
    }
    return WebsiteAdapter.instance;
  }
}  /**

   * Analyze website for adaptation
   */
  async analyzeWebsite(url: string): Promise<WebsiteProfile> {
    this.logger.info('websiteAdapter', `Analyzing website: ${url}`);

    const domain = new URL(url).hostname;
    const startTime = Date.now();

    try {
      // Detect website characteristics
      const framework = this.detectFramework();
      const cssFrameworks = this.detectCSSFrameworks();
      const jsLibraries = this.detectJSLibraries();
      const contentType = this.detectContentType();
      const layout = this.detectLayoutType();
      const complexity = this.calculateComplexity();

      // Run adaptation tests
      const issues = await this.runAdaptationTests();
      const score = this.calculateAdaptationScore(issues);

      const profile: WebsiteProfile = {
        domain,
        url,
        title: document.title || 'Unknown',
        framework,
        cssFrameworks,
        jsLibraries,
        contentType,
        layout,
        complexity,
        issues,
        score,
        lastTested: Date.now()
      };

      this.profiles.set(domain, profile);

      this.logger.info('websiteAdapter', `Website analysis completed: ${url}`, {
        score,
        issues: issues.length,
        duration: Date.now() - startTime
      });

      return profile;

    } catch (error) {
      this.logger.error('websiteAdapter', `Website analysis failed: ${url}`, error);
      
      const errorProfile: WebsiteProfile = {
        domain,
        url,
        title: 'Error',
        framework: [],
        cssFrameworks: [],
        jsLibraries: [],
        contentType: 'static',
        layout: 'fixed',
        complexity: 'high',
        issues: [{
          type: 'functionality',
          severity: 'critical',
          description: `Analysis failed: ${error instanceof Error ? error.message : String(error)}`,
          impact: 'Cannot determine website compatibility'
        }],
        score: 0,
        lastTested: Date.now()
      };

      this.profiles.set(domain, errorProfile);
      return errorProfile;
    }
  }

  /**
   * Apply adaptation fixes
   */
  async applyAdaptationFixes(domain: string): Promise<boolean> {
    const profile = this.profiles.get(domain);
    if (!profile) {
      this.logger.warn('websiteAdapter', `No profile found for domain: ${domain}`);
      return false;
    }

    this.logger.info('websiteAdapter', `Applying adaptation fixes for: ${domain}`);

    const appliedFixes: AdaptationFix[] = [];
    let successCount = 0;

    for (const issue of profile.issues) {
      if (issue.fix) {
        try {
          const success = await this.applyFix(issue.fix);
          if (success) {
            appliedFixes.push(issue.fix);
            successCount++;
          }
        } catch (error) {
          this.logger.error('websiteAdapter', `Failed to apply fix for issue: ${issue.description}`, error);
        }
      }
    }

    this.appliedFixes.set(domain, appliedFixes);

    this.logger.info('websiteAdapter', `Applied ${successCount} fixes for: ${domain}`);
    return successCount > 0;
  }

  /**
   * Run test scenarios
   */
  async runTestScenarios(domain: string): Promise<TestResult[]> {
    this.logger.info('websiteAdapter', `Running test scenarios for: ${domain}`);

    const results: TestResult[] = [];

    for (const [name, scenario] of this.testScenarios) {
      try {
        const result = await this.runTestScenario(scenario);
        results.push(result);
      } catch (error) {
        this.logger.error('websiteAdapter', `Test scenario failed: ${name}`, error);
        
        results.push({
          scenario: name,
          success: false,
          duration: 0,
          issues: [{
            type: 'functionality',
            severity: 'high',
            description: `Test scenario failed: ${error instanceof Error ? error.message : String(error)}`,
            impact: 'Test could not be completed'
          }],
          screenshots: [],
          metrics: {},
          timestamp: Date.now()
        });
      }
    }

    this.logger.info('websiteAdapter', `Completed ${results.length} test scenarios for: ${domain}`);
    return results;
  }

  /**
   * Get website profile
   */
  getWebsiteProfile(domain: string): WebsiteProfile | null {
    return this.profiles.get(domain) || null;
  }

  /**
   * Get all profiles
   */
  getAllProfiles(): WebsiteProfile[] {
    return Array.from(this.profiles.values());
  }

  /**
   * Get adaptation rules
   */
  getAdaptationRules(): AdaptationRule[] {
    return Array.from(this.adaptationRules.values());
  }

  /**
   * Add custom adaptation rule
   */
  addAdaptationRule(rule: AdaptationRule): void {
    this.adaptationRules.set(rule.id, rule);
    this.logger.debug('websiteAdapter', `Added adaptation rule: ${rule.name}`);
  }

  /**
   * Remove adaptation rule
   */
  removeAdaptationRule(ruleId: string): void {
    this.adaptationRules.delete(ruleId);
    this.logger.debug('websiteAdapter', `Removed adaptation rule: ${ruleId}`);
  }

  /**
   * Detect framework
   */
  private detectFramework(): string[] {
    const frameworks: string[] = [];

    // React
    if (window.React || document.querySelector('[data-reactroot]') || 
        document.querySelector('script[src*="react"]')) {
      frameworks.push('React');
    }

    // Vue
    if (window.Vue || document.querySelector('[data-v-]') || 
        document.querySelector('script[src*="vue"]')) {
      frameworks.push('Vue');
    }

    // Angular
    if (window.angular || document.querySelector('[ng-app]') || 
        document.querySelector('script[src*="angular"]')) {
      frameworks.push('Angular');
    }

    // jQuery
    if (window.$ || window.jQuery || 
        document.querySelector('script[src*="jquery"]')) {
      frameworks.push('jQuery');
    }

    // Svelte
    if (document.querySelector('script[src*="svelte"]')) {
      frameworks.push('Svelte');
    }

    return frameworks;
  }

  /**
   * Detect CSS frameworks
   */
  private detectCSSFrameworks(): string[] {
    const frameworks: string[] = [];

    // Bootstrap
    if (document.querySelector('link[href*="bootstrap"]') || 
        document.querySelector('.container, .row, .col-')) {
      frameworks.push('Bootstrap');
    }

    // Tailwind CSS
    if (document.querySelector('link[href*="tailwind"]') || 
        document.querySelector('[class*="bg-"], [class*="text-"], [class*="p-"]')) {
      frameworks.push('Tailwind CSS');
    }

    // Foundation
    if (document.querySelector('link[href*="foundation"]') || 
        document.querySelector('.foundation-')) {
      frameworks.push('Foundation');
    }

    // Bulma
    if (document.querySelector('link[href*="bulma"]') || 
        document.querySelector('.bulma, .is-')) {
      frameworks.push('Bulma');
    }

    // Material UI
    if (document.querySelector('[class*="MuiButton"], [class*="MuiTextField"]')) {
      frameworks.push('Material-UI');
    }

    return frameworks;
  }

  /**
   * Detect JavaScript libraries
   */
  private detectJSLibraries(): string[] {
    const libraries: string[] = [];

    // Lodash
    if (window._ && window._.VERSION) {
      libraries.push('Lodash');
    }

    // Moment.js
    if (window.moment) {
      libraries.push('Moment.js');
    }

    // D3.js
    if (window.d3) {
      libraries.push('D3.js');
    }

    // Chart.js
    if (window.Chart) {
      libraries.push('Chart.js');
    }

    // Three.js
    if (window.THREE) {
      libraries.push('Three.js');
    }

    return libraries;
  }

  /**
   * Detect content type
   */
  private detectContentType(): WebsiteProfile['contentType'] {
    // Check for SPA indicators
    if (document.querySelector('[data-reactroot]') || 
        document.querySelector('[ng-app]') || 
        window.history.pushState) {
      return 'spa';
    }

    // Check for dynamic content
    if (document.querySelector('script[src*="ajax"]') || 
        document.querySelector('[data-ajax]')) {
      return 'dynamic';
    }

    // Check for hybrid
    if (document.querySelector('iframe') && 
        document.querySelector('script[src*="react"]')) {
      return 'hybrid';
    }

    return 'static';
  }

  /**
   * Detect layout type
   */
  private detectLayoutType(): WebsiteProfile['layout'] {
    const body = document.body;
    const computedStyle = window.getComputedStyle(body);

    // Check for responsive design
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    const hasMediaQueries = this.hasMediaQueries();
    
    if (viewportMeta && hasMediaQueries) {
      return 'responsive';
    }

    // Check for fluid layout
    if (computedStyle.width.includes('%') || 
        computedStyle.maxWidth === 'none') {
      return 'fluid';
    }

    // Check for adaptive layout
    if (document.querySelector('[class*="mobile"], [class*="tablet"], [class*="desktop"]')) {
      return 'adaptive';
    }

    return 'fixed';
  }

  /**
   * Calculate complexity
   */
  private calculateComplexity(): WebsiteProfile['complexity'] {
    let score = 0;

    // DOM complexity
    const elementCount = document.querySelectorAll('*').length;
    if (elementCount > 1000) score += 2;
    else if (elementCount > 500) score += 1;

    // Script complexity
    const scriptCount = document.querySelectorAll('script').length;
    if (scriptCount > 20) score += 2;
    else if (scriptCount > 10) score += 1;

    // CSS complexity
    const styleSheetCount = document.styleSheets.length;
    if (styleSheetCount > 10) score += 2;
    else if (styleSheetCount > 5) score += 1;

    // Framework complexity
    const frameworks = this.detectFramework();
    if (frameworks.length > 2) score += 2;
    else if (frameworks.length > 0) score += 1;

    if (score >= 6) return 'very-high';
    if (score >= 4) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  /**
   * Check for media queries
   */
  private hasMediaQueries(): boolean {
    try {
      for (let i = 0; i < document.styleSheets.length; i++) {
        const styleSheet = document.styleSheets[i];
        try {
          const rules = styleSheet.cssRules || styleSheet.rules;
          for (let j = 0; j < rules.length; j++) {
            if (rules[j] instanceof CSSMediaRule) {
              return true;
            }
          }
        } catch (error) {
          // Cross-origin stylesheet, ignore
        }
      }
    } catch (error) {
      // Ignore errors
    }
    return false;
  }

  /**
   * Run adaptation tests
   */
  private async runAdaptationTests(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    // Test sidebar injection
    const injectionIssues = await this.testSidebarInjection();
    issues.push(...injectionIssues);

    // Test layout impact
    const layoutIssues = await this.testLayoutImpact();
    issues.push(...layoutIssues);

    // Test visual conflicts
    const visualIssues = await this.testVisualConflicts();
    issues.push(...visualIssues);

    // Test interaction conflicts
    const interactionIssues = await this.testInteractionConflicts();
    issues.push(...interactionIssues);

    // Test performance impact
    const performanceIssues = await this.testPerformanceImpact();
    issues.push(...performanceIssues);

    return issues;
  }

  /**
   * Test sidebar injection
   */
  private async testSidebarInjection(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    try {
      // Test if sidebar can be created
      const sidebar = document.createElement('div');
      sidebar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 300px;
        height: 100vh;
        background: white;
        z-index: 999999;
        pointer-events: auto;
      `;

      document.body.appendChild(sidebar);

      // Check if sidebar is visible
      const rect = sidebar.getBoundingClientRect();
      if (rect.width === 0 || rect.height === 0) {
        issues.push({
          type: 'layout',
          severity: 'critical',
          description: 'Sidebar cannot be displayed (zero dimensions)',
          element: 'sidebar',
          impact: 'Sidebar will not be visible to users',
          fix: {
            type: 'css',
            code: 'position: fixed !important; display: block !important;',
            description: 'Force sidebar visibility with important declarations',
            tested: false,
            effectiveness: 80
          }
        });
      }

      // Check z-index conflicts
      const elementsAbove = this.findElementsAboveZIndex(999999);
      if (elementsAbove.length > 0) {
        issues.push({
          type: 'visual',
          severity: 'high',
          description: `Elements with higher z-index found: ${elementsAbove.length}`,
          impact: 'Sidebar may be hidden behind other elements',
          fix: {
            type: 'css',
            code: 'z-index: 2147483647;',
            description: 'Use maximum z-index value',
            tested: false,
            effectiveness: 90
          }
        });
      }

      document.body.removeChild(sidebar);

    } catch (error) {
      issues.push({
        type: 'functionality',
        severity: 'critical',
        description: `Sidebar injection failed: ${error instanceof Error ? error.message : String(error)}`,
        impact: 'Sidebar cannot be injected into this website'
      });
    }

    return issues;
  }

  /**
   * Test layout impact
   */
  private async testLayoutImpact(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    try {
      const originalWidth = document.body.scrollWidth;
      
      // Simulate sidebar injection
      const sidebar = document.createElement('div');
      sidebar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 300px;
        height: 100vh;
        background: white;
        z-index: 999999;
      `;

      document.body.appendChild(sidebar);

      // Check if page layout is affected
      const newWidth = document.body.scrollWidth;
      const widthChange = Math.abs(newWidth - originalWidth);

      if (widthChange > 50) {
        issues.push({
          type: 'layout',
          severity: 'medium',
          description: `Page width changed by ${widthChange}px when sidebar is injected`,
          impact: 'Page layout may be disrupted',
          fix: {
            type: 'css',
            code: 'body { margin-left: 300px; transition: margin-left 0.3s ease; }',
            description: 'Adjust body margin to accommodate sidebar',
            tested: false,
            effectiveness: 85
          }
        });
      }

      // Check for horizontal scrollbar
      if (document.body.scrollWidth > window.innerWidth) {
        issues.push({
          type: 'layout',
          severity: 'medium',
          description: 'Horizontal scrollbar appears when sidebar is injected',
          impact: 'Users may need to scroll horizontally',
          fix: {
            type: 'css',
            code: 'body { overflow-x: hidden; }',
            description: 'Hide horizontal scrollbar',
            tested: false,
            effectiveness: 70
          }
        });
      }

      document.body.removeChild(sidebar);

    } catch (error) {
      issues.push({
        type: 'layout',
        severity: 'medium',
        description: `Layout impact test failed: ${error instanceof Error ? error.message : String(error)}`,
        impact: 'Cannot determine layout impact'
      });
    }

    return issues;
  }

  /**
   * Test visual conflicts
   */
  private async testVisualConflicts(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    // Test for fixed positioned elements that might conflict
    const fixedElements = document.querySelectorAll('*');
    let conflictCount = 0;

    fixedElements.forEach(element => {
      const style = window.getComputedStyle(element);
      if (style.position === 'fixed') {
        const rect = element.getBoundingClientRect();
        if (rect.left < 300 && rect.top < window.innerHeight) {
          conflictCount++;
        }
      }
    });

    if (conflictCount > 0) {
      issues.push({
        type: 'visual',
        severity: 'medium',
        description: `${conflictCount} fixed positioned elements may conflict with sidebar`,
        impact: 'Visual elements may overlap with sidebar',
        fix: {
          type: 'css',
          code: '[style*="position: fixed"] { left: calc(var(--original-left, 0px) + 300px) !important; }',
          description: 'Adjust fixed elements to avoid sidebar area',
          tested: false,
          effectiveness: 75
        }
      });
    }

    return issues;
  }

  /**
   * Test interaction conflicts
   */
  private async testInteractionConflicts(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    // Test for click handlers in sidebar area
    const clickableElements = document.querySelectorAll('a, button, [onclick], [role="button"]');
    let conflictCount = 0;

    clickableElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.left < 300 && rect.right > 0) {
        conflictCount++;
      }
    });

    if (conflictCount > 0) {
      issues.push({
        type: 'interaction',
        severity: 'high',
        description: `${conflictCount} interactive elements in sidebar area`,
        impact: 'User interactions may be blocked by sidebar',
        fix: {
          type: 'javascript',
          code: `
            document.addEventListener('click', function(e) {
              if (e.target.closest('.sidebar-area')) {
                e.stopPropagation();
              }
            }, true);
          `,
          description: 'Prevent click events from reaching elements behind sidebar',
          tested: false,
          effectiveness: 80
        }
      });
    }

    return issues;
  }

  /**
   * Test performance impact
   */
  private async testPerformanceImpact(): Promise<AdaptationIssue[]> {
    const issues: AdaptationIssue[] = [];

    try {
      const startTime = performance.now();
      const startMemory = 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0;

      // Simulate sidebar creation and rendering
      const sidebar = document.createElement('div');
      sidebar.innerHTML = '<div>'.repeat(100) + '</div>'.repeat(100);
      document.body.appendChild(sidebar);
      
      // Force reflow
      sidebar.offsetHeight;
      
      const endTime = performance.now();
      const endMemory = 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0;

      const renderTime = endTime - startTime;
      const memoryIncrease = endMemory - startMemory;

      if (renderTime > 100) {
        issues.push({
          type: 'performance',
          severity: 'medium',
          description: `Sidebar rendering takes ${renderTime.toFixed(2)}ms`,
          impact: 'May cause noticeable delay when showing sidebar',
          fix: {
            type: 'javascript',
            code: 'requestAnimationFrame(() => { /* render sidebar */ });',
            description: 'Use requestAnimationFrame for smoother rendering',
            tested: false,
            effectiveness: 70
          }
        });
      }

      if (memoryIncrease > 1024 * 1024) { // 1MB
        issues.push({
          type: 'performance',
          severity: 'low',
          description: `Sidebar uses ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB memory`,
          impact: 'May increase memory usage',
          fix: {
            type: 'javascript',
            code: '/* Implement virtual scrolling and lazy loading */',
            description: 'Optimize memory usage with virtual scrolling',
            tested: false,
            effectiveness: 60
          }
        });
      }

      document.body.removeChild(sidebar);

    } catch (error) {
      issues.push({
        type: 'performance',
        severity: 'low',
        description: `Performance test failed: ${error instanceof Error ? error.message : String(error)}`,
        impact: 'Cannot determine performance impact'
      });
    }

    return issues;
  }

  /**
   * Find elements above z-index
   */
  private findElementsAboveZIndex(zIndex: number): Element[] {
    const elements: Element[] = [];
    const allElements = document.querySelectorAll('*');

    allElements.forEach(element => {
      const style = window.getComputedStyle(element);
      const elementZIndex = parseInt(style.zIndex);
      
      if (!isNaN(elementZIndex) && elementZIndex > zIndex) {
        elements.push(element);
      }
    });

    return elements;
  }

  /**
   * Apply fix
   */
  private async applyFix(fix: AdaptationFix): Promise<boolean> {
    try {
      switch (fix.type) {
        case 'css':
          return this.applyCSSFix(fix);
        case 'javascript':
          return this.applyJavaScriptFix(fix);
        case 'config':
          return this.applyConfigFix(fix);
        default:
          return false;
      }
    } catch (error) {
      this.logger.error('websiteAdapter', 'Failed to apply fix', error);
      return false;
    }
  }

  /**
   * Apply CSS fix
   */
  private applyCSSFix(fix: AdaptationFix): boolean {
    if (!fix.code) return false;

    try {
      const style = document.createElement('style');
      style.textContent = fix.code;
      style.setAttribute('data-sidebar-fix', 'true');
      document.head.appendChild(style);
      
      fix.tested = true;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Apply JavaScript fix
   */
  private applyJavaScriptFix(fix: AdaptationFix): boolean {
    if (!fix.code) return false;

    try {
      const script = document.createElement('script');
      script.textContent = fix.code;
      script.setAttribute('data-sidebar-fix', 'true');
      document.head.appendChild(script);
      
      fix.tested = true;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Apply config fix
   */
  private applyConfigFix(fix: AdaptationFix): boolean {
    // Config fixes would be applied through the extension's configuration
    // This is a placeholder for configuration-based fixes
    fix.tested = true;
    return true;
  }

  /**
   * Run test scenario
   */
  private async runTestScenario(scenario: TestScenario): Promise<TestResult> {
    const startTime = Date.now();
    const issues: AdaptationIssue[] = [];
    const screenshots: string[] = [];
    const metrics: Record<string, number> = {};

    try {
      for (const step of scenario.steps) {
        await this.executeTestStep(step, issues, metrics);
      }

      return {
        scenario: scenario.name,
        success: issues.length === 0,
        duration: Date.now() - startTime,
        issues,
        screenshots,
        metrics,
        timestamp: Date.now()
      };

    } catch (error) {
      issues.push({
        type: 'functionality',
        severity: 'high',
        description: `Test step failed: ${error instanceof Error ? error.message : String(error)}`,
        impact: 'Test scenario could not be completed'
      });

      return {
        scenario: scenario.name,
        success: false,
        duration: Date.now() - startTime,
        issues,
        screenshots,
        metrics,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Execute test step
   */
  private async executeTestStep(
    step: TestStep, 
    issues: AdaptationIssue[], 
    metrics: Record<string, number>
  ): Promise<void> {
    switch (step.action) {
      case 'click':
        if (step.target) {
          const element = document.querySelector(step.target);
          if (element) {
            (element as HTMLElement).click();
          } else {
            issues.push({
              type: 'functionality',
              severity: 'medium',
              description: `Click target not found: ${step.target}`,
              impact: 'User interaction may not work as expected'
            });
          }
        }
        break;

      case 'scroll':
        if (step.value) {
          window.scrollTo(0, step.value);
        }
        break;

      case 'wait':
        if (step.value) {
          await new Promise(resolve => setTimeout(resolve, step.value));
        }
        break;

      case 'check':
        if (step.target) {
          const element = document.querySelector(step.target);
          if (!element) {
            issues.push({
              type: 'functionality',
              severity: 'medium',
              description: `Check target not found: ${step.target}`,
              impact: 'Expected element is missing'
            });
          }
        }
        break;

      case 'measure':
        if (step.target) {
          const startTime = performance.now();
          // Perform measurement
          const endTime = performance.now();
          metrics[step.target] = endTime - startTime;
        }
        break;
    }
  }

  /**
   * Calculate adaptation score
   */
  private calculateAdaptationScore(issues: AdaptationIssue[]): number {
    let score = 100;

    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical':
          score -= 25;
          break;
        case 'high':
          score -= 15;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    return Math.max(0, score);
  }

  /**
   * Initialize default adaptation rules
   */
  private initializeDefaultRules(): void {
    // Add common adaptation rules here
    this.addAdaptationRule({
      id: 'fixed-header-adjustment',
      name: 'Fixed Header Adjustment',
      description: 'Adjust fixed headers to accommodate sidebar',
      selector: 'header[style*="position: fixed"], .header-fixed',
      fix: {
        type: 'css',
        code: '.header-fixed { left: 300px !important; width: calc(100% - 300px) !important; }',
        description: 'Adjust fixed header position and width',
        tested: false,
        effectiveness: 85
      },
      priority: 8,
      category: 'layout'
    });

    this.addAdaptationRule({
      id: 'modal-z-index-fix',
      name: 'Modal Z-Index Fix',
      description: 'Ensure modals appear above sidebar',
      selector: '.modal, .dialog, [role="dialog"]',
      fix: {
        type: 'css',
        code: '.modal, .dialog, [role="dialog"] { z-index: 1000000 !important; }',
        description: 'Increase modal z-index to appear above sidebar',
        tested: false,
        effectiveness: 90
      },
      priority: 9,
      category: 'styling'
    });
  }

  /**
   * Initialize test scenarios
   */
  private initializeTestScenarios(): void {
    this.testScenarios.set('basic-functionality', {
      name: 'Basic Functionality Test',
      description: 'Test basic sidebar functionality on the website',
      steps: [
        { action: 'wait', value: 1000 },
        { action: 'check', target: 'body' },
        { action: 'measure', target: 'sidebar-injection-time' }
      ],
      expectedResult: 'Sidebar should be injectable and functional',
      timeout: 10000
    });

    this.testScenarios.set('responsive-behavior', {
      name: 'Responsive Behavior Test',
      description: 'Test sidebar behavior on different screen sizes',
      steps: [
        { action: 'wait', value: 500 },
        { action: 'measure', target: 'layout-shift' },
        { action: 'check', target: '.sidebar' }
      ],
      expectedResult: 'Sidebar should adapt to different screen sizes',
      timeout: 5000
    });
  }
}

// Export convenience functions
export const websiteAdapter = WebsiteAdapter.getInstance();

export function analyzeWebsite(url: string): Promise<WebsiteProfile> {
  return websiteAdapter.analyzeWebsite(url);
}

export function applyAdaptationFixes(domain: string): Promise<boolean> {
  return websiteAdapter.applyAdaptationFixes(domain);
}

export function getWebsiteProfile(domain: string): WebsiteProfile | null {
  return websiteAdapter.getWebsiteProfile(domain);
}