/**
 * Error Handler - 错误处理器
 */

import { 
  SidebarError, 
  <PERSON>rror<PERSON>ategory, 
  <PERSON>rror<PERSON>everity, 
  <PERSON>rror<PERSON>ode,
  ErrorRecoveryStrategy,
  ErrorReportData,
  ErrorContext
} from './ErrorTypes';

export interface ErrorHandlerOptions {
  enableReporting?: boolean;
  enableRecovery?: boolean;
  enableLogging?: boolean;
  maxRetryAttempts?: number;
  retryDelay?: number;
  reportingEndpoint?: string;
  debugMode?: boolean;
}

export interface ErrorListener {
  (error: SidebarError): void | Promise<void>;
}

export interface ErrorRecoveryResult {
  success: boolean;
  error?: SidebarError;
  recoveryAction?: string;
  attempts: number;
}

/**
 * Global Error Handler
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private options: ErrorHandlerOptions;
  private listeners: ErrorListener[] = [];
  private recoveryStrategies: Map<ErrorCode, ErrorRecoveryStrategy> = new Map();
  private errorCounts: Map<string, number> = new Map();
  private errorHistory: SidebarError[] = [];
  private maxHistorySize = 100;
  private retryAttempts: Map<string, number> = new Map();

  private constructor(options: ErrorHandlerOptions = {}) {
    this.options = {
      enableReporting: true,
      enableRecovery: true,
      enableLogging: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      debugMode: false,
      ...options
    };

    this.setupDefaultRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Get singleton instance
   */
  static getInstance(options?: ErrorHandlerOptions): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(options);
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle error
   */
  async handleError(error: Error | SidebarError, context?: ErrorContext): Promise<ErrorRecoveryResult> {
    let sidebarError: SidebarError;
    
    if (error instanceof SidebarError) {
      sidebarError = error;
    } else {
      sidebarError = this.convertToSidebarError(error, context);
    }

    // Add context if provided
    if (context && !sidebarError.context) {
      sidebarError = new SidebarError({
        ...sidebarError,
        context: { ...sidebarError.context, ...context }
      });
    }

    // Log error
    if (this.options.enableLogging) {
      this.logError(sidebarError);
    }

    // Update error statistics
    this.updateErrorStatistics(sidebarError);

    // Add to history
    this.addToHistory(sidebarError);

    // Notify listeners
    await this.notifyListeners(sidebarError);

    // Attempt recovery
    let recoveryResult: ErrorRecoveryResult = {
      success: false,
      attempts: 0
    };

    if (this.options.enableRecovery && sidebarError.recoverable) {
      recoveryResult = await this.attemptRecovery(sidebarError);
    }

    // Report error if recovery failed or error is critical
    if (this.options.enableReporting && 
        (sidebarError.severity === ErrorSeverity.CRITICAL || !recoveryResult.success)) {
      await this.reportError(sidebarError);
    }

    return recoveryResult;
  }

  /**
   * Add error listener
   */
  addListener(listener: ErrorListener): void {
    this.listeners.push(listener);
  }

  /**
   * Remove error listener
   */
  removeListener(listener: ErrorListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Add recovery strategy
   */
  addRecoveryStrategy(errorCode: ErrorCode, strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.set(errorCode, strategy);
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    this.errorCounts.forEach((count, key) => {
      stats[key] = count;
    });

    return {
      totalErrors: this.errorHistory.length,
      errorCounts: stats,
      recentErrors: this.errorHistory.slice(-10),
      criticalErrors: this.errorHistory.filter(e => e.severity === ErrorSeverity.CRITICAL).length,
      recoverableErrors: this.errorHistory.filter(e => e.recoverable).length
    };
  }

  /**
   * Clear error history
   */
  clearHistory(): void {
    this.errorHistory = [];
    this.errorCounts.clear();
    this.retryAttempts.clear();
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): SidebarError[] {
    return this.errorHistory.slice(-limit);
  }

  /**
   * Check if error should be ignored
   */
  shouldIgnoreError(error: SidebarError): boolean {
    // Ignore low severity errors that occur too frequently
    const errorKey = `${error.code}_${error.message}`;
    const count = this.errorCounts.get(errorKey) || 0;
    
    if (error.severity === ErrorSeverity.LOW && count > 10) {
      return true;
    }

    // Ignore validation errors from the same component
    if (error.category === ErrorCategory.VALIDATION && 
        error.context?.component && count > 5) {
      return true;
    }

    return false;
  }

  /**
   * Convert generic error to SidebarError
   */
  private convertToSidebarError(error: Error, context?: ErrorContext): SidebarError {
    // Check if it's a Chrome runtime error
    if (chrome.runtime.lastError) {
      return this.createChromeError(chrome.runtime.lastError, context);
    }

    // Check error message patterns
    const message = error.message.toLowerCase();
    
    if (message.includes('permission')) {
      return new SidebarError({
        code: ErrorCode.PERMISSION_DENIED,
        category: ErrorCategory.PERMISSION,
        severity: ErrorSeverity.CRITICAL,
        message: error.message,
        cause: error,
        context,
        recoverable: false,
        userMessage: '缺少必要权限，请检查扩展设置'
      });
    }

    if (message.includes('storage') || message.includes('quota')) {
      return new SidebarError({
        code: ErrorCode.STORAGE_ACCESS_DENIED,
        category: ErrorCategory.STORAGE,
        severity: ErrorSeverity.HIGH,
        message: error.message,
        cause: error,
        context,
        recoverable: true,
        retryable: false,
        userMessage: '存储空间不足或访问被拒绝'
      });
    }

    if (message.includes('network') || message.includes('fetch')) {
      return new SidebarError({
        code: ErrorCode.NETWORK_CONNECTION_FAILED,
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        cause: error,
        context,
        recoverable: true,
        retryable: true,
        userMessage: '网络连接失败，请检查网络设置'
      });
    }

    // Default unknown error
    return new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: error.message,
      cause: error,
      context,
      recoverable: true,
      userMessage: '发生未知错误，请重试'
    });
  }

  /**
   * Create Chrome API error
   */
  private createChromeError(chromeError: any, context?: ErrorContext): SidebarError {
    const message = chromeError.message || 'Chrome API error';
    
    if (message.includes('permissions')) {
      return new SidebarError({
        code: ErrorCode.PERMISSION_DENIED,
        category: ErrorCategory.PERMISSION,
        severity: ErrorSeverity.CRITICAL,
        message,
        context,
        recoverable: false,
        userMessage: '缺少Chrome扩展权限'
      });
    }

    if (message.includes('tabs')) {
      return new SidebarError({
        code: ErrorCode.API_CHROME_TABS_ERROR,
        category: ErrorCategory.API,
        severity: ErrorSeverity.HIGH,
        message,
        context,
        recoverable: true,
        retryable: true,
        userMessage: '标签页API调用失败'
      });
    }

    if (message.includes('bookmarks')) {
      return new SidebarError({
        code: ErrorCode.API_CHROME_BOOKMARKS_ERROR,
        category: ErrorCategory.API,
        severity: ErrorSeverity.HIGH,
        message,
        context,
        recoverable: true,
        retryable: true,
        userMessage: '收藏夹API调用失败'
      });
    }

    if (message.includes('storage')) {
      return new SidebarError({
        code: ErrorCode.API_CHROME_STORAGE_ERROR,
        category: ErrorCategory.API,
        severity: ErrorSeverity.HIGH,
        message,
        context,
        recoverable: true,
        retryable: false,
        userMessage: '存储API调用失败'
      });
    }

    return new SidebarError({
      code: ErrorCode.API_CHROME_RUNTIME_ERROR,
      category: ErrorCategory.API,
      severity: ErrorSeverity.HIGH,
      message,
      context,
      recoverable: true,
      retryable: true,
      userMessage: 'Chrome API调用失败'
    });
  }

  /**
   * Setup default recovery strategies
   */
  private setupDefaultRecoveryStrategies(): void {
    // Network errors - retry with exponential backoff
    this.recoveryStrategies.set(ErrorCode.NETWORK_CONNECTION_FAILED, {
      type: 'retry',
      maxAttempts: 3,
      delay: 1000
    });

    this.recoveryStrategies.set(ErrorCode.NETWORK_TIMEOUT, {
      type: 'retry',
      maxAttempts: 2,
      delay: 2000
    });

    // Storage errors - try fallback storage
    this.recoveryStrategies.set(ErrorCode.STORAGE_QUOTA_EXCEEDED, {
      type: 'fallback',
      fallbackAction: async () => {
        // Clear old data or use local storage
        console.log('Attempting storage cleanup...');
      }
    });

    // API errors - retry with delay
    this.recoveryStrategies.set(ErrorCode.API_CHROME_TABS_ERROR, {
      type: 'retry',
      maxAttempts: 2,
      delay: 500
    });

    this.recoveryStrategies.set(ErrorCode.API_CHROME_BOOKMARKS_ERROR, {
      type: 'retry',
      maxAttempts: 2,
      delay: 500
    });

    // UI errors - reload component
    this.recoveryStrategies.set(ErrorCode.UI_RENDER_FAILED, {
      type: 'reload'
    });

    // System errors - reset to safe state
    this.recoveryStrategies.set(ErrorCode.SYSTEM_INITIALIZATION_FAILED, {
      type: 'reset'
    });
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
        this.handleError(error, {
          timestamp: Date.now(),
          component: 'global',
          action: 'unhandledrejection'
        });
      });

      // Handle global errors
      window.addEventListener('error', (event) => {
        this.handleError(event.error || new Error(event.message), {
          timestamp: Date.now(),
          component: 'global',
          action: 'error',
          url: event.filename,
          metadata: {
            line: event.lineno,
            column: event.colno
          }
        });
      });
    }
  }

  /**
   * Attempt error recovery
   */
  private async attemptRecovery(error: SidebarError): Promise<ErrorRecoveryResult> {
    const strategy = this.recoveryStrategies.get(error.code);
    if (!strategy) {
      return { success: false, attempts: 0 };
    }

    // Check if strategy condition is met
    if (strategy.condition && !strategy.condition(error)) {
      return { success: false, attempts: 0 };
    }

    const errorKey = `${error.code}_${error.context?.component || 'unknown'}`;
    const currentAttempts = this.retryAttempts.get(errorKey) || 0;

    switch (strategy.type) {
      case 'retry':
        return await this.attemptRetry(error, strategy, errorKey, currentAttempts);
      
      case 'fallback':
        return await this.attemptFallback(error, strategy);
      
      case 'ignore':
        return { success: true, attempts: 0, recoveryAction: 'ignored' };
      
      case 'reload':
        return await this.attemptReload(error);
      
      case 'reset':
        return await this.attemptReset(error);
      
      default:
        return { success: false, attempts: 0 };
    }
  }

  /**
   * Attempt retry recovery
   */
  private async attemptRetry(
    error: SidebarError, 
    strategy: ErrorRecoveryStrategy, 
    errorKey: string, 
    currentAttempts: number
  ): Promise<ErrorRecoveryResult> {
    const maxAttempts = strategy.maxAttempts || this.options.maxRetryAttempts || 3;
    
    if (currentAttempts >= maxAttempts) {
      return { success: false, attempts: currentAttempts };
    }

    const delay = strategy.delay || this.options.retryDelay || 1000;
    const backoffDelay = delay * Math.pow(2, currentAttempts); // Exponential backoff

    await new Promise(resolve => setTimeout(resolve, backoffDelay));

    this.retryAttempts.set(errorKey, currentAttempts + 1);

    return { 
      success: true, 
      attempts: currentAttempts + 1, 
      recoveryAction: 'retry' 
    };
  }

  /**
   * Attempt fallback recovery
   */
  private async attemptFallback(error: SidebarError, strategy: ErrorRecoveryStrategy): Promise<ErrorRecoveryResult> {
    try {
      if (strategy.fallbackAction) {
        await strategy.fallbackAction();
      }
      return { success: true, attempts: 1, recoveryAction: 'fallback' };
    } catch (fallbackError) {
      return { 
        success: false, 
        attempts: 1, 
        error: this.convertToSidebarError(fallbackError as Error)
      };
    }
  }

  /**
   * Attempt reload recovery
   */
  private async attemptReload(error: SidebarError): Promise<ErrorRecoveryResult> {
    try {
      // Trigger component reload or page refresh
      if (error.context?.component) {
        // Send message to reload specific component
        chrome.runtime.sendMessage({
          type: 'RELOAD_COMPONENT',
          component: error.context.component
        });
      } else {
        // Reload entire sidebar
        chrome.runtime.sendMessage({
          type: 'RELOAD_SIDEBAR'
        });
      }
      
      return { success: true, attempts: 1, recoveryAction: 'reload' };
    } catch (reloadError) {
      return { 
        success: false, 
        attempts: 1, 
        error: this.convertToSidebarError(reloadError as Error)
      };
    }
  }

  /**
   * Attempt reset recovery
   */
  private async attemptReset(error: SidebarError): Promise<ErrorRecoveryResult> {
    try {
      // Reset to safe state
      chrome.runtime.sendMessage({
        type: 'RESET_TO_SAFE_STATE'
      });
      
      return { success: true, attempts: 1, recoveryAction: 'reset' };
    } catch (resetError) {
      return { 
        success: false, 
        attempts: 1, 
        error: this.convertToSidebarError(resetError as Error)
      };
    }
  }

  /**
   * Log error
   */
  private logError(error: SidebarError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.category.toUpperCase()}] ${error.code}: ${error.message}`;
    
    if (this.options.debugMode) {
      console.group(`🚨 ${logMessage}`);
      console.error('Error Details:', error);
      console.error('Stack Trace:', error.stack);
      if (error.context) {
        console.log('Context:', error.context);
      }
      if (error.cause) {
        console.error('Caused by:', error.cause);
      }
      console.groupEnd();
    } else {
      console[logLevel](logMessage);
    }
  }

  /**
   * Get log level for severity
   */
  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'error';
    }
  }

  /**
   * Update error statistics
   */
  private updateErrorStatistics(error: SidebarError): void {
    const errorKey = `${error.code}_${error.message}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
  }

  /**
   * Add error to history
   */
  private addToHistory(error: SidebarError): void {
    this.errorHistory.push(error);
    
    // Keep history size manageable
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Notify error listeners
   */
  private async notifyListeners(error: SidebarError): Promise<void> {
    const promises = this.listeners.map(async (listener) => {
      try {
        await listener(error);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Report error to external service
   */
  private async reportError(error: SidebarError): Promise<void> {
    if (!this.options.reportingEndpoint) {
      return;
    }

    try {
      const reportData: ErrorReportData = {
        errorId: `${error.code}_${error.timestamp}`,
        timestamp: error.timestamp,
        code: error.code,
        category: error.category,
        severity: error.severity,
        message: error.message,
        stack: error.stack,
        context: error.context,
        userAgent: navigator.userAgent,
        extensionVersion: chrome.runtime.getManifest().version,
        chromeVersion: /Chrome\/([0-9.]+)/.exec(navigator.userAgent)?.[1] || 'unknown',
        os: navigator.platform,
        frequency: this.errorCounts.get(`${error.code}_${error.message}`) || 1,
        firstOccurrence: error.timestamp,
        lastOccurrence: error.timestamp
      };

      await fetch(this.options.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData)
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }
}