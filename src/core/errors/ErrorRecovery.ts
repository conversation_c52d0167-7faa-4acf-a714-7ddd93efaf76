/**
 * Error Recovery Service - 错误恢复服务
 */

import { SidebarError, ErrorCode, ErrorCategory, ErrorSeverity } from './ErrorTypes';
import { ErrorHandler } from './ErrorHandler';

export interface RecoveryAction {
  id: string;
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  condition?: (error: SidebarError) => boolean;
  priority: number;
}

export interface RecoveryPlan {
  error: SidebarError;
  actions: RecoveryAction[];
  selectedAction?: RecoveryAction;
  executed: boolean;
  success: boolean;
  timestamp: number;
}

export interface SafeState {
  settings: Record<string, any>;
  tabs: any[];
  bookmarks: any[];
  timestamp: number;
}

/**
 * Error Recovery Service
 */
export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private errorHandler: ErrorHandler;
  private recoveryActions: Map<ErrorCode, RecoveryAction[]> = new Map();
  private recoveryHistory: RecoveryPlan[] = [];
  private safeStates: SafeState[] = [];
  private maxSafeStates = 5;
  private isRecovering = false;

  private constructor() {
    this.errorHandler = ErrorHandler.getInstance();
    this.setupDefaultRecoveryActions();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  /**
   * Create recovery plan for error
   */
  createRecoveryPlan(error: SidebarError): RecoveryPlan {
    const actions = this.getRecoveryActions(error);
    
    const plan: RecoveryPlan = {
      error,
      actions: actions.sort((a, b) => b.priority - a.priority),
      executed: false,
      success: false,
      timestamp: Date.now()
    };

    // Auto-select best action
    if (plan.actions.length > 0) {
      plan.selectedAction = plan.actions[0];
    }

    return plan;
  }

  /**
   * Execute recovery plan
   */
  async executeRecoveryPlan(plan: RecoveryPlan): Promise<boolean> {
    if (this.isRecovering) {
      console.warn('Recovery already in progress');
      return false;
    }

    if (!plan.selectedAction) {
      console.warn('No recovery action selected');
      return false;
    }

    this.isRecovering = true;

    try {
      console.log(`Executing recovery action: ${plan.selectedAction.name}`);
      
      const success = await plan.selectedAction.execute();
      
      plan.executed = true;
      plan.success = success;
      
      this.recoveryHistory.push(plan);
      
      if (success) {
        console.log(`Recovery successful: ${plan.selectedAction.name}`);
      } else {
        console.error(`Recovery failed: ${plan.selectedAction.name}`);
        
        // Try next action if available
        const nextActionIndex = plan.actions.indexOf(plan.selectedAction) + 1;
        if (nextActionIndex < plan.actions.length) {
          plan.selectedAction = plan.actions[nextActionIndex];
          return await this.executeRecoveryPlan(plan);
        }
      }
      
      return success;
    } catch (recoveryError) {
      console.error('Recovery execution failed:', recoveryError);
      plan.executed = true;
      plan.success = false;
      return false;
    } finally {
      this.isRecovering = false;
    }
  }

  /**
   * Add custom recovery action
   */
  addRecoveryAction(errorCode: ErrorCode, action: RecoveryAction): void {
    if (!this.recoveryActions.has(errorCode)) {
      this.recoveryActions.set(errorCode, []);
    }
    
    this.recoveryActions.get(errorCode)!.push(action);
  }

  /**
   * Save current state as safe state
   */
  async saveSafeState(): Promise<void> {
    try {
      const safeState: SafeState = {
        settings: await this.getCurrentSettings(),
        tabs: await this.getCurrentTabs(),
        bookmarks: await this.getCurrentBookmarks(),
        timestamp: Date.now()
      };

      this.safeStates.push(safeState);

      // Keep only recent safe states
      if (this.safeStates.length > this.maxSafeStates) {
        this.safeStates = this.safeStates.slice(-this.maxSafeStates);
      }

      console.log('Safe state saved successfully');
    } catch (error) {
      console.error('Failed to save safe state:', error);
    }
  }

  /**
   * Restore to safe state
   */
  async restoreToSafeState(stateIndex: number = -1): Promise<boolean> {
    try {
      const safeState = stateIndex >= 0 ? 
        this.safeStates[stateIndex] : 
        this.safeStates[this.safeStates.length - 1];

      if (!safeState) {
        console.error('No safe state available');
        return false;
      }

      console.log('Restoring to safe state...');

      // Restore settings
      await this.restoreSettings(safeState.settings);

      // Note: We don't restore tabs and bookmarks as they are managed by Chrome
      // Instead, we reset the sidebar state

      console.log('Safe state restored successfully');
      return true;
    } catch (error) {
      console.error('Failed to restore safe state:', error);
      return false;
    }
  }

  /**
   * Get recovery history
   */
  getRecoveryHistory(): RecoveryPlan[] {
    return [...this.recoveryHistory];
  }

  /**
   * Get available safe states
   */
  getSafeStates(): SafeState[] {
    return [...this.safeStates];
  }

  /**
   * Clear recovery history
   */
  clearRecoveryHistory(): void {
    this.recoveryHistory = [];
  }

  /**
   * Get recovery actions for error
   */
  private getRecoveryActions(error: SidebarError): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    // Get specific actions for error code
    const specificActions = this.recoveryActions.get(error.code) || [];
    actions.push(...specificActions.filter(action => 
      !action.condition || action.condition(error)
    ));

    // Get category-based actions
    const categoryActions = this.getCategoryRecoveryActions(error.category);
    actions.push(...categoryActions.filter(action => 
      !action.condition || action.condition(error)
    ));

    // Get general actions
    const generalActions = this.getGeneralRecoveryActions(error);
    actions.push(...generalActions);

    // Remove duplicates
    const uniqueActions = actions.filter((action, index, self) => 
      self.findIndex(a => a.id === action.id) === index
    );

    return uniqueActions;
  }

  /**
   * Get category-based recovery actions
   */
  private getCategoryRecoveryActions(category: ErrorCategory): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    switch (category) {
      case ErrorCategory.STORAGE:
        actions.push(
          this.createClearCacheAction(),
          this.createResetStorageAction()
        );
        break;

      case ErrorCategory.PERMISSION:
        actions.push(
          this.createRequestPermissionsAction(),
          this.createShowPermissionGuideAction()
        );
        break;

      case ErrorCategory.API:
        actions.push(
          this.createRetryApiCallAction(),
          this.createReloadExtensionAction()
        );
        break;

      case ErrorCategory.UI:
        actions.push(
          this.createRefreshUIAction(),
          this.createResetUIStateAction()
        );
        break;

      case ErrorCategory.NETWORK:
        actions.push(
          this.createRetryNetworkAction(),
          this.createOfflineModeAction()
        );
        break;
    }

    return actions;
  }

  /**
   * Get general recovery actions
   */
  private getGeneralRecoveryActions(error: SidebarError): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    if (error.recoverable) {
      actions.push(this.createRestartSidebarAction());
    }

    if (error.severity === ErrorSeverity.CRITICAL) {
      actions.push(
        this.createRestoreToSafeStateAction(),
        this.createResetToDefaultsAction()
      );
    }

    return actions;
  }

  /**
   * Setup default recovery actions
   */
  private setupDefaultRecoveryActions(): void {
    // Storage quota exceeded
    this.addRecoveryAction(ErrorCode.STORAGE_QUOTA_EXCEEDED, {
      id: 'clear_old_data',
      name: '清理旧数据',
      description: '删除旧的缓存数据以释放存储空间',
      execute: async () => {
        try {
          // Clear old cache data
          const result = await chrome.storage.local.get(null);
          const keysToRemove: string[] = [];
          
          Object.keys(result).forEach(key => {
            if (key.startsWith('cache_') || key.startsWith('temp_')) {
              keysToRemove.push(key);
            }
          });
          
          if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
          }
          
          return true;
        } catch (error) {
          console.error('Failed to clear old data:', error);
          return false;
        }
      },
      priority: 10
    });

    // Permission denied
    this.addRecoveryAction(ErrorCode.PERMISSION_DENIED, {
      id: 'request_permissions',
      name: '请求权限',
      description: '重新请求必要的扩展权限',
      execute: async () => {
        try {
          const granted = await chrome.permissions.request({
            permissions: ['tabs', 'bookmarks', 'storage'],
            origins: ['<all_urls>']
          });
          return granted;
        } catch (error) {
          console.error('Failed to request permissions:', error);
          return false;
        }
      },
      priority: 10
    });

    // Network connection failed
    this.addRecoveryAction(ErrorCode.NETWORK_CONNECTION_FAILED, {
      id: 'retry_connection',
      name: '重试连接',
      description: '等待网络恢复后重试连接',
      execute: async () => {
        // Wait and retry
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        try {
          // Test network connectivity
          const response = await fetch('https://www.google.com/favicon.ico', {
            method: 'HEAD',
            mode: 'no-cors'
          });
          return true;
        } catch (error) {
          return false;
        }
      },
      priority: 8
    });

    // System initialization failed
    this.addRecoveryAction(ErrorCode.SYSTEM_INITIALIZATION_FAILED, {
      id: 'reinitialize_system',
      name: '重新初始化系统',
      description: '重新初始化侧边栏系统',
      execute: async () => {
        try {
          // Send message to background script to reinitialize
          await chrome.runtime.sendMessage({
            type: 'REINITIALIZE_SYSTEM'
          });
          return true;
        } catch (error) {
          console.error('Failed to reinitialize system:', error);
          return false;
        }
      },
      priority: 9
    });
  }

  /**
   * Create clear cache action
   */
  private createClearCacheAction(): RecoveryAction {
    return {
      id: 'clear_cache',
      name: '清理缓存',
      description: '清理所有缓存数据',
      execute: async () => {
        try {
          const result = await chrome.storage.local.get(null);
          const cacheKeys = Object.keys(result).filter(key => 
            key.startsWith('cache_') || key.startsWith('temp_')
          );
          
          if (cacheKeys.length > 0) {
            await chrome.storage.local.remove(cacheKeys);
          }
          
          return true;
        } catch (error) {
          console.error('Failed to clear cache:', error);
          return false;
        }
      },
      priority: 7
    };
  }

  /**
   * Create reset storage action
   */
  private createResetStorageAction(): RecoveryAction {
    return {
      id: 'reset_storage',
      name: '重置存储',
      description: '重置所有存储数据到默认值',
      execute: async () => {
        try {
          await chrome.storage.sync.clear();
          await chrome.storage.local.clear();
          return true;
        } catch (error) {
          console.error('Failed to reset storage:', error);
          return false;
        }
      },
      priority: 5
    };
  }

  /**
   * Create request permissions action
   */
  private createRequestPermissionsAction(): RecoveryAction {
    return {
      id: 'request_permissions',
      name: '请求权限',
      description: '请求必要的扩展权限',
      execute: async () => {
        try {
          const granted = await chrome.permissions.request({
            permissions: ['tabs', 'bookmarks', 'storage'],
            origins: ['<all_urls>']
          });
          return granted;
        } catch (error) {
          console.error('Failed to request permissions:', error);
          return false;
        }
      },
      priority: 10
    };
  }

  /**
   * Create show permission guide action
   */
  private createShowPermissionGuideAction(): RecoveryAction {
    return {
      id: 'show_permission_guide',
      name: '显示权限指南',
      description: '显示如何手动授予权限的指南',
      execute: async () => {
        try {
          await chrome.tabs.create({
            url: 'chrome://extensions/',
            active: true
          });
          return true;
        } catch (error) {
          console.error('Failed to show permission guide:', error);
          return false;
        }
      },
      priority: 6
    };
  }

  /**
   * Create retry API call action
   */
  private createRetryApiCallAction(): RecoveryAction {
    return {
      id: 'retry_api_call',
      name: '重试API调用',
      description: '重新尝试失败的API调用',
      execute: async () => {
        // This would be implemented based on the specific API call that failed
        await new Promise(resolve => setTimeout(resolve, 1000));
        return true;
      },
      priority: 8
    };
  }

  /**
   * Create reload extension action
   */
  private createReloadExtensionAction(): RecoveryAction {
    return {
      id: 'reload_extension',
      name: '重载扩展',
      description: '重新加载整个扩展',
      execute: async () => {
        try {
          chrome.runtime.reload();
          return true;
        } catch (error) {
          console.error('Failed to reload extension:', error);
          return false;
        }
      },
      priority: 4
    };
  }

  /**
   * Create refresh UI action
   */
  private createRefreshUIAction(): RecoveryAction {
    return {
      id: 'refresh_ui',
      name: '刷新界面',
      description: '刷新侧边栏界面',
      execute: async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'REFRESH_SIDEBAR_UI'
          });
          return true;
        } catch (error) {
          console.error('Failed to refresh UI:', error);
          return false;
        }
      },
      priority: 7
    };
  }

  /**
   * Create reset UI state action
   */
  private createResetUIStateAction(): RecoveryAction {
    return {
      id: 'reset_ui_state',
      name: '重置界面状态',
      description: '重置侧边栏界面到初始状态',
      execute: async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'RESET_SIDEBAR_STATE'
          });
          return true;
        } catch (error) {
          console.error('Failed to reset UI state:', error);
          return false;
        }
      },
      priority: 6
    };
  }

  /**
   * Create retry network action
   */
  private createRetryNetworkAction(): RecoveryAction {
    return {
      id: 'retry_network',
      name: '重试网络连接',
      description: '重新尝试网络连接',
      execute: async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        try {
          const response = await fetch('https://www.google.com/favicon.ico', {
            method: 'HEAD',
            mode: 'no-cors'
          });
          return true;
        } catch (error) {
          return false;
        }
      },
      priority: 8
    };
  }

  /**
   * Create offline mode action
   */
  private createOfflineModeAction(): RecoveryAction {
    return {
      id: 'enable_offline_mode',
      name: '启用离线模式',
      description: '切换到离线模式，使用缓存数据',
      execute: async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'ENABLE_OFFLINE_MODE'
          });
          return true;
        } catch (error) {
          console.error('Failed to enable offline mode:', error);
          return false;
        }
      },
      priority: 6
    };
  }

  /**
   * Create restart sidebar action
   */
  private createRestartSidebarAction(): RecoveryAction {
    return {
      id: 'restart_sidebar',
      name: '重启侧边栏',
      description: '重新启动侧边栏服务',
      execute: async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'RESTART_SIDEBAR'
          });
          return true;
        } catch (error) {
          console.error('Failed to restart sidebar:', error);
          return false;
        }
      },
      priority: 7
    };
  }

  /**
   * Create restore to safe state action
   */
  private createRestoreToSafeStateAction(): RecoveryAction {
    return {
      id: 'restore_safe_state',
      name: '恢复到安全状态',
      description: '恢复到最近的安全状态',
      execute: async () => {
        return await this.restoreToSafeState();
      },
      condition: (error) => this.safeStates.length > 0,
      priority: 9
    };
  }

  /**
   * Create reset to defaults action
   */
  private createResetToDefaultsAction(): RecoveryAction {
    return {
      id: 'reset_to_defaults',
      name: '重置到默认设置',
      description: '重置所有设置到默认值',
      execute: async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'RESET_TO_DEFAULTS'
          });
          return true;
        } catch (error) {
          console.error('Failed to reset to defaults:', error);
          return false;
        }
      },
      priority: 3
    };
  }

  /**
   * Get current settings
   */
  private async getCurrentSettings(): Promise<Record<string, any>> {
    try {
      const result = await chrome.storage.sync.get(null);
      return result;
    } catch (error) {
      console.error('Failed to get current settings:', error);
      return {};
    }
  }

  /**
   * Get current tabs
   */
  private async getCurrentTabs(): Promise<any[]> {
    try {
      const tabs = await chrome.tabs.query({});
      return tabs.map(tab => ({
        id: tab.id,
        url: tab.url,
        title: tab.title,
        favIconUrl: tab.favIconUrl
      }));
    } catch (error) {
      console.error('Failed to get current tabs:', error);
      return [];
    }
  }

  /**
   * Get current bookmarks
   */
  private async getCurrentBookmarks(): Promise<any[]> {
    try {
      const bookmarks = await chrome.bookmarks.getTree();
      return bookmarks;
    } catch (error) {
      console.error('Failed to get current bookmarks:', error);
      return [];
    }
  }

  /**
   * Restore settings
   */
  private async restoreSettings(settings: Record<string, any>): Promise<void> {
    try {
      await chrome.storage.sync.clear();
      await chrome.storage.sync.set(settings);
    } catch (error) {
      console.error('Failed to restore settings:', error);
      throw error;
    }
  }
}