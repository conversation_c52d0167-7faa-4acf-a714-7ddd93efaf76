/**
 * Error Reporting Service - 错误上报服务
 */

import { SidebarError, ErrorReportData, ErrorCategory, ErrorSeverity } from './ErrorTypes';

export interface ReportingConfig {
  endpoint?: string;
  apiKey?: string;
  enableUserReports?: boolean;
  enableAutoReports?: boolean;
  enableTelemetry?: boolean;
  batchSize?: number;
  batchTimeout?: number;
  retryAttempts?: number;
  privacyMode?: boolean;
}

export interface UserFeedback {
  errorId: string;
  rating: number; // 1-5
  description: string;
  reproductionSteps?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  userAgent: string;
  timestamp: number;
  contactEmail?: string;
}

export interface ErrorReport {
  id: string;
  error: SidebarError;
  reportData: ErrorReportData;
  userFeedback?: UserFeedback;
  status: 'pending' | 'sent' | 'failed';
  attempts: number;
  timestamp: number;
}

export interface TelemetryData {
  sessionId: string;
  userId?: string;
  events: TelemetryEvent[];
  startTime: number;
  endTime: number;
  duration: number;
}

export interface TelemetryEvent {
  type: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Error Reporting Service
 */
export class ErrorReportingService {
  private static instance: ErrorReportingService;
  private config: ReportingConfig;
  private reportQueue: ErrorReport[] = [];
  private batchTimer?: NodeJS.Timeout;
  private sessionId: string;
  private telemetryEvents: TelemetryEvent[] = [];
  private isOnline = navigator.onLine;

  private constructor(config: ReportingConfig = {}) {
    this.config = {
      enableUserReports: true,
      enableAutoReports: true,
      enableTelemetry: false,
      batchSize: 10,
      batchTimeout: 30000, // 30 seconds
      retryAttempts: 3,
      privacyMode: false,
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.setupEventListeners();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: ReportingConfig): ErrorReportingService {
    if (!ErrorReportingService.instance) {
      ErrorReportingService.instance = new ErrorReportingService(config);
    }
    return ErrorReportingService.instance;
  }

  /**
   * Report error automatically
   */
  async reportError(error: SidebarError): Promise<string> {
    if (!this.config.enableAutoReports) {
      return '';
    }

    const reportId = this.generateReportId();
    const reportData = this.createReportData(error);
    
    const report: ErrorReport = {
      id: reportId,
      error,
      reportData,
      status: 'pending',
      attempts: 0,
      timestamp: Date.now()
    };

    this.reportQueue.push(report);
    
    // Process queue immediately for critical errors
    if (error.severity === ErrorSeverity.CRITICAL) {
      await this.processReportQueue();
    } else {
      this.scheduleBatchProcessing();
    }

    return reportId;
  }

  /**
   * Report error with user feedback
   */
  async reportErrorWithFeedback(error: SidebarError, feedback: Partial<UserFeedback>): Promise<string> {
    if (!this.config.enableUserReports) {
      return '';
    }

    const reportId = this.generateReportId();
    const reportData = this.createReportData(error);
    
    const userFeedback: UserFeedback = {
      errorId: reportId,
      rating: feedback.rating || 1,
      description: feedback.description || '',
      reproductionSteps: feedback.reproductionSteps,
      expectedBehavior: feedback.expectedBehavior,
      actualBehavior: feedback.actualBehavior,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      contactEmail: feedback.contactEmail
    };

    const report: ErrorReport = {
      id: reportId,
      error,
      reportData,
      userFeedback,
      status: 'pending',
      attempts: 0,
      timestamp: Date.now()
    };

    this.reportQueue.push(report);
    
    // Process user reports immediately
    await this.processReportQueue();

    return reportId;
  }

  /**
   * Track telemetry event
   */
  trackEvent(type: string, category: string, action: string, label?: string, value?: number, metadata?: Record<string, any>): void {
    if (!this.config.enableTelemetry) {
      return;
    }

    const event: TelemetryEvent = {
      type,
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      metadata: this.config.privacyMode ? this.sanitizeMetadata(metadata) : metadata
    };

    this.telemetryEvents.push(event);

    // Keep telemetry events manageable
    if (this.telemetryEvents.length > 1000) {
      this.telemetryEvents = this.telemetryEvents.slice(-500);
    }
  }

  /**
   * Send telemetry data
   */
  async sendTelemetryData(): Promise<boolean> {
    if (!this.config.enableTelemetry || this.telemetryEvents.length === 0) {
      return true;
    }

    try {
      const telemetryData: TelemetryData = {
        sessionId: this.sessionId,
        events: [...this.telemetryEvents],
        startTime: this.telemetryEvents[0]?.timestamp || Date.now(),
        endTime: Date.now(),
        duration: Date.now() - (this.telemetryEvents[0]?.timestamp || Date.now())
      };

      const success = await this.sendData('/telemetry', telemetryData);
      
      if (success) {
        this.telemetryEvents = [];
      }

      return success;
    } catch (error) {
      console.error('Failed to send telemetry data:', error);
      return false;
    }
  }

  /**
   * Get report status
   */
  getReportStatus(reportId: string): 'pending' | 'sent' | 'failed' | 'not_found' {
    const report = this.reportQueue.find(r => r.id === reportId);
    return report ? report.status : 'not_found';
  }

  /**
   * Get pending reports count
   */
  getPendingReportsCount(): number {
    return this.reportQueue.filter(r => r.status === 'pending').length;
  }

  /**
   * Clear report queue
   */
  clearReportQueue(): void {
    this.reportQueue = [];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<ReportingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Create error report data
   */
  private createReportData(error: SidebarError): ErrorReportData {
    const manifest = chrome.runtime.getManifest();
    
    return {
      errorId: `${error.code}_${error.timestamp}`,
      timestamp: error.timestamp,
      code: error.code,
      category: error.category,
      severity: error.severity,
      message: this.config.privacyMode ? this.sanitizeMessage(error.message) : error.message,
      stack: this.config.privacyMode ? this.sanitizeStack(error.stack) : error.stack,
      context: this.config.privacyMode ? this.sanitizeContext(error.context) : error.context,
      userAgent: navigator.userAgent,
      extensionVersion: manifest.version,
      chromeVersion: this.getChromeVersion(),
      os: navigator.platform,
      frequency: 1, // This would be calculated based on error history
      firstOccurrence: error.timestamp,
      lastOccurrence: error.timestamp
    };
  }

  /**
   * Schedule batch processing
   */
  private scheduleBatchProcessing(): void {
    if (this.batchTimer) {
      return;
    }

    this.batchTimer = setTimeout(async () => {
      await this.processReportQueue();
      this.batchTimer = undefined;
    }, this.config.batchTimeout);

    // Process immediately if batch size is reached
    if (this.reportQueue.filter(r => r.status === 'pending').length >= this.config.batchSize!) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
      this.processReportQueue();
    }
  }

  /**
   * Process report queue
   */
  private async processReportQueue(): Promise<void> {
    if (!this.isOnline || !this.config.endpoint) {
      return;
    }

    const pendingReports = this.reportQueue.filter(r => r.status === 'pending');
    
    if (pendingReports.length === 0) {
      return;
    }

    console.log(`Processing ${pendingReports.length} error reports...`);

    for (const report of pendingReports) {
      try {
        const success = await this.sendErrorReport(report);
        
        if (success) {
          report.status = 'sent';
          console.log(`Error report sent successfully: ${report.id}`);
        } else {
          report.attempts++;
          
          if (report.attempts >= this.config.retryAttempts!) {
            report.status = 'failed';
            console.error(`Error report failed after ${report.attempts} attempts: ${report.id}`);
          }
        }
      } catch (error) {
        console.error(`Failed to send error report ${report.id}:`, error);
        report.attempts++;
        
        if (report.attempts >= this.config.retryAttempts!) {
          report.status = 'failed';
        }
      }
    }

    // Clean up old reports
    this.cleanupOldReports();
  }

  /**
   * Send error report
   */
  private async sendErrorReport(report: ErrorReport): Promise<boolean> {
    const payload = {
      report: report.reportData,
      feedback: report.userFeedback,
      sessionId: this.sessionId,
      timestamp: Date.now()
    };

    return await this.sendData('/errors', payload);
  }

  /**
   * Send data to endpoint
   */
  private async sendData(path: string, data: any): Promise<boolean> {
    if (!this.config.endpoint) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.endpoint}${path}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify(data)
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to send data:', error);
      return false;
    }
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processReportQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Send telemetry before page unload
    window.addEventListener('beforeunload', () => {
      if (this.telemetryEvents.length > 0) {
        // Use sendBeacon for reliable delivery
        this.sendTelemetryBeacon();
      }
    });

    // Periodic telemetry sending
    setInterval(() => {
      this.sendTelemetryData();
    }, 300000); // Every 5 minutes
  }

  /**
   * Send telemetry using beacon
   */
  private sendTelemetryBeacon(): void {
    if (!this.config.endpoint || !this.config.enableTelemetry) {
      return;
    }

    try {
      const telemetryData: TelemetryData = {
        sessionId: this.sessionId,
        events: [...this.telemetryEvents],
        startTime: this.telemetryEvents[0]?.timestamp || Date.now(),
        endTime: Date.now(),
        duration: Date.now() - (this.telemetryEvents[0]?.timestamp || Date.now())
      };

      navigator.sendBeacon(
        `${this.config.endpoint}/telemetry`,
        JSON.stringify(telemetryData)
      );

      this.telemetryEvents = [];
    } catch (error) {
      console.error('Failed to send telemetry beacon:', error);
    }
  }

  /**
   * Clean up old reports
   */
  private cleanupOldReports(): void {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const now = Date.now();
    
    this.reportQueue = this.reportQueue.filter(report => {
      return (now - report.timestamp) < maxAge || report.status === 'pending';
    });
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate report ID
   */
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get Chrome version
   */
  private getChromeVersion(): string {
    const match = navigator.userAgent.match(/Chrome\/([0-9.]+)/);
    return match ? match[1] : 'unknown';
  }

  /**
   * Sanitize message for privacy
   */
  private sanitizeMessage(message?: string): string {
    if (!message) return '';
    
    // Remove URLs, file paths, and other sensitive data
    return message
      .replace(/https?:\/\/[^\s]+/g, '[URL]')
      .replace(/file:\/\/[^\s]+/g, '[FILE_PATH]')
      .replace(/[a-zA-Z]:\\[^\s]+/g, '[FILE_PATH]')
      .replace(/\/[^\s]+/g, '[PATH]')
      .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP_ADDRESS]');
  }

  /**
   * Sanitize stack trace for privacy
   */
  private sanitizeStack(stack?: string): string {
    if (!stack) return '';
    
    return stack
      .replace(/https?:\/\/[^\s]+/g, '[URL]')
      .replace(/file:\/\/[^\s]+/g, '[FILE_PATH]')
      .replace(/chrome-extension:\/\/[^\s]+/g, '[EXTENSION_URL]');
  }

  /**
   * Sanitize context for privacy
   */
  private sanitizeContext(context?: any): any {
    if (!context) return context;
    
    const sanitized = { ...context };
    
    // Remove sensitive fields
    delete sanitized.userId;
    delete sanitized.sessionId;
    delete sanitized.url;
    
    // Sanitize metadata
    if (sanitized.metadata) {
      sanitized.metadata = this.sanitizeMetadata(sanitized.metadata);
    }
    
    return sanitized;
  }

  /**
   * Sanitize metadata for privacy
   */
  private sanitizeMetadata(metadata?: Record<string, any>): Record<string, any> {
    if (!metadata) return {};
    
    const sanitized: Record<string, any> = {};
    
    Object.entries(metadata).forEach(([key, value]) => {
      // Skip sensitive keys
      if (['password', 'token', 'key', 'secret', 'auth'].some(sensitive => 
          key.toLowerCase().includes(sensitive))) {
        return;
      }
      
      // Sanitize string values
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeMessage(value);
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }
}

/**
 * User Feedback Dialog
 */
export class UserFeedbackDialog {
  private container?: HTMLElement;
  private onSubmit?: (feedback: UserFeedback) => void;
  private onCancel?: () => void;

  /**
   * Show feedback dialog
   */
  show(error: SidebarError, onSubmit: (feedback: UserFeedback) => void, onCancel?: () => void): void {
    this.onSubmit = onSubmit;
    this.onCancel = onCancel;
    
    this.createDialog(error);
    this.attachEventListeners();
  }

  /**
   * Hide feedback dialog
   */
  hide(): void {
    if (this.container) {
      this.container.remove();
      this.container = undefined;
    }
  }

  /**
   * Create dialog HTML
   */
  private createDialog(error: SidebarError): void {
    this.container = document.createElement('div');
    this.container.className = 'error-feedback-dialog';
    this.container.innerHTML = `
      <div class="dialog-overlay">
        <div class="dialog-content">
          <div class="dialog-header">
            <h3>错误反馈</h3>
            <button class="close-btn" type="button">&times;</button>
          </div>
          
          <div class="dialog-body">
            <div class="error-info">
              <p><strong>错误信息:</strong> ${error.userMessage || error.message}</p>
              <p><strong>错误代码:</strong> ${error.code}</p>
            </div>
            
            <form class="feedback-form">
              <div class="form-group">
                <label for="rating">问题严重程度 (1-5):</label>
                <div class="rating-stars">
                  <input type="radio" name="rating" value="1" id="star1">
                  <label for="star1">★</label>
                  <input type="radio" name="rating" value="2" id="star2">
                  <label for="star2">★</label>
                  <input type="radio" name="rating" value="3" id="star3" checked>
                  <label for="star3">★</label>
                  <input type="radio" name="rating" value="4" id="star4">
                  <label for="star4">★</label>
                  <input type="radio" name="rating" value="5" id="star5">
                  <label for="star5">★</label>
                </div>
              </div>
              
              <div class="form-group">
                <label for="description">问题描述:</label>
                <textarea id="description" rows="4" placeholder="请描述您遇到的问题..."></textarea>
              </div>
              
              <div class="form-group">
                <label for="reproduction">重现步骤 (可选):</label>
                <textarea id="reproduction" rows="3" placeholder="请描述如何重现这个问题..."></textarea>
              </div>
              
              <div class="form-group">
                <label for="expected">期望行为 (可选):</label>
                <textarea id="expected" rows="2" placeholder="您期望发生什么..."></textarea>
              </div>
              
              <div class="form-group">
                <label for="actual">实际行为 (可选):</label>
                <textarea id="actual" rows="2" placeholder="实际发生了什么..."></textarea>
              </div>
              
              <div class="form-group">
                <label for="email">联系邮箱 (可选):</label>
                <input type="email" id="email" placeholder="如需回复请留下邮箱...">
              </div>
            </form>
          </div>
          
          <div class="dialog-footer">
            <button class="btn btn-cancel" type="button">取消</button>
            <button class="btn btn-submit" type="button">提交反馈</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(this.container);
  }

  /**
   * Attach event listeners
   */
  private attachEventListeners(): void {
    if (!this.container) return;

    const closeBtn = this.container.querySelector('.close-btn');
    const cancelBtn = this.container.querySelector('.btn-cancel');
    const submitBtn = this.container.querySelector('.btn-submit');

    closeBtn?.addEventListener('click', () => {
      this.hide();
      this.onCancel?.();
    });

    cancelBtn?.addEventListener('click', () => {
      this.hide();
      this.onCancel?.();
    });

    submitBtn?.addEventListener('click', () => {
      this.handleSubmit();
    });

    // Close on overlay click
    const overlay = this.container.querySelector('.dialog-overlay');
    overlay?.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.hide();
        this.onCancel?.();
      }
    });
  }

  /**
   * Handle form submission
   */
  private handleSubmit(): void {
    if (!this.container) return;

    const form = this.container.querySelector('.feedback-form') as HTMLFormElement;
    const formData = new FormData(form);

    const rating = parseInt(formData.get('rating') as string) || 3;
    const description = (this.container.querySelector('#description') as HTMLTextAreaElement).value;
    const reproductionSteps = (this.container.querySelector('#reproduction') as HTMLTextAreaElement).value;
    const expectedBehavior = (this.container.querySelector('#expected') as HTMLTextAreaElement).value;
    const actualBehavior = (this.container.querySelector('#actual') as HTMLTextAreaElement).value;
    const contactEmail = (this.container.querySelector('#email') as HTMLInputElement).value;

    if (!description.trim()) {
      alert('请填写问题描述');
      return;
    }

    const feedback: UserFeedback = {
      errorId: '',
      rating,
      description: description.trim(),
      reproductionSteps: reproductionSteps.trim() || undefined,
      expectedBehavior: expectedBehavior.trim() || undefined,
      actualBehavior: actualBehavior.trim() || undefined,
      contactEmail: contactEmail.trim() || undefined,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    };

    this.hide();
    this.onSubmit?.(feedback);
  }
}