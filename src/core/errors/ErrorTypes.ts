/**
 * Error Types - 错误类型定义
 */

export enum ErrorCategory {
  SYSTEM = 'system',
  NETWORK = 'network',
  STORAGE = 'storage',
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  UI = 'ui',
  API = 'api',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCode {
  // System errors (1000-1999)
  SYSTEM_INITIALIZATION_FAILED = 1001,
  SYSTEM_SERVICE_UNAVAILABLE = 1002,
  SYSTEM_RESOURCE_EXHAUSTED = 1003,
  SYSTEM_TIMEOUT = 1004,
  SYSTEM_CONFIGURATION_ERROR = 1005,

  // Network errors (2000-2999)
  NETWORK_CONNECTION_FAILED = 2001,
  NETWORK_TIMEOUT = 2002,
  NETWORK_UNAUTHORIZED = 2003,
  NETWORK_FORBIDDEN = 2004,
  NETWORK_NOT_FOUND = 2005,
  NETWORK_SERVER_ERROR = 2006,

  // Storage errors (3000-3999)
  STORAGE_QUOTA_EXCEEDED = 3001,
  STORAGE_ACCESS_DENIED = 3002,
  STORAGE_CORRUPTION = 3003,
  STORAGE_SYNC_FAILED = 3004,
  STORAGE_MIGRATION_FAILED = 3005,

  // Permission errors (4000-4999)
  PERMISSION_DENIED = 4001,
  PERMISSION_TABS_REQUIRED = 4002,
  PERMISSION_BOOKMARKS_REQUIRED = 4003,
  PERMISSION_STORAGE_REQUIRED = 4004,
  PERMISSION_HOST_REQUIRED = 4005,

  // Validation errors (5000-5999)
  VALIDATION_INVALID_INPUT = 5001,
  VALIDATION_REQUIRED_FIELD = 5002,
  VALIDATION_FORMAT_ERROR = 5003,
  VALIDATION_RANGE_ERROR = 5004,
  VALIDATION_TYPE_ERROR = 5005,

  // UI errors (6000-6999)
  UI_COMPONENT_NOT_FOUND = 6001,
  UI_RENDER_FAILED = 6002,
  UI_EVENT_HANDLER_ERROR = 6003,
  UI_ANIMATION_ERROR = 6004,
  UI_LAYOUT_ERROR = 6005,

  // API errors (7000-7999)
  API_CHROME_TABS_ERROR = 7001,
  API_CHROME_BOOKMARKS_ERROR = 7002,
  API_CHROME_STORAGE_ERROR = 7003,
  API_CHROME_RUNTIME_ERROR = 7004,
  API_METHOD_NOT_SUPPORTED = 7005,

  // Unknown errors (9000-9999)
  UNKNOWN_ERROR = 9001,
  UNEXPECTED_ERROR = 9002
}

export interface ErrorContext {
  timestamp: number;
  userAgent?: string;
  url?: string;
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface ErrorDetails {
  code: ErrorCode;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  description?: string;
  cause?: Error;
  context?: ErrorContext;
  stack?: string;
  recoverable?: boolean;
  retryable?: boolean;
  userMessage?: string;
  helpUrl?: string;
}

export interface ErrorRecoveryStrategy {
  type: 'retry' | 'fallback' | 'ignore' | 'reload' | 'reset';
  maxAttempts?: number;
  delay?: number;
  fallbackAction?: () => Promise<void>;
  condition?: (error: SidebarError) => boolean;
}

export interface ErrorReportData {
  errorId: string;
  timestamp: number;
  code: ErrorCode;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  context?: ErrorContext;
  userAgent: string;
  extensionVersion: string;
  chromeVersion: string;
  os: string;
  frequency: number;
  firstOccurrence: number;
  lastOccurrence: number;
}

/**
 * Base Sidebar Error Class
 */
export class SidebarError extends Error {
  public readonly code: ErrorCode;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly description?: string;
  public readonly cause?: Error;
  public readonly context?: ErrorContext;
  public readonly recoverable: boolean;
  public readonly retryable: boolean;
  public readonly userMessage?: string;
  public readonly helpUrl?: string;
  public readonly timestamp: number;

  constructor(details: ErrorDetails) {
    super(details.message);
    
    this.name = 'SidebarError';
    this.code = details.code;
    this.category = details.category;
    this.severity = details.severity;
    this.description = details.description;
    this.cause = details.cause;
    this.context = details.context;
    this.recoverable = details.recoverable ?? true;
    this.retryable = details.retryable ?? false;
    this.userMessage = details.userMessage;
    this.helpUrl = details.helpUrl;
    this.timestamp = Date.now();

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SidebarError);
    }

    // Include cause stack if available
    if (details.cause && details.cause.stack) {
      this.stack = `${this.stack}\nCaused by: ${details.cause.stack}`;
    }
  }

  /**
   * Convert error to JSON for serialization
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      code: this.code,
      category: this.category,
      severity: this.severity,
      message: this.message,
      description: this.description,
      context: this.context,
      recoverable: this.recoverable,
      retryable: this.retryable,
      userMessage: this.userMessage,
      helpUrl: this.helpUrl,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }

  /**
   * Create error from JSON
   */
  static fromJSON(data: any): SidebarError {
    return new SidebarError({
      code: data.code,
      category: data.category,
      severity: data.severity,
      message: data.message,
      description: data.description,
      context: data.context,
      recoverable: data.recoverable,
      retryable: data.retryable,
      userMessage: data.userMessage,
      helpUrl: data.helpUrl
    });
  }
}

/**
 * System Error
 */
export class SystemError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      message,
      cause,
      context,
      recoverable: false,
      retryable: true,
      userMessage: '系统出现错误，请稍后重试'
    });
    this.name = 'SystemError';
  }
}

/**
 * Network Error
 */
export class NetworkError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      message,
      cause,
      context,
      recoverable: true,
      retryable: true,
      userMessage: '网络连接出现问题，请检查网络设置'
    });
    this.name = 'NetworkError';
  }
}

/**
 * Storage Error
 */
export class StorageError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.STORAGE,
      severity: ErrorSeverity.HIGH,
      message,
      cause,
      context,
      recoverable: true,
      retryable: false,
      userMessage: '数据存储出现问题，请检查存储空间'
    });
    this.name = 'StorageError';
  }
}

/**
 * Permission Error
 */
export class PermissionError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.PERMISSION,
      severity: ErrorSeverity.CRITICAL,
      message,
      cause,
      context,
      recoverable: false,
      retryable: false,
      userMessage: '缺少必要的权限，请检查扩展设置',
      helpUrl: 'chrome://extensions/'
    });
    this.name = 'PermissionError';
  }
}

/**
 * Validation Error
 */
export class ValidationError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW,
      message,
      cause,
      context,
      recoverable: true,
      retryable: false,
      userMessage: '输入的数据格式不正确，请检查后重试'
    });
    this.name = 'ValidationError';
  }
}

/**
 * UI Error
 */
export class UIError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.UI,
      severity: ErrorSeverity.MEDIUM,
      message,
      cause,
      context,
      recoverable: true,
      retryable: true,
      userMessage: '界面显示出现问题，请刷新页面重试'
    });
    this.name = 'UIError';
  }
}

/**
 * API Error
 */
export class APIError extends SidebarError {
  constructor(code: ErrorCode, message: string, cause?: Error, context?: ErrorContext) {
    super({
      code,
      category: ErrorCategory.API,
      severity: ErrorSeverity.HIGH,
      message,
      cause,
      context,
      recoverable: true,
      retryable: true,
      userMessage: 'Chrome API调用失败，请重启浏览器重试'
    });
    this.name = 'APIError';
  }
}

/**
 * Error Factory
 */
export class ErrorFactory {
  /**
   * Create error from Chrome runtime error
   */
  static fromChromeError(chromeError: any, context?: ErrorContext): SidebarError {
    const message = chromeError?.message || 'Chrome API error';
    
    if (message.includes('permissions')) {
      return new PermissionError(ErrorCode.PERMISSION_DENIED, message, chromeError, context);
    }
    
    if (message.includes('storage')) {
      return new StorageError(ErrorCode.STORAGE_ACCESS_DENIED, message, chromeError, context);
    }
    
    if (message.includes('tabs')) {
      return new APIError(ErrorCode.API_CHROME_TABS_ERROR, message, chromeError, context);
    }
    
    if (message.includes('bookmarks')) {
      return new APIError(ErrorCode.API_CHROME_BOOKMARKS_ERROR, message, chromeError, context);
    }
    
    return new SystemError(ErrorCode.UNKNOWN_ERROR, message, chromeError, context);
  }

  /**
   * Create error from generic error
   */
  static fromError(error: Error, context?: ErrorContext): SidebarError {
    if (error instanceof SidebarError) {
      return error;
    }

    // Try to categorize based on error message
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return new NetworkError(ErrorCode.NETWORK_CONNECTION_FAILED, error.message, error, context);
    }
    
    if (message.includes('storage') || message.includes('quota')) {
      return new StorageError(ErrorCode.STORAGE_ACCESS_DENIED, error.message, error, context);
    }
    
    if (message.includes('permission') || message.includes('denied')) {
      return new PermissionError(ErrorCode.PERMISSION_DENIED, error.message, error, context);
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return new ValidationError(ErrorCode.VALIDATION_INVALID_INPUT, error.message, error, context);
    }
    
    return new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: error.message,
      cause: error,
      context,
      recoverable: true,
      retryable: false,
      userMessage: '发生未知错误，请重试'
    });
  }

  /**
   * Create validation error
   */
  static createValidationError(field: string, value: any, rule: string, context?: ErrorContext): ValidationError {
    return new ValidationError(
      ErrorCode.VALIDATION_INVALID_INPUT,
      `Validation failed for field '${field}' with value '${value}': ${rule}`,
      undefined,
      context
    );
  }

  /**
   * Create permission error
   */
  static createPermissionError(permission: string, context?: ErrorContext): PermissionError {
    const codeMap: Record<string, ErrorCode> = {
      'tabs': ErrorCode.PERMISSION_TABS_REQUIRED,
      'bookmarks': ErrorCode.PERMISSION_BOOKMARKS_REQUIRED,
      'storage': ErrorCode.PERMISSION_STORAGE_REQUIRED,
      'host': ErrorCode.PERMISSION_HOST_REQUIRED
    };

    const code = codeMap[permission] || ErrorCode.PERMISSION_DENIED;
    
    return new PermissionError(
      code,
      `Missing required permission: ${permission}`,
      undefined,
      context
    );
  }

  /**
   * Create storage error
   */
  static createStorageError(operation: string, cause?: Error, context?: ErrorContext): StorageError {
    const codeMap: Record<string, ErrorCode> = {
      'quota': ErrorCode.STORAGE_QUOTA_EXCEEDED,
      'access': ErrorCode.STORAGE_ACCESS_DENIED,
      'corruption': ErrorCode.STORAGE_CORRUPTION,
      'sync': ErrorCode.STORAGE_SYNC_FAILED,
      'migration': ErrorCode.STORAGE_MIGRATION_FAILED
    };

    const code = codeMap[operation] || ErrorCode.STORAGE_ACCESS_DENIED;
    
    return new StorageError(
      code,
      `Storage operation failed: ${operation}`,
      cause,
      context
    );
  }
}