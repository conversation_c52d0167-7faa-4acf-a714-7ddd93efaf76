/**
 * <PERSON><PERSON><PERSON> Handler Tests
 */

import { ErrorHandler } from '../ErrorHandler';
import { 
  SidebarError, 
  ErrorCode, 
  ErrorCategory, 
  ErrorSeverity,
  SystemError,
  NetworkError,
  StorageError,
  PermissionError,
  ValidationError,
  UIError,
  APIError
} from '../ErrorTypes';

// Mock Chrome API
const mockChrome = {
  runtime: {
    lastError: null,
    sendMessage: jest.fn(),
    getManifest: jest.fn(() => ({ version: '1.0.0' }))
  },
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn()
    }
  }
};

(global as any).chrome = mockChrome;
(global as any).navigator = {
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124',
  platform: 'Win32'
};

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;

  beforeEach(() => {
    // Reset singleton
    (ErrorHandler as any).instance = undefined;
    errorHandler = ErrorHandler.getInstance();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = ErrorHandler.getInstance();
      const instance2 = ErrorHandler.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('should initialize with default options', () => {
      const handler = ErrorHandler.getInstance();
      expect(handler).toBeInstanceOf(ErrorHandler);
    });

    test('should initialize with custom options', () => {
      const options = {
        enableReporting: false,
        enableRecovery: false,
        maxRetryAttempts: 5
      };
      
      const handler = ErrorHandler.getInstance(options);
      expect(handler).toBeInstanceOf(ErrorHandler);
    });
  });

  describe('Error Handling', () => {
    test('should handle SidebarError', async () => {
      const error = new SidebarError({
        code: ErrorCode.SYSTEM_INITIALIZATION_FAILED,
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.HIGH,
        message: 'System initialization failed',
        recoverable: true
      });

      const result = await errorHandler.handleError(error);
      
      expect(result.success).toBeDefined();
      expect(result.attempts).toBeDefined();
    });

    test('should handle generic Error', async () => {
      const error = new Error('Generic error message');
      
      const result = await errorHandler.handleError(error);
      
      expect(result.success).toBeDefined();
      expect(result.attempts).toBeDefined();
    });

    test('should handle Chrome runtime error', async () => {
      mockChrome.runtime.lastError = { message: 'Permission denied' };
      
      const error = new Error('Chrome API error');
      const result = await errorHandler.handleError(error);
      
      expect(result.success).toBeDefined();
      expect(result.attempts).toBeDefined();
      
      mockChrome.runtime.lastError = null;
    });

    test('should add context to error', async () => {
      const error = new Error('Test error');
      const context = {
        timestamp: Date.now(),
        component: 'test-component',
        action: 'test-action'
      };

      await errorHandler.handleError(error, context);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].context).toMatchObject(context);
    });
  });

  describe('Error Listeners', () => {
    test('should add and notify listeners', async () => {
      const listener = jest.fn();
      errorHandler.addListener(listener);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Test error'
      });

      await errorHandler.handleError(error);
      
      expect(listener).toHaveBeenCalledWith(expect.any(SidebarError));
    });

    test('should remove listeners', async () => {
      const listener = jest.fn();
      errorHandler.addListener(listener);
      errorHandler.removeListener(listener);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Test error'
      });

      await errorHandler.handleError(error);
      
      expect(listener).not.toHaveBeenCalled();
    });

    test('should handle listener errors gracefully', async () => {
      const faultyListener = jest.fn(() => {
        throw new Error('Listener error');
      });
      
      errorHandler.addListener(faultyListener);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Test error'
      });

      // Should not throw
      await expect(errorHandler.handleError(error)).resolves.toBeDefined();
    });
  });

  describe('Recovery Strategies', () => {
    test('should add custom recovery strategy', async () => {
      const strategy = {
        type: 'retry' as const,
        maxAttempts: 2,
        delay: 100
      };

      errorHandler.addRecoveryStrategy(ErrorCode.NETWORK_CONNECTION_FAILED, strategy);

      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const result = await errorHandler.handleError(error);
      expect(result.attempts).toBeGreaterThan(0);
    });

    test('should attempt retry recovery', async () => {
      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const result = await errorHandler.handleError(error);
      expect(result.recoveryAction).toBe('retry');
    });

    test('should attempt fallback recovery', async () => {
      const fallbackAction = jest.fn().mockResolvedValue(undefined);
      const strategy = {
        type: 'fallback' as const,
        fallbackAction
      };

      errorHandler.addRecoveryStrategy(ErrorCode.STORAGE_QUOTA_EXCEEDED, strategy);

      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const result = await errorHandler.handleError(error);
      expect(fallbackAction).toHaveBeenCalled();
      expect(result.recoveryAction).toBe('fallback');
    });
  });

  describe('Error Statistics', () => {
    test('should track error statistics', async () => {
      const error1 = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Error 1'
      });

      const error2 = new SidebarError({
        code: ErrorCode.SYSTEM_INITIALIZATION_FAILED,
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.HIGH,
        message: 'Error 2'
      });

      await errorHandler.handleError(error1);
      await errorHandler.handleError(error2);

      const stats = errorHandler.getErrorStatistics();
      
      expect(stats.totalErrors).toBe(2);
      expect(stats.errorCounts).toBeDefined();
      expect(stats.recentErrors).toHaveLength(2);
    });

    test('should get recent errors', async () => {
      const errors = [
        new SidebarError({
          code: ErrorCode.UNKNOWN_ERROR,
          category: ErrorCategory.UNKNOWN,
          severity: ErrorSeverity.LOW,
          message: 'Error 1'
        }),
        new SidebarError({
          code: ErrorCode.UNKNOWN_ERROR,
          category: ErrorCategory.UNKNOWN,
          severity: ErrorSeverity.LOW,
          message: 'Error 2'
        })
      ];

      for (const error of errors) {
        await errorHandler.handleError(error);
      }

      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors).toHaveLength(1);
      expect(recentErrors[0].message).toBe('Error 2');
    });

    test('should clear error history', async () => {
      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Test error'
      });

      await errorHandler.handleError(error);
      
      let stats = errorHandler.getErrorStatistics();
      expect(stats.totalErrors).toBe(1);

      errorHandler.clearHistory();
      
      stats = errorHandler.getErrorStatistics();
      expect(stats.totalErrors).toBe(0);
    });
  });

  describe('Error Filtering', () => {
    test('should ignore frequent low severity errors', async () => {
      const error = new ValidationError(
        ErrorCode.VALIDATION_INVALID_INPUT,
        'Validation failed'
      );

      // Simulate many occurrences
      for (let i = 0; i < 15; i++) {
        await errorHandler.handleError(error);
      }

      const shouldIgnore = errorHandler.shouldIgnoreError(error);
      expect(shouldIgnore).toBe(true);
    });

    test('should not ignore critical errors', async () => {
      const error = new PermissionError(
        ErrorCode.PERMISSION_DENIED,
        'Permission denied'
      );

      // Even with many occurrences
      for (let i = 0; i < 15; i++) {
        await errorHandler.handleError(error);
      }

      const shouldIgnore = errorHandler.shouldIgnoreError(error);
      expect(shouldIgnore).toBe(false);
    });
  });

  describe('Error Conversion', () => {
    test('should convert network errors', async () => {
      const error = new Error('Network connection failed');
      
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].category).toBe(ErrorCategory.NETWORK);
    });

    test('should convert storage errors', async () => {
      const error = new Error('Storage quota exceeded');
      
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].category).toBe(ErrorCategory.STORAGE);
    });

    test('should convert permission errors', async () => {
      const error = new Error('Permission denied');
      
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].category).toBe(ErrorCategory.PERMISSION);
    });

    test('should convert validation errors', async () => {
      const error = new Error('Invalid input provided');
      
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].category).toBe(ErrorCategory.VALIDATION);
    });
  });

  describe('Chrome API Error Handling', () => {
    test('should handle tabs API errors', async () => {
      mockChrome.runtime.lastError = { message: 'tabs API error' };
      
      const error = new Error('Chrome API error');
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].code).toBe(ErrorCode.API_CHROME_TABS_ERROR);
      
      mockChrome.runtime.lastError = null;
    });

    test('should handle bookmarks API errors', async () => {
      mockChrome.runtime.lastError = { message: 'bookmarks API error' };
      
      const error = new Error('Chrome API error');
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].code).toBe(ErrorCode.API_CHROME_BOOKMARKS_ERROR);
      
      mockChrome.runtime.lastError = null;
    });

    test('should handle storage API errors', async () => {
      mockChrome.runtime.lastError = { message: 'storage API error' };
      
      const error = new Error('Chrome API error');
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].code).toBe(ErrorCode.API_CHROME_STORAGE_ERROR);
      
      mockChrome.runtime.lastError = null;
    });

    test('should handle permission API errors', async () => {
      mockChrome.runtime.lastError = { message: 'permissions API error' };
      
      const error = new Error('Chrome API error');
      await errorHandler.handleError(error);
      
      const recentErrors = errorHandler.getRecentErrors(1);
      expect(recentErrors[0].code).toBe(ErrorCode.PERMISSION_DENIED);
      
      mockChrome.runtime.lastError = null;
    });
  });

  describe('Recovery Execution', () => {
    test('should execute reload recovery', async () => {
      mockChrome.runtime.sendMessage.mockResolvedValue(undefined);
      
      const strategy = {
        type: 'reload' as const
      };

      errorHandler.addRecoveryStrategy(ErrorCode.UI_RENDER_FAILED, strategy);

      const error = new UIError(
        ErrorCode.UI_RENDER_FAILED,
        'UI render failed'
      );

      const result = await errorHandler.handleError(error);
      expect(result.recoveryAction).toBe('reload');
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'RELOAD_SIDEBAR'
      });
    });

    test('should execute reset recovery', async () => {
      mockChrome.runtime.sendMessage.mockResolvedValue(undefined);
      
      const strategy = {
        type: 'reset' as const
      };

      errorHandler.addRecoveryStrategy(ErrorCode.SYSTEM_INITIALIZATION_FAILED, strategy);

      const error = new SystemError(
        ErrorCode.SYSTEM_INITIALIZATION_FAILED,
        'System initialization failed'
      );

      const result = await errorHandler.handleError(error);
      expect(result.recoveryAction).toBe('reset');
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'RESET_TO_SAFE_STATE'
      });
    });

    test('should handle recovery failures', async () => {
      mockChrome.runtime.sendMessage.mockRejectedValue(new Error('Recovery failed'));
      
      const strategy = {
        type: 'reload' as const
      };

      errorHandler.addRecoveryStrategy(ErrorCode.UI_RENDER_FAILED, strategy);

      const error = new UIError(
        ErrorCode.UI_RENDER_FAILED,
        'UI render failed'
      );

      const result = await errorHandler.handleError(error);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Retry Logic', () => {
    test('should respect max retry attempts', async () => {
      const strategy = {
        type: 'retry' as const,
        maxAttempts: 2,
        delay: 10
      };

      errorHandler.addRecoveryStrategy(ErrorCode.NETWORK_TIMEOUT, strategy);

      const error = new NetworkError(
        ErrorCode.NETWORK_TIMEOUT,
        'Network timeout'
      );

      // First attempt
      let result = await errorHandler.handleError(error);
      expect(result.attempts).toBe(1);

      // Second attempt
      result = await errorHandler.handleError(error);
      expect(result.attempts).toBe(2);

      // Third attempt should not retry
      result = await errorHandler.handleError(error);
      expect(result.success).toBe(false);
    });

    test('should use exponential backoff for retries', async () => {
      const strategy = {
        type: 'retry' as const,
        maxAttempts: 3,
        delay: 100
      };

      errorHandler.addRecoveryStrategy(ErrorCode.NETWORK_CONNECTION_FAILED, strategy);

      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const startTime = Date.now();
      await errorHandler.handleError(error);
      const endTime = Date.now();

      // Should have some delay (though minimal in test)
      expect(endTime - startTime).toBeGreaterThanOrEqual(0);
    });
  });
});