/**
 * Error Recovery Service Tests
 */

import { ErrorRecoveryService } from '../ErrorRecovery';
import { 
  SidebarError, 
  ErrorCode, 
  ErrorCategory, 
  ErrorSeverity,
  StorageError,
  NetworkError,
  PermissionError,
  UIError,
  SystemError
} from '../ErrorTypes';

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: jest.fn(),
    reload: jest.fn()
  },
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn()
    },
    local: {
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn(),
      remove: jest.fn()
    }
  },
  permissions: {
    request: jest.fn()
  },
  tabs: {
    query: jest.fn(),
    create: jest.fn()
  },
  bookmarks: {
    getTree: jest.fn()
  }
};

(global as any).chrome = mockChrome;
(global as any).fetch = jest.fn();

describe('ErrorRecoveryService', () => {
  let recoveryService: ErrorRecoveryService;

  beforeEach(() => {
    // Reset singleton
    (ErrorRecoveryService as any).instance = undefined;
    recoveryService = ErrorRecoveryService.getInstance();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = ErrorRecoveryService.getInstance();
      const instance2 = ErrorRecoveryService.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('should setup default recovery actions', () => {
      const service = ErrorRecoveryService.getInstance();
      expect(service).toBeInstanceOf(ErrorRecoveryService);
    });
  });

  describe('Recovery Plan Creation', () => {
    test('should create recovery plan for storage error', () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.error).toBe(error);
      expect(plan.actions.length).toBeGreaterThan(0);
      expect(plan.executed).toBe(false);
      expect(plan.success).toBe(false);
      expect(plan.timestamp).toBeGreaterThan(0);
    });

    test('should create recovery plan for network error', () => {
      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.actions.length).toBeGreaterThan(0);
      expect(plan.actions.some(action => action.id === 'retry_network')).toBe(true);
    });

    test('should create recovery plan for permission error', () => {
      const error = new PermissionError(
        ErrorCode.PERMISSION_DENIED,
        'Permission denied'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.actions.length).toBeGreaterThan(0);
      expect(plan.actions.some(action => action.id === 'request_permissions')).toBe(true);
    });

    test('should create recovery plan for UI error', () => {
      const error = new UIError(
        ErrorCode.UI_RENDER_FAILED,
        'UI render failed'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.actions.length).toBeGreaterThan(0);
      expect(plan.actions.some(action => action.id === 'refresh_ui')).toBe(true);
    });

    test('should auto-select best action', () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.selectedAction).toBeDefined();
      expect(plan.selectedAction).toBe(plan.actions[0]);
    });

    test('should sort actions by priority', () => {
      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const plan = recoveryService.createRecoveryPlan(error);

      // Check that actions are sorted by priority (descending)
      for (let i = 1; i < plan.actions.length; i++) {
        expect(plan.actions[i - 1].priority).toBeGreaterThanOrEqual(plan.actions[i].priority);
      }
    });
  });

  describe('Recovery Plan Execution', () => {
    test('should execute successful recovery action', async () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      mockChrome.storage.local.get.mockResolvedValue({
        cache_old: 'data',
        temp_data: 'data',
        normal_data: 'data'
      });
      mockChrome.storage.local.remove.mockResolvedValue(undefined);

      const plan = recoveryService.createRecoveryPlan(error);
      const result = await recoveryService.executeRecoveryPlan(plan);

      expect(result).toBe(true);
      expect(plan.executed).toBe(true);
      expect(plan.success).toBe(true);
    });

    test('should handle recovery action failure', async () => {
      const error = new PermissionError(
        ErrorCode.PERMISSION_DENIED,
        'Permission denied'
      );

      mockChrome.permissions.request.mockRejectedValue(new Error('Permission request failed'));

      const plan = recoveryService.createRecoveryPlan(error);
      const result = await recoveryService.executeRecoveryPlan(plan);

      expect(result).toBe(false);
      expect(plan.executed).toBe(true);
      expect(plan.success).toBe(false);
    });

    test('should try next action if first fails', async () => {
      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      // Mock first action to fail, second to succeed
      (global.fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(new Response());

      const plan = recoveryService.createRecoveryPlan(error);
      
      // Ensure we have multiple actions
      if (plan.actions.length > 1) {
        const result = await recoveryService.executeRecoveryPlan(plan);
        expect(result).toBe(true);
      }
    });

    test('should not execute if no action selected', async () => {
      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Unknown error'
      });

      const plan = recoveryService.createRecoveryPlan(error);
      plan.selectedAction = undefined;

      const result = await recoveryService.executeRecoveryPlan(plan);

      expect(result).toBe(false);
      expect(plan.executed).toBe(false);
    });

    test('should prevent concurrent recovery execution', async () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const plan1 = recoveryService.createRecoveryPlan(error);
      const plan2 = recoveryService.createRecoveryPlan(error);

      // Start both executions simultaneously
      const promise1 = recoveryService.executeRecoveryPlan(plan1);
      const promise2 = recoveryService.executeRecoveryPlan(plan2);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      // One should succeed, one should fail due to concurrent execution
      expect(result1 !== result2).toBe(true);
    });
  });

  describe('Custom Recovery Actions', () => {
    test('should add custom recovery action', async () => {
      const customAction = {
        id: 'custom_action',
        name: 'Custom Action',
        description: 'Custom recovery action',
        execute: jest.fn().mockResolvedValue(true),
        priority: 10
      };

      recoveryService.addRecoveryAction(ErrorCode.UNKNOWN_ERROR, customAction);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: 'Unknown error'
      });

      const plan = recoveryService.createRecoveryPlan(error);

      expect(plan.actions.some(action => action.id === 'custom_action')).toBe(true);
    });

    test('should execute custom recovery action', async () => {
      const customAction = {
        id: 'custom_action',
        name: 'Custom Action',
        description: 'Custom recovery action',
        execute: jest.fn().mockResolvedValue(true),
        priority: 10
      };

      recoveryService.addRecoveryAction(ErrorCode.UNKNOWN_ERROR, customAction);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: 'Unknown error'
      });

      const plan = recoveryService.createRecoveryPlan(error);
      plan.selectedAction = customAction;

      const result = await recoveryService.executeRecoveryPlan(plan);

      expect(result).toBe(true);
      expect(customAction.execute).toHaveBeenCalled();
    });

    test('should respect action conditions', async () => {
      const conditionalAction = {
        id: 'conditional_action',
        name: 'Conditional Action',
        description: 'Action with condition',
        execute: jest.fn().mockResolvedValue(true),
        condition: (error: SidebarError) => error.severity === ErrorSeverity.HIGH,
        priority: 10
      };

      recoveryService.addRecoveryAction(ErrorCode.UNKNOWN_ERROR, conditionalAction);

      // Test with low severity error (should not include action)
      const lowSeverityError = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.LOW,
        message: 'Low severity error'
      });

      const lowSeverityPlan = recoveryService.createRecoveryPlan(lowSeverityError);
      expect(lowSeverityPlan.actions.some(action => action.id === 'conditional_action')).toBe(false);

      // Test with high severity error (should include action)
      const highSeverityError = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.HIGH,
        message: 'High severity error'
      });

      const highSeverityPlan = recoveryService.createRecoveryPlan(highSeverityError);
      expect(highSeverityPlan.actions.some(action => action.id === 'conditional_action')).toBe(true);
    });
  });

  describe('Safe State Management', () => {
    test('should save safe state', async () => {
      mockChrome.storage.sync.get.mockResolvedValue({ setting1: 'value1' });
      mockChrome.tabs.query.mockResolvedValue([
        { id: 1, url: 'https://example.com', title: 'Example' }
      ]);
      mockChrome.bookmarks.getTree.mockResolvedValue([
        { id: '1', title: 'Bookmark 1', url: 'https://example.com' }
      ]);

      await recoveryService.saveSafeState();

      const safeStates = recoveryService.getSafeStates();
      expect(safeStates.length).toBe(1);
      expect(safeStates[0].settings).toEqual({ setting1: 'value1' });
      expect(safeStates[0].tabs.length).toBe(1);
      expect(safeStates[0].bookmarks.length).toBe(1);
    });

    test('should restore to safe state', async () => {
      // First save a safe state
      mockChrome.storage.sync.get.mockResolvedValue({ setting1: 'value1' });
      mockChrome.tabs.query.mockResolvedValue([]);
      mockChrome.bookmarks.getTree.mockResolvedValue([]);
      mockChrome.storage.sync.clear.mockResolvedValue(undefined);
      mockChrome.storage.sync.set.mockResolvedValue(undefined);

      await recoveryService.saveSafeState();

      // Then restore it
      const result = await recoveryService.restoreToSafeState();

      expect(result).toBe(true);
      expect(mockChrome.storage.sync.clear).toHaveBeenCalled();
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith({ setting1: 'value1' });
    });

    test('should handle restore failure when no safe state exists', async () => {
      const result = await recoveryService.restoreToSafeState();

      expect(result).toBe(false);
    });

    test('should limit number of safe states', async () => {
      mockChrome.storage.sync.get.mockResolvedValue({});
      mockChrome.tabs.query.mockResolvedValue([]);
      mockChrome.bookmarks.getTree.mockResolvedValue([]);

      // Save more than max safe states
      for (let i = 0; i < 10; i++) {
        await recoveryService.saveSafeState();
      }

      const safeStates = recoveryService.getSafeStates();
      expect(safeStates.length).toBeLessThanOrEqual(5); // maxSafeStates = 5
    });
  });

  describe('Recovery History', () => {
    test('should track recovery history', async () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      mockChrome.storage.local.get.mockResolvedValue({});
      mockChrome.storage.local.remove.mockResolvedValue(undefined);

      const plan = recoveryService.createRecoveryPlan(error);
      await recoveryService.executeRecoveryPlan(plan);

      const history = recoveryService.getRecoveryHistory();
      expect(history.length).toBe(1);
      expect(history[0].error).toBe(error);
      expect(history[0].executed).toBe(true);
    });

    test('should clear recovery history', async () => {
      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const plan = recoveryService.createRecoveryPlan(error);
      await recoveryService.executeRecoveryPlan(plan);

      let history = recoveryService.getRecoveryHistory();
      expect(history.length).toBe(1);

      recoveryService.clearRecoveryHistory();

      history = recoveryService.getRecoveryHistory();
      expect(history.length).toBe(0);
    });
  });

  describe('Default Recovery Actions', () => {
    test('should execute clear old data action', async () => {
      mockChrome.storage.local.get.mockResolvedValue({
        cache_old: 'data',
        temp_data: 'data',
        normal_data: 'data'
      });
      mockChrome.storage.local.remove.mockResolvedValue(undefined);

      const error = new StorageError(
        ErrorCode.STORAGE_QUOTA_EXCEEDED,
        'Storage quota exceeded'
      );

      const plan = recoveryService.createRecoveryPlan(error);
      const clearAction = plan.actions.find(action => action.id === 'clear_old_data');

      expect(clearAction).toBeDefined();

      if (clearAction) {
        const result = await clearAction.execute();
        expect(result).toBe(true);
        expect(mockChrome.storage.local.remove).toHaveBeenCalledWith(['cache_old', 'temp_data']);
      }
    });

    test('should execute request permissions action', async () => {
      mockChrome.permissions.request.mockResolvedValue(true);

      const error = new PermissionError(
        ErrorCode.PERMISSION_DENIED,
        'Permission denied'
      );

      const plan = recoveryService.createRecoveryPlan(error);
      const permissionAction = plan.actions.find(action => action.id === 'request_permissions');

      expect(permissionAction).toBeDefined();

      if (permissionAction) {
        const result = await permissionAction.execute();
        expect(result).toBe(true);
        expect(mockChrome.permissions.request).toHaveBeenCalledWith({
          permissions: ['tabs', 'bookmarks', 'storage'],
          origins: ['<all_urls>']
        });
      }
    });

    test('should execute retry connection action', async () => {
      (global.fetch as jest.Mock).mockResolvedValue(new Response());

      const error = new NetworkError(
        ErrorCode.NETWORK_CONNECTION_FAILED,
        'Network connection failed'
      );

      const plan = recoveryService.createRecoveryPlan(error);
      const retryAction = plan.actions.find(action => action.id === 'retry_connection');

      expect(retryAction).toBeDefined();

      if (retryAction) {
        const result = await retryAction.execute();
        expect(result).toBe(true);
      }
    });

    test('should execute reinitialize system action', async () => {
      mockChrome.runtime.sendMessage.mockResolvedValue(undefined);

      const error = new SystemError(
        ErrorCode.SYSTEM_INITIALIZATION_FAILED,
        'System initialization failed'
      );

      const plan = recoveryService.createRecoveryPlan(error);
      const reinitAction = plan.actions.find(action => action.id === 'reinitialize_system');

      expect(reinitAction).toBeDefined();

      if (reinitAction) {
        const result = await reinitAction.execute();
        expect(result).toBe(true);
        expect(mockChrome.runtime.sendMessage).toHaveBeenCalledWith({
          type: 'REINITIALIZE_SYSTEM'
        });
      }
    });
  });

  describe('Error Handling in Recovery', () => {
    test('should handle errors during safe state saving', async () => {
      mockChrome.storage.sync.get.mockRejectedValue(new Error('Storage error'));

      // Should not throw
      await expect(recoveryService.saveSafeState()).resolves.not.toThrow();
    });

    test('should handle errors during safe state restoration', async () => {
      // Save a safe state first
      mockChrome.storage.sync.get.mockResolvedValue({ setting1: 'value1' });
      mockChrome.tabs.query.mockResolvedValue([]);
      mockChrome.bookmarks.getTree.mockResolvedValue([]);
      await recoveryService.saveSafeState();

      // Then make restoration fail
      mockChrome.storage.sync.clear.mockRejectedValue(new Error('Storage error'));

      const result = await recoveryService.restoreToSafeState();
      expect(result).toBe(false);
    });

    test('should handle errors during recovery action execution', async () => {
      const faultyAction = {
        id: 'faulty_action',
        name: 'Faulty Action',
        description: 'Action that throws error',
        execute: jest.fn().mockRejectedValue(new Error('Action failed')),
        priority: 10
      };

      recoveryService.addRecoveryAction(ErrorCode.UNKNOWN_ERROR, faultyAction);

      const error = new SidebarError({
        code: ErrorCode.UNKNOWN_ERROR,
        category: ErrorCategory.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: 'Unknown error'
      });

      const plan = recoveryService.createRecoveryPlan(error);
      plan.selectedAction = faultyAction;

      const result = await recoveryService.executeRecoveryPlan(plan);

      expect(result).toBe(false);
      expect(plan.executed).toBe(true);
      expect(plan.success).toBe(false);
    });
  });
});