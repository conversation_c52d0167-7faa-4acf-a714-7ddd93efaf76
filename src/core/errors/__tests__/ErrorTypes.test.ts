/**
 * Error Types Tests
 */

import {
  SidebarError,
  SystemError,
  NetworkError,
  StorageError,
  PermissionError,
  ValidationError,
  UIError,
  APIError,
  ErrorFactory,
  ErrorCode,
  ErrorCategory,
  ErrorSeverity
} from '../ErrorTypes';

describe('SidebarError', () => {
  test('should create basic error', () => {
    const error = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error'
    });

    expect(error.name).toBe('SidebarError');
    expect(error.code).toBe(ErrorCode.UNKNOWN_ERROR);
    expect(error.category).toBe(ErrorCategory.UNKNOWN);
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.message).toBe('Test error');
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(false);
    expect(error.timestamp).toBeGreaterThan(0);
  });

  test('should create error with all properties', () => {
    const context = {
      timestamp: Date.now(),
      component: 'test-component',
      action: 'test-action'
    };

    const cause = new Error('Original error');

    const error = new SidebarError({
      code: ErrorCode.SYSTEM_INITIALIZATION_FAILED,
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      message: 'System failed',
      description: 'Detailed description',
      cause,
      context,
      recoverable: false,
      retryable: true,
      userMessage: 'User friendly message',
      helpUrl: 'https://help.example.com'
    });

    expect(error.description).toBe('Detailed description');
    expect(error.cause).toBe(cause);
    expect(error.context).toBe(context);
    expect(error.recoverable).toBe(false);
    expect(error.retryable).toBe(true);
    expect(error.userMessage).toBe('User friendly message');
    expect(error.helpUrl).toBe('https://help.example.com');
  });

  test('should serialize to JSON', () => {
    const error = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error',
      description: 'Test description'
    });

    const json = error.toJSON();

    expect(json.name).toBe('SidebarError');
    expect(json.code).toBe(ErrorCode.UNKNOWN_ERROR);
    expect(json.category).toBe(ErrorCategory.UNKNOWN);
    expect(json.severity).toBe(ErrorSeverity.MEDIUM);
    expect(json.message).toBe('Test error');
    expect(json.description).toBe('Test description');
    expect(json.timestamp).toBeGreaterThan(0);
  });

  test('should create from JSON', () => {
    const data = {
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error',
      description: 'Test description',
      recoverable: false,
      retryable: true
    };

    const error = SidebarError.fromJSON(data);

    expect(error.code).toBe(ErrorCode.UNKNOWN_ERROR);
    expect(error.category).toBe(ErrorCategory.UNKNOWN);
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.message).toBe('Test error');
    expect(error.description).toBe('Test description');
    expect(error.recoverable).toBe(false);
    expect(error.retryable).toBe(true);
  });

  test('should include cause stack in error stack', () => {
    const cause = new Error('Original error');
    const error = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error',
      cause
    });

    expect(error.stack).toContain('Caused by:');
  });
});

describe('Specific Error Types', () => {
  test('should create SystemError', () => {
    const error = new SystemError(
      ErrorCode.SYSTEM_INITIALIZATION_FAILED,
      'System initialization failed'
    );

    expect(error.name).toBe('SystemError');
    expect(error.category).toBe(ErrorCategory.SYSTEM);
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.recoverable).toBe(false);
    expect(error.retryable).toBe(true);
    expect(error.userMessage).toBe('系统出现错误，请稍后重试');
  });

  test('should create NetworkError', () => {
    const error = new NetworkError(
      ErrorCode.NETWORK_CONNECTION_FAILED,
      'Network connection failed'
    );

    expect(error.name).toBe('NetworkError');
    expect(error.category).toBe(ErrorCategory.NETWORK);
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(true);
    expect(error.userMessage).toBe('网络连接出现问题，请检查网络设置');
  });

  test('should create StorageError', () => {
    const error = new StorageError(
      ErrorCode.STORAGE_QUOTA_EXCEEDED,
      'Storage quota exceeded'
    );

    expect(error.name).toBe('StorageError');
    expect(error.category).toBe(ErrorCategory.STORAGE);
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(false);
    expect(error.userMessage).toBe('数据存储出现问题，请检查存储空间');
  });

  test('should create PermissionError', () => {
    const error = new PermissionError(
      ErrorCode.PERMISSION_DENIED,
      'Permission denied'
    );

    expect(error.name).toBe('PermissionError');
    expect(error.category).toBe(ErrorCategory.PERMISSION);
    expect(error.severity).toBe(ErrorSeverity.CRITICAL);
    expect(error.recoverable).toBe(false);
    expect(error.retryable).toBe(false);
    expect(error.userMessage).toBe('缺少必要的权限，请检查扩展设置');
    expect(error.helpUrl).toBe('chrome://extensions/');
  });

  test('should create ValidationError', () => {
    const error = new ValidationError(
      ErrorCode.VALIDATION_INVALID_INPUT,
      'Invalid input'
    );

    expect(error.name).toBe('ValidationError');
    expect(error.category).toBe(ErrorCategory.VALIDATION);
    expect(error.severity).toBe(ErrorSeverity.LOW);
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(false);
    expect(error.userMessage).toBe('输入的数据格式不正确，请检查后重试');
  });

  test('should create UIError', () => {
    const error = new UIError(
      ErrorCode.UI_RENDER_FAILED,
      'UI render failed'
    );

    expect(error.name).toBe('UIError');
    expect(error.category).toBe(ErrorCategory.UI);
    expect(error.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(true);
    expect(error.userMessage).toBe('界面显示出现问题，请刷新页面重试');
  });

  test('should create APIError', () => {
    const error = new APIError(
      ErrorCode.API_CHROME_TABS_ERROR,
      'Chrome tabs API error'
    );

    expect(error.name).toBe('APIError');
    expect(error.category).toBe(ErrorCategory.API);
    expect(error.severity).toBe(ErrorSeverity.HIGH);
    expect(error.recoverable).toBe(true);
    expect(error.retryable).toBe(true);
    expect(error.userMessage).toBe('Chrome API调用失败，请重启浏览器重试');
  });
});

describe('ErrorFactory', () => {
  test('should create error from Chrome permission error', () => {
    const chromeError = { message: 'permissions denied' };
    const error = ErrorFactory.fromChromeError(chromeError);

    expect(error).toBeInstanceOf(PermissionError);
    expect(error.code).toBe(ErrorCode.PERMISSION_DENIED);
    expect(error.category).toBe(ErrorCategory.PERMISSION);
  });

  test('should create error from Chrome storage error', () => {
    const chromeError = { message: 'storage quota exceeded' };
    const error = ErrorFactory.fromChromeError(chromeError);

    expect(error).toBeInstanceOf(StorageError);
    expect(error.code).toBe(ErrorCode.STORAGE_ACCESS_DENIED);
    expect(error.category).toBe(ErrorCategory.STORAGE);
  });

  test('should create error from Chrome tabs error', () => {
    const chromeError = { message: 'tabs API error' };
    const error = ErrorFactory.fromChromeError(chromeError);

    expect(error).toBeInstanceOf(APIError);
    expect(error.code).toBe(ErrorCode.API_CHROME_TABS_ERROR);
    expect(error.category).toBe(ErrorCategory.API);
  });

  test('should create error from Chrome bookmarks error', () => {
    const chromeError = { message: 'bookmarks API error' };
    const error = ErrorFactory.fromChromeError(chromeError);

    expect(error).toBeInstanceOf(APIError);
    expect(error.code).toBe(ErrorCode.API_CHROME_BOOKMARKS_ERROR);
    expect(error.category).toBe(ErrorCategory.API);
  });

  test('should create system error for unknown Chrome error', () => {
    const chromeError = { message: 'unknown Chrome error' };
    const error = ErrorFactory.fromChromeError(chromeError);

    expect(error).toBeInstanceOf(SystemError);
    expect(error.code).toBe(ErrorCode.UNKNOWN_ERROR);
    expect(error.category).toBe(ErrorCategory.SYSTEM);
  });

  test('should create error from generic Error', () => {
    const genericError = new Error('Generic error message');
    const error = ErrorFactory.fromError(genericError);

    expect(error).toBeInstanceOf(SidebarError);
    expect(error.code).toBe(ErrorCode.UNKNOWN_ERROR);
    expect(error.category).toBe(ErrorCategory.UNKNOWN);
    expect(error.cause).toBe(genericError);
  });

  test('should return SidebarError as-is', () => {
    const sidebarError = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error'
    });

    const result = ErrorFactory.fromError(sidebarError);
    expect(result).toBe(sidebarError);
  });

  test('should categorize network errors', () => {
    const networkError = new Error('Network connection failed');
    const error = ErrorFactory.fromError(networkError);

    expect(error).toBeInstanceOf(NetworkError);
    expect(error.code).toBe(ErrorCode.NETWORK_CONNECTION_FAILED);
    expect(error.category).toBe(ErrorCategory.NETWORK);
  });

  test('should categorize storage errors', () => {
    const storageError = new Error('Storage quota exceeded');
    const error = ErrorFactory.fromError(storageError);

    expect(error).toBeInstanceOf(StorageError);
    expect(error.code).toBe(ErrorCode.STORAGE_ACCESS_DENIED);
    expect(error.category).toBe(ErrorCategory.STORAGE);
  });

  test('should categorize permission errors', () => {
    const permissionError = new Error('Permission denied');
    const error = ErrorFactory.fromError(permissionError);

    expect(error).toBeInstanceOf(PermissionError);
    expect(error.code).toBe(ErrorCode.PERMISSION_DENIED);
    expect(error.category).toBe(ErrorCategory.PERMISSION);
  });

  test('should categorize validation errors', () => {
    const validationError = new Error('Invalid input provided');
    const error = ErrorFactory.fromError(validationError);

    expect(error).toBeInstanceOf(ValidationError);
    expect(error.code).toBe(ErrorCode.VALIDATION_INVALID_INPUT);
    expect(error.category).toBe(ErrorCategory.VALIDATION);
  });

  test('should create validation error with details', () => {
    const error = ErrorFactory.createValidationError('email', 'invalid-email', 'must be valid email');

    expect(error).toBeInstanceOf(ValidationError);
    expect(error.code).toBe(ErrorCode.VALIDATION_INVALID_INPUT);
    expect(error.message).toContain('email');
    expect(error.message).toContain('invalid-email');
    expect(error.message).toContain('must be valid email');
  });

  test('should create permission error with permission type', () => {
    const error = ErrorFactory.createPermissionError('tabs');

    expect(error).toBeInstanceOf(PermissionError);
    expect(error.code).toBe(ErrorCode.PERMISSION_TABS_REQUIRED);
    expect(error.message).toContain('tabs');
  });

  test('should create storage error with operation type', () => {
    const cause = new Error('Quota exceeded');
    const error = ErrorFactory.createStorageError('quota', cause);

    expect(error).toBeInstanceOf(StorageError);
    expect(error.code).toBe(ErrorCode.STORAGE_QUOTA_EXCEEDED);
    expect(error.message).toContain('quota');
    expect(error.cause).toBe(cause);
  });

  test('should handle unknown permission types', () => {
    const error = ErrorFactory.createPermissionError('unknown');

    expect(error.code).toBe(ErrorCode.PERMISSION_DENIED);
  });

  test('should handle unknown storage operations', () => {
    const error = ErrorFactory.createStorageError('unknown');

    expect(error.code).toBe(ErrorCode.STORAGE_ACCESS_DENIED);
  });
});

describe('Error Context', () => {
  test('should include context in error', () => {
    const context = {
      timestamp: Date.now(),
      userAgent: 'test-agent',
      url: 'https://example.com',
      userId: 'user123',
      sessionId: 'session456',
      component: 'test-component',
      action: 'test-action',
      metadata: {
        key1: 'value1',
        key2: 'value2'
      }
    };

    const error = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error',
      context
    });

    expect(error.context).toBe(context);
    expect(error.context?.timestamp).toBe(context.timestamp);
    expect(error.context?.userAgent).toBe(context.userAgent);
    expect(error.context?.url).toBe(context.url);
    expect(error.context?.userId).toBe(context.userId);
    expect(error.context?.sessionId).toBe(context.sessionId);
    expect(error.context?.component).toBe(context.component);
    expect(error.context?.action).toBe(context.action);
    expect(error.context?.metadata).toBe(context.metadata);
  });

  test('should work without context', () => {
    const error = new SidebarError({
      code: ErrorCode.UNKNOWN_ERROR,
      category: ErrorCategory.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: 'Test error'
    });

    expect(error.context).toBeUndefined();
  });
});

describe('Error Codes', () => {
  test('should have system error codes in 1000-1999 range', () => {
    expect(ErrorCode.SYSTEM_INITIALIZATION_FAILED).toBeGreaterThanOrEqual(1000);
    expect(ErrorCode.SYSTEM_INITIALIZATION_FAILED).toBeLessThan(2000);
    expect(ErrorCode.SYSTEM_SERVICE_UNAVAILABLE).toBeGreaterThanOrEqual(1000);
    expect(ErrorCode.SYSTEM_SERVICE_UNAVAILABLE).toBeLessThan(2000);
  });

  test('should have network error codes in 2000-2999 range', () => {
    expect(ErrorCode.NETWORK_CONNECTION_FAILED).toBeGreaterThanOrEqual(2000);
    expect(ErrorCode.NETWORK_CONNECTION_FAILED).toBeLessThan(3000);
    expect(ErrorCode.NETWORK_TIMEOUT).toBeGreaterThanOrEqual(2000);
    expect(ErrorCode.NETWORK_TIMEOUT).toBeLessThan(3000);
  });

  test('should have storage error codes in 3000-3999 range', () => {
    expect(ErrorCode.STORAGE_QUOTA_EXCEEDED).toBeGreaterThanOrEqual(3000);
    expect(ErrorCode.STORAGE_QUOTA_EXCEEDED).toBeLessThan(4000);
    expect(ErrorCode.STORAGE_ACCESS_DENIED).toBeGreaterThanOrEqual(3000);
    expect(ErrorCode.STORAGE_ACCESS_DENIED).toBeLessThan(4000);
  });

  test('should have permission error codes in 4000-4999 range', () => {
    expect(ErrorCode.PERMISSION_DENIED).toBeGreaterThanOrEqual(4000);
    expect(ErrorCode.PERMISSION_DENIED).toBeLessThan(5000);
    expect(ErrorCode.PERMISSION_TABS_REQUIRED).toBeGreaterThanOrEqual(4000);
    expect(ErrorCode.PERMISSION_TABS_REQUIRED).toBeLessThan(5000);
  });

  test('should have validation error codes in 5000-5999 range', () => {
    expect(ErrorCode.VALIDATION_INVALID_INPUT).toBeGreaterThanOrEqual(5000);
    expect(ErrorCode.VALIDATION_INVALID_INPUT).toBeLessThan(6000);
    expect(ErrorCode.VALIDATION_REQUIRED_FIELD).toBeGreaterThanOrEqual(5000);
    expect(ErrorCode.VALIDATION_REQUIRED_FIELD).toBeLessThan(6000);
  });

  test('should have UI error codes in 6000-6999 range', () => {
    expect(ErrorCode.UI_COMPONENT_NOT_FOUND).toBeGreaterThanOrEqual(6000);
    expect(ErrorCode.UI_COMPONENT_NOT_FOUND).toBeLessThan(7000);
    expect(ErrorCode.UI_RENDER_FAILED).toBeGreaterThanOrEqual(6000);
    expect(ErrorCode.UI_RENDER_FAILED).toBeLessThan(7000);
  });

  test('should have API error codes in 7000-7999 range', () => {
    expect(ErrorCode.API_CHROME_TABS_ERROR).toBeGreaterThanOrEqual(7000);
    expect(ErrorCode.API_CHROME_TABS_ERROR).toBeLessThan(8000);
    expect(ErrorCode.API_CHROME_BOOKMARKS_ERROR).toBeGreaterThanOrEqual(7000);
    expect(ErrorCode.API_CHROME_BOOKMARKS_ERROR).toBeLessThan(8000);
  });

  test('should have unknown error codes in 9000-9999 range', () => {
    expect(ErrorCode.UNKNOWN_ERROR).toBeGreaterThanOrEqual(9000);
    expect(ErrorCode.UNKNOWN_ERROR).toBeLessThan(10000);
    expect(ErrorCode.UNEXPECTED_ERROR).toBeGreaterThanOrEqual(9000);
    expect(ErrorCode.UNEXPECTED_ERROR).toBeLessThan(10000);
  });
});