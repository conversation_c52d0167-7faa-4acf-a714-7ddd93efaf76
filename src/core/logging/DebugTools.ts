/**
 * Debug Tools - 调试工具和开发者工具集成
 */

import { Logger, LogLevel, LogEntry } from './Logger';
import { PerformanceMonitor, PerformanceMetric } from './PerformanceMonitor';
import { ErrorHandler } from '../errors/ErrorHandler';
import { SidebarError } from '../errors/ErrorTypes';

export interface DebugConfig {
  enabled: boolean;
  logLevel: LogLevel;
  showPerformanceMetrics: boolean;
  showErrorDetails: boolean;
  enableConsoleCommands: boolean;
  enableInspector: boolean;
  enableNetworkMonitoring: boolean;
  enableMemoryProfiling: boolean;
}

export interface DebugSession {
  id: string;
  startTime: number;
  endTime?: number;
  logs: LogEntry[];
  metrics: PerformanceMetric[];
  errors: SidebarError[];
  actions: DebugAction[];
}

export interface DebugAction {
  id: string;
  type: string;
  timestamp: number;
  data: any;
  result?: any;
  error?: string;
}

export interface InspectorData {
  component: string;
  props: Record<string, any>;
  state: Record<string, any>;
  events: Array<{ type: string; timestamp: number; data: any }>;
  performance: {
    renderTime: number;
    updateCount: number;
    lastUpdate: number;
  };
}

/**
 * Debug Tools Manager
 */
export class DebugTools {
  private static instance: DebugTools;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private errorHandler: ErrorHandler;
  private config: DebugConfig;
  private currentSession?: DebugSession;
  private inspectorData: Map<string, InspectorData> = new Map();
  private networkRequests: Array<{
    id: string;
    url: string;
    method: string;
    timestamp: number;
    duration?: number;
    status?: number;
    error?: string;
  }> = [];

  private constructor(config: Partial<DebugConfig> = {}) {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.errorHandler = ErrorHandler.getInstance();
    
    this.config = {
      enabled: false,
      logLevel: LogLevel.DEBUG,
      showPerformanceMetrics: true,
      showErrorDetails: true,
      enableConsoleCommands: true,
      enableInspector: true,
      enableNetworkMonitoring: true,
      enableMemoryProfiling: true,
      ...config
    };

    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<DebugConfig>): DebugTools {
    if (!DebugTools.instance) {
      DebugTools.instance = new DebugTools(config);
    }
    return DebugTools.instance;
  }

  /**
   * Enable debug mode
   */
  enable(): void {
    this.config.enabled = true;
    this.initialize();
    this.logger.info('debug', 'Debug mode enabled');
  }

  /**
   * Disable debug mode
   */
  disable(): void {
    this.config.enabled = false;
    this.cleanup();
    this.logger.info('debug', 'Debug mode disabled');
  }

  /**
   * Check if debug mode is enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Start debug session
   */
  startSession(): string {
    const sessionId = `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      logs: [],
      metrics: [],
      errors: [],
      actions: []
    };

    this.logger.info('debug', `Debug session started: ${sessionId}`);
    return sessionId;
  }

  /**
   * End debug session
   */
  endSession(): DebugSession | null {
    if (!this.currentSession) {
      return null;
    }

    this.currentSession.endTime = Date.now();
    
    // Collect final data
    this.currentSession.logs = this.logger.getRecentEntries(1000);
    this.currentSession.metrics = this.performanceMonitor.getMetrics();
    this.currentSession.errors = this.errorHandler.getRecentErrors(100);

    const session = this.currentSession;
    this.currentSession = undefined;

    this.logger.info('debug', `Debug session ended: ${session.id}`, {
      duration: session.endTime - session.startTime,
      logsCount: session.logs.length,
      metricsCount: session.metrics.length,
      errorsCount: session.errors.length
    });

    return session;
  }

  /**
   * Get current session
   */
  getCurrentSession(): DebugSession | null {
    return this.currentSession || null;
  }

  /**
   * Log debug action
   */
  logAction(type: string, data: any, result?: any, error?: string): void {
    if (!this.config.enabled || !this.currentSession) {
      return;
    }

    const action: DebugAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      timestamp: Date.now(),
      data,
      result,
      error
    };

    this.currentSession.actions.push(action);
    
    this.logger.debug('debug', `Action logged: ${type}`, {
      actionId: action.id,
      data,
      result,
      error
    });
  }

  /**
   * Register component for inspection
   */
  registerComponent(
    componentId: string,
    component: string,
    props: Record<string, any> = {},
    state: Record<string, any> = {}
  ): void {
    if (!this.config.enabled || !this.config.enableInspector) {
      return;
    }

    const inspectorData: InspectorData = {
      component,
      props,
      state,
      events: [],
      performance: {
        renderTime: 0,
        updateCount: 0,
        lastUpdate: Date.now()
      }
    };

    this.inspectorData.set(componentId, inspectorData);
    
    this.logger.debug('debug', `Component registered: ${component}`, {
      componentId,
      props,
      state
    });
  }

  /**
   * Update component data
   */
  updateComponent(
    componentId: string,
    props?: Record<string, any>,
    state?: Record<string, any>
  ): void {
    if (!this.config.enabled || !this.config.enableInspector) {
      return;
    }

    const data = this.inspectorData.get(componentId);
    if (!data) {
      return;
    }

    if (props) {
      data.props = { ...data.props, ...props };
    }

    if (state) {
      data.state = { ...data.state, ...state };
    }

    data.performance.updateCount++;
    data.performance.lastUpdate = Date.now();

    this.logger.debug('debug', `Component updated: ${data.component}`, {
      componentId,
      updateCount: data.performance.updateCount,
      props: data.props,
      state: data.state
    });
  }

  /**
   * Log component event
   */
  logComponentEvent(componentId: string, eventType: string, eventData: any): void {
    if (!this.config.enabled || !this.config.enableInspector) {
      return;
    }

    const data = this.inspectorData.get(componentId);
    if (!data) {
      return;
    }

    data.events.push({
      type: eventType,
      timestamp: Date.now(),
      data: eventData
    });

    // Keep only recent events
    if (data.events.length > 100) {
      data.events = data.events.slice(-100);
    }

    this.logger.debug('debug', `Component event: ${eventType}`, {
      componentId,
      component: data.component,
      eventData
    });
  }

  /**
   * Measure component render time
   */
  measureComponentRender(componentId: string, renderFn: () => void): void {
    if (!this.config.enabled || !this.config.enableInspector) {
      renderFn();
      return;
    }

    const data = this.inspectorData.get(componentId);
    if (!data) {
      renderFn();
      return;
    }

    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    
    data.performance.renderTime = endTime - startTime;
    data.performance.lastUpdate = Date.now();

    this.logger.debug('debug', `Component render measured: ${data.component}`, {
      componentId,
      renderTime: data.performance.renderTime
    });
  }

  /**
   * Get component inspector data
   */
  getComponentData(componentId: string): InspectorData | null {
    return this.inspectorData.get(componentId) || null;
  }

  /**
   * Get all component data
   */
  getAllComponentData(): Record<string, InspectorData> {
    const result: Record<string, InspectorData> = {};
    this.inspectorData.forEach((data, id) => {
      result[id] = data;
    });
    return result;
  }

  /**
   * Monitor network request
   */
  monitorNetworkRequest(url: string, method: string = 'GET'): string {
    if (!this.config.enabled || !this.config.enableNetworkMonitoring) {
      return '';
    }

    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.networkRequests.push({
      id: requestId,
      url,
      method,
      timestamp: Date.now()
    });

    this.logger.debug('debug', `Network request started: ${method} ${url}`, {
      requestId
    });

    return requestId;
  }

  /**
   * Complete network request monitoring
   */
  completeNetworkRequest(requestId: string, status: number, error?: string): void {
    if (!this.config.enabled || !this.config.enableNetworkMonitoring) {
      return;
    }

    const request = this.networkRequests.find(r => r.id === requestId);
    if (!request) {
      return;
    }

    request.duration = Date.now() - request.timestamp;
    request.status = status;
    request.error = error;

    this.logger.debug('debug', `Network request completed: ${request.method} ${request.url}`, {
      requestId,
      duration: request.duration,
      status,
      error
    });

    // Record performance metric
    this.performanceMonitor.recordNetworkRequest(
      request.url,
      request.method,
      status,
      request.duration,
      0 // Size not available
    );
  }

  /**
   * Get network requests
   */
  getNetworkRequests(): Array<{
    id: string;
    url: string;
    method: string;
    timestamp: number;
    duration?: number;
    status?: number;
    error?: string;
  }> {
    return [...this.networkRequests];
  }

  /**
   * Take memory snapshot
   */
  takeMemorySnapshot(): any {
    if (!this.config.enabled || !this.config.enableMemoryProfiling) {
      return null;
    }

    try {
      // Use Chrome's memory API if available
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        
        const snapshot = {
          timestamp: Date.now(),
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        };

        this.logger.debug('debug', 'Memory snapshot taken', snapshot);
        return snapshot;
      }
    } catch (error) {
      this.logger.error('debug', 'Failed to take memory snapshot', error);
    }

    return null;
  }

  /**
   * Get debug information
   */
  getDebugInfo(): {
    config: DebugConfig;
    session: DebugSession | null;
    components: Record<string, InspectorData>;
    networkRequests: any[];
    memorySnapshot: any;
    logStats: any;
    performanceStats: any;
  } {
    return {
      config: this.config,
      session: this.currentSession,
      components: this.getAllComponentData(),
      networkRequests: this.getNetworkRequests(),
      memorySnapshot: this.takeMemorySnapshot(),
      logStats: this.logger.getStats(),
      performanceStats: this.performanceMonitor.getPerformanceSummary()
    };
  }

  /**
   * Export debug data
   */
  exportDebugData(): string {
    const debugInfo = this.getDebugInfo();
    return JSON.stringify(debugInfo, null, 2);
  }

  /**
   * Create debug report
   */
  createDebugReport(): {
    timestamp: number;
    session: DebugSession | null;
    summary: {
      totalLogs: number;
      totalMetrics: number;
      totalErrors: number;
      totalComponents: number;
      totalNetworkRequests: number;
    };
    topErrors: SidebarError[];
    performanceIssues: any[];
    memoryUsage: any;
    recommendations: string[];
  } {
    const debugInfo = this.getDebugInfo();
    const recentErrors = this.errorHandler.getRecentErrors(10);
    const performanceAlerts = this.performanceMonitor.getAlerts(false);

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (debugInfo.memorySnapshot && debugInfo.memorySnapshot.percentage > 80) {
      recommendations.push('Memory usage is high (>80%). Consider optimizing memory usage.');
    }

    if (performanceAlerts.length > 0) {
      recommendations.push(`${performanceAlerts.length} performance alerts detected. Review performance metrics.`);
    }

    if (recentErrors.length > 5) {
      recommendations.push('High error rate detected. Review error logs and implement fixes.');
    }

    const networkErrors = debugInfo.networkRequests.filter(r => r.error || (r.status && r.status >= 400));
    if (networkErrors.length > 0) {
      recommendations.push(`${networkErrors.length} network errors detected. Check network connectivity and API endpoints.`);
    }

    return {
      timestamp: Date.now(),
      session: debugInfo.session,
      summary: {
        totalLogs: debugInfo.logStats.totalEntries,
        totalMetrics: debugInfo.performanceStats.totalMetrics,
        totalErrors: recentErrors.length,
        totalComponents: Object.keys(debugInfo.components).length,
        totalNetworkRequests: debugInfo.networkRequests.length
      },
      topErrors: recentErrors.slice(0, 5),
      performanceIssues: performanceAlerts,
      memoryUsage: debugInfo.memorySnapshot,
      recommendations
    };
  }

  /**
   * Initialize debug tools
   */
  private initialize(): void {
    this.setupConsoleCommands();
    this.setupErrorHandling();
    this.setupPerformanceMonitoring();
    this.setupNetworkInterception();
    
    this.logger.setLevel(this.config.logLevel);
    this.logger.info('debug', 'Debug tools initialized', this.config);
  }

  /**
   * Setup console commands
   */
  private setupConsoleCommands(): void {
    if (!this.config.enableConsoleCommands) {
      return;
    }

    // Add debug commands to global scope
    (window as any).sidebarDebug = {
      enable: () => this.enable(),
      disable: () => this.disable(),
      startSession: () => this.startSession(),
      endSession: () => this.endSession(),
      getInfo: () => this.getDebugInfo(),
      export: () => this.exportDebugData(),
      report: () => this.createDebugReport(),
      clearLogs: () => this.logger.clearBuffer(),
      clearMetrics: () => this.performanceMonitor.clearMetrics(),
      takeSnapshot: () => this.takeMemorySnapshot(),
      getComponents: () => this.getAllComponentData(),
      getNetworkRequests: () => this.getNetworkRequests()
    };

    this.logger.info('debug', 'Console commands registered under window.sidebarDebug');
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    if (!this.config.showErrorDetails) {
      return;
    }

    this.errorHandler.addListener((error: SidebarError) => {
      if (this.currentSession) {
        this.currentSession.errors.push(error);
      }

      this.logger.error('debug', `Error captured: ${error.message}`, {
        code: error.code,
        category: error.category,
        severity: error.severity,
        stack: error.stack,
        context: error.context
      });
    });
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (!this.config.showPerformanceMetrics) {
      return;
    }

    // Monitor performance metrics
    setInterval(() => {
      const summary = this.performanceMonitor.getPerformanceSummary();
      
      if (summary.memoryUsage && summary.memoryUsage.percentage > 90) {
        this.logger.warn('debug', 'High memory usage detected', summary.memoryUsage);
      }

      if (summary.activeAlerts > 0) {
        this.logger.warn('debug', `${summary.activeAlerts} performance alerts active`);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Setup network interception
   */
  private setupNetworkInterception(): void {
    if (!this.config.enableNetworkMonitoring) {
      return;
    }

    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method || 'GET';
      
      const requestId = this.monitorNetworkRequest(url, method);
      
      try {
        const response = await originalFetch(input, init);
        this.completeNetworkRequest(requestId, response.status);
        return response;
      } catch (error) {
        this.completeNetworkRequest(requestId, 0, error instanceof Error ? error.message : 'Unknown error');
        throw error;
      }
    };

    // Intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      (this as any)._debugRequestId = DebugTools.instance.monitorNetworkRequest(url.toString(), method);
      (this as any)._debugStartTime = Date.now();
      return originalXHROpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function(...args: any[]) {
      const requestId = (this as any)._debugRequestId;
      
      this.addEventListener('loadend', () => {
        if (requestId) {
          DebugTools.instance.completeNetworkRequest(requestId, this.status);
        }
      });

      this.addEventListener('error', () => {
        if (requestId) {
          DebugTools.instance.completeNetworkRequest(requestId, 0, 'Network error');
        }
      });

      return originalXHRSend.apply(this, args);
    };
  }

  /**
   * Cleanup debug tools
   */
  private cleanup(): void {
    // Remove console commands
    if ((window as any).sidebarDebug) {
      delete (window as any).sidebarDebug;
    }

    // Clear data
    this.inspectorData.clear();
    this.networkRequests = [];
    this.currentSession = undefined;
  }
}

// Export convenience functions
export const debugTools = DebugTools.getInstance();

export function enableDebug(): void {
  debugTools.enable();
}

export function disableDebug(): void {
  debugTools.disable();
}

export function logAction(type: string, data: any, result?: any, error?: string): void {
  debugTools.logAction(type, data, result, error);
}

export function registerComponent(
  componentId: string,
  component: string,
  props?: Record<string, any>,
  state?: Record<string, any>
): void {
  debugTools.registerComponent(componentId, component, props, state);
}

export function updateComponent(
  componentId: string,
  props?: Record<string, any>,
  state?: Record<string, any>
): void {
  debugTools.updateComponent(componentId, props, state);
}

export function logComponentEvent(componentId: string, eventType: string, eventData: any): void {
  debugTools.logComponentEvent(componentId, eventType, eventData);
}

export function measureComponentRender(componentId: string, renderFn: () => void): void {
  debugTools.measureComponentRender(componentId, renderFn);
}