/**
 * Logger - 分级日志系统
 */

export enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5,
  OFF = 6
}

export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  stack?: string;
  context?: LogContext;
}

export interface LogContext {
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  url?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxStorageEntries: number;
  remoteEndpoint?: string;
  categories: Record<string, LogLevel>;
  formatters: Record<string, LogFormatter>;
  transports: LogTransport[];
}

export interface LogFormatter {
  format(entry: LogEntry): string;
}

export interface LogTransport {
  name: string;
  level: LogLevel;
  write(entry: LogEntry): Promise<void>;
}

export interface LogFilter {
  shouldLog(entry: LogEntry): boolean;
}

/**
 * Main Logger Class
 */
export class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private context: LogContext = {};
  private filters: LogFilter[] = [];
  private buffer: LogEntry[] = [];
  private maxBufferSize = 1000;
  private flushTimer?: NodeJS.Timeout;
  private flushInterval = 5000; // 5 seconds

  private constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      maxStorageEntries: 1000,
      categories: {},
      formatters: {},
      transports: [],
      ...config
    };

    this.setupDefaultFormatters();
    this.setupDefaultTransports();
    this.startFlushTimer();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config);
    }
    return Logger.instance;
  }

  /**
   * Set global context
   */
  setContext(context: Partial<LogContext>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Get current context
   */
  getContext(): LogContext {
    return { ...this.context };
  }

  /**
   * Set log level
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * Set category log level
   */
  setCategoryLevel(category: string, level: LogLevel): void {
    this.config.categories[category] = level;
  }

  /**
   * Add log filter
   */
  addFilter(filter: LogFilter): void {
    this.filters.push(filter);
  }

  /**
   * Remove log filter
   */
  removeFilter(filter: LogFilter): void {
    const index = this.filters.indexOf(filter);
    if (index > -1) {
      this.filters.splice(index, 1);
    }
  }

  /**
   * Add transport
   */
  addTransport(transport: LogTransport): void {
    this.config.transports.push(transport);
  }

  /**
   * Remove transport
   */
  removeTransport(name: string): void {
    this.config.transports = this.config.transports.filter(t => t.name !== name);
  }

  /**
   * Trace level logging
   */
  trace(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.TRACE, category, message, data, context);
  }

  /**
   * Debug level logging
   */
  debug(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.DEBUG, category, message, data, context);
  }

  /**
   * Info level logging
   */
  info(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.INFO, category, message, data, context);
  }

  /**
   * Warn level logging
   */
  warn(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.WARN, category, message, data, context);
  }

  /**
   * Error level logging
   */
  error(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.ERROR, category, message, data, context);
  }

  /**
   * Fatal level logging
   */
  fatal(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.log(LogLevel.FATAL, category, message, data, context);
  }

  /**
   * Log with specific level
   */
  log(level: LogLevel, category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    // Check if logging is enabled for this level and category
    if (!this.shouldLog(level, category)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data,
      context: { ...this.context, ...context }
    };

    // Add stack trace for errors
    if (level >= LogLevel.ERROR) {
      entry.stack = new Error().stack;
    }

    // Apply filters
    if (!this.filters.every(filter => filter.shouldLog(entry))) {
      return;
    }

    // Add to buffer
    this.buffer.push(entry);

    // Manage buffer size
    if (this.buffer.length > this.maxBufferSize) {
      this.buffer = this.buffer.slice(-this.maxBufferSize);
    }

    // Immediate processing for high priority logs
    if (level >= LogLevel.ERROR) {
      this.processEntry(entry);
    }
  }

  /**
   * Create child logger with additional context
   */
  child(context: Partial<LogContext>): ChildLogger {
    return new ChildLogger(this, context);
  }

  /**
   * Get recent log entries
   */
  getRecentEntries(count: number = 100, level?: LogLevel, category?: string): LogEntry[] {
    let entries = [...this.buffer];

    if (level !== undefined) {
      entries = entries.filter(entry => entry.level >= level);
    }

    if (category) {
      entries = entries.filter(entry => entry.category === category);
    }

    return entries.slice(-count);
  }

  /**
   * Clear log buffer
   */
  clearBuffer(): void {
    this.buffer = [];
  }

  /**
   * Flush all pending logs
   */
  async flush(): Promise<void> {
    const entriesToFlush = [...this.buffer];
    
    for (const entry of entriesToFlush) {
      await this.processEntry(entry);
    }
  }

  /**
   * Get log statistics
   */
  getStats(): {
    totalEntries: number;
    entriesByLevel: Record<string, number>;
    entriesByCategory: Record<string, number>;
    bufferSize: number;
    oldestEntry?: number;
    newestEntry?: number;
  } {
    const entriesByLevel: Record<string, number> = {};
    const entriesByCategory: Record<string, number> = {};

    this.buffer.forEach(entry => {
      const levelName = LogLevel[entry.level];
      entriesByLevel[levelName] = (entriesByLevel[levelName] || 0) + 1;
      entriesByCategory[entry.category] = (entriesByCategory[entry.category] || 0) + 1;
    });

    return {
      totalEntries: this.buffer.length,
      entriesByLevel,
      entriesByCategory,
      bufferSize: this.buffer.length,
      oldestEntry: this.buffer.length > 0 ? this.buffer[0].timestamp : undefined,
      newestEntry: this.buffer.length > 0 ? this.buffer[this.buffer.length - 1].timestamp : undefined
    };
  }

  /**
   * Export logs
   */
  exportLogs(format: 'json' | 'csv' | 'text' = 'json'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(this.buffer, null, 2);
      case 'csv':
        return this.exportToCsv();
      case 'text':
        return this.exportToText();
      default:
        return JSON.stringify(this.buffer, null, 2);
    }
  }

  /**
   * Import logs
   */
  importLogs(data: string, format: 'json' = 'json'): void {
    try {
      if (format === 'json') {
        const entries = JSON.parse(data) as LogEntry[];
        this.buffer.push(...entries);
        
        // Manage buffer size
        if (this.buffer.length > this.maxBufferSize) {
          this.buffer = this.buffer.slice(-this.maxBufferSize);
        }
      }
    } catch (error) {
      console.error('Failed to import logs:', error);
    }
  }

  /**
   * Check if should log for level and category
   */
  private shouldLog(level: LogLevel, category: string): boolean {
    // Check category-specific level
    const categoryLevel = this.config.categories[category];
    if (categoryLevel !== undefined) {
      return level >= categoryLevel;
    }

    // Check global level
    return level >= this.config.level;
  }

  /**
   * Process log entry
   */
  private async processEntry(entry: LogEntry): Promise<void> {
    // Process through all transports
    const promises = this.config.transports
      .filter(transport => entry.level >= transport.level)
      .map(transport => transport.write(entry).catch(error => {
        console.error(`Transport ${transport.name} failed:`, error);
      }));

    await Promise.allSettled(promises);
  }

  /**
   * Setup default formatters
   */
  private setupDefaultFormatters(): void {
    this.config.formatters['default'] = new DefaultFormatter();
    this.config.formatters['json'] = new JsonFormatter();
    this.config.formatters['compact'] = new CompactFormatter();
  }

  /**
   * Setup default transports
   */
  private setupDefaultTransports(): void {
    if (this.config.enableConsole) {
      this.config.transports.push(new ConsoleTransport());
    }

    if (this.config.enableStorage) {
      this.config.transports.push(new StorageTransport(this.config.maxStorageEntries));
    }

    if (this.config.enableRemote && this.config.remoteEndpoint) {
      this.config.transports.push(new RemoteTransport(this.config.remoteEndpoint));
    }
  }

  /**
   * Start flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushLowPriorityLogs();
    }, this.flushInterval);
  }

  /**
   * Flush low priority logs
   */
  private flushLowPriorityLogs(): void {
    const lowPriorityEntries = this.buffer.filter(entry => entry.level < LogLevel.ERROR);
    
    lowPriorityEntries.forEach(entry => {
      this.processEntry(entry).catch(error => {
        console.error('Failed to process log entry:', error);
      });
    });
  }

  /**
   * Export to CSV format
   */
  private exportToCsv(): string {
    const headers = ['Timestamp', 'Level', 'Category', 'Message', 'Data', 'Context'];
    const rows = this.buffer.map(entry => [
      new Date(entry.timestamp).toISOString(),
      LogLevel[entry.level],
      entry.category,
      entry.message,
      entry.data ? JSON.stringify(entry.data) : '',
      entry.context ? JSON.stringify(entry.context) : ''
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');
  }

  /**
   * Export to text format
   */
  private exportToText(): string {
    const formatter = this.config.formatters['default'];
    return this.buffer.map(entry => formatter.format(entry)).join('\n');
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    this.flush().catch(error => {
      console.error('Failed to flush logs during disposal:', error);
    });
  }
}

/**
 * Child Logger
 */
export class ChildLogger {
  constructor(
    private parent: Logger,
    private childContext: Partial<LogContext>
  ) {}

  trace(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.trace(category, message, data, { ...this.childContext, ...context });
  }

  debug(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.debug(category, message, data, { ...this.childContext, ...context });
  }

  info(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.info(category, message, data, { ...this.childContext, ...context });
  }

  warn(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.warn(category, message, data, { ...this.childContext, ...context });
  }

  error(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.error(category, message, data, { ...this.childContext, ...context });
  }

  fatal(category: string, message: string, data?: any, context?: Partial<LogContext>): void {
    this.parent.fatal(category, message, data, { ...this.childContext, ...context });
  }

  child(context: Partial<LogContext>): ChildLogger {
    return new ChildLogger(this.parent, { ...this.childContext, ...context });
  }
}

/**
 * Default Formatter
 */
export class DefaultFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString();
    const level = LogLevel[entry.level].padEnd(5);
    const category = entry.category.padEnd(15);
    
    let message = `${timestamp} [${level}] ${category} ${entry.message}`;
    
    if (entry.data) {
      message += ` | Data: ${JSON.stringify(entry.data)}`;
    }
    
    if (entry.context && Object.keys(entry.context).length > 0) {
      message += ` | Context: ${JSON.stringify(entry.context)}`;
    }
    
    return message;
  }
}

/**
 * JSON Formatter
 */
export class JsonFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify(entry);
  }
}

/**
 * Compact Formatter
 */
export class CompactFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const level = LogLevel[entry.level][0]; // First letter only
    return `${timestamp} ${level} [${entry.category}] ${entry.message}`;
  }
}

/**
 * Console Transport
 */
export class ConsoleTransport implements LogTransport {
  name = 'console';
  level = LogLevel.TRACE;
  private formatter = new DefaultFormatter();

  async write(entry: LogEntry): Promise<void> {
    const message = this.formatter.format(entry);
    
    switch (entry.level) {
      case LogLevel.TRACE:
      case LogLevel.DEBUG:
        console.debug(message);
        break;
      case LogLevel.INFO:
        console.info(message);
        break;
      case LogLevel.WARN:
        console.warn(message);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message);
        if (entry.stack) {
          console.error(entry.stack);
        }
        break;
    }
  }
}

/**
 * Storage Transport
 */
export class StorageTransport implements LogTransport {
  name = 'storage';
  level = LogLevel.INFO;
  private storageKey = 'sidebar_logs';

  constructor(private maxEntries: number = 1000) {}

  async write(entry: LogEntry): Promise<void> {
    try {
      const result = await chrome.storage.local.get(this.storageKey);
      const logs: LogEntry[] = result[this.storageKey] || [];
      
      logs.push(entry);
      
      // Keep only recent entries
      if (logs.length > this.maxEntries) {
        logs.splice(0, logs.length - this.maxEntries);
      }
      
      await chrome.storage.local.set({ [this.storageKey]: logs });
    } catch (error) {
      console.error('Failed to write log to storage:', error);
    }
  }

  async getLogs(): Promise<LogEntry[]> {
    try {
      const result = await chrome.storage.local.get(this.storageKey);
      return result[this.storageKey] || [];
    } catch (error) {
      console.error('Failed to get logs from storage:', error);
      return [];
    }
  }

  async clearLogs(): Promise<void> {
    try {
      await chrome.storage.local.remove(this.storageKey);
    } catch (error) {
      console.error('Failed to clear logs from storage:', error);
    }
  }
}

/**
 * Remote Transport
 */
export class RemoteTransport implements LogTransport {
  name = 'remote';
  level = LogLevel.WARN;
  private buffer: LogEntry[] = [];
  private batchSize = 10;
  private flushTimer?: NodeJS.Timeout;

  constructor(private endpoint: string) {
    this.startBatchTimer();
  }

  async write(entry: LogEntry): Promise<void> {
    this.buffer.push(entry);
    
    // Immediate send for critical logs
    if (entry.level >= LogLevel.ERROR) {
      await this.flush();
    } else if (this.buffer.length >= this.batchSize) {
      await this.flush();
    }
  }

  private async flush(): Promise<void> {
    if (this.buffer.length === 0) {
      return;
    }

    const logsToSend = [...this.buffer];
    this.buffer = [];

    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ logs: logsToSend })
      });
    } catch (error) {
      console.error('Failed to send logs to remote endpoint:', error);
      // Put logs back in buffer for retry
      this.buffer.unshift(...logsToSend);
    }
  }

  private startBatchTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush().catch(error => {
        console.error('Failed to flush remote logs:', error);
      });
    }, 30000); // 30 seconds
  }

  dispose(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
    
    this.flush().catch(error => {
      console.error('Failed to flush logs during disposal:', error);
    });
  }
}

/**
 * Common log filters
 */
export class RateLimitFilter implements LogFilter {
  private counts: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(
    private maxCount: number = 10,
    private windowMs: number = 60000 // 1 minute
  ) {}

  shouldLog(entry: LogEntry): boolean {
    const key = `${entry.category}:${entry.message}`;
    const now = Date.now();
    
    let record = this.counts.get(key);
    
    if (!record || now > record.resetTime) {
      record = { count: 0, resetTime: now + this.windowMs };
      this.counts.set(key, record);
    }
    
    record.count++;
    
    return record.count <= this.maxCount;
  }
}

export class SensitiveDataFilter implements LogFilter {
  private sensitivePatterns = [
    /password/i,
    /token/i,
    /key/i,
    /secret/i,
    /auth/i,
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/ // Email
  ];

  shouldLog(entry: LogEntry): boolean {
    const textToCheck = `${entry.message} ${JSON.stringify(entry.data || {})}`;
    
    return !this.sensitivePatterns.some(pattern => pattern.test(textToCheck));
  }
}

// Export convenience functions
export const logger = Logger.getInstance();

export function createLogger(config?: Partial<LoggerConfig>): Logger {
  return Logger.getInstance(config);
}

export function getLogger(): Logger {
  return Logger.getInstance();
}