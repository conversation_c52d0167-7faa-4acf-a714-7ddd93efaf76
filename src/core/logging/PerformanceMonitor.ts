/**
 * Performance Monitor - 性能监控系统
 */

import { Logger, LogLevel } from './Logger';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  category: string;
  tags?: Record<string, string>;
  context?: Record<string, any>;
}

export interface TimingMetric extends PerformanceMetric {
  startTime: number;
  endTime: number;
  duration: number;
}

export interface CounterMetric extends PerformanceMetric {
  count: number;
  rate?: number;
}

export interface GaugeMetric extends PerformanceMetric {
  current: number;
  min?: number;
  max?: number;
  average?: number;
}

export interface MemoryMetric extends PerformanceMetric {
  used: number;
  total: number;
  percentage: number;
}

export interface NetworkMetric extends PerformanceMetric {
  url: string;
  method: string;
  status: number;
  responseTime: number;
  size: number;
}

export interface PerformanceAlert {
  id: string;
  metric: string;
  threshold: number;
  value: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  acknowledged: boolean;
}

export interface PerformanceConfig {
  enableMetrics: boolean;
  enableAlerts: boolean;
  enableAutoCollection: boolean;
  collectionInterval: number;
  maxMetrics: number;
  alertThresholds: Record<string, number>;
  categories: string[];
}

/**
 * Performance Monitor
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private logger: Logger;
  private config: PerformanceConfig;
  private metrics: PerformanceMetric[] = [];
  private timers: Map<string, number> = new Map();
  private counters: Map<string, number> = new Map();
  private gauges: Map<string, GaugeMetric> = new Map();
  private alerts: PerformanceAlert[] = [];
  private collectionTimer?: NodeJS.Timeout;
  private observers: PerformanceObserver[] = [];

  private constructor(config: Partial<PerformanceConfig> = {}) {
    this.logger = Logger.getInstance();
    this.config = {
      enableMetrics: true,
      enableAlerts: true,
      enableAutoCollection: true,
      collectionInterval: 30000, // 30 seconds
      maxMetrics: 1000,
      alertThresholds: {
        'memory.usage': 80, // 80%
        'response.time': 5000, // 5 seconds
        'error.rate': 5, // 5%
        'cpu.usage': 80 // 80%
      },
      categories: ['performance', 'memory', 'network', 'ui', 'api'],
      ...config
    };

    this.setupPerformanceObservers();
    this.startAutoCollection();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<PerformanceConfig>): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor(config);
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing measurement
   */
  startTiming(name: string, category: string = 'performance', tags?: Record<string, string>): void {
    if (!this.config.enableMetrics) return;

    const key = `${category}:${name}`;
    this.timers.set(key, performance.now());
    
    this.logger.debug('performance', `Started timing: ${name}`, { category, tags });
  }

  /**
   * End timing measurement
   */
  endTiming(name: string, category: string = 'performance', tags?: Record<string, string>): number {
    if (!this.config.enableMetrics) return 0;

    const key = `${category}:${name}`;
    const startTime = this.timers.get(key);
    
    if (startTime === undefined) {
      this.logger.warn('performance', `No start time found for: ${name}`, { category });
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.timers.delete(key);

    const metric: TimingMetric = {
      name,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      category,
      tags,
      startTime,
      endTime,
      duration
    };

    this.recordMetric(metric);
    this.checkAlerts(metric);

    this.logger.debug('performance', `Timing completed: ${name}`, { 
      duration: `${duration.toFixed(2)}ms`,
      category,
      tags 
    });

    return duration;
  }

  /**
   * Measure function execution time
   */
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    category: string = 'performance',
    tags?: Record<string, string>
  ): Promise<T> {
    this.startTiming(name, category, tags);
    
    try {
      const result = await fn();
      this.endTiming(name, category, tags);
      return result;
    } catch (error) {
      this.endTiming(name, category, tags);
      this.incrementCounter(`${name}.error`, category, tags);
      throw error;
    }
  }

  /**
   * Measure synchronous function execution time
   */
  measure<T>(
    name: string,
    fn: () => T,
    category: string = 'performance',
    tags?: Record<string, string>
  ): T {
    this.startTiming(name, category, tags);
    
    try {
      const result = fn();
      this.endTiming(name, category, tags);
      return result;
    } catch (error) {
      this.endTiming(name, category, tags);
      this.incrementCounter(`${name}.error`, category, tags);
      throw error;
    }
  }

  /**
   * Increment counter
   */
  incrementCounter(name: string, category: string = 'performance', tags?: Record<string, string>): void {
    if (!this.config.enableMetrics) return;

    const key = `${category}:${name}`;
    const currentCount = this.counters.get(key) || 0;
    const newCount = currentCount + 1;
    
    this.counters.set(key, newCount);

    const metric: CounterMetric = {
      name,
      value: newCount,
      unit: 'count',
      timestamp: Date.now(),
      category,
      tags,
      count: newCount
    };

    this.recordMetric(metric);
    this.checkAlerts(metric);
  }

  /**
   * Set gauge value
   */
  setGauge(name: string, value: number, category: string = 'performance', tags?: Record<string, string>): void {
    if (!this.config.enableMetrics) return;

    const key = `${category}:${name}`;
    const existing = this.gauges.get(key);
    
    const metric: GaugeMetric = {
      name,
      value,
      unit: 'value',
      timestamp: Date.now(),
      category,
      tags,
      current: value,
      min: existing ? Math.min(existing.min || value, value) : value,
      max: existing ? Math.max(existing.max || value, value) : value,
      average: existing ? (existing.average || 0 + value) / 2 : value
    };

    this.gauges.set(key, metric);
    this.recordMetric(metric);
    this.checkAlerts(metric);
  }

  /**
   * Record custom metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.config.enableMetrics) return;

    this.metrics.push(metric);

    // Manage metrics size
    if (this.metrics.length > this.config.maxMetrics) {
      this.metrics = this.metrics.slice(-this.config.maxMetrics);
    }

    this.logger.trace('performance', `Metric recorded: ${metric.name}`, {
      value: metric.value,
      unit: metric.unit,
      category: metric.category,
      tags: metric.tags
    });
  }

  /**
   * Record memory usage
   */
  recordMemoryUsage(): void {
    if (!this.config.enableMetrics) return;

    try {
      // Use Chrome's memory API if available
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        
        const metric: MemoryMetric = {
          name: 'memory.usage',
          value: memory.usedJSHeapSize,
          unit: 'bytes',
          timestamp: Date.now(),
          category: 'memory',
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        };

        this.recordMetric(metric);
        this.checkAlerts(metric);
      }
    } catch (error) {
      this.logger.error('performance', 'Failed to record memory usage', error);
    }
  }

  /**
   * Record network request
   */
  recordNetworkRequest(
    url: string,
    method: string,
    status: number,
    responseTime: number,
    size: number = 0
  ): void {
    if (!this.config.enableMetrics) return;

    const metric: NetworkMetric = {
      name: 'network.request',
      value: responseTime,
      unit: 'ms',
      timestamp: Date.now(),
      category: 'network',
      url,
      method,
      status,
      responseTime,
      size
    };

    this.recordMetric(metric);
    this.checkAlerts(metric);

    // Record additional metrics
    this.incrementCounter('network.requests.total', 'network', { method, status: status.toString() });
    
    if (status >= 400) {
      this.incrementCounter('network.requests.error', 'network', { method, status: status.toString() });
    }
  }

  /**
   * Get metrics by category
   */
  getMetrics(category?: string, limit?: number): PerformanceMetric[] {
    let metrics = [...this.metrics];

    if (category) {
      metrics = metrics.filter(m => m.category === category);
    }

    if (limit) {
      metrics = metrics.slice(-limit);
    }

    return metrics;
  }

  /**
   * Get metric statistics
   */
  getMetricStats(name: string, category?: string): {
    count: number;
    min: number;
    max: number;
    average: number;
    median: number;
    p95: number;
    p99: number;
  } | null {
    const metrics = this.metrics.filter(m => 
      m.name === name && (!category || m.category === category)
    );

    if (metrics.length === 0) {
      return null;
    }

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count,
      min: values[0],
      max: values[count - 1],
      average: sum / count,
      median: values[Math.floor(count / 2)],
      p95: values[Math.floor(count * 0.95)],
      p99: values[Math.floor(count * 0.99)]
    };
  }

  /**
   * Get current alerts
   */
  getAlerts(acknowledged: boolean = false): PerformanceAlert[] {
    return this.alerts.filter(alert => alert.acknowledged === acknowledged);
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.logger.info('performance', `Alert acknowledged: ${alertId}`, { alert });
    }
  }

  /**
   * Clear old alerts
   */
  clearOldAlerts(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    totalMetrics: number;
    activeAlerts: number;
    categories: Record<string, number>;
    topMetrics: Array<{ name: string; category: string; count: number; avgValue: number }>;
    memoryUsage?: { used: number; total: number; percentage: number };
  } {
    const categories: Record<string, number> = {};
    const metricCounts: Record<string, { count: number; totalValue: number }> = {};

    this.metrics.forEach(metric => {
      categories[metric.category] = (categories[metric.category] || 0) + 1;
      
      const key = `${metric.category}:${metric.name}`;
      if (!metricCounts[key]) {
        metricCounts[key] = { count: 0, totalValue: 0 };
      }
      metricCounts[key].count++;
      metricCounts[key].totalValue += metric.value;
    });

    const topMetrics = Object.entries(metricCounts)
      .map(([key, data]) => {
        const [category, name] = key.split(':');
        return {
          name,
          category,
          count: data.count,
          avgValue: data.totalValue / data.count
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Get latest memory usage
    const memoryMetrics = this.metrics
      .filter(m => m.name === 'memory.usage')
      .slice(-1)[0] as MemoryMetric;

    return {
      totalMetrics: this.metrics.length,
      activeAlerts: this.getAlerts(false).length,
      categories,
      topMetrics,
      memoryUsage: memoryMetrics ? {
        used: memoryMetrics.used,
        total: memoryMetrics.total,
        percentage: memoryMetrics.percentage
      } : undefined
    };
  }

  /**
   * Export metrics
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['Timestamp', 'Name', 'Category', 'Value', 'Unit', 'Tags'];
      const rows = this.metrics.map(metric => [
        new Date(metric.timestamp).toISOString(),
        metric.name,
        metric.category,
        metric.value.toString(),
        metric.unit,
        metric.tags ? JSON.stringify(metric.tags) : ''
      ]);

      return [headers, ...rows]
        .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
        .join('\n');
    }

    return JSON.stringify(this.metrics, null, 2);
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.counters.clear();
    this.gauges.clear();
    this.timers.clear();
    this.logger.info('performance', 'All metrics cleared');
  }

  /**
   * Setup performance observers
   */
  private setupPerformanceObservers(): void {
    if (!this.config.enableAutoCollection) return;

    try {
      // Observe navigation timing
      if ('PerformanceObserver' in window) {
        const navigationObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric({
                name: 'navigation.loadTime',
                value: navEntry.loadEventEnd - navEntry.navigationStart,
                unit: 'ms',
                timestamp: Date.now(),
                category: 'performance'
              });
            }
          });
        });

        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);

        // Observe resource timing
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            if (entry.entryType === 'resource') {
              const resourceEntry = entry as PerformanceResourceTiming;
              this.recordNetworkRequest(
                resourceEntry.name,
                'GET', // Default method
                200, // Assume success if no error
                resourceEntry.responseEnd - resourceEntry.requestStart,
                resourceEntry.transferSize || 0
              );
            }
          });
        });

        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);

        // Observe long tasks
        const longTaskObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            this.recordMetric({
              name: 'longtask.duration',
              value: entry.duration,
              unit: 'ms',
              timestamp: Date.now(),
              category: 'performance'
            });
          });
        });

        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      }
    } catch (error) {
      this.logger.error('performance', 'Failed to setup performance observers', error);
    }
  }

  /**
   * Start auto collection
   */
  private startAutoCollection(): void {
    if (!this.config.enableAutoCollection) return;

    this.collectionTimer = setInterval(() => {
      this.recordMemoryUsage();
      this.collectSystemMetrics();
    }, this.config.collectionInterval);
  }

  /**
   * Collect system metrics
   */
  private collectSystemMetrics(): void {
    try {
      // Collect DOM metrics
      this.setGauge('dom.elements', document.querySelectorAll('*').length, 'ui');
      
      // Collect extension-specific metrics
      if (chrome && chrome.runtime) {
        this.setGauge('extension.uptime', Date.now() - (performance.timeOrigin || 0), 'system');
      }

      // Collect timing metrics
      if (performance.timing) {
        const timing = performance.timing;
        this.setGauge('page.loadTime', timing.loadEventEnd - timing.navigationStart, 'performance');
        this.setGauge('page.domReady', timing.domContentLoadedEventEnd - timing.navigationStart, 'performance');
      }
    } catch (error) {
      this.logger.error('performance', 'Failed to collect system metrics', error);
    }
  }

  /**
   * Check alerts for metric
   */
  private checkAlerts(metric: PerformanceMetric): void {
    if (!this.config.enableAlerts) return;

    const threshold = this.config.alertThresholds[metric.name];
    if (threshold === undefined) return;

    let shouldAlert = false;
    let severity: PerformanceAlert['severity'] = 'low';

    // Determine if alert should be triggered
    if (metric.value > threshold) {
      shouldAlert = true;
      
      if (metric.value > threshold * 2) {
        severity = 'critical';
      } else if (metric.value > threshold * 1.5) {
        severity = 'high';
      } else if (metric.value > threshold * 1.2) {
        severity = 'medium';
      }
    }

    if (shouldAlert) {
      const alert: PerformanceAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        metric: metric.name,
        threshold,
        value: metric.value,
        severity,
        message: `${metric.name} exceeded threshold: ${metric.value}${metric.unit} > ${threshold}${metric.unit}`,
        timestamp: Date.now(),
        acknowledged: false
      };

      this.alerts.push(alert);
      
      this.logger.warn('performance', `Performance alert: ${alert.message}`, {
        alert,
        metric
      });

      // Limit alerts
      if (this.alerts.length > 100) {
        this.alerts = this.alerts.slice(-100);
      }
    }
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    if (this.collectionTimer) {
      clearInterval(this.collectionTimer);
      this.collectionTimer = undefined;
    }

    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        this.logger.error('performance', 'Failed to disconnect observer', error);
      }
    });

    this.observers = [];
  }
}

// Export convenience functions
export const performanceMonitor = PerformanceMonitor.getInstance();

export function startTiming(name: string, category?: string, tags?: Record<string, string>): void {
  performanceMonitor.startTiming(name, category, tags);
}

export function endTiming(name: string, category?: string, tags?: Record<string, string>): number {
  return performanceMonitor.endTiming(name, category, tags);
}

export function measureAsync<T>(
  name: string,
  fn: () => Promise<T>,
  category?: string,
  tags?: Record<string, string>
): Promise<T> {
  return performanceMonitor.measureAsync(name, fn, category, tags);
}

export function measure<T>(
  name: string,
  fn: () => T,
  category?: string,
  tags?: Record<string, string>
): T {
  return performanceMonitor.measure(name, fn, category, tags);
}

export function incrementCounter(name: string, category?: string, tags?: Record<string, string>): void {
  performanceMonitor.incrementCounter(name, category, tags);
}

export function setGauge(name: string, value: number, category?: string, tags?: Record<string, string>): void {
  performanceMonitor.setGauge(name, value, category, tags);
}