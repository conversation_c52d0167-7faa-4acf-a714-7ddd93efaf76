/**
 * User Analytics - 用户操作追踪和分析
 */

import { Logger } from './Logger';
import { PerformanceMonitor } from './PerformanceMonitor';

export interface UserEvent {
  id: string;
  type: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  sessionId: string;
  userId?: string;
  properties: Record<string, any>;
  context: UserEventContext;
}

export interface UserEventContext {
  url?: string;
  userAgent: string;
  viewport: { width: number; height: number };
  screen: { width: number; height: number };
  timezone: string;
  language: string;
  platform: string;
  component?: string;
  feature?: string;
  metadata?: Record<string, any>;
}

export interface UserSession {
  id: string;
  userId?: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  events: UserEvent[];
  pageViews: number;
  interactions: number;
  errors: number;
  features: Set<string>;
  lastActivity: number;
}

export interface UserJourney {
  sessionId: string;
  steps: Array<{
    event: UserEvent;
    timeFromStart: number;
    timeFromPrevious: number;
  }>;
  totalDuration: number;
  completionRate: number;
  dropOffPoints: string[];
}

export interface AnalyticsConfig {
  enabled: boolean;
  enableUserTracking: boolean;
  enableSessionTracking: boolean;
  enablePerformanceTracking: boolean;
  enableErrorTracking: boolean;
  enableFeatureUsage: boolean;
  sessionTimeout: number;
  maxEvents: number;
  batchSize: number;
  flushInterval: number;
  endpoint?: string;
  privacyMode: boolean;
}

export interface FeatureUsage {
  feature: string;
  usageCount: number;
  uniqueUsers: Set<string>;
  averageSessionTime: number;
  lastUsed: number;
  popularActions: Record<string, number>;
}

export interface UserBehaviorPattern {
  pattern: string;
  frequency: number;
  users: Set<string>;
  averageDuration: number;
  successRate: number;
  commonSequence: string[];
}

/**
 * User Analytics Service
 */
export class UserAnalytics {
  private static instance: UserAnalytics;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private config: AnalyticsConfig;
  private currentSession?: UserSession;
  private eventQueue: UserEvent[] = [];
  private featureUsage: Map<string, FeatureUsage> = new Map();
  private behaviorPatterns: Map<string, UserBehaviorPattern> = new Map();
  private flushTimer?: NodeJS.Timeout;
  private sessionTimer?: NodeJS.Timeout;
  private isOnline = navigator.onLine;

  private constructor(config: Partial<AnalyticsConfig> = {}) {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    
    this.config = {
      enabled: true,
      enableUserTracking: true,
      enableSessionTracking: true,
      enablePerformanceTracking: true,
      enableErrorTracking: true,
      enableFeatureUsage: true,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      maxEvents: 1000,
      batchSize: 50,
      flushInterval: 60000, // 1 minute
      privacyMode: false,
      ...config
    };

    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<AnalyticsConfig>): UserAnalytics {
    if (!UserAnalytics.instance) {
      UserAnalytics.instance = new UserAnalytics(config);
    }
    return UserAnalytics.instance;
  }

  /**
   * Start user session
   */
  startSession(userId?: string): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentSession = {
      id: sessionId,
      userId,
      startTime: Date.now(),
      events: [],
      pageViews: 0,
      interactions: 0,
      errors: 0,
      features: new Set(),
      lastActivity: Date.now()
    };

    this.trackEvent('session', 'start', 'user_session', undefined, undefined, {
      sessionId,
      userId
    });

    this.startSessionTimer();
    
    this.logger.info('analytics', `User session started: ${sessionId}`, { userId });
    return sessionId;
  }

  /**
   * End user session
   */
  endSession(): UserSession | null {
    if (!this.currentSession) {
      return null;
    }

    this.currentSession.endTime = Date.now();
    this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;

    this.trackEvent('session', 'end', 'user_session', undefined, this.currentSession.duration, {
      sessionId: this.currentSession.id,
      duration: this.currentSession.duration,
      events: this.currentSession.events.length,
      interactions: this.currentSession.interactions,
      errors: this.currentSession.errors,
      features: Array.from(this.currentSession.features)
    });

    const session = this.currentSession;
    this.currentSession = undefined;

    this.stopSessionTimer();
    
    this.logger.info('analytics', `User session ended: ${session.id}`, {
      duration: session.duration,
      events: session.events.length
    });

    return session;
  }

  /**
   * Track user event
   */
  trackEvent(
    category: string,
    action: string,
    label?: string,
    value?: number,
    properties: Record<string, any> = {}
  ): void {
    if (!this.config.enabled) return;

    const event: UserEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'user_event',
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      sessionId: this.currentSession?.id || 'no_session',
      userId: this.currentSession?.userId,
      properties: this.config.privacyMode ? this.sanitizeProperties(properties) : properties,
      context: this.getEventContext()
    };

    this.processEvent(event);
  }

  /**
   * Track page view
   */
  trackPageView(page: string, title?: string, properties: Record<string, any> = {}): void {
    if (!this.config.enabled) return;

    this.trackEvent('navigation', 'page_view', page, undefined, {
      page,
      title,
      ...properties
    });

    if (this.currentSession) {
      this.currentSession.pageViews++;
    }
  }

  /**
   * Track user interaction
   */
  trackInteraction(
    element: string,
    action: string,
    properties: Record<string, any> = {}
  ): void {
    if (!this.config.enabled) return;

    this.trackEvent('interaction', action, element, undefined, {
      element,
      ...properties
    });

    if (this.currentSession) {
      this.currentSession.interactions++;
    }
  }

  /**
   * Track feature usage
   */
  trackFeatureUsage(
    feature: string,
    action: string,
    properties: Record<string, any> = {}
  ): void {
    if (!this.config.enabled || !this.config.enableFeatureUsage) return;

    this.trackEvent('feature', action, feature, undefined, {
      feature,
      ...properties
    });

    // Update feature usage statistics
    this.updateFeatureUsage(feature, action);

    if (this.currentSession) {
      this.currentSession.features.add(feature);
    }
  }

  /**
   * Track error
   */
  trackError(
    error: Error | string,
    category: string = 'javascript',
    properties: Record<string, any> = {}
  ): void {
    if (!this.config.enabled || !this.config.enableErrorTracking) return;

    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    this.trackEvent('error', category, errorMessage, undefined, {
      error: errorMessage,
      stack: this.config.privacyMode ? this.sanitizeStack(errorStack) : errorStack,
      ...properties
    });

    if (this.currentSession) {
      this.currentSession.errors++;
    }
  }

  /**
   * Track performance metric
   */
  trackPerformance(
    metric: string,
    value: number,
    unit: string = 'ms',
    properties: Record<string, any> = {}
  ): void {
    if (!this.config.enabled || !this.config.enablePerformanceTracking) return;

    this.trackEvent('performance', metric, undefined, value, {
      metric,
      unit,
      ...properties
    });
  }

  /**
   * Track user journey
   */
  trackUserJourney(journeyName: string, step: string, properties: Record<string, any> = {}): void {
    if (!this.config.enabled) return;

    this.trackEvent('journey', step, journeyName, undefined, {
      journey: journeyName,
      step,
      ...properties
    });
  }

  /**
   * Get current session
   */
  getCurrentSession(): UserSession | null {
    return this.currentSession || null;
  }

  /**
   * Get feature usage statistics
   */
  getFeatureUsage(feature?: string): FeatureUsage[] | FeatureUsage | null {
    if (feature) {
      return this.featureUsage.get(feature) || null;
    }

    return Array.from(this.featureUsage.values());
  }

  /**
   * Get user behavior patterns
   */
  getBehaviorPatterns(): UserBehaviorPattern[] {
    return Array.from(this.behaviorPatterns.values());
  }

  /**
   * Analyze user journey
   */
  analyzeUserJourney(sessionId: string): UserJourney | null {
    if (!this.currentSession || this.currentSession.id !== sessionId) {
      return null;
    }

    const events = this.currentSession.events.filter(e => e.category === 'journey');
    if (events.length === 0) {
      return null;
    }

    const startTime = events[0].timestamp;
    const steps = events.map((event, index) => ({
      event,
      timeFromStart: event.timestamp - startTime,
      timeFromPrevious: index > 0 ? event.timestamp - events[index - 1].timestamp : 0
    }));

    const totalDuration = events[events.length - 1].timestamp - startTime;
    const completionRate = this.calculateCompletionRate(events);
    const dropOffPoints = this.identifyDropOffPoints(events);

    return {
      sessionId,
      steps,
      totalDuration,
      completionRate,
      dropOffPoints
    };
  }

  /**
   * Get analytics summary
   */
  getAnalyticsSummary(): {
    currentSession: UserSession | null;
    totalEvents: number;
    topFeatures: Array<{ feature: string; usage: number }>;
    topActions: Array<{ action: string; count: number }>;
    errorRate: number;
    averageSessionDuration: number;
    behaviorPatterns: number;
  } {
    const events = this.eventQueue;
    const actionCounts: Record<string, number> = {};
    let totalErrors = 0;
    let totalSessions = 0;
    let totalSessionDuration = 0;

    events.forEach(event => {
      actionCounts[event.action] = (actionCounts[event.action] || 0) + 1;
      
      if (event.category === 'error') {
        totalErrors++;
      }
      
      if (event.action === 'end' && event.category === 'session' && event.value) {
        totalSessions++;
        totalSessionDuration += event.value;
      }
    });

    const topFeatures = Array.from(this.featureUsage.entries())
      .map(([feature, usage]) => ({ feature, usage: usage.usageCount }))
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 10);

    const topActions = Object.entries(actionCounts)
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      currentSession: this.currentSession,
      totalEvents: events.length,
      topFeatures,
      topActions,
      errorRate: events.length > 0 ? (totalErrors / events.length) * 100 : 0,
      averageSessionDuration: totalSessions > 0 ? totalSessionDuration / totalSessions : 0,
      behaviorPatterns: this.behaviorPatterns.size
    };
  }

  /**
   * Export analytics data
   */
  exportAnalyticsData(format: 'json' | 'csv' = 'json'): string {
    const data = {
      config: this.config,
      currentSession: this.currentSession,
      events: this.eventQueue,
      featureUsage: Array.from(this.featureUsage.entries()),
      behaviorPatterns: Array.from(this.behaviorPatterns.entries()),
      summary: this.getAnalyticsSummary()
    };

    if (format === 'csv') {
      return this.exportToCsv(this.eventQueue);
    }

    return JSON.stringify(data, null, 2);
  }

  /**
   * Clear analytics data
   */
  clearAnalyticsData(): void {
    this.eventQueue = [];
    this.featureUsage.clear();
    this.behaviorPatterns.clear();
    
    this.logger.info('analytics', 'Analytics data cleared');
  }

  /**
   * Process event
   */
  private processEvent(event: UserEvent): void {
    // Add to queue
    this.eventQueue.push(event);

    // Manage queue size
    if (this.eventQueue.length > this.config.maxEvents) {
      this.eventQueue = this.eventQueue.slice(-this.config.maxEvents);
    }

    // Add to current session
    if (this.currentSession) {
      this.currentSession.events.push(event);
      this.currentSession.lastActivity = Date.now();
    }

    // Update behavior patterns
    this.updateBehaviorPatterns(event);

    // Log event
    this.logger.debug('analytics', `Event tracked: ${event.category}.${event.action}`, {
      eventId: event.id,
      label: event.label,
      value: event.value,
      properties: event.properties
    });

    // Schedule flush if needed
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flushEvents();
    }
  }

  /**
   * Update feature usage
   */
  private updateFeatureUsage(feature: string, action: string): void {
    let usage = this.featureUsage.get(feature);
    
    if (!usage) {
      usage = {
        feature,
        usageCount: 0,
        uniqueUsers: new Set(),
        averageSessionTime: 0,
        lastUsed: 0,
        popularActions: {}
      };
      this.featureUsage.set(feature, usage);
    }

    usage.usageCount++;
    usage.lastUsed = Date.now();
    usage.popularActions[action] = (usage.popularActions[action] || 0) + 1;

    if (this.currentSession?.userId) {
      usage.uniqueUsers.add(this.currentSession.userId);
    }
  }

  /**
   * Update behavior patterns
   */
  private updateBehaviorPatterns(event: UserEvent): void {
    // Simple pattern detection based on action sequences
    const patternKey = `${event.category}.${event.action}`;
    
    let pattern = this.behaviorPatterns.get(patternKey);
    
    if (!pattern) {
      pattern = {
        pattern: patternKey,
        frequency: 0,
        users: new Set(),
        averageDuration: 0,
        successRate: 0,
        commonSequence: []
      };
      this.behaviorPatterns.set(patternKey, pattern);
    }

    pattern.frequency++;
    
    if (event.userId) {
      pattern.users.add(event.userId);
    }
  }

  /**
   * Get event context
   */
  private getEventContext(): UserEventContext {
    return {
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth || 0,
        height: window.innerHeight || 0
      },
      screen: {
        width: screen.width || 0,
        height: screen.height || 0
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform
    };
  }

  /**
   * Calculate completion rate
   */
  private calculateCompletionRate(events: UserEvent[]): number {
    // Simple completion rate based on journey events
    const startEvents = events.filter(e => e.action.includes('start'));
    const endEvents = events.filter(e => e.action.includes('end') || e.action.includes('complete'));
    
    if (startEvents.length === 0) return 0;
    
    return (endEvents.length / startEvents.length) * 100;
  }

  /**
   * Identify drop-off points
   */
  private identifyDropOffPoints(events: UserEvent[]): string[] {
    const dropOffPoints: string[] = [];
    
    // Analyze event sequences to find common drop-off points
    for (let i = 0; i < events.length - 1; i++) {
      const currentEvent = events[i];
      const nextEvent = events[i + 1];
      const timeDiff = nextEvent.timestamp - currentEvent.timestamp;
      
      // If there's a long gap (>5 minutes), consider it a drop-off point
      if (timeDiff > 5 * 60 * 1000) {
        dropOffPoints.push(`${currentEvent.category}.${currentEvent.action}`);
      }
    }
    
    return dropOffPoints;
  }

  /**
   * Sanitize properties for privacy
   */
  private sanitizeProperties(properties: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    Object.entries(properties).forEach(([key, value]) => {
      // Skip sensitive keys
      if (['password', 'token', 'key', 'secret', 'email', 'phone'].some(sensitive => 
          key.toLowerCase().includes(sensitive))) {
        return;
      }
      
      // Sanitize string values
      if (typeof value === 'string') {
        sanitized[key] = value
          .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]')
          .replace(/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, '[CARD]')
          .replace(/https?:\/\/[^\s]+/g, '[URL]');
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }

  /**
   * Sanitize stack trace
   */
  private sanitizeStack(stack?: string): string | undefined {
    if (!stack) return undefined;
    
    return stack
      .replace(/https?:\/\/[^\s]+/g, '[URL]')
      .replace(/file:\/\/[^\s]+/g, '[FILE]')
      .replace(/chrome-extension:\/\/[^\s]+/g, '[EXTENSION]');
  }

  /**
   * Export to CSV
   */
  private exportToCsv(events: UserEvent[]): string {
    const headers = [
      'Timestamp', 'Type', 'Category', 'Action', 'Label', 'Value',
      'SessionId', 'UserId', 'UserAgent', 'URL'
    ];
    
    const rows = events.map(event => [
      new Date(event.timestamp).toISOString(),
      event.type,
      event.category,
      event.action,
      event.label || '',
      event.value?.toString() || '',
      event.sessionId,
      event.userId || '',
      event.context.userAgent,
      event.context.url || ''
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');
  }

  /**
   * Flush events to endpoint
   */
  private async flushEvents(): Promise<void> {
    if (!this.config.endpoint || this.eventQueue.length === 0 || !this.isOnline) {
      return;
    }

    const eventsToSend = this.eventQueue.splice(0, this.config.batchSize);
    
    try {
      await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          events: eventsToSend,
          timestamp: Date.now()
        })
      });

      this.logger.debug('analytics', `Flushed ${eventsToSend.length} events to endpoint`);
    } catch (error) {
      // Put events back in queue for retry
      this.eventQueue.unshift(...eventsToSend);
      this.logger.error('analytics', 'Failed to flush events', error);
    }
  }

  /**
   * Start session timer
   */
  private startSessionTimer(): void {
    this.sessionTimer = setInterval(() => {
      if (this.currentSession) {
        const inactiveTime = Date.now() - this.currentSession.lastActivity;
        
        if (inactiveTime > this.config.sessionTimeout) {
          this.endSession();
        }
      }
    }, 60000); // Check every minute
  }

  /**
   * Stop session timer
   */
  private stopSessionTimer(): void {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = undefined;
    }
  }

  /**
   * Initialize analytics
   */
  private initialize(): void {
    // Setup flush timer
    this.flushTimer = setInterval(() => {
      this.flushEvents();
    }, this.config.flushInterval);

    // Setup online/offline listeners
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushEvents();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Setup page unload handler
    window.addEventListener('beforeunload', () => {
      this.endSession();
      this.flushEvents();
    });

    // Auto-start session if user tracking is enabled
    if (this.config.enableUserTracking && this.config.enableSessionTracking) {
      this.startSession();
    }

    this.logger.info('analytics', 'User analytics initialized', this.config);
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    this.stopSessionTimer();
    this.endSession();
    this.flushEvents();
  }
}

// Export convenience functions
export const userAnalytics = UserAnalytics.getInstance();

export function trackEvent(
  category: string,
  action: string,
  label?: string,
  value?: number,
  properties?: Record<string, any>
): void {
  userAnalytics.trackEvent(category, action, label, value, properties);
}

export function trackPageView(page: string, title?: string, properties?: Record<string, any>): void {
  userAnalytics.trackPageView(page, title, properties);
}

export function trackInteraction(element: string, action: string, properties?: Record<string, any>): void {
  userAnalytics.trackInteraction(element, action, properties);
}

export function trackFeatureUsage(feature: string, action: string, properties?: Record<string, any>): void {
  userAnalytics.trackFeatureUsage(feature, action, properties);
}

export function trackError(error: Error | string, category?: string, properties?: Record<string, any>): void {
  userAnalytics.trackError(error, category, properties);
}

export function trackPerformance(metric: string, value: number, unit?: string, properties?: Record<string, any>): void {
  userAnalytics.trackPerformance(metric, value, unit, properties);
}