/**
 * Logger Tests
 */

import { 
  <PERSON><PERSON>, 
  <PERSON>g<PERSON><PERSON>l, 
  <PERSON>g<PERSON><PERSON>,
  <PERSON><PERSON>ult<PERSON><PERSON><PERSON>,
  JsonFormatter,
  CompactFormatter,
  ConsoleTransport,
  StorageTransport,
  RemoteTransport,
  RateLimitFilter,
  SensitiveDataFilter
} from '../Logger';

// Mock Chrome API
const mockChrome = {
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    }
  }
};

(global as any).chrome = mockChrome;
(global as any).fetch = jest.fn();

describe('Logger', () => {
  let logger: Logger;

  beforeEach(() => {
    // Reset singleton
    (Logger as any).instance = undefined;
    logger = Logger.getInstance();
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should create singleton instance', () => {
      const instance1 = Logger.getInstance();
      const instance2 = Logger.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('should initialize with default config', () => {
      const instance = Logger.getInstance();
      expect(instance).toBeInstanceOf(Logger);
    });

    test('should initialize with custom config', () => {
      const config = {
        level: LogLevel.DEBUG,
        enableConsole: false,
        maxStorageEntries: 500
      };
      
      const instance = Logger.getInstance(config);
      expect(instance).toBeInstanceOf(Logger);
    });
  });

  describe('Log Levels', () => {
    test('should log trace messages', () => {
      logger.setLevel(LogLevel.TRACE);
      logger.trace('test', 'trace message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.TRACE);
      expect(entries[0].message).toBe('trace message');
    });

    test('should log debug messages', () => {
      logger.setLevel(LogLevel.DEBUG);
      logger.debug('test', 'debug message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.DEBUG);
    });

    test('should log info messages', () => {
      logger.setLevel(LogLevel.INFO);
      logger.info('test', 'info message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.INFO);
    });

    test('should log warn messages', () => {
      logger.setLevel(LogLevel.WARN);
      logger.warn('test', 'warn message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.WARN);
    });

    test('should log error messages', () => {
      logger.setLevel(LogLevel.ERROR);
      logger.error('test', 'error message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.ERROR);
      expect(entries[0].stack).toBeDefined();
    });

    test('should log fatal messages', () => {
      logger.setLevel(LogLevel.FATAL);
      logger.fatal('test', 'fatal message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries).toHaveLength(1);
      expect(entries[0].level).toBe(LogLevel.FATAL);
    });

    test('should respect log level filtering', () => {
      logger.setLevel(LogLevel.WARN);
      
      logger.debug('test', 'debug message');
      logger.info('test', 'info message');
      logger.warn('test', 'warn message');
      logger.error('test', 'error message');
      
      const entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(2);
      expect(entries[0].level).toBe(LogLevel.WARN);
      expect(entries[1].level).toBe(LogLevel.ERROR);
    });
  });

  describe('Context Management', () => {
    test('should set global context', () => {
      const context = {
        userId: 'user123',
        sessionId: 'session456'
      };
      
      logger.setContext(context);
      logger.info('test', 'test message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries[0].context).toMatchObject(context);
    });

    test('should merge context', () => {
      logger.setContext({ userId: 'user123' });
      logger.info('test', 'test message', undefined, { component: 'TestComponent' });
      
      const entries = logger.getRecentEntries(1);
      expect(entries[0].context).toMatchObject({
        userId: 'user123',
        component: 'TestComponent'
      });
    });

    test('should get current context', () => {
      const context = { userId: 'user123' };
      logger.setContext(context);
      
      expect(logger.getContext()).toMatchObject(context);
    });
  });

  describe('Category-specific Logging', () => {
    test('should set category-specific log level', () => {
      logger.setLevel(LogLevel.WARN);
      logger.setCategoryLevel('debug-category', LogLevel.DEBUG);
      
      logger.debug('normal-category', 'should not log');
      logger.debug('debug-category', 'should log');
      
      const entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(1);
      expect(entries[0].category).toBe('debug-category');
    });
  });

  describe('Child Loggers', () => {
    test('should create child logger with additional context', () => {
      const childLogger = logger.child({ component: 'TestComponent' });
      childLogger.info('test', 'child message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries[0].context).toMatchObject({ component: 'TestComponent' });
    });

    test('should create nested child loggers', () => {
      const childLogger = logger.child({ component: 'TestComponent' });
      const grandChildLogger = childLogger.child({ method: 'testMethod' });
      
      grandChildLogger.info('test', 'nested message');
      
      const entries = logger.getRecentEntries(1);
      expect(entries[0].context).toMatchObject({
        component: 'TestComponent',
        method: 'testMethod'
      });
    });
  });

  describe('Filters', () => {
    test('should apply rate limit filter', () => {
      const filter = new RateLimitFilter(2, 1000); // Max 2 per second
      logger.addFilter(filter);
      
      logger.info('test', 'message 1');
      logger.info('test', 'message 1');
      logger.info('test', 'message 1'); // Should be filtered
      
      const entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(2);
    });

    test('should apply sensitive data filter', () => {
      const filter = new SensitiveDataFilter();
      logger.addFilter(filter);
      
      logger.info('test', 'normal message');
      logger.info('test', 'password: secret123'); // Should be filtered
      
      const entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(1);
      expect(entries[0].message).toBe('normal message');
    });

    test('should remove filters', () => {
      const filter = new RateLimitFilter(1, 1000);
      logger.addFilter(filter);
      
      logger.info('test', 'message 1');
      logger.info('test', 'message 2'); // Should be filtered
      
      let entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(1);
      
      logger.removeFilter(filter);
      logger.info('test', 'message 3'); // Should not be filtered
      
      entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(2);
    });
  });

  describe('Buffer Management', () => {
    test('should get recent entries', () => {
      logger.info('test', 'message 1');
      logger.info('test', 'message 2');
      logger.info('test', 'message 3');
      
      const entries = logger.getRecentEntries(2);
      expect(entries).toHaveLength(2);
      expect(entries[0].message).toBe('message 2');
      expect(entries[1].message).toBe('message 3');
    });

    test('should filter entries by level', () => {
      logger.info('test', 'info message');
      logger.warn('test', 'warn message');
      logger.error('test', 'error message');
      
      const entries = logger.getRecentEntries(10, LogLevel.WARN);
      expect(entries).toHaveLength(2);
      expect(entries.every(e => e.level >= LogLevel.WARN)).toBe(true);
    });

    test('should filter entries by category', () => {
      logger.info('category1', 'message 1');
      logger.info('category2', 'message 2');
      logger.info('category1', 'message 3');
      
      const entries = logger.getRecentEntries(10, undefined, 'category1');
      expect(entries).toHaveLength(2);
      expect(entries.every(e => e.category === 'category1')).toBe(true);
    });

    test('should clear buffer', () => {
      logger.info('test', 'message 1');
      logger.info('test', 'message 2');
      
      let entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(2);
      
      logger.clearBuffer();
      
      entries = logger.getRecentEntries(10);
      expect(entries).toHaveLength(0);
    });
  });

  describe('Statistics', () => {
    test('should provide log statistics', () => {
      logger.info('category1', 'message 1');
      logger.warn('category2', 'message 2');
      logger.error('category1', 'message 3');
      
      const stats = logger.getStats();
      
      expect(stats.totalEntries).toBe(3);
      expect(stats.entriesByLevel).toHaveProperty('INFO');
      expect(stats.entriesByLevel).toHaveProperty('WARN');
      expect(stats.entriesByLevel).toHaveProperty('ERROR');
      expect(stats.entriesByCategory).toHaveProperty('category1');
      expect(stats.entriesByCategory).toHaveProperty('category2');
    });
  });

  describe('Import/Export', () => {
    test('should export logs as JSON', () => {
      logger.info('test', 'message 1');
      logger.warn('test', 'message 2');
      
      const exported = logger.exportLogs('json');
      const parsed = JSON.parse(exported);
      
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0].message).toBe('message 1');
      expect(parsed[1].message).toBe('message 2');
    });

    test('should export logs as CSV', () => {
      logger.info('test', 'message 1');
      logger.warn('test', 'message 2');
      
      const exported = logger.exportLogs('csv');
      const lines = exported.split('\n');
      
      expect(lines).toHaveLength(3); // Header + 2 data rows
      expect(lines[0]).toContain('Timestamp');
      expect(lines[0]).toContain('Level');
      expect(lines[0]).toContain('Category');
    });

    test('should export logs as text', () => {
      logger.info('test', 'message 1');
      logger.warn('test', 'message 2');
      
      const exported = logger.exportLogs('text');
      const lines = exported.split('\n');
      
      expect(lines).toHaveLength(2);
      expect(lines[0]).toContain('INFO');
      expect(lines[0]).toContain('message 1');
      expect(lines[1]).toContain('WARN');
      expect(lines[1]).toContain('message 2');
    });

    test('should import logs from JSON', () => {
      const logData = [
        {
          timestamp: Date.now(),
          level: LogLevel.INFO,
          category: 'test',
          message: 'imported message',
          data: { key: 'value' }
        }
      ];
      
      logger.importLogs(JSON.stringify(logData));
      
      const entries = logger.getRecentEntries(10);
      expect(entries.some(e => e.message === 'imported message')).toBe(true);
    });
  });

  describe('Flush Operations', () => {
    test('should flush logs', async () => {
      logger.info('test', 'message 1');
      logger.warn('test', 'message 2');
      
      await expect(logger.flush()).resolves.not.toThrow();
    });
  });
});

describe('Formatters', () => {
  describe('DefaultFormatter', () => {
    test('should format log entry with all fields', () => {
      const formatter = new DefaultFormatter();
      const entry: LogEntry = {
        timestamp: new Date('2023-01-01T12:00:00Z').getTime(),
        level: LogLevel.INFO,
        category: 'test',
        message: 'test message',
        data: { key: 'value' },
        context: { userId: 'user123' }
      };
      
      const formatted = formatter.format(entry);
      
      expect(formatted).toContain('2023-01-01T12:00:00.000Z');
      expect(formatted).toContain('[INFO ]');
      expect(formatted).toContain('test           ');
      expect(formatted).toContain('test message');
      expect(formatted).toContain('Data: {"key":"value"}');
      expect(formatted).toContain('Context: {"userId":"user123"}');
    });

    test('should format log entry without optional fields', () => {
      const formatter = new DefaultFormatter();
      const entry: LogEntry = {
        timestamp: new Date('2023-01-01T12:00:00Z').getTime(),
        level: LogLevel.WARN,
        category: 'test',
        message: 'simple message'
      };
      
      const formatted = formatter.format(entry);
      
      expect(formatted).toContain('[WARN ]');
      expect(formatted).toContain('simple message');
      expect(formatted).not.toContain('Data:');
      expect(formatted).not.toContain('Context:');
    });
  });

  describe('JsonFormatter', () => {
    test('should format log entry as JSON', () => {
      const formatter = new JsonFormatter();
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.ERROR,
        category: 'test',
        message: 'error message'
      };
      
      const formatted = formatter.format(entry);
      const parsed = JSON.parse(formatted);
      
      expect(parsed.level).toBe(LogLevel.ERROR);
      expect(parsed.category).toBe('test');
      expect(parsed.message).toBe('error message');
    });
  });

  describe('CompactFormatter', () => {
    test('should format log entry compactly', () => {
      const formatter = new CompactFormatter();
      const entry: LogEntry = {
        timestamp: new Date('2023-01-01T12:00:00Z').getTime(),
        level: LogLevel.DEBUG,
        category: 'test',
        message: 'debug message'
      };
      
      const formatted = formatter.format(entry);
      
      expect(formatted).toMatch(/\d{1,2}:\d{2}:\d{2} D \[test\] debug message/);
    });
  });
});

describe('Transports', () => {
  describe('ConsoleTransport', () => {
    test('should write to console', async () => {
      const transport = new ConsoleTransport();
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation();
      
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.INFO,
        category: 'test',
        message: 'test message'
      };
      
      await transport.write(entry);
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    test('should use appropriate console method for level', async () => {
      const transport = new ConsoleTransport();
      const debugSpy = jest.spyOn(console, 'debug').mockImplementation();
      const warnSpy = jest.spyOn(console, 'warn').mockImplementation();
      const errorSpy = jest.spyOn(console, 'error').mockImplementation();
      
      await transport.write({
        timestamp: Date.now(),
        level: LogLevel.DEBUG,
        category: 'test',
        message: 'debug'
      });
      
      await transport.write({
        timestamp: Date.now(),
        level: LogLevel.WARN,
        category: 'test',
        message: 'warn'
      });
      
      await transport.write({
        timestamp: Date.now(),
        level: LogLevel.ERROR,
        category: 'test',
        message: 'error'
      });
      
      expect(debugSpy).toHaveBeenCalled();
      expect(warnSpy).toHaveBeenCalled();
      expect(errorSpy).toHaveBeenCalled();
      
      debugSpy.mockRestore();
      warnSpy.mockRestore();
      errorSpy.mockRestore();
    });
  });

  describe('StorageTransport', () => {
    test('should write to storage', async () => {
      mockChrome.storage.local.get.mockResolvedValue({});
      mockChrome.storage.local.set.mockResolvedValue(undefined);
      
      const transport = new StorageTransport(100);
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.INFO,
        category: 'test',
        message: 'test message'
      };
      
      await transport.write(entry);
      
      expect(mockChrome.storage.local.set).toHaveBeenCalled();
    });

    test('should manage storage size limit', async () => {
      const existingLogs = Array(150).fill(null).map((_, i) => ({
        timestamp: Date.now() - i,
        level: LogLevel.INFO,
        category: 'test',
        message: `message ${i}`
      }));
      
      mockChrome.storage.local.get.mockResolvedValue({
        sidebar_logs: existingLogs
      });
      mockChrome.storage.local.set.mockResolvedValue(undefined);
      
      const transport = new StorageTransport(100);
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.INFO,
        category: 'test',
        message: 'new message'
      };
      
      await transport.write(entry);
      
      const setCall = mockChrome.storage.local.set.mock.calls[0][0];
      expect(setCall.sidebar_logs).toHaveLength(100);
    });

    test('should get logs from storage', async () => {
      const logs = [
        { timestamp: Date.now(), level: LogLevel.INFO, category: 'test', message: 'message 1' },
        { timestamp: Date.now(), level: LogLevel.WARN, category: 'test', message: 'message 2' }
      ];
      
      mockChrome.storage.local.get.mockResolvedValue({ sidebar_logs: logs });
      
      const transport = new StorageTransport();
      const result = await transport.getLogs();
      
      expect(result).toEqual(logs);
    });

    test('should clear logs from storage', async () => {
      mockChrome.storage.local.remove.mockResolvedValue(undefined);
      
      const transport = new StorageTransport();
      await transport.clearLogs();
      
      expect(mockChrome.storage.local.remove).toHaveBeenCalledWith('sidebar_logs');
    });
  });

  describe('RemoteTransport', () => {
    test('should send logs to remote endpoint', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        status: 200
      });
      
      const transport = new RemoteTransport('https://api.example.com/logs');
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.ERROR,
        category: 'test',
        message: 'error message'
      };
      
      await transport.write(entry);
      
      // Wait for immediate flush (error level)
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.example.com/logs',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('error message')
        })
      );
    });

    test('should handle remote endpoint errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      const transport = new RemoteTransport('https://api.example.com/logs');
      const entry: LogEntry = {
        timestamp: Date.now(),
        level: LogLevel.ERROR,
        category: 'test',
        message: 'error message'
      };
      
      // Should not throw
      await expect(transport.write(entry)).resolves.not.toThrow();
    });
  });
});