/**
 * DOM Optimizer - DOM操作优化和内存管理
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';

export interface DOMOptimizationConfig {
  enableBatching: boolean;
  enableVirtualization: boolean;
  enableLazyLoading: boolean;
  enableMemoryCleanup: boolean;
  batchSize: number;
  batchDelay: number;
  virtualThreshold: number;
  lazyThreshold: number;
}

export interface DOMOperation {
  id: string;
  type: 'create' | 'update' | 'remove' | 'style' | 'attribute';
  element: Element | string;
  property?: string;
  value?: any;
  priority: number;
  timestamp: number;
}

export interface VirtualListConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
  data: any[];
  renderItem: (item: any, index: number) => HTMLElement;
}

export interface LazyLoadConfig {
  rootMargin: string;
  threshold: number;
  placeholder?: HTMLElement;
  onLoad: (element: Element) => void;
}

export interface MemoryCleanupResult {
  elementsRemoved: number;
  listenersRemoved: number;
  memoryFreed: number;
  duration: number;
}

/**
 * DOM Optimizer
 */
export class DOMOptimizer {
  private static instance: DOMOptimizer;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private config: DOMOptimizationConfig;
  private operationQueue: DOMOperation[] = [];
  private batchTimer?: NodeJS.Timeout;
  private mutationObserver?: MutationObserver;
  private intersectionObserver?: IntersectionObserver;
  private virtualLists: Map<string, VirtualList> = new Map();
  private lazyElements: WeakSet<Element> = new WeakSet();
  private elementRegistry: WeakMap<Element, ElementMetadata> = new WeakMap();

  private constructor(config: Partial<DOMOptimizationConfig> = {}) {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    
    this.config = {
      enableBatching: true,
      enableVirtualization: true,
      enableLazyLoading: true,
      enableMemoryCleanup: true,
      batchSize: 50,
      batchDelay: 16, // ~60fps
      virtualThreshold: 100,
      lazyThreshold: 10,
      ...config
    };

    this.initialize();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<DOMOptimizationConfig>): DOMOptimizer {
    if (!DOMOptimizer.instance) {
      DOMOptimizer.instance = new DOMOptimizer(config);
    }
    return DOMOptimizer.instance;
  }

  /**
   * Batch DOM operation
   */
  batchOperation(operation: Omit<DOMOperation, 'id' | 'timestamp'>): void {
    if (!this.config.enableBatching) {
      this.executeOperation(operation);
      return;
    }

    const domOperation: DOMOperation = {
      ...operation,
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    this.operationQueue.push(domOperation);
    this.scheduleBatch();
  }

  /**
   * Create virtual list
   */
  createVirtualList(containerId: string, config: VirtualListConfig): VirtualList {
    const virtualList = new VirtualList(containerId, config, this.logger, this.performanceMonitor);
    this.virtualLists.set(containerId, virtualList);
    
    this.logger.info('dom', `Virtual list created: ${containerId}`, {
      itemCount: config.data.length,
      itemHeight: config.itemHeight
    });

    return virtualList;
  }

  /**
   * Setup lazy loading
   */
  setupLazyLoading(elements: Element[], config: LazyLoadConfig): void {
    if (!this.config.enableLazyLoading) {
      return;
    }

    if (!this.intersectionObserver) {
      this.intersectionObserver = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: config.rootMargin,
          threshold: config.threshold
        }
      );
    }

    elements.forEach(element => {
      this.lazyElements.add(element);
      this.elementRegistry.set(element, {
        lazyConfig: config,
        isLoaded: false,
        loadTime: 0
      });
      
      this.intersectionObserver!.observe(element);
    });

    this.logger.info('dom', `Lazy loading setup for ${elements.length} elements`);
  }

  /**
   * Optimize element creation
   */
  createElement(tagName: string, options: {
    className?: string;
    id?: string;
    attributes?: Record<string, string>;
    styles?: Record<string, string>;
    textContent?: string;
    innerHTML?: string;
  } = {}): HTMLElement {
    this.performanceMonitor.startTiming('dom.createElement', 'dom');

    const element = document.createElement(tagName);

    // Batch all modifications
    if (options.className) element.className = options.className;
    if (options.id) element.id = options.id;
    if (options.textContent) element.textContent = options.textContent;
    if (options.innerHTML) element.innerHTML = options.innerHTML;

    // Set attributes
    if (options.attributes) {
      Object.entries(options.attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
    }

    // Set styles
    if (options.styles) {
      Object.entries(options.styles).forEach(([property, value]) => {
        (element.style as any)[property] = value;
      });
    }

    // Register element
    this.elementRegistry.set(element, {
      createdAt: Date.now(),
      tagName,
      isOptimized: true
    });

    this.performanceMonitor.endTiming('dom.createElement', 'dom');
    return element;
  }

  /**
   * Optimize element updates
   */
  updateElement(element: Element, updates: {
    className?: string;
    attributes?: Record<string, string>;
    styles?: Record<string, string>;
    textContent?: string;
    innerHTML?: string;
  }): void {
    this.performanceMonitor.startTiming('dom.updateElement', 'dom');

    // Batch updates to minimize reflows
    const fragment = document.createDocumentFragment();
    const clone = element.cloneNode(true) as Element;

    if (updates.className !== undefined) clone.className = updates.className;
    if (updates.textContent !== undefined) clone.textContent = updates.textContent;
    if (updates.innerHTML !== undefined) clone.innerHTML = updates.innerHTML;

    if (updates.attributes) {
      Object.entries(updates.attributes).forEach(([key, value]) => {
        clone.setAttribute(key, value);
      });
    }

    if (updates.styles) {
      Object.entries(updates.styles).forEach(([property, value]) => {
        (clone as HTMLElement).style.setProperty(property, value);
      });
    }

    // Replace element in one operation
    element.parentNode?.replaceChild(clone, element);

    this.performanceMonitor.endTiming('dom.updateElement', 'dom');
  }

  /**
   * Optimize list rendering
   */
  renderList<T>(
    container: Element,
    items: T[],
    renderItem: (item: T, index: number) => HTMLElement,
    options: {
      useVirtualization?: boolean;
      itemHeight?: number;
      chunkSize?: number;
    } = {}
  ): void {
    this.performanceMonitor.startTiming('dom.renderList', 'dom');

    const { useVirtualization = items.length > this.config.virtualThreshold, chunkSize = 50 } = options;

    if (useVirtualization && options.itemHeight) {
      // Use virtual list
      const virtualList = this.createVirtualList(container.id || 'virtual-list', {
        itemHeight: options.itemHeight,
        containerHeight: container.clientHeight,
        overscan: 5,
        data: items,
        renderItem: renderItem as any
      });
      
      virtualList.render();
    } else {
      // Use chunked rendering
      this.renderListInChunks(container, items, renderItem, chunkSize);
    }

    this.performanceMonitor.endTiming('dom.renderList', 'dom');
  }

  /**
   * Clean up memory
   */
  cleanupMemory(): MemoryCleanupResult {
    this.performanceMonitor.startTiming('dom.cleanup', 'dom');
    const startTime = Date.now();
    let elementsRemoved = 0;
    let listenersRemoved = 0;

    // Clean up detached elements
    const detachedElements = this.findDetachedElements();
    detachedElements.forEach(element => {
      this.cleanupElement(element);
      elementsRemoved++;
    });

    // Clean up virtual lists
    this.virtualLists.forEach((virtualList, id) => {
      if (!document.getElementById(id)) {
        virtualList.dispose();
        this.virtualLists.delete(id);
      }
    });

    // Force garbage collection if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }

    const duration = Date.now() - startTime;
    const memoryFreed = this.estimateMemoryFreed(elementsRemoved, listenersRemoved);

    this.performanceMonitor.endTiming('dom.cleanup', 'dom');

    const result: MemoryCleanupResult = {
      elementsRemoved,
      listenersRemoved,
      memoryFreed,
      duration
    };

    this.logger.info('dom', 'Memory cleanup completed', result);
    return result;
  }

  /**
   * Get DOM statistics
   */
  getDOMStats(): {
    totalElements: number;
    optimizedElements: number;
    virtualLists: number;
    lazyElements: number;
    queuedOperations: number;
    memoryUsage: number;
  } {
    const totalElements = document.querySelectorAll('*').length;
    let optimizedElements = 0;
    let lazyElementsCount = 0;

    // Count optimized elements
    document.querySelectorAll('*').forEach(element => {
      const metadata = this.elementRegistry.get(element);
      if (metadata?.isOptimized) {
        optimizedElements++;
      }
      if (this.lazyElements.has(element)) {
        lazyElementsCount++;
      }
    });

    const memoryUsage = 'memory' in performance ? 
      (performance as any).memory.usedJSHeapSize : 0;

    return {
      totalElements,
      optimizedElements,
      virtualLists: this.virtualLists.size,
      lazyElements: lazyElementsCount,
      queuedOperations: this.operationQueue.length,
      memoryUsage
    };
  }

  /**
   * Initialize DOM optimizer
   */
  private initialize(): void {
    // Setup mutation observer
    if (this.config.enableMemoryCleanup) {
      this.mutationObserver = new MutationObserver(this.handleMutations.bind(this));
      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false,
        attributeOldValue: false,
        characterData: false,
        characterDataOldValue: false
      });
    }

    // Setup periodic cleanup
    setInterval(() => {
      if (this.config.enableMemoryCleanup) {
        this.cleanupMemory();
      }
    }, 60000); // Every minute

    this.logger.info('dom', 'DOM optimizer initialized', this.config);
  }

  /**
   * Schedule batch processing
   */
  private scheduleBatch(): void {
    if (this.batchTimer) {
      return;
    }

    this.batchTimer = setTimeout(() => {
      this.processBatch();
      this.batchTimer = undefined;
    }, this.config.batchDelay);

    // Process immediately if queue is full
    if (this.operationQueue.length >= this.config.batchSize) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
      this.processBatch();
    }
  }

  /**
   * Process batch of operations
   */
  private processBatch(): void {
    if (this.operationQueue.length === 0) {
      return;
    }

    this.performanceMonitor.startTiming('dom.batch', 'dom');

    const operations = this.operationQueue.splice(0, this.config.batchSize);
    
    // Sort by priority
    operations.sort((a, b) => b.priority - a.priority);

    // Execute operations
    operations.forEach(operation => {
      try {
        this.executeOperation(operation);
      } catch (error) {
        this.logger.error('dom', `Failed to execute DOM operation: ${operation.id}`, error);
      }
    });

    this.performanceMonitor.endTiming('dom.batch', 'dom');
    
    this.logger.debug('dom', `Processed batch of ${operations.length} DOM operations`);

    // Schedule next batch if queue is not empty
    if (this.operationQueue.length > 0) {
      this.scheduleBatch();
    }
  }

  /**
   * Execute DOM operation
   */
  private executeOperation(operation: Omit<DOMOperation, 'id' | 'timestamp'>): void {
    const element = typeof operation.element === 'string' ? 
      document.querySelector(operation.element) : operation.element;

    if (!element) {
      this.logger.warn('dom', `Element not found for operation: ${operation.type}`);
      return;
    }

    switch (operation.type) {
      case 'create':
        // Handle element creation
        break;
      case 'update':
        if (operation.property && operation.value !== undefined) {
          (element as any)[operation.property] = operation.value;
        }
        break;
      case 'remove':
        element.remove();
        this.cleanupElement(element);
        break;
      case 'style':
        if (operation.property && operation.value !== undefined) {
          (element as HTMLElement).style.setProperty(operation.property, operation.value);
        }
        break;
      case 'attribute':
        if (operation.property && operation.value !== undefined) {
          element.setAttribute(operation.property, operation.value);
        }
        break;
    }
  }

  /**
   * Handle intersection observer
   */
  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target;
        const metadata = this.elementRegistry.get(element);
        
        if (metadata?.lazyConfig && !metadata.isLoaded) {
          this.performanceMonitor.startTiming('dom.lazyLoad', 'dom');
          
          metadata.lazyConfig.onLoad(element);
          metadata.isLoaded = true;
          metadata.loadTime = Date.now();
          
          this.intersectionObserver?.unobserve(element);
          
          this.performanceMonitor.endTiming('dom.lazyLoad', 'dom');
          
          this.logger.debug('dom', 'Lazy loaded element', {
            tagName: element.tagName,
            id: element.id,
            className: element.className
          });
        }
      }
    });
  }

  /**
   * Handle mutations
   */
  private handleMutations(mutations: MutationRecord[]): void {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        // Track removed nodes for cleanup
        mutation.removedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            this.cleanupElement(node as Element);
          }
        });
      }
    });
  }

  /**
   * Render list in chunks
   */
  private renderListInChunks<T>(
    container: Element,
    items: T[],
    renderItem: (item: T, index: number) => HTMLElement,
    chunkSize: number
  ): void {
    let index = 0;

    const renderChunk = () => {
      const fragment = document.createDocumentFragment();
      const endIndex = Math.min(index + chunkSize, items.length);

      for (let i = index; i < endIndex; i++) {
        const element = renderItem(items[i], i);
        fragment.appendChild(element);
      }

      container.appendChild(fragment);
      index = endIndex;

      if (index < items.length) {
        requestAnimationFrame(renderChunk);
      }
    };

    // Clear container
    container.innerHTML = '';
    renderChunk();
  }

  /**
   * Find detached elements
   */
  private findDetachedElements(): Element[] {
    const detached: Element[] = [];
    
    // This is a simplified implementation
    // In practice, you'd track elements more carefully
    
    return detached;
  }

  /**
   * Cleanup element
   */
  private cleanupElement(element: Element): void {
    // Remove from registries
    this.elementRegistry.delete(element);
    this.lazyElements.delete(element);

    // Remove event listeners (if tracked)
    // This would require more sophisticated tracking

    // Clean up child elements recursively
    element.querySelectorAll('*').forEach(child => {
      this.elementRegistry.delete(child);
      this.lazyElements.delete(child);
    });
  }

  /**
   * Estimate memory freed
   */
  private estimateMemoryFreed(elementsRemoved: number, listenersRemoved: number): number {
    // Rough estimation: 1KB per element, 100 bytes per listener
    return (elementsRemoved * 1024) + (listenersRemoved * 100);
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = undefined;
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
      this.intersectionObserver = undefined;
    }

    this.virtualLists.forEach(virtualList => {
      virtualList.dispose();
    });
    this.virtualLists.clear();

    this.operationQueue = [];
    this.elementRegistry = new WeakMap();
    this.lazyElements = new WeakSet();
  }
}

/**
 * Virtual List Implementation
 */
class VirtualList {
  private container: HTMLElement;
  private config: VirtualListConfig;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private scrollTop = 0;
  private visibleItems: HTMLElement[] = [];
  private startIndex = 0;
  private endIndex = 0;

  constructor(
    containerId: string,
    config: VirtualListConfig,
    logger: Logger,
    performanceMonitor: PerformanceMonitor
  ) {
    const container = document.getElementById(containerId);
    if (!container) {
      throw new Error(`Container with id ${containerId} not found`);
    }

    this.container = container;
    this.config = config;
    this.logger = logger;
    this.performanceMonitor = performanceMonitor;

    this.setupContainer();
    this.setupScrollListener();
  }

  /**
   * Render virtual list
   */
  render(): void {
    this.performanceMonitor.startTiming('virtualList.render', 'dom');

    this.calculateVisibleRange();
    this.renderVisibleItems();

    this.performanceMonitor.endTiming('virtualList.render', 'dom');
  }

  /**
   * Update data
   */
  updateData(data: any[]): void {
    this.config.data = data;
    this.render();
  }

  /**
   * Scroll to index
   */
  scrollToIndex(index: number): void {
    const scrollTop = index * this.config.itemHeight;
    this.container.scrollTop = scrollTop;
  }

  /**
   * Setup container
   */
  private setupContainer(): void {
    this.container.style.overflow = 'auto';
    this.container.style.position = 'relative';

    // Create spacer for total height
    const spacer = document.createElement('div');
    spacer.style.height = `${this.config.data.length * this.config.itemHeight}px`;
    spacer.style.position = 'absolute';
    spacer.style.top = '0';
    spacer.style.left = '0';
    spacer.style.right = '0';
    spacer.style.pointerEvents = 'none';
    
    this.container.appendChild(spacer);
  }

  /**
   * Setup scroll listener
   */
  private setupScrollListener(): void {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.scrollTop = this.container.scrollTop;
          this.render();
          ticking = false;
        });
        ticking = true;
      }
    };

    this.container.addEventListener('scroll', handleScroll, { passive: true });
  }

  /**
   * Calculate visible range
   */
  private calculateVisibleRange(): void {
    const visibleStart = Math.floor(this.scrollTop / this.config.itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(this.config.containerHeight / this.config.itemHeight),
      this.config.data.length - 1
    );

    this.startIndex = Math.max(0, visibleStart - this.config.overscan);
    this.endIndex = Math.min(this.config.data.length - 1, visibleEnd + this.config.overscan);
  }

  /**
   * Render visible items
   */
  private renderVisibleItems(): void {
    // Remove existing items
    this.visibleItems.forEach(item => {
      if (item.parentNode) {
        item.parentNode.removeChild(item);
      }
    });
    this.visibleItems = [];

    // Render new items
    for (let i = this.startIndex; i <= this.endIndex; i++) {
      const item = this.config.renderItem(this.config.data[i], i);
      
      // Position item
      item.style.position = 'absolute';
      item.style.top = `${i * this.config.itemHeight}px`;
      item.style.left = '0';
      item.style.right = '0';
      item.style.height = `${this.config.itemHeight}px`;

      this.container.appendChild(item);
      this.visibleItems.push(item);
    }
  }

  /**
   * Dispose virtual list
   */
  dispose(): void {
    this.visibleItems.forEach(item => {
      if (item.parentNode) {
        item.parentNode.removeChild(item);
      }
    });
    this.visibleItems = [];
  }
}

/**
 * Element metadata interface
 */
interface ElementMetadata {
  createdAt?: number;
  tagName?: string;
  isOptimized?: boolean;
  lazyConfig?: LazyLoadConfig;
  isLoaded?: boolean;
  loadTime?: number;
}

// Export convenience functions
export const domOptimizer = DOMOptimizer.getInstance();

export function batchDOMOperation(operation: Omit<DOMOperation, 'id' | 'timestamp'>): void {
  domOptimizer.batchOperation(operation);
}

export function createOptimizedElement(
  tagName: string,
  options?: Parameters<typeof domOptimizer.createElement>[1]
): HTMLElement {
  return domOptimizer.createElement(tagName, options);
}

export function setupLazyLoading(elements: Element[], config: LazyLoadConfig): void {
  domOptimizer.setupLazyLoading(elements, config);
}

export function renderOptimizedList<T>(
  container: Element,
  items: T[],
  renderItem: (item: T, index: number) => HTMLElement,
  options?: Parameters<typeof domOptimizer.renderList>[3]
): void {
  domOptimizer.renderList(container, items, renderItem, options);
}

export function cleanupDOMMemory(): MemoryCleanupResult {
  return domOptimizer.cleanupMemory();
}