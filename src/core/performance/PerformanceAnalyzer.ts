/**
 * Performance Analyzer - 性能分析器
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor, PerformanceMetric } from '../logging/PerformanceMonitor';

export interface PerformanceReport {
  timestamp: number;
  duration: number;
  metrics: {
    memory: MemoryAnalysis;
    timing: TimingAnalysis;
    network: NetworkAnalysis;
    dom: DOMAnalysis;
    rendering: RenderingAnalysis;
  };
  issues: PerformanceIssue[];
  recommendations: string[];
  score: number;
}

export interface MemoryAnalysis {
  current: number;
  peak: number;
  average: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  leaks: MemoryLeak[];
  efficiency: number;
}

export interface TimingAnalysis {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  criticalPath: string[];
  bottlenecks: Bottleneck[];
}

export interface NetworkAnalysis {
  totalRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  slowRequests: SlowRequest[];
  cacheHitRate: number;
}

export interface DOMAnalysis {
  elementCount: number;
  depth: number;
  complexity: number;
  mutations: number;
  inefficiencies: DOMInefficiency[];
}

export interface RenderingAnalysis {
  fps: number;
  frameDrops: number;
  paintTime: number;
  layoutTime: number;
  compositeTime: number;
  renderingIssues: RenderingIssue[];
}

export interface PerformanceIssue {
  id: string;
  type: 'memory' | 'timing' | 'network' | 'dom' | 'rendering';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  solution: string;
  metrics: Record<string, number>;
}

export interface MemoryLeak {
  component: string;
  size: number;
  growth: number;
  duration: number;
}

export interface Bottleneck {
  operation: string;
  duration: number;
  frequency: number;
  impact: number;
}

export interface SlowRequest {
  url: string;
  method: string;
  duration: number;
  size: number;
  status: number;
}

export interface DOMInefficiency {
  type: 'deep_nesting' | 'excessive_elements' | 'frequent_mutations' | 'large_subtrees';
  element: string;
  impact: number;
  suggestion: string;
}

export interface RenderingIssue {
  type: 'layout_thrashing' | 'paint_storms' | 'composite_layers' | 'animation_jank';
  frequency: number;
  impact: number;
  cause: string;
}

/**
 * Performance Analyzer
 */
export class PerformanceAnalyzer {
  private static instance: PerformanceAnalyzer;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private analysisHistory: PerformanceReport[] = [];
  private isAnalyzing = false;
  private observers: PerformanceObserver[] = [];
  private memorySnapshots: Array<{ timestamp: number; usage: number }> = [];
  private renderingMetrics: Array<{ timestamp: number; fps: number }> = [];

  private constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.setupObservers();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): PerformanceAnalyzer {
    if (!PerformanceAnalyzer.instance) {
      PerformanceAnalyzer.instance = new PerformanceAnalyzer();
    }
    return PerformanceAnalyzer.instance;
  }
} 
 /**
   * Analyze current performance
   */
  async analyzePerformance(duration: number = 30000): Promise<PerformanceReport> {
    if (this.isAnalyzing) {
      throw new Error('Performance analysis already in progress');
    }

    this.isAnalyzing = true;
    const startTime = Date.now();

    try {
      this.logger.info('performance', 'Starting performance analysis', { duration });

      // Collect baseline metrics
      await this.collectBaselineMetrics();

      // Wait for analysis duration
      await new Promise(resolve => setTimeout(resolve, duration));

      // Analyze collected data
      const report = await this.generateReport(startTime, duration);

      this.analysisHistory.push(report);
      
      // Keep only recent reports
      if (this.analysisHistory.length > 10) {
        this.analysisHistory = this.analysisHistory.slice(-10);
      }

      this.logger.info('performance', 'Performance analysis completed', {
        score: report.score,
        issues: report.issues.length,
        recommendations: report.recommendations.length
      });

      return report;
    } finally {
      this.isAnalyzing = false;
    }
  }

  /**
   * Get analysis history
   */
  getAnalysisHistory(): PerformanceReport[] {
    return [...this.analysisHistory];
  }

  /**
   * Get latest report
   */
  getLatestReport(): PerformanceReport | null {
    return this.analysisHistory.length > 0 ? 
      this.analysisHistory[this.analysisHistory.length - 1] : null;
  }

  /**
   * Analyze memory usage
   */
  analyzeMemory(): MemoryAnalysis {
    const snapshots = this.memorySnapshots.slice(-100); // Last 100 snapshots
    
    if (snapshots.length === 0) {
      return {
        current: 0,
        peak: 0,
        average: 0,
        trend: 'stable',
        leaks: [],
        efficiency: 100
      };
    }

    const current = snapshots[snapshots.length - 1].usage;
    const peak = Math.max(...snapshots.map(s => s.usage));
    const average = snapshots.reduce((sum, s) => sum + s.usage, 0) / snapshots.length;
    
    // Analyze trend
    const recentSnapshots = snapshots.slice(-10);
    const trend = this.analyzeTrend(recentSnapshots.map(s => s.usage));
    
    // Detect memory leaks
    const leaks = this.detectMemoryLeaks(snapshots);
    
    // Calculate efficiency
    const efficiency = this.calculateMemoryEfficiency(snapshots);

    return {
      current,
      peak,
      average,
      trend,
      leaks,
      efficiency
    };
  }

  /**
   * Analyze timing performance
   */
  analyzeTiming(): TimingAnalysis {
    const metrics = this.performanceMonitor.getMetrics('performance');
    
    const loadTimeMetrics = metrics.filter(m => m.name.includes('load'));
    const renderTimeMetrics = metrics.filter(m => m.name.includes('render'));
    const interactionMetrics = metrics.filter(m => m.name.includes('interaction'));

    const loadTime = this.calculateAverageMetric(loadTimeMetrics);
    const renderTime = this.calculateAverageMetric(renderTimeMetrics);
    const interactionTime = this.calculateAverageMetric(interactionMetrics);

    const criticalPath = this.identifyCriticalPath(metrics);
    const bottlenecks = this.identifyBottlenecks(metrics);

    return {
      loadTime,
      renderTime,
      interactionTime,
      criticalPath,
      bottlenecks
    };
  }

  /**
   * Analyze network performance
   */
  analyzeNetwork(): NetworkAnalysis {
    const networkMetrics = this.performanceMonitor.getMetrics('network');
    
    const totalRequests = networkMetrics.length;
    const failedRequests = networkMetrics.filter(m => 
      m.name === 'network.request' && m.value > 5000 // > 5s considered failed
    ).length;
    
    const responseTimes = networkMetrics
      .filter(m => m.name === 'network.request')
      .map(m => m.value);
    
    const averageResponseTime = responseTimes.length > 0 ? 
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

    const slowRequests = this.identifySlowRequests(networkMetrics);
    const cacheHitRate = this.calculateCacheHitRate(networkMetrics);

    return {
      totalRequests,
      failedRequests,
      averageResponseTime,
      slowRequests,
      cacheHitRate
    };
  }

  /**
   * Analyze DOM performance
   */
  analyzeDom(): DOMAnalysis {
    const elementCount = document.querySelectorAll('*').length;
    const depth = this.calculateDOMDepth();
    const complexity = this.calculateDOMComplexity();
    const mutations = this.getDOMMutationCount();
    const inefficiencies = this.identifyDOMInefficiencies();

    return {
      elementCount,
      depth,
      complexity,
      mutations,
      inefficiencies
    };
  }

  /**
   * Analyze rendering performance
   */
  analyzeRendering(): RenderingAnalysis {
    const recentMetrics = this.renderingMetrics.slice(-60); // Last 60 frames
    
    const fps = recentMetrics.length > 0 ? 
      recentMetrics.reduce((sum, m) => sum + m.fps, 0) / recentMetrics.length : 60;
    
    const frameDrops = recentMetrics.filter(m => m.fps < 30).length;
    
    const paintMetrics = this.performanceMonitor.getMetrics('ui').filter(m => m.name.includes('paint'));
    const layoutMetrics = this.performanceMonitor.getMetrics('ui').filter(m => m.name.includes('layout'));
    const compositeMetrics = this.performanceMonitor.getMetrics('ui').filter(m => m.name.includes('composite'));

    const paintTime = this.calculateAverageMetric(paintMetrics);
    const layoutTime = this.calculateAverageMetric(layoutMetrics);
    const compositeTime = this.calculateAverageMetric(compositeMetrics);

    const renderingIssues = this.identifyRenderingIssues();

    return {
      fps,
      frameDrops,
      paintTime,
      layoutTime,
      compositeTime,
      renderingIssues
    };
  }

  /**
   * Generate performance report
   */
  private async generateReport(startTime: number, duration: number): Promise<PerformanceReport> {
    const memory = this.analyzeMemory();
    const timing = this.analyzeTiming();
    const network = this.analyzeNetwork();
    const dom = this.analyzeDom();
    const rendering = this.analyzeRendering();

    const issues = this.identifyPerformanceIssues({
      memory,
      timing,
      network,
      dom,
      rendering
    });

    const recommendations = this.generateRecommendations(issues);
    const score = this.calculatePerformanceScore({
      memory,
      timing,
      network,
      dom,
      rendering
    });

    return {
      timestamp: startTime,
      duration,
      metrics: {
        memory,
        timing,
        network,
        dom,
        rendering
      },
      issues,
      recommendations,
      score
    };
  }

  /**
   * Collect baseline metrics
   */
  private async collectBaselineMetrics(): Promise<void> {
    // Take memory snapshot
    this.takeMemorySnapshot();
    
    // Measure initial render time
    this.performanceMonitor.startTiming('baseline.render', 'performance');
    await new Promise(resolve => requestAnimationFrame(resolve));
    this.performanceMonitor.endTiming('baseline.render', 'performance');
    
    // Measure DOM complexity
    this.performanceMonitor.setGauge('baseline.dom.elements', document.querySelectorAll('*').length, 'performance');
    this.performanceMonitor.setGauge('baseline.dom.depth', this.calculateDOMDepth(), 'performance');
  }

  /**
   * Setup performance observers
   */
  private setupObservers(): void {
    try {
      // Memory observer
      setInterval(() => {
        this.takeMemorySnapshot();
      }, 5000); // Every 5 seconds

      // FPS observer
      let lastTime = performance.now();
      let frameCount = 0;
      
      const measureFPS = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
          this.renderingMetrics.push({ timestamp: Date.now(), fps });
          
          // Keep only recent metrics
          if (this.renderingMetrics.length > 300) {
            this.renderingMetrics = this.renderingMetrics.slice(-300);
          }
          
          frameCount = 0;
          lastTime = currentTime;
        }
        
        requestAnimationFrame(measureFPS);
      };
      
      requestAnimationFrame(measureFPS);

      // Performance observer for navigation and resource timing
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.performanceMonitor.recordMetric({
                name: 'navigation.total',
                value: navEntry.loadEventEnd - navEntry.navigationStart,
                unit: 'ms',
                timestamp: Date.now(),
                category: 'performance'
              });
            }
          });
        });

        observer.observe({ entryTypes: ['navigation', 'resource', 'paint', 'layout-shift'] });
        this.observers.push(observer);
      }
    } catch (error) {
      this.logger.error('performance', 'Failed to setup performance observers', error);
    }
  }

  /**
   * Take memory snapshot
   */
  private takeMemorySnapshot(): void {
    try {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.memorySnapshots.push({
          timestamp: Date.now(),
          usage: memory.usedJSHeapSize
        });

        // Keep only recent snapshots
        if (this.memorySnapshots.length > 1000) {
          this.memorySnapshots = this.memorySnapshots.slice(-1000);
        }
      }
    } catch (error) {
      this.logger.error('performance', 'Failed to take memory snapshot', error);
    }
  }

  /**
   * Analyze trend
   */
  private analyzeTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';
    
    const first = values[0];
    const last = values[values.length - 1];
    const change = (last - first) / first;
    
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * Detect memory leaks
   */
  private detectMemoryLeaks(snapshots: Array<{ timestamp: number; usage: number }>): MemoryLeak[] {
    const leaks: MemoryLeak[] = [];
    
    if (snapshots.length < 10) return leaks;
    
    // Simple leak detection: consistent growth over time
    const recentSnapshots = snapshots.slice(-20);
    const growthRate = this.calculateGrowthRate(recentSnapshots.map(s => s.usage));
    
    if (growthRate > 0.05) { // 5% growth rate
      leaks.push({
        component: 'unknown',
        size: recentSnapshots[recentSnapshots.length - 1].usage,
        growth: growthRate,
        duration: recentSnapshots[recentSnapshots.length - 1].timestamp - recentSnapshots[0].timestamp
      });
    }
    
    return leaks;
  }

  /**
   * Calculate memory efficiency
   */
  private calculateMemoryEfficiency(snapshots: Array<{ timestamp: number; usage: number }>): number {
    if (snapshots.length === 0) return 100;
    
    const current = snapshots[snapshots.length - 1].usage;
    const peak = Math.max(...snapshots.map(s => s.usage));
    
    return Math.max(0, 100 - ((current / peak) * 100));
  }

  /**
   * Calculate average metric
   */
  private calculateAverageMetric(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 0;
    return metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length;
  }

  /**
   * Identify critical path
   */
  private identifyCriticalPath(metrics: PerformanceMetric[]): string[] {
    const criticalOperations = metrics
      .filter(m => m.value > 100) // Operations taking more than 100ms
      .sort((a, b) => b.value - a.value)
      .slice(0, 5)
      .map(m => m.name);
    
    return criticalOperations;
  }

  /**
   * Identify bottlenecks
   */
  private identifyBottlenecks(metrics: PerformanceMetric[]): Bottleneck[] {
    const operationStats: Record<string, { total: number; count: number; max: number }> = {};
    
    metrics.forEach(metric => {
      if (!operationStats[metric.name]) {
        operationStats[metric.name] = { total: 0, count: 0, max: 0 };
      }
      
      operationStats[metric.name].total += metric.value;
      operationStats[metric.name].count++;
      operationStats[metric.name].max = Math.max(operationStats[metric.name].max, metric.value);
    });
    
    return Object.entries(operationStats)
      .map(([operation, stats]) => ({
        operation,
        duration: stats.total / stats.count,
        frequency: stats.count,
        impact: stats.max
      }))
      .filter(bottleneck => bottleneck.duration > 50) // More than 50ms average
      .sort((a, b) => b.impact - a.impact)
      .slice(0, 10);
  }

  /**
   * Identify slow requests
   */
  private identifySlowRequests(networkMetrics: PerformanceMetric[]): SlowRequest[] {
    return networkMetrics
      .filter(m => m.name === 'network.request' && m.value > 2000) // Slower than 2s
      .map(m => ({
        url: m.context?.url || 'unknown',
        method: m.context?.method || 'GET',
        duration: m.value,
        size: m.context?.size || 0,
        status: m.context?.status || 200
      }))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);
  }

  /**
   * Calculate cache hit rate
   */
  private calculateCacheHitRate(networkMetrics: PerformanceMetric[]): number {
    const totalRequests = networkMetrics.filter(m => m.name === 'network.request').length;
    const cachedRequests = networkMetrics.filter(m => 
      m.name === 'network.request' && m.context?.cached === true
    ).length;
    
    return totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;
  }

  /**
   * Calculate DOM depth
   */
  private calculateDOMDepth(): number {
    let maxDepth = 0;
    
    const calculateDepth = (element: Element, depth: number = 0): void => {
      maxDepth = Math.max(maxDepth, depth);
      
      for (const child of element.children) {
        calculateDepth(child, depth + 1);
      }
    };
    
    calculateDepth(document.documentElement);
    return maxDepth;
  }

  /**
   * Calculate DOM complexity
   */
  private calculateDOMComplexity(): number {
    const elements = document.querySelectorAll('*');
    let complexity = 0;
    
    elements.forEach(element => {
      // Add complexity for attributes
      complexity += element.attributes.length;
      
      // Add complexity for event listeners (approximation)
      const tagName = element.tagName.toLowerCase();
      if (['button', 'input', 'select', 'textarea', 'a'].includes(tagName)) {
        complexity += 2;
      }
      
      // Add complexity for inline styles
      if (element.getAttribute('style')) {
        complexity += 3;
      }
    });
    
    return complexity;
  }

  /**
   * Get DOM mutation count
   */
  private getDOMMutationCount(): number {
    // This would need to be tracked over time with MutationObserver
    // For now, return a placeholder
    return 0;
  }

  /**
   * Identify DOM inefficiencies
   */
  private identifyDOMInefficiencies(): DOMInefficiency[] {
    const inefficiencies: DOMInefficiency[] = [];
    
    // Check for deep nesting
    const maxDepth = this.calculateDOMDepth();
    if (maxDepth > 15) {
      inefficiencies.push({
        type: 'deep_nesting',
        element: 'document',
        impact: maxDepth - 15,
        suggestion: 'Reduce DOM nesting depth to improve rendering performance'
      });
    }
    
    // Check for excessive elements
    const elementCount = document.querySelectorAll('*').length;
    if (elementCount > 1000) {
      inefficiencies.push({
        type: 'excessive_elements',
        element: 'document',
        impact: Math.floor((elementCount - 1000) / 100),
        suggestion: 'Consider virtualization or pagination for large lists'
      });
    }
    
    return inefficiencies;
  }

  /**
   * Identify rendering issues
   */
  private identifyRenderingIssues(): RenderingIssue[] {
    const issues: RenderingIssue[] = [];
    
    // Check for low FPS
    const recentFPS = this.renderingMetrics.slice(-10);
    const averageFPS = recentFPS.length > 0 ? 
      recentFPS.reduce((sum, m) => sum + m.fps, 0) / recentFPS.length : 60;
    
    if (averageFPS < 30) {
      issues.push({
        type: 'animation_jank',
        frequency: recentFPS.filter(m => m.fps < 30).length,
        impact: 30 - averageFPS,
        cause: 'Low frame rate detected'
      });
    }
    
    return issues;
  }

  /**
   * Calculate growth rate
   */
  private calculateGrowthRate(values: number[]): number {
    if (values.length < 2) return 0;
    
    const first = values[0];
    const last = values[values.length - 1];
    
    return (last - first) / first;
  }

  /**
   * Identify performance issues
   */
  private identifyPerformanceIssues(metrics: {
    memory: MemoryAnalysis;
    timing: TimingAnalysis;
    network: NetworkAnalysis;
    dom: DOMAnalysis;
    rendering: RenderingAnalysis;
  }): PerformanceIssue[] {
    const issues: PerformanceIssue[] = [];
    
    // Memory issues
    if (metrics.memory.trend === 'increasing' && metrics.memory.leaks.length > 0) {
      issues.push({
        id: 'memory_leak',
        type: 'memory',
        severity: 'high',
        title: 'Memory Leak Detected',
        description: 'Memory usage is consistently increasing over time',
        impact: 'Application may become slow and unresponsive',
        solution: 'Review component lifecycle and event listener cleanup',
        metrics: { growth: metrics.memory.leaks[0]?.growth || 0 }
      });
    }
    
    // Timing issues
    if (metrics.timing.loadTime > 3000) {
      issues.push({
        id: 'slow_load',
        type: 'timing',
        severity: 'medium',
        title: 'Slow Load Time',
        description: `Load time is ${metrics.timing.loadTime}ms, which exceeds recommended 3000ms`,
        impact: 'Poor user experience and potential user abandonment',
        solution: 'Optimize critical rendering path and reduce bundle size',
        metrics: { loadTime: metrics.timing.loadTime }
      });
    }
    
    // Network issues
    if (metrics.network.failedRequests > metrics.network.totalRequests * 0.05) {
      issues.push({
        id: 'network_failures',
        type: 'network',
        severity: 'high',
        title: 'High Network Failure Rate',
        description: `${metrics.network.failedRequests} out of ${metrics.network.totalRequests} requests failed`,
        impact: 'Features may not work correctly for users',
        solution: 'Implement proper error handling and retry mechanisms',
        metrics: { failureRate: (metrics.network.failedRequests / metrics.network.totalRequests) * 100 }
      });
    }
    
    // DOM issues
    if (metrics.dom.elementCount > 1500) {
      issues.push({
        id: 'dom_complexity',
        type: 'dom',
        severity: 'medium',
        title: 'High DOM Complexity',
        description: `DOM contains ${metrics.dom.elementCount} elements`,
        impact: 'Slower rendering and increased memory usage',
        solution: 'Consider virtualization or component optimization',
        metrics: { elementCount: metrics.dom.elementCount }
      });
    }
    
    // Rendering issues
    if (metrics.rendering.fps < 30) {
      issues.push({
        id: 'low_fps',
        type: 'rendering',
        severity: 'high',
        title: 'Low Frame Rate',
        description: `Average FPS is ${metrics.rendering.fps}, below recommended 60 FPS`,
        impact: 'Janky animations and poor user experience',
        solution: 'Optimize animations and reduce layout thrashing',
        metrics: { fps: metrics.rendering.fps }
      });
    }
    
    return issues;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(issues: PerformanceIssue[]): string[] {
    const recommendations: string[] = [];
    
    issues.forEach(issue => {
      switch (issue.type) {
        case 'memory':
          recommendations.push('Implement proper cleanup in component unmount lifecycle');
          recommendations.push('Use WeakMap and WeakSet for temporary references');
          break;
        case 'timing':
          recommendations.push('Implement code splitting and lazy loading');
          recommendations.push('Optimize critical rendering path');
          break;
        case 'network':
          recommendations.push('Implement request caching and retry logic');
          recommendations.push('Use service workers for offline functionality');
          break;
        case 'dom':
          recommendations.push('Implement virtual scrolling for large lists');
          recommendations.push('Minimize DOM manipulations and batch updates');
          break;
        case 'rendering':
          recommendations.push('Use CSS transforms instead of changing layout properties');
          recommendations.push('Implement requestAnimationFrame for smooth animations');
          break;
      }
    });
    
    return [...new Set(recommendations)]; // Remove duplicates
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(metrics: {
    memory: MemoryAnalysis;
    timing: TimingAnalysis;
    network: NetworkAnalysis;
    dom: DOMAnalysis;
    rendering: RenderingAnalysis;
  }): number {
    let score = 100;
    
    // Memory score (25%)
    const memoryScore = Math.max(0, 100 - (metrics.memory.leaks.length * 20));
    score = score * 0.75 + memoryScore * 0.25;
    
    // Timing score (25%)
    const timingScore = Math.max(0, 100 - Math.max(0, (metrics.timing.loadTime - 1000) / 50));
    score = score * 0.75 + timingScore * 0.25;
    
    // Network score (20%)
    const networkScore = Math.max(0, 100 - (metrics.network.failedRequests / Math.max(1, metrics.network.totalRequests)) * 100);
    score = score * 0.8 + networkScore * 0.2;
    
    // DOM score (15%)
    const domScore = Math.max(0, 100 - Math.max(0, (metrics.dom.elementCount - 500) / 20));
    score = score * 0.85 + domScore * 0.15;
    
    // Rendering score (15%)
    const renderingScore = Math.max(0, Math.min(100, (metrics.rendering.fps / 60) * 100));
    score = score * 0.85 + renderingScore * 0.15;
    
    return Math.round(score);
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        this.logger.error('performance', 'Failed to disconnect observer', error);
      }
    });
    
    this.observers = [];
    this.memorySnapshots = [];
    this.renderingMetrics = [];
  }
}

// Export convenience functions
export const performanceAnalyzer = PerformanceAnalyzer.getInstance();

export function analyzePerformance(duration?: number): Promise<PerformanceReport> {
  return performanceAnalyzer.analyzePerformance(duration);
}

export function getLatestReport(): PerformanceReport | null {
  return performanceAnalyzer.getLatestReport();
}

export function getAnalysisHistory(): PerformanceReport[] {
  return performanceAnalyzer.getAnalysisHistory();
}