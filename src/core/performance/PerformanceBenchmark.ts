/**
 * Performance Benchmark - 性能基准测试套件
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';

export interface BenchmarkConfig {
  name: string;
  description: string;
  iterations: number;
  warmupIterations: number;
  timeout: number;
  setup?: () => Promise<void> | void;
  teardown?: () => Promise<void> | void;
  beforeEach?: () => Promise<void> | void;
  afterEach?: () => Promise<void> | void;
}

export interface BenchmarkResult {
  name: string;
  description: string;
  iterations: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  medianTime: number;
  standardDeviation: number;
  operationsPerSecond: number;
  memoryUsage: {
    before: number;
    after: number;
    peak: number;
    delta: number;
  };
  success: boolean;
  error?: string;
  timestamp: number;
}

export interface BenchmarkSuite {
  name: string;
  description: string;
  benchmarks: Benchmark[];
  results: BenchmarkResult[];
  totalTime: number;
  timestamp: number;
}

export interface PerformanceProfile {
  name: string;
  duration: number;
  samples: PerformanceSample[];
  summary: {
    totalTime: number;
    selfTime: number;
    callCount: number;
    averageTime: number;
    children: PerformanceProfile[];
  };
}

export interface PerformanceSample {
  timestamp: number;
  duration: number;
  memoryUsage: number;
  cpuUsage?: number;
}

/**
 * Benchmark Class
 */
export class Benchmark {
  private config: BenchmarkConfig;
  private fn: () => Promise<void> | void;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;

  constructor(
    config: BenchmarkConfig,
    fn: () => Promise<void> | void
  ) {
    this.config = config;
    this.fn = fn;
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  /**
   * Run benchmark
   */
  async run(): Promise<BenchmarkResult> {
    this.logger.info('benchmark', `Starting benchmark: ${this.config.name}`);

    const result: BenchmarkResult = {
      name: this.config.name,
      description: this.config.description,
      iterations: this.config.iterations,
      totalTime: 0,
      averageTime: 0,
      minTime: Infinity,
      maxTime: 0,
      medianTime: 0,
      standardDeviation: 0,
      operationsPerSecond: 0,
      memoryUsage: {
        before: 0,
        after: 0,
        peak: 0,
        delta: 0
      },
      success: false,
      timestamp: Date.now()
    };

    try {
      // Setup
      if (this.config.setup) {
        await this.config.setup();
      }

      // Warmup
      if (this.config.warmupIterations > 0) {
        await this.runWarmup();
      }

      // Measure memory before
      result.memoryUsage.before = this.getMemoryUsage();

      // Run benchmark iterations
      const times = await this.runIterations();

      // Measure memory after
      result.memoryUsage.after = this.getMemoryUsage();
      result.memoryUsage.delta = result.memoryUsage.after - result.memoryUsage.before;

      // Calculate statistics
      this.calculateStatistics(times, result);

      result.success = true;

      // Teardown
      if (this.config.teardown) {
        await this.config.teardown();
      }

      this.logger.info('benchmark', `Benchmark completed: ${this.config.name}`, {
        averageTime: result.averageTime,
        operationsPerSecond: result.operationsPerSecond
      });

    } catch (error) {
      result.error = error instanceof Error ? error.message : String(error);
      result.success = false;
      
      this.logger.error('benchmark', `Benchmark failed: ${this.config.name}`, error);
    }

    return result;
  }

  /**
   * Run warmup iterations
   */
  private async runWarmup(): Promise<void> {
    this.logger.debug('benchmark', `Running ${this.config.warmupIterations} warmup iterations`);

    for (let i = 0; i < this.config.warmupIterations; i++) {
      if (this.config.beforeEach) {
        await this.config.beforeEach();
      }

      await this.fn();

      if (this.config.afterEach) {
        await this.config.afterEach();
      }
    }
  }

  /**
   * Run benchmark iterations
   */
  private async runIterations(): Promise<number[]> {
    const times: number[] = [];
    let peakMemory = this.getMemoryUsage();

    for (let i = 0; i < this.config.iterations; i++) {
      if (this.config.beforeEach) {
        await this.config.beforeEach();
      }

      const startTime = performance.now();
      await this.fn();
      const endTime = performance.now();

      const duration = endTime - startTime;
      times.push(duration);

      // Track peak memory
      const currentMemory = this.getMemoryUsage();
      peakMemory = Math.max(peakMemory, currentMemory);

      if (this.config.afterEach) {
        await this.config.afterEach();
      }

      // Check timeout
      if (duration > this.config.timeout) {
        throw new Error(`Benchmark iteration exceeded timeout: ${duration}ms > ${this.config.timeout}ms`);
      }
    }

    return times;
  }

  /**
   * Calculate statistics
   */
  private calculateStatistics(times: number[], result: BenchmarkResult): void {
    result.totalTime = times.reduce((sum, time) => sum + time, 0);
    result.averageTime = result.totalTime / times.length;
    result.minTime = Math.min(...times);
    result.maxTime = Math.max(...times);
    
    // Calculate median
    const sortedTimes = [...times].sort((a, b) => a - b);
    const mid = Math.floor(sortedTimes.length / 2);
    result.medianTime = sortedTimes.length % 2 === 0 ?
      (sortedTimes[mid - 1] + sortedTimes[mid]) / 2 :
      sortedTimes[mid];

    // Calculate standard deviation
    const variance = times.reduce((sum, time) => {
      const diff = time - result.averageTime;
      return sum + (diff * diff);
    }, 0) / times.length;
    result.standardDeviation = Math.sqrt(variance);

    // Calculate operations per second
    result.operationsPerSecond = 1000 / result.averageTime;

    // Set peak memory
    result.memoryUsage.peak = Math.max(
      result.memoryUsage.before,
      result.memoryUsage.after
    );
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
}

/**
 * Benchmark Suite
 */
export class BenchmarkSuite {
  private name: string;
  private description: string;
  private benchmarks: Benchmark[] = [];
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;

  constructor(name: string, description: string = '') {
    this.name = name;
    this.description = description;
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  /**
   * Add benchmark
   */
  add(config: BenchmarkConfig, fn: () => Promise<void> | void): void {
    const benchmark = new Benchmark(config, fn);
    this.benchmarks.push(benchmark);
  }

  /**
   * Run all benchmarks
   */
  async run(): Promise<BenchmarkSuite> {
    this.logger.info('benchmark', `Starting benchmark suite: ${this.name}`);

    const startTime = Date.now();
    const results: BenchmarkResult[] = [];

    for (const benchmark of this.benchmarks) {
      const result = await benchmark.run();
      results.push(result);
    }

    const totalTime = Date.now() - startTime;

    const suite: BenchmarkSuite = {
      name: this.name,
      description: this.description,
      benchmarks: this.benchmarks,
      results,
      totalTime,
      timestamp: startTime
    };

    this.logger.info('benchmark', `Benchmark suite completed: ${this.name}`, {
      totalBenchmarks: results.length,
      successfulBenchmarks: results.filter(r => r.success).length,
      totalTime
    });

    return suite;
  }
}

/**
 * Performance Profiler
 */
export class PerformanceProfiler {
  private static instance: PerformanceProfiler;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private profiles: Map<string, PerformanceProfile> = new Map();
  private activeProfiles: Map<string, { startTime: number; samples: PerformanceSample[] }> = new Map();
  private sampleInterval = 10; // ms

  private constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): PerformanceProfiler {
    if (!PerformanceProfiler.instance) {
      PerformanceProfiler.instance = new PerformanceProfiler();
    }
    return PerformanceProfiler.instance;
  }

  /**
   * Start profiling
   */
  startProfile(name: string): void {
    if (this.activeProfiles.has(name)) {
      this.logger.warn('profiler', `Profile ${name} is already active`);
      return;
    }

    this.activeProfiles.set(name, {
      startTime: performance.now(),
      samples: []
    });

    // Start sampling
    this.startSampling(name);

    this.logger.debug('profiler', `Started profiling: ${name}`);
  }

  /**
   * End profiling
   */
  endProfile(name: string): PerformanceProfile | null {
    const activeProfile = this.activeProfiles.get(name);
    if (!activeProfile) {
      this.logger.warn('profiler', `Profile ${name} is not active`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - activeProfile.startTime;

    const profile: PerformanceProfile = {
      name,
      duration,
      samples: activeProfile.samples,
      summary: {
        totalTime: duration,
        selfTime: duration,
        callCount: 1,
        averageTime: duration,
        children: []
      }
    };

    this.profiles.set(name, profile);
    this.activeProfiles.delete(name);

    this.logger.debug('profiler', `Ended profiling: ${name}`, { duration });

    return profile;
  }

  /**
   * Profile function
   */
  async profile<T>(name: string, fn: () => Promise<T> | T): Promise<T> {
    this.startProfile(name);
    
    try {
      const result = await fn();
      return result;
    } finally {
      this.endProfile(name);
    }
  }

  /**
   * Get profile
   */
  getProfile(name: string): PerformanceProfile | null {
    return this.profiles.get(name) || null;
  }

  /**
   * Get all profiles
   */
  getAllProfiles(): PerformanceProfile[] {
    return Array.from(this.profiles.values());
  }

  /**
   * Clear profiles
   */
  clearProfiles(): void {
    this.profiles.clear();
    this.activeProfiles.clear();
  }

  /**
   * Start sampling
   */
  private startSampling(name: string): void {
    const activeProfile = this.activeProfiles.get(name);
    if (!activeProfile) return;

    const sampleTimer = setInterval(() => {
      if (!this.activeProfiles.has(name)) {
        clearInterval(sampleTimer);
        return;
      }

      const sample: PerformanceSample = {
        timestamp: performance.now(),
        duration: performance.now() - activeProfile.startTime,
        memoryUsage: this.getMemoryUsage()
      };

      activeProfile.samples.push(sample);
    }, this.sampleInterval);
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
}

/**
 * Built-in Benchmarks
 */
export class BuiltinBenchmarks {
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;

  constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
  }

  /**
   * DOM manipulation benchmark
   */
  createDOMBenchmark(): BenchmarkSuite {
    const suite = new BenchmarkSuite('DOM Operations', 'Benchmark DOM manipulation operations');

    // Element creation
    suite.add({
      name: 'createElement',
      description: 'Create DOM elements',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000
    }, () => {
      const element = document.createElement('div');
      element.className = 'test-element';
      element.textContent = 'Test content';
      document.body.appendChild(element);
      document.body.removeChild(element);
    });

    // Query selector
    suite.add({
      name: 'querySelector',
      description: 'Query DOM elements',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000,
      setup: () => {
        // Create test elements
        for (let i = 0; i < 100; i++) {
          const element = document.createElement('div');
          element.className = 'benchmark-element';
          element.id = `element-${i}`;
          document.body.appendChild(element);
        }
      },
      teardown: () => {
        // Clean up test elements
        document.querySelectorAll('.benchmark-element').forEach(el => el.remove());
      }
    }, () => {
      document.querySelector('.benchmark-element');
      document.getElementById('element-50');
      document.querySelectorAll('.benchmark-element');
    });

    // Style manipulation
    suite.add({
      name: 'styleManipulation',
      description: 'Manipulate element styles',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000,
      setup: () => {
        const element = document.createElement('div');
        element.id = 'style-test-element';
        document.body.appendChild(element);
      },
      teardown: () => {
        document.getElementById('style-test-element')?.remove();
      }
    }, () => {
      const element = document.getElementById('style-test-element') as HTMLElement;
      element.style.width = '100px';
      element.style.height = '100px';
      element.style.backgroundColor = 'red';
      element.style.transform = 'translateX(10px)';
    });

    return suite;
  }

  /**
   * Array operations benchmark
   */
  createArrayBenchmark(): BenchmarkSuite {
    const suite = new BenchmarkSuite('Array Operations', 'Benchmark array manipulation operations');

    const testData = Array.from({ length: 10000 }, (_, i) => i);

    // Array iteration
    suite.add({
      name: 'forEach',
      description: 'Array forEach iteration',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      let sum = 0;
      testData.forEach(item => {
        sum += item;
      });
    });

    suite.add({
      name: 'forLoop',
      description: 'Traditional for loop',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      let sum = 0;
      for (let i = 0; i < testData.length; i++) {
        sum += testData[i];
      }
    });

    suite.add({
      name: 'forOfLoop',
      description: 'For-of loop',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      let sum = 0;
      for (const item of testData) {
        sum += item;
      }
    });

    // Array methods
    suite.add({
      name: 'map',
      description: 'Array map operation',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      testData.map(item => item * 2);
    });

    suite.add({
      name: 'filter',
      description: 'Array filter operation',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      testData.filter(item => item % 2 === 0);
    });

    suite.add({
      name: 'reduce',
      description: 'Array reduce operation',
      iterations: 100,
      warmupIterations: 10,
      timeout: 5000
    }, () => {
      testData.reduce((sum, item) => sum + item, 0);
    });

    return suite;
  }

  /**
   * Memory allocation benchmark
   */
  createMemoryBenchmark(): BenchmarkSuite {
    const suite = new BenchmarkSuite('Memory Operations', 'Benchmark memory allocation and garbage collection');

    // Object creation
    suite.add({
      name: 'objectCreation',
      description: 'Create objects',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000
    }, () => {
      const objects = [];
      for (let i = 0; i < 100; i++) {
        objects.push({
          id: i,
          name: `Object ${i}`,
          data: new Array(100).fill(i)
        });
      }
    });

    // Array creation
    suite.add({
      name: 'arrayCreation',
      description: 'Create arrays',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000
    }, () => {
      const arrays = [];
      for (let i = 0; i < 100; i++) {
        arrays.push(new Array(1000).fill(i));
      }
    });

    // String concatenation
    suite.add({
      name: 'stringConcatenation',
      description: 'String concatenation',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000
    }, () => {
      let result = '';
      for (let i = 0; i < 1000; i++) {
        result += `String ${i} `;
      }
    });

    suite.add({
      name: 'stringJoin',
      description: 'Array join for strings',
      iterations: 1000,
      warmupIterations: 100,
      timeout: 5000
    }, () => {
      const parts = [];
      for (let i = 0; i < 1000; i++) {
        parts.push(`String ${i}`);
      }
      parts.join(' ');
    });

    return suite;
  }
}

// Export convenience functions
export const performanceProfiler = PerformanceProfiler.getInstance();

export function createBenchmark(config: BenchmarkConfig, fn: () => Promise<void> | void): Benchmark {
  return new Benchmark(config, fn);
}

export function createBenchmarkSuite(name: string, description?: string): BenchmarkSuite {
  return new BenchmarkSuite(name, description);
}

export function profile<T>(name: string, fn: () => Promise<T> | T): Promise<T> {
  return performanceProfiler.profile(name, fn);
}

export function startProfile(name: string): void {
  performanceProfiler.startProfile(name);
}

export function endProfile(name: string): PerformanceProfile | null {
  return performanceProfiler.endProfile(name);
}

export function getBuiltinBenchmarks(): BuiltinBenchmarks {
  return new BuiltinBenchmarks();
}