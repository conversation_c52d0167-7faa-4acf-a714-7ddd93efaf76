/**
 * Virtual Scrolling - 虚拟滚动优化
 */

import { Logger } from '../logging/Logger';
import { PerformanceMonitor } from '../logging/PerformanceMonitor';

export interface VirtualScrollConfig<T> {
  itemHeight: number | ((item: T, index: number) => number);
  containerHeight: number;
  overscan: number;
  data: T[];
  renderItem: (item: T, index: number, isVisible: boolean) => HTMLElement;
  onScroll?: (scrollTop: number, visibleRange: { start: number; end: number }) => void;
  onItemsChanged?: (visibleItems: T[]) => void;
  estimatedItemHeight?: number;
  bufferSize?: number;
  scrollDebounce?: number;
}

export interface VirtualScrollState {
  scrollTop: number;
  visibleStart: number;
  visibleEnd: number;
  totalHeight: number;
  itemHeights: Map<number, number>;
  renderedItems: Map<number, HTMLElement>;
}

export interface ScrollMetrics {
  scrollTop: number;
  scrollHeight: number;
  clientHeight: number;
  scrollDirection: 'up' | 'down' | 'none';
  scrollSpeed: number;
  isScrolling: boolean;
}

/**
 * Virtual Scroll Container
 */
export class VirtualScrollContainer<T> {
  private container: HTMLElement;
  private config: VirtualScrollConfig<T>;
  private state: VirtualScrollState;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private scrollMetrics: ScrollMetrics;
  private scrollTimer?: NodeJS.Timeout;
  private resizeObserver?: ResizeObserver;
  private itemPool: HTMLElement[] = [];
  private lastScrollTime = 0;
  private lastScrollTop = 0;

  constructor(container: HTMLElement, config: VirtualScrollConfig<T>) {
    this.container = container;
    this.config = {
      overscan: 5,
      estimatedItemHeight: 50,
      bufferSize: 10,
      scrollDebounce: 16,
      ...config
    };
    
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();

    this.state = {
      scrollTop: 0,
      visibleStart: 0,
      visibleEnd: 0,
      totalHeight: 0,
      itemHeights: new Map(),
      renderedItems: new Map()
    };

    this.scrollMetrics = {
      scrollTop: 0,
      scrollHeight: 0,
      clientHeight: 0,
      scrollDirection: 'none',
      scrollSpeed: 0,
      isScrolling: false
    };

    this.initialize();
  }

  /**
   * Update data
   */
  updateData(data: T[]): void {
    this.config.data = data;
    this.state.itemHeights.clear();
    this.calculateTotalHeight();
    this.render();
    
    this.logger.debug('virtualScroll', `Data updated: ${data.length} items`);
  }

  /**
   * Scroll to index
   */
  scrollToIndex(index: number, align: 'start' | 'center' | 'end' = 'start'): void {
    const itemTop = this.getItemOffset(index);
    const itemHeight = this.getItemHeight(index);
    
    let scrollTop: number;
    
    switch (align) {
      case 'start':
        scrollTop = itemTop;
        break;
      case 'center':
        scrollTop = itemTop - (this.container.clientHeight - itemHeight) / 2;
        break;
      case 'end':
        scrollTop = itemTop - this.container.clientHeight + itemHeight;
        break;
    }

    this.container.scrollTop = Math.max(0, Math.min(scrollTop, this.state.totalHeight - this.container.clientHeight));
    
    this.logger.debug('virtualScroll', `Scrolled to index ${index}`, { scrollTop, align });
  }

  /**
   * Get visible range
   */
  getVisibleRange(): { start: number; end: number; items: T[] } {
    const items = this.config.data.slice(this.state.visibleStart, this.state.visibleEnd + 1);
    
    return {
      start: this.state.visibleStart,
      end: this.state.visibleEnd,
      items
    };
  }

  /**
   * Get scroll metrics
   */
  getScrollMetrics(): ScrollMetrics {
    return { ...this.scrollMetrics };
  }

  /**
   * Force re-render
   */
  forceUpdate(): void {
    this.render();
  }

  /**
   * Dispose virtual scroll
   */
  dispose(): void {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    this.container.removeEventListener('scroll', this.handleScroll);
    
    // Clean up rendered items
    this.state.renderedItems.forEach(item => {
      if (item.parentNode) {
        item.parentNode.removeChild(item);
      }
    });

    this.state.renderedItems.clear();
    this.itemPool = [];
  }

  /**
   * Initialize virtual scroll
   */
  private initialize(): void {
    this.setupContainer();
    this.setupScrollListener();
    this.setupResizeObserver();
    this.calculateTotalHeight();
    this.render();
    
    this.logger.info('virtualScroll', 'Virtual scroll initialized', {
      itemCount: this.config.data.length,
      containerHeight: this.container.clientHeight
    });
  }

  /**
   * Setup container
   */
  private setupContainer(): void {
    this.container.style.overflow = 'auto';
    this.container.style.position = 'relative';
    
    // Create viewport
    const viewport = document.createElement('div');
    viewport.style.position = 'relative';
    viewport.style.height = '100%';
    viewport.className = 'virtual-scroll-viewport';
    
    // Move existing children to viewport
    while (this.container.firstChild) {
      viewport.appendChild(this.container.firstChild);
    }
    
    this.container.appendChild(viewport);
  }

  /**
   * Setup scroll listener
   */
  private setupScrollListener(): void {
    let ticking = false;

    this.handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.updateScrollMetrics();
          this.updateVisibleRange();
          this.render();
          ticking = false;
        });
        ticking = true;
      }
    };

    this.container.addEventListener('scroll', this.handleScroll, { passive: true });
  }

  /**
   * Setup resize observer
   */
  private setupResizeObserver(): void {
    if ('ResizeObserver' in window) {
      this.resizeObserver = new ResizeObserver(() => {
        this.config.containerHeight = this.container.clientHeight;
        this.updateVisibleRange();
        this.render();
      });

      this.resizeObserver.observe(this.container);
    }
  }

  /**
   * Handle scroll event
   */
  private handleScroll = (): void => {
    // Implemented above in setupScrollListener
  };

  /**
   * Update scroll metrics
   */
  private updateScrollMetrics(): void {
    const now = Date.now();
    const scrollTop = this.container.scrollTop;
    const scrollHeight = this.container.scrollHeight;
    const clientHeight = this.container.clientHeight;

    // Calculate scroll direction and speed
    const scrollDelta = scrollTop - this.lastScrollTop;
    const timeDelta = now - this.lastScrollTime;
    
    this.scrollMetrics = {
      scrollTop,
      scrollHeight,
      clientHeight,
      scrollDirection: scrollDelta > 0 ? 'down' : scrollDelta < 0 ? 'up' : 'none',
      scrollSpeed: timeDelta > 0 ? Math.abs(scrollDelta) / timeDelta : 0,
      isScrolling: true
    };

    this.state.scrollTop = scrollTop;
    this.lastScrollTop = scrollTop;
    this.lastScrollTime = now;

    // Clear scrolling flag after delay
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    this.scrollTimer = setTimeout(() => {
      this.scrollMetrics.isScrolling = false;
    }, this.config.scrollDebounce);
  }

  /**
   * Update visible range
   */
  private updateVisibleRange(): void {
    const { scrollTop } = this.state;
    const { containerHeight, overscan } = this.config;

    let visibleStart = 0;
    let visibleEnd = 0;

    if (typeof this.config.itemHeight === 'number') {
      // Fixed height items
      const itemHeight = this.config.itemHeight;
      visibleStart = Math.floor(scrollTop / itemHeight);
      visibleEnd = Math.min(
        this.config.data.length - 1,
        Math.ceil((scrollTop + containerHeight) / itemHeight) - 1
      );
    } else {
      // Variable height items
      visibleStart = this.findStartIndex(scrollTop);
      visibleEnd = this.findEndIndex(scrollTop + containerHeight);
    }

    // Apply overscan
    this.state.visibleStart = Math.max(0, visibleStart - overscan);
    this.state.visibleEnd = Math.min(this.config.data.length - 1, visibleEnd + overscan);

    // Notify callback
    if (this.config.onScroll) {
      this.config.onScroll(scrollTop, {
        start: this.state.visibleStart,
        end: this.state.visibleEnd
      });
    }
  }

  /**
   * Find start index for variable height items
   */
  private findStartIndex(scrollTop: number): number {
    let offset = 0;
    
    for (let i = 0; i < this.config.data.length; i++) {
      const itemHeight = this.getItemHeight(i);
      
      if (offset + itemHeight > scrollTop) {
        return i;
      }
      
      offset += itemHeight;
    }
    
    return this.config.data.length - 1;
  }

  /**
   * Find end index for variable height items
   */
  private findEndIndex(scrollBottom: number): number {
    let offset = 0;
    
    for (let i = 0; i < this.config.data.length; i++) {
      const itemHeight = this.getItemHeight(i);
      offset += itemHeight;
      
      if (offset >= scrollBottom) {
        return i;
      }
    }
    
    return this.config.data.length - 1;
  }

  /**
   * Get item height
   */
  private getItemHeight(index: number): number {
    // Check cached height
    if (this.state.itemHeights.has(index)) {
      return this.state.itemHeights.get(index)!;
    }

    // Calculate height
    let height: number;
    
    if (typeof this.config.itemHeight === 'number') {
      height = this.config.itemHeight;
    } else {
      height = this.config.itemHeight(this.config.data[index], index);
    }

    // Cache height
    this.state.itemHeights.set(index, height);
    
    return height;
  }

  /**
   * Get item offset
   */
  private getItemOffset(index: number): number {
    let offset = 0;
    
    for (let i = 0; i < index; i++) {
      offset += this.getItemHeight(i);
    }
    
    return offset;
  }

  /**
   * Calculate total height
   */
  private calculateTotalHeight(): void {
    if (typeof this.config.itemHeight === 'number') {
      this.state.totalHeight = this.config.data.length * this.config.itemHeight;
    } else {
      let totalHeight = 0;
      
      for (let i = 0; i < this.config.data.length; i++) {
        totalHeight += this.getItemHeight(i);
      }
      
      this.state.totalHeight = totalHeight;
    }

    // Update container height
    const viewport = this.container.querySelector('.virtual-scroll-viewport') as HTMLElement;
    if (viewport) {
      viewport.style.height = `${this.state.totalHeight}px`;
    }
  }

  /**
   * Render visible items
   */
  private render(): void {
    this.performanceMonitor.startTiming('virtualScroll.render', 'ui');

    const { visibleStart, visibleEnd, renderedItems } = this.state;
    const newRenderedItems = new Map<number, HTMLElement>();

    // Remove items that are no longer visible
    renderedItems.forEach((item, index) => {
      if (index < visibleStart || index > visibleEnd) {
        if (item.parentNode) {
          item.parentNode.removeChild(item);
        }
        this.returnItemToPool(item);
      } else {
        newRenderedItems.set(index, item);
      }
    });

    // Render new visible items
    for (let i = visibleStart; i <= visibleEnd; i++) {
      if (!newRenderedItems.has(i)) {
        const item = this.renderItem(i);
        newRenderedItems.set(i, item);
      }
    }

    this.state.renderedItems = newRenderedItems;

    // Notify callback
    if (this.config.onItemsChanged) {
      const visibleItems = this.config.data.slice(visibleStart, visibleEnd + 1);
      this.config.onItemsChanged(visibleItems);
    }

    this.performanceMonitor.endTiming('virtualScroll.render', 'ui');
  }

  /**
   * Render single item
   */
  private renderItem(index: number): HTMLElement {
    const data = this.config.data[index];
    const isVisible = index >= this.state.visibleStart && index <= this.state.visibleEnd;
    
    // Try to reuse item from pool
    let item = this.getItemFromPool();
    
    if (!item) {
      item = this.config.renderItem(data, index, isVisible);
    } else {
      // Update existing item
      const newItem = this.config.renderItem(data, index, isVisible);
      item.innerHTML = newItem.innerHTML;
      item.className = newItem.className;
      
      // Copy attributes
      Array.from(newItem.attributes).forEach(attr => {
        item.setAttribute(attr.name, attr.value);
      });
    }

    // Position item
    const offset = this.getItemOffset(index);
    const height = this.getItemHeight(index);
    
    item.style.position = 'absolute';
    item.style.top = `${offset}px`;
    item.style.left = '0';
    item.style.right = '0';
    item.style.height = `${height}px`;
    item.style.boxSizing = 'border-box';

    // Add to container
    const viewport = this.container.querySelector('.virtual-scroll-viewport');
    if (viewport) {
      viewport.appendChild(item);
    }

    return item;
  }

  /**
   * Get item from pool
   */
  private getItemFromPool(): HTMLElement | null {
    return this.itemPool.pop() || null;
  }

  /**
   * Return item to pool
   */
  private returnItemToPool(item: HTMLElement): void {
    if (this.itemPool.length < (this.config.bufferSize || 10)) {
      // Clean up item
      item.style.display = 'none';
      item.innerHTML = '';
      item.className = '';
      
      // Remove custom attributes (keep style and standard attributes)
      Array.from(item.attributes).forEach(attr => {
        if (!['style', 'class', 'id'].includes(attr.name)) {
          item.removeAttribute(attr.name);
        }
      });
      
      this.itemPool.push(item);
    }
  }
}

/**
 * Infinite Scroll Implementation
 */
export class InfiniteScroll<T> {
  private container: HTMLElement;
  private config: {
    threshold: number;
    loadMore: () => Promise<T[]>;
    renderItem: (item: T, index: number) => HTMLElement;
    hasMore: () => boolean;
    loading?: HTMLElement;
    error?: HTMLElement;
  };
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private isLoading = false;
  private observer?: IntersectionObserver;
  private sentinel?: HTMLElement;
  private data: T[] = [];

  constructor(
    container: HTMLElement,
    config: InfiniteScroll<T>['config']
  ) {
    this.container = container;
    this.config = {
      threshold: 0.1,
      ...config
    };
    
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();

    this.initialize();
  }

  /**
   * Add initial data
   */
  setData(data: T[]): void {
    this.data = data;
    this.renderItems();
  }

  /**
   * Add more data
   */
  addData(data: T[]): void {
    this.data.push(...data);
    this.renderNewItems(data);
  }

  /**
   * Reset data
   */
  reset(): void {
    this.data = [];
    this.container.innerHTML = '';
    this.setupSentinel();
  }

  /**
   * Dispose infinite scroll
   */
  dispose(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  /**
   * Initialize infinite scroll
   */
  private initialize(): void {
    this.setupObserver();
    this.setupSentinel();
    
    this.logger.info('infiniteScroll', 'Infinite scroll initialized');
  }

  /**
   * Setup intersection observer
   */
  private setupObserver(): void {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.isLoading && this.config.hasMore()) {
            this.loadMore();
          }
        });
      },
      {
        threshold: this.config.threshold,
        rootMargin: '100px'
      }
    );
  }

  /**
   * Setup sentinel element
   */
  private setupSentinel(): void {
    this.sentinel = document.createElement('div');
    this.sentinel.style.height = '1px';
    this.sentinel.style.width = '100%';
    this.sentinel.className = 'infinite-scroll-sentinel';
    
    this.container.appendChild(this.sentinel);
    
    if (this.observer) {
      this.observer.observe(this.sentinel);
    }
  }

  /**
   * Load more items
   */
  private async loadMore(): Promise<void> {
    if (this.isLoading) return;

    this.isLoading = true;
    this.showLoading();

    try {
      this.performanceMonitor.startTiming('infiniteScroll.loadMore', 'ui');
      
      const newData = await this.config.loadMore();
      this.addData(newData);
      
      this.performanceMonitor.endTiming('infiniteScroll.loadMore', 'ui');
      
      this.logger.debug('infiniteScroll', `Loaded ${newData.length} more items`);
    } catch (error) {
      this.showError();
      this.logger.error('infiniteScroll', 'Failed to load more items', error);
    } finally {
      this.isLoading = false;
      this.hideLoading();
    }
  }

  /**
   * Render all items
   */
  private renderItems(): void {
    // Clear container except sentinel
    while (this.container.firstChild && this.container.firstChild !== this.sentinel) {
      this.container.removeChild(this.container.firstChild);
    }

    // Render items
    this.renderNewItems(this.data);
  }

  /**
   * Render new items
   */
  private renderNewItems(items: T[]): void {
    const fragment = document.createDocumentFragment();
    const startIndex = this.data.length - items.length;

    items.forEach((item, index) => {
      const element = this.config.renderItem(item, startIndex + index);
      fragment.appendChild(element);
    });

    // Insert before sentinel
    if (this.sentinel) {
      this.container.insertBefore(fragment, this.sentinel);
    } else {
      this.container.appendChild(fragment);
    }
  }

  /**
   * Show loading indicator
   */
  private showLoading(): void {
    if (this.config.loading && this.sentinel) {
      this.container.insertBefore(this.config.loading, this.sentinel);
    }
  }

  /**
   * Hide loading indicator
   */
  private hideLoading(): void {
    if (this.config.loading && this.config.loading.parentNode) {
      this.config.loading.parentNode.removeChild(this.config.loading);
    }
  }

  /**
   * Show error indicator
   */
  private showError(): void {
    if (this.config.error && this.sentinel) {
      this.container.insertBefore(this.config.error, this.sentinel);
    }
  }
}

// Export convenience functions
export function createVirtualScroll<T>(
  container: HTMLElement,
  config: VirtualScrollConfig<T>
): VirtualScrollContainer<T> {
  return new VirtualScrollContainer(container, config);
}

export function createInfiniteScroll<T>(
  container: HTMLElement,
  config: InfiniteScroll<T>['config']
): InfiniteScroll<T> {
  return new InfiniteScroll(container, config);
}