import { TabInfo, TabGroup, BookmarkNode, UserSettings, Message, MessageResponse } from '../types/index.js';

/**
 * Background Script服务接口
 * 定义后台脚本提供的核心功能
 */
export interface BackgroundService {
  // 标签页管理
  getAllTabs(): Promise<TabInfo[]>;
  getTabGroups(): Promise<TabGroup[]>;
  switchToTab(tabId: number): Promise<void>;
  closeTab(tabId: number): Promise<void>;
  createTabGroup(name: string, color: string, tabIds: number[]): Promise<TabGroup>;
  updateTabGroup(groupId: number, updates: Partial<TabGroup>): Promise<void>;
  duplicateTab(tabId: number): Promise<TabInfo>;
  createTab(url: string, active?: boolean): Promise<TabInfo>;
  closeOtherTabs(keepTabId: number): Promise<void>;
  ungroupTabs(groupId: number): Promise<void>;
  closeTabGroup(groupId: number): Promise<void>;
  
  // 收藏夹管理
  getBookmarks(): Promise<BookmarkNode[]>;
  addBookmark(url: string, title: string, folderId?: string): Promise<BookmarkNode>;
  removeBookmark(bookmarkId: string): Promise<void>;
  updateBookmark(bookmarkId: string, updates: Partial<BookmarkNode>): Promise<void>;
  
  // 设置管理
  getSettings(): Promise<UserSettings>;
  updateSettings(settings: Partial<UserSettings>): Promise<void>;
  resetSettings(): Promise<void>;
  
  // 消息处理
  handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<MessageResponse>;
  
  // 事件监听
  onTabsChanged(callback: (tabs: TabInfo[]) => void): void;
  onBookmarksChanged(callback: (bookmarks: BookmarkNode[]) => void): void;
  onSettingsChanged(callback: (settings: UserSettings) => void): void;
}

/**
 * 标签页管理器接口
 */
export interface TabManager {
  getCurrentWindowTabs(): Promise<TabInfo[]>;
  getTabById(tabId: number): Promise<TabInfo | null>;
  activateTab(tabId: number): Promise<void>;
  closeTab(tabId: number): Promise<void>;
  pinTab(tabId: number, pinned: boolean): Promise<void>;
  moveTab(tabId: number, index: number): Promise<void>;
  duplicateTab(tabId: number): Promise<TabInfo>;
  createTab(url: string, active?: boolean): Promise<TabInfo>;
  closeOtherTabs(keepTabId: number): Promise<void>;
  getTabGroups(): Promise<TabGroup[]>;
  createTabGroup(name: string, color: string, tabIds: number[]): Promise<TabGroup>;
  updateTabGroup(groupId: number, updates: Partial<TabGroup>): Promise<void>;
  ungroupTabs(groupId: number): Promise<void>;
  closeTabGroup(groupId: number): Promise<void>;
}

/**
 * 收藏夹管理器接口
 */
export interface BookmarkManager {
  getBookmarkTree(): Promise<BookmarkNode[]>;
  searchBookmarks(query: string): Promise<BookmarkNode[]>;
  createBookmark(bookmark: Partial<BookmarkNode>): Promise<BookmarkNode>;
  updateBookmark(id: string, updates: Partial<BookmarkNode>): Promise<void>;
  deleteBookmark(id: string): Promise<void>;
  moveBookmark(id: string, parentId: string, index?: number): Promise<void>;
}

/**
 * 设置管理器接口
 */
export interface SettingsManager {
  load(): Promise<UserSettings>;
  save(settings: Partial<UserSettings>): Promise<void>;
  reset(): Promise<void>;
  export(): Promise<string>;
  import(data: string): Promise<void>;
  validate(settings: any): boolean;
}