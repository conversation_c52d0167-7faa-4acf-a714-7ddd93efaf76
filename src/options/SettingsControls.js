/**
 * Settings Controls - 设置控件管理器
 */

class SettingsControls {
  constructor(optionsManager, validator) {
    this.optionsManager = optionsManager;
    this.validator = validator;
    this.controls = new Map();
    this.validationTimeouts = new Map();
    this.changeCallbacks = [];
  }

  /**
   * 注册控件
   */
  registerControl(key, element, type, options = {}) {
    const control = {
      key,
      element,
      type,
      options,
      isValid: true,
      lastValue: null,
      errorElement: null
    };

    this.controls.set(key, control);
    this.setupControlEvents(control);
    this.createErrorDisplay(control);
    
    return control;
  }

  /**
   * 设置控件事件
   */
  setupControlEvents(control) {
    const { element, type, key } = control;

    switch (type) {
      case 'switch':
        this.setupSwitchEvents(control);
        break;
      case 'select':
        this.setupSelectEvents(control);
        break;
      case 'slider':
        this.setupSliderEvents(control);
        break;
      case 'input':
        this.setupInputEvents(control);
        break;
      case 'color':
        this.setupColorEvents(control);
        break;
      case 'range':
        this.setupRangeEvents(control);
        break;
    }

    // 通用事件
    element.addEventListener('focus', () => {
      this.handleControlFocus(control);
    });

    element.addEventListener('blur', () => {
      this.handleControlBlur(control);
    });
  }

  /**
   * 设置开关控件事件
   */
  setupSwitchEvents(control) {
    const input = control.element.querySelector('input[type="checkbox"]');
    if (!input) return;

    input.addEventListener('change', (e) => {
      const value = e.target.checked;
      this.handleValueChange(control, value);
    });

    // 键盘支持
    input.addEventListener('keydown', (e) => {
      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        input.checked = !input.checked;
        input.dispatchEvent(new Event('change'));
      }
    });
  }

  /**
   * 设置选择控件事件
   */
  setupSelectEvents(control) {
    const select = control.element.querySelector('select');
    if (!select) return;

    select.addEventListener('change', (e) => {
      const value = e.target.value;
      this.handleValueChange(control, value);
    });

    // 键盘导航增强
    select.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        select.blur();
      }
    });
  }

  /**
   * 设置滑块控件事件
   */
  setupSliderEvents(control) {
    const slider = control.element.querySelector('.slider');
    const valueDisplay = control.element.querySelector('.slider-value');
    if (!slider || !valueDisplay) return;

    // 实时更新显示值
    slider.addEventListener('input', (e) => {
      const value = this.parseSliderValue(e.target.value, control.options);
      valueDisplay.textContent = this.formatSliderValue(value, control.options.unit);
      
      // 实时验证（防抖）
      this.debounceValidation(control, value);
    });

    // 最终值变更
    slider.addEventListener('change', (e) => {
      const value = this.parseSliderValue(e.target.value, control.options);
      this.handleValueChange(control, value);
    });

    // 键盘精确控制
    slider.addEventListener('keydown', (e) => {
      const step = parseFloat(slider.step) || 1;
      const currentValue = parseFloat(slider.value);
      let newValue = currentValue;

      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowDown':
          newValue = Math.max(parseFloat(slider.min), currentValue - step);
          break;
        case 'ArrowRight':
        case 'ArrowUp':
          newValue = Math.min(parseFloat(slider.max), currentValue + step);
          break;
        case 'Home':
          newValue = parseFloat(slider.min);
          break;
        case 'End':
          newValue = parseFloat(slider.max);
          break;
        case 'PageDown':
          newValue = Math.max(parseFloat(slider.min), currentValue - step * 10);
          break;
        case 'PageUp':
          newValue = Math.min(parseFloat(slider.max), currentValue + step * 10);
          break;
        default:
          return;
      }

      e.preventDefault();
      slider.value = newValue;
      slider.dispatchEvent(new Event('input'));
      slider.dispatchEvent(new Event('change'));
    });
  }

  /**
   * 设置输入控件事件
   */
  setupInputEvents(control) {
    const input = control.element.querySelector('input');
    if (!input) return;

    // 实时验证（防抖）
    input.addEventListener('input', (e) => {
      const value = e.target.value;
      this.debounceValidation(control, value);
    });

    // 最终值变更
    input.addEventListener('change', (e) => {
      const value = this.parseInputValue(e.target.value, control.options);
      this.handleValueChange(control, value);
    });

    // 特殊键处理
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        input.blur();
      }
      
      // 数字输入限制
      if (control.options.type === 'number') {
        this.handleNumberInputKeydown(e, input, control.options);
      }
    });

    // 粘贴处理
    input.addEventListener('paste', (e) => {
      setTimeout(() => {
        const value = this.parseInputValue(input.value, control.options);
        if (value !== input.value) {
          input.value = value;
          input.dispatchEvent(new Event('input'));
        }
      }, 0);
    });
  }

  /**
   * 设置颜色控件事件
   */
  setupColorEvents(control) {
    const input = control.element.querySelector('input[type="color"]');
    if (!input) return;

    input.addEventListener('change', (e) => {
      const value = e.target.value;
      this.handleValueChange(control, value);
    });

    // 实时预览
    input.addEventListener('input', (e) => {
      const value = e.target.value;
      this.showColorPreview(control, value);
    });
  }

  /**
   * 设置范围控件事件
   */
  setupRangeEvents(control) {
    const inputs = control.element.querySelectorAll('input[type="range"]');
    if (inputs.length !== 2) return;

    const [minInput, maxInput] = inputs;
    const minDisplay = control.element.querySelector('.range-min-value');
    const maxDisplay = control.element.querySelector('.range-max-value');

    // 最小值变更
    minInput.addEventListener('input', (e) => {
      const minValue = parseFloat(e.target.value);
      const maxValue = parseFloat(maxInput.value);
      
      if (minValue >= maxValue) {
        minInput.value = maxValue - parseFloat(minInput.step || 1);
      }
      
      if (minDisplay) {
        minDisplay.textContent = this.formatSliderValue(parseFloat(minInput.value), control.options.unit);
      }
      
      this.debounceValidation(control, [parseFloat(minInput.value), parseFloat(maxInput.value)]);
    });

    // 最大值变更
    maxInput.addEventListener('input', (e) => {
      const minValue = parseFloat(minInput.value);
      const maxValue = parseFloat(e.target.value);
      
      if (maxValue <= minValue) {
        maxInput.value = minValue + parseFloat(maxInput.step || 1);
      }
      
      if (maxDisplay) {
        maxDisplay.textContent = this.formatSliderValue(parseFloat(maxInput.value), control.options.unit);
      }
      
      this.debounceValidation(control, [parseFloat(minInput.value), parseFloat(maxInput.value)]);
    });

    // 最终值变更
    [minInput, maxInput].forEach(input => {
      input.addEventListener('change', () => {
        const value = [parseFloat(minInput.value), parseFloat(maxInput.value)];
        this.handleValueChange(control, value);
      });
    });
  }

  /**
   * 处理值变更
   */
  handleValueChange(control, value) {
    const { key } = control;
    const oldValue = this.optionsManager.settings[key];
    
    // 验证新值
    const validation = this.validator.validateRealtime(key, value, this.optionsManager.settings);
    
    if (validation.valid) {
      // 更新设置
      this.optionsManager.settings[key] = value;
      control.lastValue = value;
      control.isValid = true;
      
      // 清除错误显示
      this.clearError(control);
      
      // 标记为已更改
      this.optionsManager.markAsChanged();
      
      // 触发变更回调
      this.notifyChange(key, value, oldValue);
      
      // 检查依赖关系并更新相关控件
      this.updateDependentControls(key, value);
      
    } else {
      // 显示错误
      this.showError(control, validation.errors);
      control.isValid = false;
    }
  }

  /**
   * 处理控件获得焦点
   */
  handleControlFocus(control) {
    control.element.classList.add('focused');
    
    // 显示帮助信息
    this.showHelpText(control);
    
    // 清除之前的错误（如果有）
    if (control.isValid) {
      this.clearError(control);
    }
  }

  /**
   * 处理控件失去焦点
   */
  handleControlBlur(control) {
    control.element.classList.remove('focused');
    
    // 隐藏帮助信息
    this.hideHelpText(control);
    
    // 执行最终验证
    const currentValue = this.getCurrentValue(control);
    if (currentValue !== control.lastValue) {
      this.handleValueChange(control, currentValue);
    }
  }

  /**
   * 防抖验证
   */
  debounceValidation(control, value) {
    const { key } = control;
    
    // 清除之前的定时器
    if (this.validationTimeouts.has(key)) {
      clearTimeout(this.validationTimeouts.get(key));
    }
    
    // 设置新的定时器
    const timeout = setTimeout(() => {
      const validation = this.validator.validateRealtime(key, value, this.optionsManager.settings);
      
      if (validation.valid) {
        this.clearError(control);
      } else {
        this.showError(control, validation.errors);
      }
      
      this.validationTimeouts.delete(key);
    }, 300);
    
    this.validationTimeouts.set(key, timeout);
  }

  /**
   * 显示错误
   */
  showError(control, errors) {
    if (!control.errorElement) {
      this.createErrorDisplay(control);
    }
    
    control.element.classList.add('error');
    control.errorElement.textContent = errors[0] || '验证失败';
    control.errorElement.style.display = 'block';
    
    // 添加ARIA属性
    const input = this.getInputElement(control);
    if (input) {
      input.setAttribute('aria-invalid', 'true');
      input.setAttribute('aria-describedby', control.errorElement.id);
    }
  }

  /**
   * 清除错误
   */
  clearError(control) {
    control.element.classList.remove('error');
    
    if (control.errorElement) {
      control.errorElement.style.display = 'none';
    }
    
    // 移除ARIA属性
    const input = this.getInputElement(control);
    if (input) {
      input.removeAttribute('aria-invalid');
      input.removeAttribute('aria-describedby');
    }
  }

  /**
   * 创建错误显示元素
   */
  createErrorDisplay(control) {
    if (control.errorElement) return;
    
    const errorElement = document.createElement('div');
    errorElement.className = 'setting-error';
    errorElement.id = `error-${control.key}`;
    errorElement.style.display = 'none';
    errorElement.style.color = '#d1242f';
    errorElement.style.fontSize = '12px';
    errorElement.style.marginTop = '4px';
    errorElement.setAttribute('role', 'alert');
    
    control.element.appendChild(errorElement);
    control.errorElement = errorElement;
  }

  /**
   * 显示帮助文本
   */
  showHelpText(control) {
    const constraints = this.validator.getConstraints(control.key);
    if (!constraints) return;
    
    let helpText = '';
    
    if (constraints.type === 'number') {
      const parts = [];
      if (constraints.min !== undefined) parts.push(`最小值: ${constraints.min}`);
      if (constraints.max !== undefined) parts.push(`最大值: ${constraints.max}`);
      if (constraints.step !== undefined) parts.push(`步长: ${constraints.step}`);
      helpText = parts.join(', ');
    } else if (constraints.enum) {
      helpText = `可选值: ${constraints.enum.join(', ')}`;
    }
    
    if (helpText) {
      this.showTooltip(control.element, helpText);
    }
  }

  /**
   * 隐藏帮助文本
   */
  hideHelpText(control) {
    this.hideTooltip();
  }

  /**
   * 显示工具提示
   */
  showTooltip(element, text) {
    // 移除现有工具提示
    this.hideTooltip();
    
    const tooltip = document.createElement('div');
    tooltip.className = 'setting-tooltip';
    tooltip.textContent = text;
    tooltip.style.position = 'absolute';
    tooltip.style.backgroundColor = '#24292f';
    tooltip.style.color = '#ffffff';
    tooltip.style.padding = '6px 8px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.fontSize = '12px';
    tooltip.style.zIndex = '1000';
    tooltip.style.whiteSpace = 'nowrap';
    tooltip.style.pointerEvents = 'none';
    
    document.body.appendChild(tooltip);
    
    // 定位工具提示
    const rect = element.getBoundingClientRect();
    tooltip.style.left = `${rect.left}px`;
    tooltip.style.top = `${rect.bottom + 5}px`;
    
    this.currentTooltip = tooltip;
  }

  /**
   * 隐藏工具提示
   */
  hideTooltip() {
    if (this.currentTooltip && this.currentTooltip.parentNode) {
      this.currentTooltip.parentNode.removeChild(this.currentTooltip);
      this.currentTooltip = null;
    }
  }

  /**
   * 更新依赖控件
   */
  updateDependentControls(changedKey, newValue) {
    // 根据变更的设置更新相关控件的状态
    const dependencies = this.getDependencies(changedKey);
    
    dependencies.forEach(depKey => {
      const control = this.controls.get(depKey);
      if (control) {
        this.updateControlState(control);
      }
    });
  }

  /**
   * 获取依赖关系
   */
  getDependencies(key) {
    const dependencies = {
      'enabled': ['showOnHover', 'pinned'],
      'showOnHover': ['pinned'],
      'pinned': ['showOnHover'],
      'enableSearch': ['showSearchHistory', 'searchDelay', 'maxSearchHistory'],
      'showRecentTabs': ['maxRecentTabs'],
      'showIcons': ['showFavicons']
    };
    
    return dependencies[key] || [];
  }

  /**
   * 更新控件状态
   */
  updateControlState(control) {
    const input = this.getInputElement(control);
    if (!input) return;
    
    // 检查是否应该禁用
    const shouldDisable = this.shouldDisableControl(control);
    
    if (shouldDisable) {
      input.disabled = true;
      control.element.classList.add('disabled');
    } else {
      input.disabled = false;
      control.element.classList.remove('disabled');
    }
    
    // 重新验证
    const currentValue = this.getCurrentValue(control);
    const validation = this.validator.validateRealtime(control.key, currentValue, this.optionsManager.settings);
    
    if (validation.valid) {
      this.clearError(control);
    } else {
      this.showError(control, validation.errors);
    }
  }

  /**
   * 检查是否应该禁用控件
   */
  shouldDisableControl(control) {
    const { key } = control;
    const settings = this.optionsManager.settings;
    
    switch (key) {
      case 'showOnHover':
        return !settings.enabled;
      case 'hoverDelay':
        return !settings.enabled || !settings.showOnHover;
      case 'showSearchHistory':
      case 'searchDelay':
      case 'maxSearchHistory':
        return !settings.enableSearch;
      case 'maxRecentTabs':
        return !settings.showRecentTabs;
      case 'showFavicons':
        return !settings.showIcons;
      default:
        return false;
    }
  }

  /**
   * 获取当前值
   */
  getCurrentValue(control) {
    const { type } = control;
    
    switch (type) {
      case 'switch':
        const checkbox = control.element.querySelector('input[type="checkbox"]');
        return checkbox ? checkbox.checked : false;
        
      case 'select':
        const select = control.element.querySelector('select');
        return select ? select.value : '';
        
      case 'slider':
        const slider = control.element.querySelector('.slider');
        return slider ? this.parseSliderValue(slider.value, control.options) : 0;
        
      case 'input':
        const input = control.element.querySelector('input');
        return input ? this.parseInputValue(input.value, control.options) : '';
        
      case 'color':
        const colorInput = control.element.querySelector('input[type="color"]');
        return colorInput ? colorInput.value : '#000000';
        
      case 'range':
        const rangeInputs = control.element.querySelectorAll('input[type="range"]');
        return rangeInputs.length === 2 ? 
          [parseFloat(rangeInputs[0].value), parseFloat(rangeInputs[1].value)] : [0, 0];
        
      default:
        return null;
    }
  }

  /**
   * 设置控件值
   */
  setControlValue(key, value) {
    const control = this.controls.get(key);
    if (!control) return;
    
    const { type } = control;
    
    switch (type) {
      case 'switch':
        const checkbox = control.element.querySelector('input[type="checkbox"]');
        if (checkbox) checkbox.checked = Boolean(value);
        break;
        
      case 'select':
        const select = control.element.querySelector('select');
        if (select) select.value = String(value);
        break;
        
      case 'slider':
        const slider = control.element.querySelector('.slider');
        const valueDisplay = control.element.querySelector('.slider-value');
        if (slider) {
          slider.value = String(value);
          if (valueDisplay) {
            valueDisplay.textContent = this.formatSliderValue(value, control.options.unit);
          }
        }
        break;
        
      case 'input':
        const input = control.element.querySelector('input');
        if (input) input.value = String(value);
        break;
        
      case 'color':
        const colorInput = control.element.querySelector('input[type="color"]');
        if (colorInput) colorInput.value = String(value);
        break;
        
      case 'range':
        const rangeInputs = control.element.querySelectorAll('input[type="range"]');
        if (rangeInputs.length === 2 && Array.isArray(value)) {
          rangeInputs[0].value = String(value[0]);
          rangeInputs[1].value = String(value[1]);
          
          const minDisplay = control.element.querySelector('.range-min-value');
          const maxDisplay = control.element.querySelector('.range-max-value');
          if (minDisplay) minDisplay.textContent = this.formatSliderValue(value[0], control.options.unit);
          if (maxDisplay) maxDisplay.textContent = this.formatSliderValue(value[1], control.options.unit);
        }
        break;
    }
    
    control.lastValue = value;
  }

  /**
   * 解析滑块值
   */
  parseSliderValue(value, options) {
    const numValue = parseFloat(value);
    return options.type === 'integer' ? Math.round(numValue) : numValue;
  }

  /**
   * 格式化滑块值
   */
  formatSliderValue(value, unit) {
    if (unit === '') {
      return Math.round(value * 100) + '%';
    }
    return value + (unit || '');
  }

  /**
   * 解析输入值
   */
  parseInputValue(value, options) {
    if (options.type === 'number') {
      const numValue = parseFloat(value);
      return isNaN(numValue) ? 0 : numValue;
    }
    return String(value).trim();
  }

  /**
   * 处理数字输入键盘事件
   */
  handleNumberInputKeydown(event, input, options) {
    const allowedKeys = [
      'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
      'Home', 'End', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'
    ];
    
    // 允许特殊键
    if (allowedKeys.includes(event.key)) {
      return;
    }
    
    // 允许Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if (event.ctrlKey || event.metaKey) {
      return;
    }
    
    // 允许数字
    if (/^\d$/.test(event.key)) {
      return;
    }
    
    // 允许小数点（如果不是整数类型）
    if (event.key === '.' && options.type !== 'integer' && !input.value.includes('.')) {
      return;
    }
    
    // 允许负号（在开头）
    if (event.key === '-' && input.selectionStart === 0 && !input.value.includes('-')) {
      return;
    }
    
    // 阻止其他输入
    event.preventDefault();
  }

  /**
   * 显示颜色预览
   */
  showColorPreview(control, color) {
    let preview = control.element.querySelector('.color-preview');
    if (!preview) {
      preview = document.createElement('div');
      preview.className = 'color-preview';
      preview.style.width = '20px';
      preview.style.height = '20px';
      preview.style.borderRadius = '4px';
      preview.style.border = '1px solid #d0d7de';
      preview.style.marginLeft = '8px';
      control.element.appendChild(preview);
    }
    
    preview.style.backgroundColor = color;
  }

  /**
   * 获取输入元素
   */
  getInputElement(control) {
    const { type } = control;
    
    switch (type) {
      case 'switch':
        return control.element.querySelector('input[type="checkbox"]');
      case 'select':
        return control.element.querySelector('select');
      case 'slider':
        return control.element.querySelector('.slider');
      case 'input':
        return control.element.querySelector('input');
      case 'color':
        return control.element.querySelector('input[type="color"]');
      case 'range':
        return control.element.querySelector('input[type="range"]');
      default:
        return null;
    }
  }

  /**
   * 添加变更回调
   */
  onChange(callback) {
    this.changeCallbacks.push(callback);
  }

  /**
   * 通知变更
   */
  notifyChange(key, newValue, oldValue) {
    this.changeCallbacks.forEach(callback => {
      try {
        callback(key, newValue, oldValue);
      } catch (error) {
        console.error('Error in change callback:', error);
      }
    });
  }

  /**
   * 验证所有控件
   */
  validateAllControls() {
    let allValid = true;
    
    this.controls.forEach(control => {
      const currentValue = this.getCurrentValue(control);
      const validation = this.validator.validateRealtime(control.key, currentValue, this.optionsManager.settings);
      
      if (validation.valid) {
        this.clearError(control);
        control.isValid = true;
      } else {
        this.showError(control, validation.errors);
        control.isValid = false;
        allValid = false;
      }
    });
    
    return allValid;
  }

  /**
   * 重置所有控件
   */
  resetAllControls() {
    this.controls.forEach(control => {
      this.clearError(control);
      control.isValid = true;
      control.lastValue = null;
    });
  }

  /**
   * 销毁控件管理器
   */
  destroy() {
    // 清除所有定时器
    this.validationTimeouts.forEach(timeout => clearTimeout(timeout));
    this.validationTimeouts.clear();
    
    // 隐藏工具提示
    this.hideTooltip();
    
    // 清除控件
    this.controls.clear();
    this.changeCallbacks = [];
  }
}

// 导出控件管理器
window.SettingsControls = SettingsControls;