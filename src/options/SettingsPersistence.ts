/**
 * Settings Persistence - 设置持久化服务
 */

import { StorageAdapter, StorageOptions } from './StorageAdapter';
import { SettingsManager, SettingsChangeEvent } from './SettingsManager';

export interface PersistenceOptions {
  autoSave?: boolean;
  autoSaveDelay?: number;
  syncAcrossDevices?: boolean;
  backupEnabled?: boolean;
  maxBackups?: number;
  compressionEnabled?: boolean;
  encryptionEnabled?: boolean;
}

export interface BackupInfo {
  id: string;
  timestamp: number;
  version: string;
  size: number;
  description?: string;
}

export interface SyncStatus {
  lastSync: number;
  syncInProgress: boolean;
  syncError?: string;
  conflictResolution: 'local' | 'remote' | 'merge';
}

export class SettingsPersistence {
  private settingsManager: SettingsManager;
  private storageAdapter: StorageAdapter;
  private options: PersistenceOptions;
  private autoSaveTimer?: NodeJS.Timeout;
  private pendingChanges: Set<string> = new Set();
  private syncStatus: SyncStatus;
  private isInitialized = false;

  constructor(
    settingsManager: SettingsManager,
    options: PersistenceOptions = {}
  ) {
    this.settingsManager = settingsManager;
    this.options = {
      autoSave: true,
      autoSaveDelay: 1000,
      syncAcrossDevices: true,
      backupEnabled: true,
      maxBackups: 5,
      compressionEnabled: true,
      encryptionEnabled: false,
      ...options
    };

    this.syncStatus = {
      lastSync: 0,
      syncInProgress: false,
      conflictResolution: 'merge'
    };

    this.initializeStorage();
    this.setupEventListeners();
  }

  /**
   * 初始化存储适配器
   */
  private initializeStorage(): void {
    const storageOptions: StorageOptions = {
      backend: this.options.syncAcrossDevices ? 'sync' : 'local',
      prefix: 'sidebar_settings_',
      compression: this.options.compressionEnabled,
      encryption: this.options.encryptionEnabled,
      maxSize: this.options.syncAcrossDevices ? 8192 : 5242880 // 8KB for sync, 5MB for local
    };

    this.storageAdapter = new StorageAdapter(storageOptions);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听设置变更
    this.settingsManager.onChange((event: SettingsChangeEvent) => {
      this.handleSettingsChange(event);
    });

    // 监听存储变更（用于跨标签页同步）
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === (this.options.syncAcrossDevices ? 'sync' : 'local')) {
          this.handleStorageChange(changes);
        }
      });
    }

    // 监听页面卸载事件，确保保存未保存的更改
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        if (this.pendingChanges.size > 0) {
          this.saveImmediately();
        }
      });
    }
  }

  /**
   * 初始化持久化服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 加载设置
      await this.loadSettings();
      
      // 检查是否需要迁移
      await this.checkAndMigrate();
      
      // 初始化备份系统
      if (this.options.backupEnabled) {
        await this.initializeBackupSystem();
      }
      
      // 执行初始同步
      if (this.options.syncAcrossDevices) {
        await this.performSync();
      }

      this.isInitialized = true;
      console.log('Settings persistence initialized successfully');
    } catch (error) {
      console.error('Error initializing settings persistence:', error);
      throw error;
    }
  }

  /**
   * 加载设置
   */
  async loadSettings(): Promise<void> {
    try {
      const storedSettings = await this.storageAdapter.get('settings');
      
      if (storedSettings) {
        // 验证和应用设置
        const validSettings = this.validateStoredSettings(storedSettings);
        this.settingsManager.setAll(validSettings);
        
        console.log('Settings loaded from storage:', validSettings);
      } else {
        // 首次运行，保存默认设置
        await this.saveSettings();
        console.log('Default settings saved for first run');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      throw error;
    }
  }

  /**
   * 保存设置
   */
  async saveSettings(): Promise<void> {
    try {
      const settings = this.settingsManager.getAll();
      const metadata = {
        version: '1.0.0',
        timestamp: Date.now(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
      };

      await this.storageAdapter.set('settings', settings);
      await this.storageAdapter.set('metadata', metadata);
      
      // 清除待保存的更改
      this.pendingChanges.clear();
      
      // 创建备份
      if (this.options.backupEnabled) {
        await this.createBackup();
      }

      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }

  /**
   * 立即保存设置
   */
  async saveImmediately(): Promise<void> {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }
    
    await this.saveSettings();
  }

  /**
   * 处理设置变更
   */
  private handleSettingsChange(event: SettingsChangeEvent): void {
    this.pendingChanges.add(event.key);

    if (this.options.autoSave) {
      this.scheduleAutoSave();
    }
  }

  /**
   * 调度自动保存
   */
  private scheduleAutoSave(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }

    this.autoSaveTimer = setTimeout(async () => {
      try {
        await this.saveSettings();
      } catch (error) {
        console.error('Auto-save failed:', error);
      }
    }, this.options.autoSaveDelay);
  }

  /**
   * 处理存储变更（跨标签页同步）
   */
  private handleStorageChange(changes: Record<string, chrome.storage.StorageChange>): void {
    if (changes.sidebar_settings_settings && changes.sidebar_settings_settings.newValue) {
      const newSettings = changes.sidebar_settings_settings.newValue;
      
      // 避免循环更新
      if (this.pendingChanges.size === 0) {
        const validSettings = this.validateStoredSettings(newSettings);
        this.settingsManager.setAll(validSettings);
        console.log('Settings synchronized from another tab');
      }
    }
  }

  /**
   * 验证存储的设置
   */
  private validateStoredSettings(storedSettings: any): Record<string, any> {
    const validSettings: Record<string, any> = {};
    
    if (typeof storedSettings === 'object' && storedSettings !== null) {
      Object.entries(storedSettings).forEach(([key, value]) => {
        const definition = this.settingsManager.getSettingDefinition(key);
        if (definition) {
          const validationResult = this.settingsManager.validateSetting(key, value);
          if (validationResult === true) {
            validSettings[key] = value;
          } else {
            console.warn(`Invalid stored setting ${key}, using default:`, validationResult);
            validSettings[key] = definition.defaultValue;
          }
        }
      });
    }
    
    return validSettings;
  }

  /**
   * 检查并执行迁移
   */
  private async checkAndMigrate(): Promise<void> {
    try {
      const metadata = await this.storageAdapter.get('metadata');
      
      if (!metadata || !metadata.version) {
        // 首次安装或旧版本，执行迁移
        await this.migrateFromLegacyStorage();
      }
    } catch (error) {
      console.warn('Migration check failed:', error);
    }
  }

  /**
   * 从旧版本存储迁移
   */
  private async migrateFromLegacyStorage(): Promise<void> {
    try {
      // 尝试从旧的存储格式加载设置
      const legacySettings = await chrome.storage.sync.get(null);
      const migratedSettings: Record<string, any> = {};
      
      // 迁移已知的设置项
      Object.entries(legacySettings).forEach(([key, value]) => {
        if (this.settingsManager.getSettingDefinition(key)) {
          migratedSettings[key] = value;
        }
      });
      
      if (Object.keys(migratedSettings).length > 0) {
        this.settingsManager.setAll(migratedSettings);
        await this.saveSettings();
        console.log('Settings migrated from legacy storage:', migratedSettings);
      }
    } catch (error) {
      console.warn('Legacy migration failed:', error);
    }
  }

  /**
   * 初始化备份系统
   */
  private async initializeBackupSystem(): Promise<void> {
    try {
      const backups = await this.getBackups();
      
      // 清理过期的备份
      if (backups.length > this.options.maxBackups!) {
        const backupsToDelete = backups
          .sort((a, b) => a.timestamp - b.timestamp)
          .slice(0, backups.length - this.options.maxBackups!);
        
        for (const backup of backupsToDelete) {
          await this.deleteBackup(backup.id);
        }
      }
    } catch (error) {
      console.warn('Backup system initialization failed:', error);
    }
  }

  /**
   * 创建备份
   */
  async createBackup(description?: string): Promise<string> {
    try {
      const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const settings = this.settingsManager.getAll();
      const metadata = await this.storageAdapter.get('metadata');
      
      const backupData = {
        id: backupId,
        timestamp: Date.now(),
        version: metadata?.version || '1.0.0',
        description: description || `Auto backup ${new Date().toLocaleString()}`,
        settings
      };
      
      await this.storageAdapter.set(`backup_${backupId}`, backupData);
      
      console.log('Backup created:', backupId);
      return backupId;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  /**
   * 获取所有备份
   */
  async getBackups(): Promise<BackupInfo[]> {
    try {
      const allData = await this.storageAdapter.get([]);
      const backups: BackupInfo[] = [];
      
      Object.entries(allData).forEach(([key, value]) => {
        if (key.startsWith('backup_') && value && typeof value === 'object') {
          const backup = value as any;
          backups.push({
            id: backup.id,
            timestamp: backup.timestamp,
            version: backup.version,
            size: this.calculateBackupSize(backup),
            description: backup.description
          });
        }
      });
      
      return backups.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Error getting backups:', error);
      return [];
    }
  }

  /**
   * 恢复备份
   */
  async restoreBackup(backupId: string): Promise<void> {
    try {
      const backupData = await this.storageAdapter.get(`backup_${backupId}`);
      
      if (!backupData || !backupData.settings) {
        throw new Error('Backup not found or invalid');
      }
      
      // 创建当前设置的备份
      await this.createBackup('Before restore');
      
      // 恢复设置
      const validSettings = this.validateStoredSettings(backupData.settings);
      this.settingsManager.setAll(validSettings);
      await this.saveSettings();
      
      console.log('Backup restored successfully:', backupId);
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw error;
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<void> {
    try {
      await this.storageAdapter.remove(`backup_${backupId}`);
      console.log('Backup deleted:', backupId);
    } catch (error) {
      console.error('Error deleting backup:', error);
      throw error;
    }
  }

  /**
   * 计算备份大小
   */
  private calculateBackupSize(backup: any): number {
    return new Blob([JSON.stringify(backup)]).size;
  }

  /**
   * 执行同步
   */
  async performSync(): Promise<void> {
    if (this.syncStatus.syncInProgress) {
      return;
    }

    this.syncStatus.syncInProgress = true;
    this.syncStatus.syncError = undefined;

    try {
      const localSettings = this.settingsManager.getAll();
      const localMetadata = await this.storageAdapter.get('metadata');
      
      // 获取远程设置（这里简化处理，实际项目中可能需要更复杂的同步逻辑）
      const remoteSettings = await this.storageAdapter.get('settings');
      const remoteMetadata = await this.storageAdapter.get('metadata');
      
      if (remoteMetadata && localMetadata) {
        const localTimestamp = localMetadata.timestamp || 0;
        const remoteTimestamp = remoteMetadata.timestamp || 0;
        
        if (remoteTimestamp > localTimestamp) {
          // 远程更新，应用远程设置
          const validSettings = this.validateStoredSettings(remoteSettings);
          this.settingsManager.setAll(validSettings);
          console.log('Settings synchronized from remote');
        } else if (localTimestamp > remoteTimestamp) {
          // 本地更新，上传本地设置
          await this.saveSettings();
          console.log('Settings synchronized to remote');
        }
      }
      
      this.syncStatus.lastSync = Date.now();
    } catch (error) {
      this.syncStatus.syncError = error instanceof Error ? error.message : 'Unknown sync error';
      console.error('Sync failed:', error);
    } finally {
      this.syncStatus.syncInProgress = false;
    }
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<string> {
    const settings = this.settingsManager.getAll();
    const metadata = await this.storageAdapter.get('metadata');
    const backups = await this.getBackups();
    
    const exportData = {
      version: '1.0.0',
      exportTimestamp: Date.now(),
      settings,
      metadata,
      backups: backups.slice(0, 3) // 只导出最近3个备份的信息
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导入设置
   */
  async importSettings(data: string): Promise<void> {
    try {
      const importData = JSON.parse(data);
      
      if (!importData.settings) {
        throw new Error('Invalid import data format');
      }
      
      // 创建导入前的备份
      await this.createBackup('Before import');
      
      // 导入设置
      const validSettings = this.validateStoredSettings(importData.settings);
      this.settingsManager.setAll(validSettings);
      await this.saveSettings();
      
      console.log('Settings imported successfully');
    } catch (error) {
      console.error('Error importing settings:', error);
      throw error;
    }
  }

  /**
   * 重置所有设置
   */
  async resetSettings(): Promise<void> {
    try {
      // 创建重置前的备份
      await this.createBackup('Before reset');
      
      // 重置设置
      this.settingsManager.reset();
      await this.saveSettings();
      
      console.log('Settings reset to defaults');
    } catch (error) {
      console.error('Error resetting settings:', error);
      throw error;
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalSize: number;
    settingsSize: number;
    backupsSize: number;
    backupsCount: number;
    lastSync: number;
    syncStatus: string;
  }> {
    try {
      const totalSize = await this.storageAdapter.getBytesInUse();
      const settingsSize = await this.storageAdapter.getBytesInUse('settings');
      const backups = await this.getBackups();
      const backupsSize = backups.reduce((total, backup) => total + backup.size, 0);
      
      return {
        totalSize,
        settingsSize,
        backupsSize,
        backupsCount: backups.length,
        lastSync: this.syncStatus.lastSync,
        syncStatus: this.syncStatus.syncInProgress ? 'syncing' : 
                   this.syncStatus.syncError ? 'error' : 'idle'
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return {
        totalSize: 0,
        settingsSize: 0,
        backupsSize: 0,
        backupsCount: 0,
        lastSync: 0,
        syncStatus: 'error'
      };
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  /**
   * 设置同步冲突解决策略
   */
  setConflictResolution(strategy: 'local' | 'remote' | 'merge'): void {
    this.syncStatus.conflictResolution = strategy;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }
    
    this.pendingChanges.clear();
    this.isInitialized = false;
  }
}