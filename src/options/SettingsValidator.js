/**
 * Settings Validator - 设置验证器
 */

class SettingsValidator {
  constructor() {
    this.validationRules = this.getValidationRules();
    this.errorMessages = this.getErrorMessages();
  }

  /**
   * 获取验证规则
   */
  getValidationRules() {
    return {
      // 常规设置验证
      enabled: {
        type: 'boolean',
        required: true
      },
      autoShow: {
        type: 'boolean',
        required: true
      },
      showOnHover: {
        type: 'boolean',
        required: true
      },
      hoverDelay: {
        type: 'number',
        required: true,
        min: 0,
        max: 2000,
        step: 50
      },
      pinned: {
        type: 'boolean',
        required: true
      },
      position: {
        type: 'string',
        required: true,
        enum: ['left', 'right']
      },

      // 外观设置验证
      theme: {
        type: 'string',
        required: true,
        enum: ['auto', 'light', 'dark']
      },
      width: {
        type: 'number',
        required: true,
        min: 200,
        max: 600,
        step: 10
      },
      opacity: {
        type: 'number',
        required: true,
        min: 0.3,
        max: 1.0,
        step: 0.05
      },
      showIcons: {
        type: 'boolean',
        required: true
      },
      showFavicons: {
        type: 'boolean',
        required: true
      },
  
      // 行为设置验证
      closeTabsOnClick: {
        type: 'boolean',
        required: true
      },
      openInNewTab: {
        type: 'boolean',
        required: true
      },
      groupTabs: {
        type: 'boolean',
        required: true
      },
      showBookmarks: {
        type: 'boolean',
        required: true
      },
      showRecentTabs: {
        type: 'boolean',
        required: true
      },
      maxRecentTabs: {
        type: 'number',
        required: true,
        min: 5,
        max: 100,
        step: 5
      },

      // 搜索设置验证
      enableSearch: {
        type: 'boolean',
        required: true
      },
      searchDelay: {
        type: 'number',
        required: true,
        min: 100,
        max: 1000,
        step: 50
      },
      showSearchHistory: {
        type: 'boolean',
        required: true
      },
      maxSearchHistory: {
        type: 'number',
        required: true,
        min: 5,
        max: 50,
        step: 5
      },

      // 高级设置验证
      debugMode: {
        type: 'boolean',
        required: true
      },
      enableAnimations: {
        type: 'boolean',
        required: true
      },
      enableSounds: {
        type: 'boolean',
        required: true
      },
      autoUpdate: {
        type: 'boolean',
        required: true
      },
      telemetry: {
        type: 'boolean',
        required: true
      }
    };
  }

  /**
   * 获取错误消息
   */
  getErrorMessages() {
    return {
      required: '此字段为必填项',
      type: '数据类型不正确',
      min: '值不能小于 {min}',
      max: '值不能大于 {max}',
      step: '值必须是 {step} 的倍数',
      enum: '值必须是以下选项之一: {options}',
      custom: '自定义验证失败'
    };
  }

  /**
   * 验证单个设置项
   */
  validateSetting(key, value) {
    const rule = this.validationRules[key];
    if (!rule) {
      return { valid: true };
    }

    const errors = [];

    // 必填验证
    if (rule.required && (value === null || value === undefined || value === '')) {
      errors.push(this.errorMessages.required);
      return { valid: false, errors };
    }

    // 类型验证
    if (!this.validateType(value, rule.type)) {
      errors.push(this.errorMessages.type);
      return { valid: false, errors };
    }

    // 数值范围验证
    if (rule.type === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push(this.errorMessages.min.replace('{min}', rule.min));
      }
      if (rule.max !== undefined && value > rule.max) {
        errors.push(this.errorMessages.max.replace('{max}', rule.max));
      }
      if (rule.step !== undefined && (value % rule.step) !== 0) {
        errors.push(this.errorMessages.step.replace('{step}', rule.step));
      }
    }

    // 枚举值验证
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push(this.errorMessages.enum.replace('{options}', rule.enum.join(', ')));
    }

    // 自定义验证
    if (rule.custom && typeof rule.custom === 'function') {
      const customResult = rule.custom(value);
      if (!customResult.valid) {
        errors.push(customResult.message || this.errorMessages.custom);
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 验证所有设置
   */
  validateAllSettings(settings) {
    const results = {};
    let hasErrors = false;

    Object.keys(this.validationRules).forEach(key => {
      const result = this.validateSetting(key, settings[key]);
      results[key] = result;
      if (!result.valid) {
        hasErrors = true;
      }
    });

    return {
      valid: !hasErrors,
      results: results
    };
  }

  /**
   * 验证类型
   */
  validateType(value, expectedType) {
    switch (expectedType) {
      case 'boolean':
        return typeof value === 'boolean';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'string':
        return typeof value === 'string';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * 清理和标准化设置值
   */
  sanitizeSettings(settings) {
    const sanitized = {};

    Object.keys(this.validationRules).forEach(key => {
      const rule = this.validationRules[key];
      let value = settings[key];

      if (value === undefined || value === null) {
        return;
      }

      // 类型转换
      switch (rule.type) {
        case 'boolean':
          value = Boolean(value);
          break;
        case 'number':
          value = Number(value);
          if (isNaN(value)) {
            return;
          }
          // 应用步长约束
          if (rule.step !== undefined) {
            value = Math.round(value / rule.step) * rule.step;
          }
          // 应用范围约束
          if (rule.min !== undefined) {
            value = Math.max(value, rule.min);
          }
          if (rule.max !== undefined) {
            value = Math.min(value, rule.max);
          }
          break;
        case 'string':
          value = String(value).trim();
          break;
      }

      sanitized[key] = value;
    });

    return sanitized;
  }

  /**
   * 获取设置项的约束信息
   */
  getConstraints(key) {
    const rule = this.validationRules[key];
    if (!rule) {
      return null;
    }

    return {
      type: rule.type,
      required: rule.required || false,
      min: rule.min,
      max: rule.max,
      step: rule.step,
      enum: rule.enum,
      hasCustomValidation: typeof rule.custom === 'function'
    };
  }

  /**
   * 检查设置项之间的依赖关系
   */
  validateDependencies(settings) {
    const errors = [];

    // 悬停显示依赖检查
    if (settings.showOnHover && !settings.enabled) {
      errors.push({
        key: 'showOnHover',
        message: '启用悬停显示需要先启用侧边栏'
      });
    }

    // 固定显示和悬停显示冲突检查
    if (settings.pinned && settings.showOnHover) {
      errors.push({
        key: 'pinned',
        message: '固定显示和悬停显示不能同时启用'
      });
    }

    // 搜索历史依赖检查
    if (settings.showSearchHistory && !settings.enableSearch) {
      errors.push({
        key: 'showSearchHistory',
        message: '显示搜索历史需要先启用搜索功能'
      });
    }

    // 最近标签页数量依赖检查
    if (settings.maxRecentTabs > 0 && !settings.showRecentTabs) {
      errors.push({
        key: 'maxRecentTabs',
        message: '设置最近标签页数量需要先启用显示最近标签页'
      });
    }

    // 图标显示依赖检查
    if (settings.showFavicons && !settings.showIcons) {
      errors.push({
        key: 'showFavicons',
        message: '显示网站图标需要先启用显示图标'
      });
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 实时验证设置项
   */
  validateRealtime(key, value, allSettings = {}) {
    // 基本验证
    const basicResult = this.validateSetting(key, value);
    if (!basicResult.valid) {
      return basicResult;
    }

    // 依赖关系验证
    const tempSettings = { ...allSettings, [key]: value };
    const dependencyResult = this.validateDependencies(tempSettings);
    
    // 过滤出与当前设置项相关的依赖错误
    const relevantErrors = dependencyResult.errors.filter(error => error.key === key);
    
    if (relevantErrors.length > 0) {
      return {
        valid: false,
        errors: relevantErrors.map(error => error.message)
      };
    }

    return { valid: true };
  }

  /**
   * 获取设置项的建议值
   */
  getSuggestedValue(key, currentValue, allSettings = {}) {
    const rule = this.validationRules[key];
    if (!rule) {
      return currentValue;
    }

    let suggestedValue = currentValue;

    // 基于类型的建议
    switch (rule.type) {
      case 'number':
        if (rule.min !== undefined && currentValue < rule.min) {
          suggestedValue = rule.min;
        } else if (rule.max !== undefined && currentValue > rule.max) {
          suggestedValue = rule.max;
        }
        
        // 应用步长约束
        if (rule.step !== undefined) {
          suggestedValue = Math.round(suggestedValue / rule.step) * rule.step;
        }
        break;
        
      case 'string':
        if (rule.enum && !rule.enum.includes(currentValue)) {
          suggestedValue = rule.enum[0];
        }
        break;
    }

    // 基于依赖关系的建议
    const tempSettings = { ...allSettings, [key]: suggestedValue };
    const dependencyResult = this.validateDependencies(tempSettings);
    
    if (!dependencyResult.valid) {
      // 根据依赖关系调整建议值
      const relevantError = dependencyResult.errors.find(error => error.key === key);
      if (relevantError) {
        // 根据具体的依赖错误提供建议
        switch (key) {
          case 'showOnHover':
            if (!allSettings.enabled) {
              return false; // 建议关闭悬停显示
            }
            break;
          case 'pinned':
            if (allSettings.showOnHover) {
              return false; // 建议关闭固定显示
            }
            break;
          case 'showSearchHistory':
            if (!allSettings.enableSearch) {
              return false; // 建议关闭搜索历史
            }
            break;
        }
      }
    }

    return suggestedValue;
  }

  /**
   * 生成验证报告
   */
  generateValidationReport(settings) {
    const basicValidation = this.validateAllSettings(settings);
    const dependencyValidation = this.validateDependencies(settings);
    
    const report = {
      overall: basicValidation.valid && dependencyValidation.valid,
      basicValidation: basicValidation,
      dependencyValidation: dependencyValidation,
      summary: {
        totalSettings: Object.keys(this.validationRules).length,
        validSettings: 0,
        invalidSettings: 0,
        dependencyErrors: dependencyValidation.errors.length
      }
    };

    // 统计有效和无效设置数量
    Object.values(basicValidation.results).forEach(result => {
      if (result.valid) {
        report.summary.validSettings++;
      } else {
        report.summary.invalidSettings++;
      }
    });

    return report;
  }
}

// 导出验证器实例
window.SettingsValidator = SettingsValidator;