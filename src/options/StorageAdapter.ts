/**
 * Storage Adapter - 存储适配器
 */

export interface StorageBackend {
  get(keys?: string | string[] | null): Promise<Record<string, any>>;
  set(items: Record<string, any>): Promise<void>;
  remove(keys: string | string[]): Promise<void>;
  clear(): Promise<void>;
  getBytesInUse?(keys?: string | string[]): Promise<number>;
}

export interface StorageOptions {
  backend: 'sync' | 'local' | 'session' | 'memory';
  prefix?: string;
  encryption?: boolean;
  compression?: boolean;
  maxSize?: number;
  ttl?: number; // Time to live in milliseconds
}

export interface StorageItem {
  value: any;
  timestamp: number;
  ttl?: number;
  compressed?: boolean;
  encrypted?: boolean;
}

export class StorageAdapter {
  private backend: StorageBackend;
  private options: StorageOptions;
  private memoryStorage: Map<string, StorageItem> = new Map();

  constructor(options: StorageOptions) {
    this.options = {
      prefix: 'sidebar_',
      encryption: false,
      compression: false,
      maxSize: 8192, // Chrome sync storage limit
      ...options
    };

    this.backend = this.createBackend();
  }

  /**
   * 创建存储后端
   */
  private createBackend(): StorageBackend {
    switch (this.options.backend) {
      case 'sync':
        return new ChromeSyncStorageBackend();
      case 'local':
        return new ChromeLocalStorageBackend();
      case 'session':
        return new ChromeSessionStorageBackend();
      case 'memory':
        return new MemoryStorageBackend(this.memoryStorage);
      default:
        throw new Error(`Unsupported storage backend: ${this.options.backend}`);
    }
  }

  /**
   * 获取数据
   */
  async get(key: string): Promise<any>;
  async get(keys: string[]): Promise<Record<string, any>>;
  async get(keys?: string | string[]): Promise<any> {
    try {
      const prefixedKeys = this.addPrefix(keys);
      const result = await this.backend.get(prefixedKeys);
      
      if (typeof keys === 'string') {
        const prefixedKey = this.addPrefix(keys);
        const item = result[prefixedKey];
        return this.deserializeItem(item);
      } else {
        const deserializedResult: Record<string, any> = {};
        Object.entries(result).forEach(([prefixedKey, item]) => {
          const originalKey = this.removePrefix(prefixedKey);
          deserializedResult[originalKey] = this.deserializeItem(item);
        });
        return deserializedResult;
      }
    } catch (error) {
      console.error('Error getting data from storage:', error);
      throw error;
    }
  }

  /**
   * 设置数据
   */
  async set(key: string, value: any): Promise<void>;
  async set(items: Record<string, any>): Promise<void>;
  async set(keyOrItems: string | Record<string, any>, value?: any): Promise<void> {
    try {
      let items: Record<string, any>;
      
      if (typeof keyOrItems === 'string') {
        items = { [keyOrItems]: value };
      } else {
        items = keyOrItems;
      }

      const serializedItems: Record<string, any> = {};
      
      for (const [key, val] of Object.entries(items)) {
        const prefixedKey = this.addPrefix(key);
        const serializedItem = this.serializeItem(val);
        
        // 检查大小限制
        if (this.options.maxSize) {
          const itemSize = this.calculateSize(serializedItem);
          if (itemSize > this.options.maxSize) {
            throw new Error(`Item ${key} exceeds maximum size limit`);
          }
        }
        
        serializedItems[prefixedKey] = serializedItem;
      }

      await this.backend.set(serializedItems);
    } catch (error) {
      console.error('Error setting data to storage:', error);
      throw error;
    }
  }

  /**
   * 删除数据
   */
  async remove(key: string): Promise<void>;
  async remove(keys: string[]): Promise<void>;
  async remove(keys: string | string[]): Promise<void> {
    try {
      const prefixedKeys = this.addPrefix(keys);
      await this.backend.remove(prefixedKeys);
    } catch (error) {
      console.error('Error removing data from storage:', error);
      throw error;
    }
  }

  /**
   * 清空存储
   */
  async clear(): Promise<void> {
    try {
      if (this.options.backend === 'memory') {
        this.memoryStorage.clear();
      } else {
        // 只清除带前缀的项目
        const allItems = await this.backend.get(null);
        const keysToRemove = Object.keys(allItems).filter(key => 
          key.startsWith(this.options.prefix || '')
        );
        
        if (keysToRemove.length > 0) {
          await this.backend.remove(keysToRemove);
        }
      }
    } catch (error) {
      console.error('Error clearing storage:', error);
      throw error;
    }
  }

  /**
   * 获取存储使用量
   */
  async getBytesInUse(keys?: string | string[]): Promise<number> {
    try {
      if (this.backend.getBytesInUse) {
        const prefixedKeys = keys ? this.addPrefix(keys) : undefined;
        return await this.backend.getBytesInUse(prefixedKeys);
      } else {
        // 估算大小
        const items = await this.get(keys || []);
        return this.calculateSize(items);
      }
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return 0;
    }
  }

  /**
   * 序列化项目
   */
  private serializeItem(value: any): StorageItem {
    const item: StorageItem = {
      value,
      timestamp: Date.now()
    };

    // 添加TTL
    if (this.options.ttl) {
      item.ttl = this.options.ttl;
    }

    // 压缩（简单的JSON字符串化，实际项目中可以使用更好的压缩算法）
    if (this.options.compression) {
      try {
        const jsonString = JSON.stringify(value);
        if (jsonString.length > 1000) { // 只压缩大于1KB的数据
          item.value = this.compress(jsonString);
          item.compressed = true;
        }
      } catch (error) {
        console.warn('Compression failed, storing uncompressed:', error);
      }
    }

    // 加密（简单的Base64编码，实际项目中应使用真正的加密）
    if (this.options.encryption) {
      try {
        item.value = this.encrypt(JSON.stringify(item.value));
        item.encrypted = true;
      } catch (error) {
        console.warn('Encryption failed, storing unencrypted:', error);
      }
    }

    return item;
  }

  /**
   * 反序列化项目
   */
  private deserializeItem(item: any): any {
    if (!item || typeof item !== 'object') {
      return item;
    }

    // 检查是否为StorageItem格式
    if (!item.hasOwnProperty('timestamp')) {
      return item;
    }

    const storageItem = item as StorageItem;

    // 检查TTL
    if (storageItem.ttl && storageItem.timestamp) {
      const now = Date.now();
      const expireTime = storageItem.timestamp + storageItem.ttl;
      if (now > expireTime) {
        return undefined; // 已过期
      }
    }

    let value = storageItem.value;

    // 解密
    if (storageItem.encrypted) {
      try {
        value = JSON.parse(this.decrypt(value));
      } catch (error) {
        console.error('Decryption failed:', error);
        return undefined;
      }
    }

    // 解压缩
    if (storageItem.compressed) {
      try {
        value = JSON.parse(this.decompress(value));
      } catch (error) {
        console.error('Decompression failed:', error);
        return undefined;
      }
    }

    return value;
  }

  /**
   * 添加前缀
   */
  private addPrefix(keys: string | string[] | null): string | string[] | null {
    if (!this.options.prefix) return keys;
    
    if (keys === null) return null;
    
    if (typeof keys === 'string') {
      return this.options.prefix + keys;
    }
    
    return keys.map(key => this.options.prefix + key);
  }

  /**
   * 移除前缀
   */
  private removePrefix(key: string): string {
    if (!this.options.prefix) return key;
    
    if (key.startsWith(this.options.prefix)) {
      return key.substring(this.options.prefix.length);
    }
    
    return key;
  }

  /**
   * 计算大小
   */
  private calculateSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  /**
   * 简单压缩（实际项目中应使用专业的压缩库）
   */
  private compress(data: string): string {
    // 这里使用简单的重复字符压缩作为示例
    // 实际项目中应使用 LZ-string 或其他压缩算法
    return btoa(data);
  }

  /**
   * 简单解压缩
   */
  private decompress(data: string): string {
    return atob(data);
  }

  /**
   * 简单加密（实际项目中应使用真正的加密算法）
   */
  private encrypt(data: string): string {
    // 这里使用简单的Base64编码作为示例
    // 实际项目中应使用 AES 或其他加密算法
    return btoa(data);
  }

  /**
   * 简单解密
   */
  private decrypt(data: string): string {
    return atob(data);
  }
}

/**
 * Chrome Sync Storage Backend
 */
class ChromeSyncStorageBackend implements StorageBackend {
  async get(keys?: string | string[] | null): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  async set(items: Record<string, any>): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(items, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async remove(keys: string | string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async clear(): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async getBytesInUse(keys?: string | string[]): Promise<number> {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.getBytesInUse(keys, (bytes) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(bytes);
        }
      });
    });
  }
}

/**
 * Chrome Local Storage Backend
 */
class ChromeLocalStorageBackend implements StorageBackend {
  async get(keys?: string | string[] | null): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  async set(items: Record<string, any>): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set(items, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async remove(keys: string | string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.remove(keys, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async clear(): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  async getBytesInUse(keys?: string | string[]): Promise<number> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.getBytesInUse(keys, (bytes) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(bytes);
        }
      });
    });
  }
}

/**
 * Chrome Session Storage Backend
 */
class ChromeSessionStorageBackend implements StorageBackend {
  async get(keys?: string | string[] | null): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      if (chrome.storage.session) {
        chrome.storage.session.get(keys, (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(result);
          }
        });
      } else {
        // Fallback to memory storage if session storage is not available
        resolve({});
      }
    });
  }

  async set(items: Record<string, any>): Promise<void> {
    return new Promise((resolve, reject) => {
      if (chrome.storage.session) {
        chrome.storage.session.set(items, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  async remove(keys: string | string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      if (chrome.storage.session) {
        chrome.storage.session.remove(keys, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  async clear(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (chrome.storage.session) {
        chrome.storage.session.clear(() => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }
}

/**
 * Memory Storage Backend
 */
class MemoryStorageBackend implements StorageBackend {
  constructor(private storage: Map<string, StorageItem>) {}

  async get(keys?: string | string[] | null): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    
    if (keys === null) {
      // 返回所有项目
      this.storage.forEach((value, key) => {
        result[key] = value;
      });
    } else if (typeof keys === 'string') {
      if (this.storage.has(keys)) {
        result[keys] = this.storage.get(keys);
      }
    } else {
      keys.forEach(key => {
        if (this.storage.has(key)) {
          result[key] = this.storage.get(key);
        }
      });
    }
    
    return result;
  }

  async set(items: Record<string, any>): Promise<void> {
    Object.entries(items).forEach(([key, value]) => {
      this.storage.set(key, value);
    });
  }

  async remove(keys: string | string[]): Promise<void> {
    const keysArray = typeof keys === 'string' ? [keys] : keys;
    keysArray.forEach(key => {
      this.storage.delete(key);
    });
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}