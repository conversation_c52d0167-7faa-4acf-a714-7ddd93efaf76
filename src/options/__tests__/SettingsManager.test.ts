/**
 * Settings Manager Tests
 */

import { SettingsManager, SettingDefinition, SettingsChangeEvent } from '../SettingsManager';

// Mock Chrome API
const mockChrome = {
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn()
    }
  },
  runtime: {
    lastError: null
  }
};

(global as any).chrome = mockChrome;

describe('SettingsManager', () => {
  let settingsManager: SettingsManager;

  beforeEach(() => {
    settingsManager = new SettingsManager();
    jest.clearAllMocks();
    mockChrome.runtime.lastError = null;
  });

  describe('Initialization', () => {
    test('should initialize with default values', () => {
      expect(settingsManager.get('enabled')).toBe(true);
      expect(settingsManager.get('autoShow')).toBe(true);
      expect(settingsManager.get('width')).toBe(300);
    });

    test('should have all required categories', () => {
      const categories = settingsManager.getCategories();
      expect(categories).toHaveProperty('general');
      expect(categories).toHaveProperty('appearance');
      expect(categories).toHaveProperty('behavior');
      expect(categories).toHaveProperty('shortcuts');
      expect(categories).toHaveProperty('advanced');
    });
  });

  describe('Settings Management', () => {
    test('should get setting value', () => {
      const value = settingsManager.get('enabled');
      expect(value).toBe(true);
    });

    test('should set setting value', () => {
      const result = settingsManager.set('enabled', false);
      expect(result).toBe(true);
      expect(settingsManager.get('enabled')).toBe(false);
    });

    test('should reject invalid setting key', () => {
      const result = settingsManager.set('invalidKey', 'value');
      expect(result).toBe(false);
    });

    test('should get all settings', () => {
      const allSettings = settingsManager.getAll();
      expect(allSettings).toHaveProperty('enabled');
      expect(allSettings).toHaveProperty('width');
    });

    test('should set multiple settings', () => {
      const newSettings = {
        enabled: false,
        width: 400
      };
      
      const result = settingsManager.setAll(newSettings);
      expect(result).toBe(true);
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
    });

    test('should reset settings to defaults', () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 500);
      
      settingsManager.reset(['enabled', 'width']);
      
      expect(settingsManager.get('enabled')).toBe(true);
      expect(settingsManager.get('width')).toBe(300);
    });

    test('should reset all settings', () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 500);
      
      settingsManager.reset();
      
      expect(settingsManager.get('enabled')).toBe(true);
      expect(settingsManager.get('width')).toBe(300);
    });
  });

  describe('Validation', () => {
    test('should validate boolean settings', () => {
      expect(settingsManager.validateSetting('enabled', true)).toBe(true);
      expect(settingsManager.validateSetting('enabled', false)).toBe(true);
      expect(settingsManager.validateSetting('enabled', 'invalid')).toBe('Value must be a boolean');
    });

    test('should validate number settings with range', () => {
      expect(settingsManager.validateSetting('width', 300)).toBe(true);
      expect(settingsManager.validateSetting('width', 200)).toBe(true);
      expect(settingsManager.validateSetting('width', 500)).toBe(true);
      expect(settingsManager.validateSetting('width', 100)).toBe('Value must be at least 200');
      expect(settingsManager.validateSetting('width', 600)).toBe('Value must be at most 500');
      expect(settingsManager.validateSetting('width', 'invalid')).toBe('Value must be a number');
    });


    test('should validate color settings', () => {
      expect(settingsManager.validateSetting('accentColor', '#0969da')).toBe(true);
      expect(settingsManager.validateSetting('accentColor', '#ffffff')).toBe(true);
      expect(settingsManager.validateSetting('accentColor', 'invalid')).toBe('Value must be a valid hex color');
      expect(settingsManager.validateSetting('accentColor', '#gggggg')).toBe('Value must be a valid hex color');
    });

    test('should validate custom validation functions', () => {
      expect(settingsManager.validateSetting('hoverDelay', 300)).toBe(true);
      expect(settingsManager.validateSetting('hoverDelay', 0)).toBe(true);
      expect(settingsManager.validateSetting('hoverDelay', 1000)).toBe(true);
      expect(settingsManager.validateSetting('hoverDelay', -100)).toBe('延迟时间必须在0-1000毫秒之间');
      expect(settingsManager.validateSetting('hoverDelay', 1500)).toBe('延迟时间必须在0-1000毫秒之间');
    });

    test('should validate all settings', () => {
      expect(settingsManager.validateAll()).toBe(true);
      
      settingsManager.set('width', 100); // Invalid value
      expect(settingsManager.validateAll()).toBe(false);
      
      const errors = settingsManager.getValidationErrors();
      expect(errors).toHaveProperty('width');
    });
  });

  describe('Dependencies', () => {
    test('should check setting dependencies', () => {
      settingsManager.set('enabled', true);
      expect(settingsManager.isSettingEnabled('autoShow')).toBe(true);
      
      settingsManager.set('enabled', false);
      expect(settingsManager.isSettingEnabled('autoShow')).toBe(false);
    });

    test('should handle multiple dependencies', () => {
      settingsManager.set('enabled', true);
      settingsManager.set('showOnHover', true);
      expect(settingsManager.isSettingEnabled('hoverDelay')).toBe(true);
      
      settingsManager.set('showOnHover', false);
      expect(settingsManager.isSettingEnabled('hoverDelay')).toBe(false);
      
      settingsManager.set('enabled', false);
      expect(settingsManager.isSettingEnabled('hoverDelay')).toBe(false);
    });
  });

  describe('Categories', () => {
    test('should get settings by category', () => {
      const generalSettings = settingsManager.getSettingsByCategory('general');
      expect(generalSettings.length).toBeGreaterThan(0);
      expect(generalSettings.every(s => s.category === 'general')).toBe(true);
    });

    test('should sort settings by title', () => {
      const settings = settingsManager.getSettingsByCategory('general');
      const titles = settings.map(s => s.title);
      const sortedTitles = [...titles].sort();
      expect(titles).toEqual(sortedTitles);
    });
  });

  describe('Change Events', () => {
    test('should trigger change events', (done) => {
      const changeHandler = (event: SettingsChangeEvent) => {
        expect(event.key).toBe('enabled');
        expect(event.oldValue).toBe(true);
        expect(event.newValue).toBe(false);
        expect(event.timestamp).toBeGreaterThan(0);
        done();
      };

      settingsManager.onChange(changeHandler);
      settingsManager.set('enabled', false);
    });

    test('should remove change listeners', () => {
      const changeHandler = jest.fn();
      
      settingsManager.onChange(changeHandler);
      settingsManager.set('enabled', false);
      expect(changeHandler).toHaveBeenCalledTimes(1);
      
      settingsManager.offChange(changeHandler);
      settingsManager.set('enabled', true);
      expect(changeHandler).toHaveBeenCalledTimes(1);
    });

    test('should execute onChange callback', () => {
      const onChangeMock = jest.fn();
      const setting = settingsManager.getSettingDefinition('enabled');
      if (setting) {
        setting.onChange = onChangeMock;
      }
      
      settingsManager.set('enabled', false);
      expect(onChangeMock).toHaveBeenCalledWith(false, settingsManager.getAll());
    });
  });

  describe('Storage Integration', () => {
    test('should load settings from storage', async () => {
      const mockSettings = {
        enabled: false,
        width: 400
      };
      
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback(mockSettings);
      });
      
      await settingsManager.load();
      
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
    });

    test('should save settings to storage', async () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);
      
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });
      
      await settingsManager.save();
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
          width: 400
        }),
        expect.any(Function)
      );
    });

    test('should handle storage errors', async () => {
      mockChrome.runtime.lastError = { message: 'Storage error' };
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });
      
      await expect(settingsManager.load()).rejects.toThrow('Failed to load settings');
    });
  });

  describe('Import/Export', () => {
    test('should export settings', () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);
      
      const exported = settingsManager.export();
      const data = JSON.parse(exported);
      
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('settings');
      expect(data.settings.enabled).toBe(false);
      expect(data.settings.width).toBe(400);
    });

    test('should import valid settings', () => {
      const importData = {
        version: '1.0.0',
        timestamp: Date.now(),
        settings: {
          enabled: false,
          width: 400
        }
      };
      
      const result = settingsManager.import(JSON.stringify(importData));
      expect(result).toBe(true);
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
    });

    test('should reject invalid import data', () => {
      const result = settingsManager.import('invalid json');
      expect(result).toBe(false);
    });

    test('should skip invalid settings during import', () => {
      const importData = {
        settings: {
          enabled: false,
          width: 1000, // Invalid value
          invalidKey: 'value'
        }
      };
      
      const result = settingsManager.import(JSON.stringify(importData));
      expect(result).toBe(true);
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(300); // Should keep default
    });
  });

  describe('Statistics', () => {
    test('should provide statistics', () => {
      const stats = settingsManager.getStats();
      
      expect(stats).toHaveProperty('totalSettings');
      expect(stats).toHaveProperty('categoriesCount');
      expect(stats).toHaveProperty('validationErrors');
      expect(stats).toHaveProperty('lastModified');
      
      expect(stats.totalSettings).toBeGreaterThan(0);
      expect(stats.categoriesCount).toBe(5);
      expect(stats.validationErrors).toBe(0);
    });
  });

  describe('Loading States', () => {
    test('should track loading state', async () => {
      expect(settingsManager.isLoadingSettings()).toBe(false);
      
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        setTimeout(() => callback({}), 100);
      });
      
      const loadPromise = settingsManager.load();
      expect(settingsManager.isLoadingSettings()).toBe(true);
      
      await loadPromise;
      expect(settingsManager.isLoadingSettings()).toBe(false);
    });

    test('should track saving state', async () => {
      expect(settingsManager.isSavingSettings()).toBe(false);
      
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        setTimeout(() => callback(), 100);
      });
      
      const savePromise = settingsManager.save();
      expect(settingsManager.isSavingSettings()).toBe(true);
      
      await savePromise;
      expect(settingsManager.isSavingSettings()).toBe(false);
    });
  });
});