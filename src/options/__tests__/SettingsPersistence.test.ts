/**
 * Settings Persistence Tests
 */

import { SettingsPersistence, PersistenceOptions } from '../SettingsPersistence';
import { SettingsManager } from '../SettingsManager';

// Mock Chrome API
const mockChrome = {
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn()
    },
    onChanged: {
      addListener: jest.fn()
    }
  },
  runtime: {
    lastError: null
  }
};

(global as any).chrome = mockChrome;

// Mock window
const mockWindow = {
  addEventListener: jest.fn()
};
(global as any).window = mockWindow;

describe('SettingsPersistence', () => {
  let settingsManager: SettingsManager;
  let persistence: SettingsPersistence;

  beforeEach(() => {
    settingsManager = new SettingsManager();
    jest.clearAllMocks();
    mockChrome.runtime.lastError = null;
  });

  describe('Initialization', () => {
    test('should create persistence with default options', () => {
      persistence = new SettingsPersistence(settingsManager);
      expect(persistence).toBeInstanceOf(SettingsPersistence);
    });

    test('should create persistence with custom options', () => {
      const options: PersistenceOptions = {
        autoSave: false,
        autoSaveDelay: 2000,
        syncAcrossDevices: false,
        backupEnabled: false
      };
      
      persistence = new SettingsPersistence(settingsManager, options);
      expect(persistence).toBeInstanceOf(SettingsPersistence);
    });

    test('should initialize successfully', async () => {
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_settings') {
          callback({ sidebar_settings_settings: { enabled: false, width: 400 } });
        } else {
          callback({});
        }
      });

      persistence = new SettingsPersistence(settingsManager);
      await expect(persistence.initialize()).resolves.not.toThrow();
    });

    test('should handle initialization errors', async () => {
      mockChrome.runtime.lastError = { message: 'Storage error' };
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });

      persistence = new SettingsPersistence(settingsManager);
      await expect(persistence.initialize()).rejects.toThrow();
    });
  });

  describe('Settings Loading', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager);
    });

    test('should load existing settings', async () => {
      const mockSettings = {
        enabled: false,
        width: 400,
        theme: 'dark'
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_settings') {
          callback({ sidebar_settings_settings: { value: mockSettings, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      await persistence.loadSettings();
      
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
      expect(settingsManager.get('theme')).toBe('dark');
    });

    test('should use defaults for first run', async () => {
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.loadSettings();
      
      // Should have default values
      expect(settingsManager.get('enabled')).toBe(true);
      expect(settingsManager.get('width')).toBe(300);
      expect(settingsManager.get('theme')).toBe('auto');
    });

    test('should validate loaded settings', async () => {
      const mockSettings = {
        enabled: false,
        width: 1000, // Invalid value
        theme: 'dark',
        invalidKey: 'value'
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_settings') {
          callback({ sidebar_settings_settings: { value: mockSettings, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      await persistence.loadSettings();
      
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(300); // Should use default for invalid value
      expect(settingsManager.get('theme')).toBe('dark');
    });
  });

  describe('Settings Saving', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager);
    });

    test('should save settings', async () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.saveSettings();
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          sidebar_settings_settings: expect.objectContaining({
            value: expect.objectContaining({
              enabled: false,
              width: 400
            })
          })
        }),
        expect.any(Function)
      );
    });

    test('should save immediately', async () => {
      settingsManager.set('enabled', false);

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.saveImmediately();
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalled();
    });

    test('should handle save errors', async () => {
      mockChrome.runtime.lastError = { message: 'Save error' };
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await expect(persistence.saveSettings()).rejects.toThrow();
    });
  });

  describe('Auto Save', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      persistence = new SettingsPersistence(settingsManager, {
        autoSave: true,
        autoSaveDelay: 1000
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should auto save after delay', async () => {
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      settingsManager.set('enabled', false);
      
      // Fast-forward time
      jest.advanceTimersByTime(1000);
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalled();
    });

    test('should debounce multiple changes', async () => {
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);
      settingsManager.set('theme', 'dark');
      
      // Fast-forward time
      jest.advanceTimersByTime(1000);
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));
      
      // Should only save once
      expect(mockChrome.storage.sync.set).toHaveBeenCalledTimes(1);
    });
  });

  describe('Backup System', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager, {
        backupEnabled: true,
        maxBackups: 3
      });
    });

    test('should create backup', async () => {
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_metadata') {
          callback({ sidebar_settings_metadata: { value: { version: '1.0.0' }, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      const backupId = await persistence.createBackup('Test backup');
      
      expect(backupId).toMatch(/^backup_\d+_[a-z0-9]+$/);
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          [`sidebar_settings_backup_${backupId}`]: expect.objectContaining({
            value: expect.objectContaining({
              id: backupId,
              description: 'Test backup',
              settings: expect.any(Object)
            })
          })
        }),
        expect.any(Function)
      );
    });

    test('should get backups list', async () => {
      const mockBackups = {
        sidebar_settings_backup_backup1: {
          value: {
            id: 'backup1',
            timestamp: Date.now() - 1000,
            version: '1.0.0',
            description: 'Backup 1',
            settings: {}
          }
        },
        sidebar_settings_backup_backup2: {
          value: {
            id: 'backup2',
            timestamp: Date.now(),
            version: '1.0.0',
            description: 'Backup 2',
            settings: {}
          }
        }
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback(mockBackups);
      });

      const backups = await persistence.getBackups();
      
      expect(backups).toHaveLength(2);
      expect(backups[0].id).toBe('backup2'); // Should be sorted by timestamp desc
      expect(backups[1].id).toBe('backup1');
    });

    test('should restore backup', async () => {
      const mockBackup = {
        id: 'backup1',
        timestamp: Date.now(),
        version: '1.0.0',
        settings: {
          enabled: false,
          width: 400,
          theme: 'dark'
        }
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_backup_backup1') {
          callback({ sidebar_settings_backup_backup1: { value: mockBackup, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.restoreBackup('backup1');
      
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
      expect(settingsManager.get('theme')).toBe('dark');
    });

    test('should delete backup', async () => {
      mockChrome.storage.sync.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await persistence.deleteBackup('backup1');
      
      expect(mockChrome.storage.sync.remove).toHaveBeenCalledWith(
        'sidebar_settings_backup_backup1',
        expect.any(Function)
      );
    });

    test('should handle backup errors', async () => {
      mockChrome.runtime.lastError = { message: 'Backup error' };
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await expect(persistence.createBackup()).rejects.toThrow();
    });
  });

  describe('Import/Export', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager);
    });

    test('should export settings', async () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_metadata') {
          callback({ sidebar_settings_metadata: { value: { version: '1.0.0' }, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      const exported = await persistence.exportSettings();
      const data = JSON.parse(exported);
      
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('exportTimestamp');
      expect(data).toHaveProperty('settings');
      expect(data.settings.enabled).toBe(false);
      expect(data.settings.width).toBe(400);
    });

    test('should import settings', async () => {
      const importData = {
        version: '1.0.0',
        exportTimestamp: Date.now(),
        settings: {
          enabled: false,
          width: 400,
          theme: 'dark'
        }
      };

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.importSettings(JSON.stringify(importData));
      
      expect(settingsManager.get('enabled')).toBe(false);
      expect(settingsManager.get('width')).toBe(400);
      expect(settingsManager.get('theme')).toBe('dark');
    });

    test('should reject invalid import data', async () => {
      await expect(persistence.importSettings('invalid json')).rejects.toThrow();
    });

    test('should reject import data without settings', async () => {
      const invalidData = {
        version: '1.0.0',
        exportTimestamp: Date.now()
        // Missing settings
      };

      await expect(persistence.importSettings(JSON.stringify(invalidData))).rejects.toThrow('Invalid import data format');
    });
  });

  describe('Synchronization', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager, {
        syncAcrossDevices: true
      });
    });

    test('should perform sync', async () => {
      const localMetadata = { timestamp: Date.now() - 1000, version: '1.0.0' };
      const remoteMetadata = { timestamp: Date.now(), version: '1.0.0' };
      const remoteSettings = { enabled: false, width: 400 };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        if (keys === 'sidebar_settings_metadata') {
          callback({ sidebar_settings_metadata: { value: remoteMetadata, timestamp: Date.now() } });
        } else if (keys === 'sidebar_settings_settings') {
          callback({ sidebar_settings_settings: { value: remoteSettings, timestamp: Date.now() } });
        } else {
          callback({});
        }
      });

      await persistence.performSync();
      
      const syncStatus = persistence.getSyncStatus();
      expect(syncStatus.lastSync).toBeGreaterThan(0);
      expect(syncStatus.syncInProgress).toBe(false);
      expect(syncStatus.syncError).toBeUndefined();
    });

    test('should handle sync errors', async () => {
      mockChrome.runtime.lastError = { message: 'Sync error' };
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });

      await persistence.performSync();
      
      const syncStatus = persistence.getSyncStatus();
      expect(syncStatus.syncError).toBe('Sync error');
    });

    test('should set conflict resolution strategy', () => {
      persistence.setConflictResolution('local');
      
      const syncStatus = persistence.getSyncStatus();
      expect(syncStatus.conflictResolution).toBe('local');
    });
  });

  describe('Storage Statistics', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager);
    });

    test('should get storage stats', async () => {
      mockChrome.storage.sync.getBytesInUse.mockImplementation((keys, callback) => {
        if (!keys) {
          callback(2048); // Total size
        } else if (keys === 'sidebar_settings_settings') {
          callback(512); // Settings size
        } else {
          callback(0);
        }
      });

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });

      const stats = await persistence.getStorageStats();
      
      expect(stats).toHaveProperty('totalSize');
      expect(stats).toHaveProperty('settingsSize');
      expect(stats).toHaveProperty('backupsSize');
      expect(stats).toHaveProperty('backupsCount');
      expect(stats).toHaveProperty('lastSync');
      expect(stats).toHaveProperty('syncStatus');
    });
  });

  describe('Reset Settings', () => {
    beforeEach(() => {
      persistence = new SettingsPersistence(settingsManager);
    });

    test('should reset settings', async () => {
      settingsManager.set('enabled', false);
      settingsManager.set('width', 400);

      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await persistence.resetSettings();
      
      // Should be back to defaults
      expect(settingsManager.get('enabled')).toBe(true);
      expect(settingsManager.get('width')).toBe(300);
    });
  });

  describe('Cleanup', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      persistence = new SettingsPersistence(settingsManager, {
        autoSave: true,
        autoSaveDelay: 1000
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should dispose resources', () => {
      settingsManager.set('enabled', false);
      
      persistence.dispose();
      
      // Fast-forward time - should not trigger save after dispose
      jest.advanceTimersByTime(1000);
      
      expect(mockChrome.storage.sync.set).not.toHaveBeenCalled();
    });
  });
});