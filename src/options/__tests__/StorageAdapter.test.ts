/**
 * Storage Adapter Tests
 */

import { StorageAdapter, StorageOptions } from '../StorageAdapter';

// Mock Chrome API
const mockChrome = {
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn()
    },
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn()
    },
    session: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    }
  },
  runtime: {
    lastError: null
  }
};

(global as any).chrome = mockChrome;

describe('StorageAdapter', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockChrome.runtime.lastError = null;
  });

  describe('Initialization', () => {
    test('should create sync storage adapter', () => {
      const options: StorageOptions = { backend: 'sync' };
      const adapter = new StorageAdapter(options);
      expect(adapter).toBeInstanceOf(StorageAdapter);
    });

    test('should create local storage adapter', () => {
      const options: StorageOptions = { backend: 'local' };
      const adapter = new StorageAdapter(options);
      expect(adapter).toBeInstanceOf(StorageAdapter);
    });

    test('should create memory storage adapter', () => {
      const options: StorageOptions = { backend: 'memory' };
      const adapter = new StorageAdapter(options);
      expect(adapter).toBeInstanceOf(StorageAdapter);
    });

    test('should throw error for unsupported backend', () => {
      const options: StorageOptions = { backend: 'invalid' as any };
      expect(() => new StorageAdapter(options)).toThrow('Unsupported storage backend: invalid');
    });

    test('should use default options', () => {
      const options: StorageOptions = { backend: 'sync' };
      const adapter = new StorageAdapter(options);
      expect(adapter).toBeInstanceOf(StorageAdapter);
    });
  });

  describe('Sync Storage Backend', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'sync',
        prefix: 'test_'
      };
      adapter = new StorageAdapter(options);
    });

    test('should get single value', async () => {
      const mockData = {
        test_key1: {
          value: 'value1',
          timestamp: Date.now()
        }
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      const result = await adapter.get('key1');
      expect(result).toBe('value1');
      expect(mockChrome.storage.sync.get).toHaveBeenCalledWith('test_key1');
    });

    test('should get multiple values', async () => {
      const mockData = {
        test_key1: {
          value: 'value1',
          timestamp: Date.now()
        },
        test_key2: {
          value: 'value2',
          timestamp: Date.now()
        }
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      const result = await adapter.get(['key1', 'key2']);
      expect(result).toEqual({
        key1: 'value1',
        key2: 'value2'
      });
      expect(mockChrome.storage.sync.get).toHaveBeenCalledWith(['test_key1', 'test_key2']);
    });

    test('should set single value', async () => {
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await adapter.set('key1', 'value1');
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          test_key1: expect.objectContaining({
            value: 'value1',
            timestamp: expect.any(Number)
          })
        }),
        expect.any(Function)
      );
    });

    test('should set multiple values', async () => {
      mockChrome.storage.sync.set.mockImplementation((items, callback) => {
        callback();
      });

      await adapter.set({
        key1: 'value1',
        key2: 'value2'
      });
      
      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          test_key1: expect.objectContaining({
            value: 'value1'
          }),
          test_key2: expect.objectContaining({
            value: 'value2'
          })
        }),
        expect.any(Function)
      );
    });

    test('should remove single key', async () => {
      mockChrome.storage.sync.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await adapter.remove('key1');
      
      expect(mockChrome.storage.sync.remove).toHaveBeenCalledWith('test_key1', expect.any(Function));
    });

    test('should remove multiple keys', async () => {
      mockChrome.storage.sync.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await adapter.remove(['key1', 'key2']);
      
      expect(mockChrome.storage.sync.remove).toHaveBeenCalledWith(['test_key1', 'test_key2'], expect.any(Function));
    });

    test('should clear storage with prefix', async () => {
      const mockData = {
        test_key1: 'value1',
        test_key2: 'value2',
        other_key: 'value3'
      };

      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback(mockData);
      });

      mockChrome.storage.sync.remove.mockImplementation((keys, callback) => {
        callback();
      });

      await adapter.clear();
      
      expect(mockChrome.storage.sync.get).toHaveBeenCalledWith(null);
      expect(mockChrome.storage.sync.remove).toHaveBeenCalledWith(['test_key1', 'test_key2'], expect.any(Function));
    });

    test('should get bytes in use', async () => {
      mockChrome.storage.sync.getBytesInUse.mockImplementation((keys, callback) => {
        callback(1024);
      });

      const result = await adapter.getBytesInUse(['key1', 'key2']);
      
      expect(result).toBe(1024);
      expect(mockChrome.storage.sync.getBytesInUse).toHaveBeenCalledWith(['test_key1', 'test_key2'], expect.any(Function));
    });

    test('should handle storage errors', async () => {
      mockChrome.runtime.lastError = { message: 'Storage error' };
      mockChrome.storage.sync.get.mockImplementation((keys, callback) => {
        callback({});
      });

      await expect(adapter.get('key1')).rejects.toThrow('Storage error');
    });
  });

  describe('Memory Storage Backend', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_'
      };
      adapter = new StorageAdapter(options);
    });

    test('should store and retrieve values in memory', async () => {
      await adapter.set('key1', 'value1');
      const result = await adapter.get('key1');
      expect(result).toBe('value1');
    });

    test('should handle multiple values', async () => {
      await adapter.set({
        key1: 'value1',
        key2: 'value2'
      });

      const result = await adapter.get(['key1', 'key2']);
      expect(result).toEqual({
        key1: 'value1',
        key2: 'value2'
      });
    });

    test('should remove values', async () => {
      await adapter.set('key1', 'value1');
      await adapter.remove('key1');
      
      const result = await adapter.get('key1');
      expect(result).toBeUndefined();
    });

    test('should clear all values', async () => {
      await adapter.set({
        key1: 'value1',
        key2: 'value2'
      });

      await adapter.clear();
      
      const result = await adapter.get(['key1', 'key2']);
      expect(result).toEqual({
        key1: undefined,
        key2: undefined
      });
    });
  });

  describe('Data Serialization', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_'
      };
      adapter = new StorageAdapter(options);
    });

    test('should serialize and deserialize complex objects', async () => {
      const complexObject = {
        string: 'test',
        number: 42,
        boolean: true,
        array: [1, 2, 3],
        nested: {
          prop: 'value'
        }
      };

      await adapter.set('complex', complexObject);
      const result = await adapter.get('complex');
      
      expect(result).toEqual(complexObject);
    });

    test('should handle null and undefined values', async () => {
      await adapter.set('null', null);
      await adapter.set('undefined', undefined);
      
      const nullResult = await adapter.get('null');
      const undefinedResult = await adapter.get('undefined');
      
      expect(nullResult).toBeNull();
      expect(undefinedResult).toBeUndefined();
    });
  });

  describe('TTL (Time To Live)', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_',
        ttl: 1000 // 1 second
      };
      adapter = new StorageAdapter(options);
    });

    test('should return value before expiration', async () => {
      await adapter.set('key1', 'value1');
      const result = await adapter.get('key1');
      expect(result).toBe('value1');
    });

    test('should return undefined after expiration', async () => {
      await adapter.set('key1', 'value1');
      
      // Mock time passage
      jest.spyOn(Date, 'now').mockReturnValue(Date.now() + 2000);
      
      const result = await adapter.get('key1');
      expect(result).toBeUndefined();
      
      jest.restoreAllMocks();
    });
  });

  describe('Compression', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_',
        compression: true
      };
      adapter = new StorageAdapter(options);
    });

    test('should compress and decompress large data', async () => {
      const largeString = 'x'.repeat(2000); // Large enough to trigger compression
      
      await adapter.set('large', largeString);
      const result = await adapter.get('large');
      
      expect(result).toBe(largeString);
    });

    test('should not compress small data', async () => {
      const smallString = 'small data';
      
      await adapter.set('small', smallString);
      const result = await adapter.get('small');
      
      expect(result).toBe(smallString);
    });
  });

  describe('Encryption', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_',
        encryption: true
      };
      adapter = new StorageAdapter(options);
    });

    test('should encrypt and decrypt data', async () => {
      const sensitiveData = 'sensitive information';
      
      await adapter.set('sensitive', sensitiveData);
      const result = await adapter.get('sensitive');
      
      expect(result).toBe(sensitiveData);
    });
  });

  describe('Size Limits', () => {
    let adapter: StorageAdapter;

    beforeEach(() => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'test_',
        maxSize: 100 // Very small limit for testing
      };
      adapter = new StorageAdapter(options);
    });

    test('should reject items exceeding size limit', async () => {
      const largeData = 'x'.repeat(1000);
      
      await expect(adapter.set('large', largeData)).rejects.toThrow('exceeds maximum size limit');
    });

    test('should accept items within size limit', async () => {
      const smallData = 'small';
      
      await expect(adapter.set('small', smallData)).resolves.not.toThrow();
      const result = await adapter.get('small');
      expect(result).toBe(smallData);
    });
  });

  describe('Prefix Handling', () => {
    test('should work without prefix', async () => {
      const options: StorageOptions = { 
        backend: 'memory'
      };
      const adapter = new StorageAdapter(options);

      await adapter.set('key1', 'value1');
      const result = await adapter.get('key1');
      expect(result).toBe('value1');
    });

    test('should apply custom prefix', async () => {
      const options: StorageOptions = { 
        backend: 'memory',
        prefix: 'custom_prefix_'
      };
      const adapter = new StorageAdapter(options);

      await adapter.set('key1', 'value1');
      const result = await adapter.get('key1');
      expect(result).toBe('value1');
    });
  });
});