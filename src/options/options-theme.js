// 高级设置页面自适应主题功能

// 初始化自适应主题
function initAutoTheme() {
    const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    const body = document.body;
    
    
    if (systemPrefersDark) {
        body.classList.add('dark-theme');
    } else {
        body.classList.remove('dark-theme');
    }
    
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initAutoTheme();
});

// 监听系统主题变化
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        const body = document.body;
        if (e.matches) {
            body.classList.add('dark-theme');
        } else {
            body.classList.remove('dark-theme');
        }
    });
}

// 如果DOM已经加载完成，立即执行
if (document.readyState !== 'loading') {
    initAutoTheme();
}