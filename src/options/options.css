/**
 * Options Page Styles - 设置页面样式
 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #24292f;
  background-color: #ffffff;
  overflow-x: hidden;
}

/* 主容器 */
.options-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.options-header {
  background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
  color: #ffffff;
  padding: 24px 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.extension-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.header-text {
  display: flex;
  flex-direction: column;
}

.extension-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.extension-version {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

.header-right {
  display: flex;
  gap: 12px;
}

.header-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.header-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.header-button:active {
  transform: translateY(0);
}

/* 主要内容区域 */
.options-main {
  flex: 1;
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 侧边导航 */
.options-nav {
  width: 240px;
  background: #f6f8fa;
  border-right: 1px solid #e1e4e8;
  padding: 24px 0;
  min-height: calc(100vh - 120px);
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: none;
  border: none;
  color: #656d76;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.nav-button:hover {
  background: #e1e4e8;
  color: #24292f;
}

.nav-button.active {
  background: #ffffff;
  color: #1a73e8;
  border-left-color: #1a73e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-button svg {
  flex-shrink: 0;
}

/* 内容区域 */
.options-content {
  flex: 1;
  padding: 32px;
  background: #ffffff;
}

.settings-section {
  display: none;
}

.settings-section.active {
  display: block;
}

.section-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e1e4e8;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 8px;
}

.section-description {
  font-size: 16px;
  color: #656d76;
  margin: 0;
}

/* 设置网格 */
.settings-grid {
  display: grid;
  gap: 24px;
}

/* 设置项样式 */
.setting-item {
  background: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
}

.setting-item:hover {
  border-color: #d0d7de;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.setting-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 16px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 4px;
}

.setting-description {
  font-size: 14px;
  color: #656d76;
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
  margin-left: 16px;
}

/* 开关控件 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d0d7de;
  transition: 0.3s;
  border-radius: 24px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.switch input:checked + .switch-slider {
  background-color: #1a73e8;
}

.switch input:checked + .switch-slider:before {
  transform: translateX(20px);
}

/* 选择框控件 */
.select-control {
  position: relative;
  min-width: 160px;
}

.select-control select {
  width: 100%;
  padding: 8px 32px 8px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background: #ffffff;
  font-size: 14px;
  color: #24292f;
  cursor: pointer;
  appearance: none;
}

.select-control::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #656d76;
  pointer-events: none;
}

/* 输入框控件 */
.input-control {
  min-width: 160px;
}

.input-control input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background: #ffffff;
  font-size: 14px;
  color: #24292f;
}

.input-control input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

/* 滑块控件 */
.slider-control {
  min-width: 200px;
}

.slider-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: #e1e4e8;
  outline: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1a73e8;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1a73e8;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.slider-value {
  min-width: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
  text-align: center;
}

/* 快捷键容器 */
.shortcuts-container {
  display: grid;
  gap: 16px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.shortcut-item:hover {
  border-color: #d0d7de;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.shortcut-info {
  flex: 1;
}

.shortcut-title {
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
  margin-bottom: 4px;
}

.shortcut-description {
  font-size: 13px;
  color: #656d76;
}

.shortcut-keys {
  display: flex;
  gap: 4px;
  align-items: center;
}

.shortcut-key {
  padding: 4px 8px;
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  color: #24292f;
  min-width: 24px;
  text-align: center;
}

.shortcut-plus {
  color: #656d76;
  font-size: 12px;
}

/* 关于页面 */
.about-content {
  max-width: 600px;
}

.about-card {
  display: flex;
  gap: 20px;
  padding: 32px;
  background: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 12px;
  margin-bottom: 32px;
}

.about-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.about-info {
  flex: 1;
}

.about-title {
  font-size: 24px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 4px;
}

.about-version {
  font-size: 14px;
  color: #656d76;
  margin-bottom: 12px;
}

.about-description {
  font-size: 14px;
  color: #656d76;
  line-height: 1.5;
}

.about-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.about-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  color: #24292f;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.about-link:hover {
  border-color: #1a73e8;
  color: #1a73e8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.about-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #e1e4e8;
}

.copyright {
  font-size: 13px;
  color: #656d76;
}

/* 底部状态栏 */
.options-footer {
  background: #f6f8fa;
  border-top: 1px solid #e1e4e8;
  padding: 16px 32px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.status-text {
  font-size: 14px;
  color: #656d76;
}

.footer-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background: #ffffff;
  color: #24292f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.footer-button:hover {
  border-color: #bbb;
  background: #f6f8fa;
}

.footer-button.primary {
  background: #1a73e8;
  border-color: #1a73e8;
  color: #ffffff;
}

.footer-button.primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

/* 模态对话框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-dialog {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e4e8;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #24292f;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #656d76;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f6f8fa;
  color: #24292f;
}

.modal-body {
  padding: 24px;
}

.modal-message {
  font-size: 14px;
  color: #24292f;
  line-height: 1.5;
  margin: 0;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid #e1e4e8;
  background: #f6f8fa;
}

.modal-button {
  padding: 8px 16px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-button.secondary {
  background: #ffffff;
  color: #24292f;
}

.modal-button.secondary:hover {
  background: #f6f8fa;
  border-color: #bbb;
}

.modal-button.primary {
  background: #1a73e8;
  border-color: #1a73e8;
  color: #ffffff;
}

.modal-button.primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

/* 通知提示 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #ffffff;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 16px 20px;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-icon {
  flex-shrink: 0;
  color: #28a745;
}

.notification-icon.error {
  color: #dc3545;
}

.notification-icon.warning {
  color: #ffc107;
}

.notification-message {
  font-size: 14px;
  color: #24292f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .options-main {
    flex-direction: column;
  }
  
  .options-nav {
    width: 100%;
    min-height: auto;
    padding: 16px 0;
  }
  
  .nav-list {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
    gap: 8px;
  }
  
  .nav-item {
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .nav-button {
    padding: 8px 16px;
    border-radius: 20px;
    border-left: none;
    white-space: nowrap;
  }
  
  .nav-button.active {
    background: #0969da;
    color: #ffffff;
    border-left: none;
  }
  
  .options-content {
    padding: 24px 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-right {
    justify-content: center;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .about-card {
    flex-direction: column;
    text-align: center;
  }
  
  .about-links {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .options-header {
    padding: 16px 20px;
  }
  
  .options-content {
    padding: 20px 16px;
  }
  
  .options-footer {
    padding: 12px 16px;
  }
  
  .section-title {
    font-size: 24px;
  }
  
  .setting-item {
    padding: 16px;
  }
  
  .setting-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .setting-control {
    margin-left: 0;
    align-self: flex-start;
  }
}

/* 暗黑模式样式 - 通过类控制 */
body.dark-theme {
  background-color: #0d1117;
  color: #f0f6fc;
}

body.dark-theme .options-header {
  background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
}

body.dark-theme .options-nav {
  background: #161b22;
  border-color: #30363d;
}

body.dark-theme .nav-button {
  color: #8b949e;
}

body.dark-theme .nav-button:hover {
  background: #21262d;
  color: #f0f6fc;
}

body.dark-theme .nav-button.active {
  background: #0d1117;
  color: #1a73e8;
  border-left-color: #1a73e8;
}

body.dark-theme .options-content {
  background: #0d1117;
}

body.dark-theme .section-header {
  border-bottom-color: #30363d;
}

body.dark-theme .setting-item {
  background: #161b22;
  border-color: #30363d;
}

body.dark-theme .setting-item:hover {
  border-color: #484f58;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-theme .setting-title {
  color: #f0f6fc;
}

body.dark-theme .setting-description {
  color: #8b949e;
}

body.dark-theme .section-title {
  color: #f0f6fc;
}

body.dark-theme .section-description {
  color: #8b949e;
}

body.dark-theme .select-control select {
  background: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .input-control input {
  background: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .input-control input:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

body.dark-theme .switch-slider {
  background-color: #484f58;
}

body.dark-theme .switch input:checked + .switch-slider {
  background-color: #1a73e8;
}

body.dark-theme .slider {
  background: #30363d;
}

body.dark-theme .slider::-webkit-slider-thumb {
  background: #1a73e8;
}

body.dark-theme .slider::-moz-range-thumb {
  background: #1a73e8;
}

body.dark-theme .shortcut-item {
  background: #161b22;
  border-color: #30363d;
}

body.dark-theme .shortcut-item:hover {
  border-color: #484f58;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

body.dark-theme .shortcut-title {
  color: #f0f6fc;
}

body.dark-theme .shortcut-description {
  color: #8b949e;
}

body.dark-theme .shortcut-key {
  background: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .about-card {
  background: #161b22;
  border-color: #30363d;
}

body.dark-theme .about-title {
  color: #f0f6fc;
}

body.dark-theme .about-version,
body.dark-theme .about-description {
  color: #8b949e;
}

body.dark-theme .about-link {
  background: #161b22;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .about-link:hover {
  border-color: #1a73e8;
  color: #1a73e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-theme .about-footer {
  border-top-color: #30363d;
}

body.dark-theme .copyright {
  color: #8b949e;
}

body.dark-theme .options-footer {
  background: #161b22;
  border-color: #30363d;
}

body.dark-theme .status-text {
  color: #8b949e;
}

body.dark-theme .footer-button {
  background: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .footer-button:hover {
  background: #30363d;
  border-color: #484f58;
}

body.dark-theme .footer-button.primary {
  background: #1a73e8;
  border-color: #1a73e8;
  color: #ffffff;
}

body.dark-theme .footer-button.primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

body.dark-theme .modal-dialog {
  background: #161b22;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.4);
}

body.dark-theme .modal-header {
  border-bottom-color: #30363d;
}

body.dark-theme .modal-title {
  color: #f0f6fc;
}

body.dark-theme .modal-close {
  color: #8b949e;
}

body.dark-theme .modal-close:hover {
  background: #21262d;
  color: #f0f6fc;
}

body.dark-theme .modal-message {
  color: #f0f6fc;
}

body.dark-theme .modal-footer {
  border-top-color: #30363d;
  background: #0d1117;
}

body.dark-theme .modal-button.secondary {
  background: #21262d;
  border-color: #30363d;
  color: #f0f6fc;
}

body.dark-theme .modal-button.secondary:hover {
  background: #30363d;
  border-color: #484f58;
}

body.dark-theme .modal-button.primary {
  background: #1a73e8;
  border-color: #1a73e8;
}

body.dark-theme .modal-button.primary:hover {
  background: #1557b0;
  border-color: #1557b0;
}

body.dark-theme .notification {
  background: #161b22;
  border-color: #30363d;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

body.dark-theme .notification-message {
  color: #f0f6fc;
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #1a73e8;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  z-index: 1001;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.theme-toggle:hover {
  background: #1557b0;
  transform: translateY(-1px);
}

body.dark-theme .theme-toggle {
  background: #1a73e8;
}

body.dark-theme .theme-toggle:hover {
  background: #1557b0;
}

/* 深色模式支持 - 保留系统偏好检测作为后备 */
@media (prefers-color-scheme: dark) {
  body:not(.light-theme) {
    background-color: #0d1117;
    color: #f0f6fc;
  }
  
  body:not(.light-theme) .options-nav {
    background: #161b22;
    border-color: #30363d;
  }
  
  body:not(.light-theme) .nav-button {
    color: #8b949e;
  }
  
  body:not(.light-theme) .nav-button:hover {
    background: #21262d;
    color: #f0f6fc;
  }
  
  body:not(.light-theme) .nav-button.active {
    background: #0d1117;
    color: #1a73e8;
    border-left-color: #1a73e8;
  }
  
  body:not(.light-theme) .options-content {
    background: #0d1117;
  }
  
  body:not(.light-theme) .setting-item {
    background: #161b22;
    border-color: #30363d;
  }
  
  body:not(.light-theme) .setting-item:hover {
    border-color: #484f58;
  }
  
  body:not(.light-theme) .setting-title {
    color: #f0f6fc;
  }
  
  body:not(.light-theme) .setting-description {
    color: #8b949e;
  }
  
  body:not(.light-theme) .section-title {
    color: #f0f6fc;
  }
  
  body:not(.light-theme) .section-description {
    color: #8b949e;
  }
  
  body:not(.light-theme) .options-footer {
    background: #161b22;
    border-color: #30363d;
  }
  
  body:not(.light-theme) .footer-button {
    background: #21262d;
    border-color: #30363d;
    color: #f0f6fc;
  }
  
  body:not(.light-theme) .footer-button:hover {
    background: #30363d;
    border-color: #484f58;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .setting-item {
    border-width: 2px;
  }
  
  .nav-button.active {
    border-left-width: 4px;
  }
  
  .switch-slider {
    border: 2px solid #24292f;
  }
  
  .switch input:checked + .switch-slider {
    border-color: #1a73e8;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}