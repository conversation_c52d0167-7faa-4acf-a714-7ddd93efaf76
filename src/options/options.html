<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>垂直侧边栏 - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="options-container">
        <!-- 头部 -->
        <header class="options-header">
            <div class="header-content">
                <div class="header-left">
                    <div class="extension-icon">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                            <path d="M4 4h8v8H4V4zm0 12h8v8H4v-8zm0 12h8v8H4v-8zm12-24h8v8h-8V4zm0 12h8v8h-8v-8zm0 12h8v8h-8v-8z"/>
                        </svg>
                    </div>
                    <div class="header-text">
                        <h1 class="extension-title">垂直侧边栏</h1>
                        <p class="extension-version">版本 1.0.0</p>
                    </div>
                </div>
                <div class="header-right">
                    <button id="reset-settings" class="header-button" title="重置所有设置">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                            <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                        </svg>
                        重置设置
                    </button>
                    <button id="export-settings" class="header-button" title="导出设置">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                        </svg>
                        导出
                    </button>
                    <button id="import-settings" class="header-button" title="导入设置">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                        </svg>
                        导入
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="options-main">
            <!-- 侧边导航 -->
            <nav class="options-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <button class="nav-button active" data-section="general">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                                <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.292-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.292c.415.764-.42 1.6-1.185 1.184l-.292-.159a1.873 1.873 0 0 0-2.692 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.693-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.292A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
                            </svg>
                            常规设置
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-button" data-section="appearance">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                <path d="M8 4a4 4 0 0 0-4 4 .5.5 0 0 1-1 0 5 5 0 0 1 5-5 .5.5 0 0 1 0 1z"/>
                            </svg>
                            外观设置
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-button" data-section="behavior">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M6 10.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                            </svg>
                            行为设置
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-button" data-section="shortcuts">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                                <path d="M3.051 3.26a.5.5 0 0 1 .354-.613l1.932-.518a.5.5 0 0 1 .62.39c.655-.079 1.35-.117 2.043-.117.72 0 1.443.041 2.12.126a.5.5 0 0 1 .622-.399l1.932.518a.5.5 0 0 1 .306.729c.14.09.266.19.373.297.408.408.78 1.05 1.095 1.772.32.733.599 1.591.805 2.466.206.875.34 1.78.364 2.606.024.816-.059 1.602-.328 2.21a1.42 1.42 0 0 1-1.445.83c-.636-.067-1.115-.394-1.513-.773-.245-.232-.496-.526-.739-.808-.126-.148-.25-.292-.368-.423-.728-.804-1.597-1.527-3.224-1.527-1.627 0-2.496.723-3.224 1.527-.119.131-.242.275-.368.423-.243.282-.494.575-.739.808-.398.38-.877.706-1.513.773a1.42 1.42 0 0 1-1.445-.83c-.27-.608-.352-1.394-.329-2.21.024-.826.16-1.73.365-2.606.206-.875.486-1.733.805-2.466.315-.722.687-1.364 1.094-1.772.107-.107.233-.207.373-.297a.5.5 0 0 1 .28-.73zM2.81 4.9c-.147.65-.436 1.316-.53 2.253-.11 1.07-.295 2.27-.295 2.847 0 .42.566.472 1.146.472.58 0 .88-.052.88-.472 0-.577-.185-1.777-.295-2.847-.094-.937-.383-1.604-.53-2.253a.5.5 0 0 1 .48-.598c.694-.043 1.353-.025 2.003.042a.5.5 0 0 1 .399.546c-.1.608-.17 1.207-.17 1.78 0 .42.566.472 1.146.472.58 0 .88-.052.88-.472 0-.573-.07-1.172-.17-1.78a.5.5 0 0 1 .399-.546c.65-.067 1.309-.085 2.003-.042a.5.5 0 0 1 .48.598c-.147.65-.436 1.316-.53 2.253-.11 1.07-.295 2.27-.295 2.847 0 .42.566.472 1.146.472.58 0 .88-.052.88-.472 0-.577-.185-1.777-.295-2.847-.094-.937-.383-1.604-.53-2.253a.5.5 0 0 1 .48-.598c.694-.043 1.353-.025 2.003.042a.5.5 0 0 1 .399.546c-.1.608-.17 1.207-.17 1.78 0 .42.566.472 1.146.472.58 0 .88-.052.88-.472 0-.573-.07-1.172-.17-1.78a.5.5 0 0 1 .399-.546c.65-.067 1.309-.085 2.003-.042a.5.5 0 0 1 .48.598z"/>
                            </svg>
                            快捷键
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-button" data-section="advanced">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M6 10.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                <path d="M13.991 3l.024.001a1.46 1.46 0 0 1 .538.143.757.757 0 0 1 .302.254c.067.1.145.277.145.602v.991l-.001.024a1.464 1.464 0 0 1-.143.538.758.758 0 0 1-.254.302c-.1.067-.277.145-.602.145H2.009l-.024-.001a1.464 1.464 0 0 1-.538-.143.758.758 0 0 1-.302-.254C1.078 5.502 1 5.325 1 5V4.009l.001-.024a1.46 1.46 0 0 1 .143-.538.758.758 0 0 1 .254-.302C1.498 3.078 1.675 3 2 3h11.991zM14 2H2C.89 2 0 2.89 0 4v1c0 1.11.89 2 2 2h12c1.11 0 2-.89 2-2V4c0-1.11-.89-2-2-2z"/>
                            </svg>
                            高级设置
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-button" data-section="about">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                            </svg>
                            关于
                        </button>
                    </li>
                </ul>
            </nav>

            <!-- 内容区域 -->
            <div class="options-content">
                <!-- 常规设置 -->
                <section id="general-section" class="settings-section active">
                    <div class="section-header">
                        <h2 class="section-title">常规设置</h2>
                        <p class="section-description">配置侧边栏的基本功能和行为</p>
                    </div>
                    <div class="settings-grid">
                        <!-- 设置项将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 外观设置 -->
                <section id="appearance-section" class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">外观设置</h2>
                        <p class="section-description">自定义侧边栏的外观和主题</p>
                    </div>
                    <div class="settings-grid">
                        <!-- 设置项将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 行为设置 -->
                <section id="behavior-section" class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">行为设置</h2>
                        <p class="section-description">配置侧边栏的交互行为和功能</p>
                    </div>
                    <div class="settings-grid">
                        <!-- 设置项将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 快捷键设置 -->
                <section id="shortcuts-section" class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">快捷键设置</h2>
                        <p class="section-description">自定义键盘快捷键</p>
                    </div>
                    <div class="shortcuts-container">
                        <!-- 快捷键列表将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 高级设置 -->
                <section id="advanced-section" class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">高级设置</h2>
                        <p class="section-description">高级功能和开发者选项</p>
                    </div>
                    <div class="settings-grid">
                        <!-- 设置项将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 关于页面 -->
                <section id="about-section" class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">关于</h2>
                        <p class="section-description">扩展信息和帮助</p>
                    </div>
                    <div class="about-content">
                        <div class="about-card">
                            <div class="about-icon">
                                <svg width="48" height="48" viewBox="0 0 32 32" fill="currentColor">
                                    <path d="M4 4h8v8H4V4zm0 12h8v8H4v-8zm0 12h8v8H4v-8zm12-24h8v8h-8V4zm0 12h8v8h-8v-8zm0 12h8v8h-8v-8z"/>
                                </svg>
                            </div>
                            <div class="about-info">
                                <h3 class="about-title">垂直侧边栏</h3>
                                <p class="about-version">版本 1.0.0</p>
                                <p class="about-description">
                                    一个功能强大的Chrome扩展，为您提供类似Edge浏览器的垂直侧边栏体验。
                                    支持标签页管理、收藏夹显示、搜索功能等。
                                </p>
                            </div>
                        </div>
                        
                        <div class="about-links">
                            <a href="#" class="about-link" id="view-changelog">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692l4.372 5.158A.5.5 0 0 1 14 14.5v2a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 1 .128-.334L6 8.692 1.628 3.534A.5.5 0 0 1 1.5 3.5v-2z"/>
                                </svg>
                                更新日志
                            </a>
                            <a href="#" class="about-link" id="view-help">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                    <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z"/>
                                </svg>
                                帮助文档
                            </a>
                            <a href="#" class="about-link" id="report-issue">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                    <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
                                </svg>
                                报告问题
                            </a>
                            <a href="#" class="about-link" id="rate-extension">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                                </svg>
                                评价扩展
                            </a>
                        </div>

                        <div class="about-footer">
                            <p class="copyright">© 2024 垂直侧边栏扩展. 保留所有权利.</p>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="options-footer">
            <div class="footer-content">
                <div class="footer-left">
                    <span class="status-text" id="status-text">设置已保存</span>
                </div>
                <div class="footer-right">
                    <button id="save-settings" class="footer-button primary">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L8.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
                        </svg>
                        保存设置
                    </button>
                </div>
            </div>
        </footer>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="import-file-input" accept=".json" style="display: none;">

    <!-- 模态对话框 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">确认操作</h3>
                <button class="modal-close" id="modal-close">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.75.75 0 1 1 1.06 1.06L9.06 8l3.22 3.22a.75.75 0 1 1-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 0 1-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p class="modal-message" id="modal-message">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="modal-cancel">取消</button>
                <button class="modal-button primary" id="modal-confirm">确认</button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification" style="display: none;">
        <div class="notification-content">
            <div class="notification-icon" id="notification-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.061L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                </svg>
            </div>
            <span class="notification-message" id="notification-message">操作成功</span>
        </div>
    </div>

    <script src="options-theme.js"></script>
    <script src="options.js"></script>
</body>
</html>