/**
 * Options Page JavaScript - 设置页面逻辑
 */

class OptionsManager {
  constructor() {
    this.currentSection = 'general';
    this.settings = {};
    this.defaultSettings = this.getDefaultSettings();
    this.settingsDefinitions = this.getSettingsDefinitions();
    
    this.init();
  }

  /**
   * 初始化
   */
  async init() {
    await this.loadSettings();
    this.setupEventListeners();
    this.renderAllSections();
    this.updateStatus('设置已加载');
  }

  /**
   * 获取默认设置
   */
  getDefaultSettings() {
    // 🚨 重要：这些默认值必须与 defaultSettings.js、options.js 和 dist/background.js 保持一致
    return {
      // 核心功能设置
      enabled: true,           // 默认启用垂直侧边栏
      position: 'left',        // 默认左侧位置
      
      // 外观设置
      theme: 'auto',           // 默认自动主题（跟随系统）
      
      // 显示内容设置
      showTabs: true,          // 默认显示标签页
      showBookmarks: false,    // 默认不显示收藏夹
      
      // 行为设置
      defaultPinned: false,    // 默认不固定
      expandDelay: 0,          // 展开延迟0ms（即时响应）
      collapseDelay: 0,        // 收起延迟0ms（即时响应）
      
      // 快捷键设置
      keyboardShortcuts: {
        toggle: 'Ctrl+Shift+S', // 切换侧边栏快捷键
        search: 'Ctrl+Shift+F'  // 搜索快捷键（避免与浏览器默认快捷键冲突）
      },
      showRecentTabs: true,
      maxRecentTabs: 10,
      
      // 搜索设置
      enableSearch: true,
      searchDelay: 300,
      showSearchHistory: true,
      maxSearchHistory: 10,
      
      // 高级设置
      debugMode: false,
      enableAnimations: true,
      enableSounds: false,
      autoUpdate: true,
      telemetry: true
    };
  }

  /**
   * 获取设置定义
   */
  getSettingsDefinitions() {
    return {
      general: [
        {
          key: 'enabled',
          type: 'switch',
          title: '启用侧边栏',
          description: '启用或禁用垂直侧边栏功能'
        },
        {
          key: 'autoShow',
          type: 'switch',
          title: '自动显示',
          description: '页面加载时自动显示侧边栏'
        },
        {
          key: 'showOnHover',
          type: 'switch',
          title: '鼠标悬停显示',
          description: '鼠标悬停在边缘时显示侧边栏'
        },
        {
          key: 'hoverDelay',
          type: 'slider',
          title: '悬停延迟',
          description: '鼠标悬停多长时间后显示侧边栏（毫秒）',
          min: 0,
          max: 1000,
          step: 50,
          unit: 'ms'
        },
        {
          key: 'pinned',
          type: 'switch',
          title: '固定显示',
          description: '始终显示侧边栏，不自动隐藏'
        },
        {
          key: 'position',
          type: 'select',
          title: '显示位置',
          description: '选择侧边栏显示在屏幕的哪一侧',
          options: [
            { value: 'left', label: '左侧' },
            { value: 'right', label: '右侧' }
          ]
        }
      ],
      appearance: [
        {
          key: 'theme',
          type: 'select',
          title: '主题',
          description: '选择侧边栏的外观主题',
          options: [
            { value: 'auto', label: '跟随系统' },
            { value: 'light', label: '浅色主题' },
            { value: 'dark', label: '深色主题' }
          ]
        },
        {
          key: 'width',
          type: 'slider',
          title: '宽度',
          description: '设置侧边栏的宽度',
          min: 200,
          max: 500,
          step: 10,
          unit: 'px'
        },
        {
          key: 'opacity',
          type: 'slider',
          title: '透明度',
          description: '设置侧边栏的透明度',
          min: 0.5,
          max: 1.0,
          step: 0.05,
          unit: ''
        },
        {
          key: 'showIcons',
          type: 'switch',
          title: '显示图标',
          description: '在标签页和收藏夹旁显示图标'
        },
        {
          key: 'showFavicons',
          type: 'switch',
          title: '显示网站图标',
          description: '显示网站的favicon图标'
        },
        ],
      behavior: [
        {
          key: 'closeTabsOnClick',
          type: 'switch',
          title: '点击关闭标签页',
          description: '中键点击标签页时关闭标签页'
        },
        {
          key: 'openInNewTab',
          type: 'switch',
          title: '新标签页打开',
          description: '点击收藏夹时在新标签页中打开'
        },
        {
          key: 'groupTabs',
          type: 'switch',
          title: '标签页分组',
          description: '按窗口对标签页进行分组显示'
        },
        {
          key: 'showBookmarks',
          type: 'switch',
          title: '显示收藏夹',
          description: '在侧边栏中显示收藏夹'
        },
        {
          key: 'showRecentTabs',
          type: 'switch',
          title: '显示最近标签页',
          description: '显示最近关闭的标签页'
        },
        {
          key: 'maxRecentTabs',
          type: 'slider',
          title: '最近标签页数量',
          description: '保留的最近标签页数量',
          min: 5,
          max: 50,
          step: 5,
          unit: '个'
        }
      ],
      advanced: [
        {
          key: 'debugMode',
          type: 'switch',
          title: '调试模式',
          description: '启用调试模式，显示详细的日志信息'
        },
        {
          key: 'enableAnimations',
          type: 'switch',
          title: '启用动画',
          description: '启用侧边栏的动画效果'
        },
        {
          key: 'enableSounds',
          type: 'switch',
          title: '启用声音',
          description: '操作时播放声音提示'
        },
        {
          key: 'autoUpdate',
          type: 'switch',
          title: '自动更新',
          description: '自动检查和安装扩展更新'
        },
        {
          key: 'telemetry',
          type: 'switch',
          title: '使用情况统计',
          description: '发送匿名使用统计数据以帮助改进扩展'
        }
      ]
    };
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 导航按钮
    document.querySelectorAll('.nav-button').forEach(button => {
      button.addEventListener('click', (e) => {
        const section = e.currentTarget.dataset.section;
        this.switchSection(section);
      });
    });

    // 头部按钮
    document.getElementById('reset-settings').addEventListener('click', () => {
      this.showConfirmDialog(
        '重置设置',
        '确定要重置所有设置到默认值吗？此操作无法撤销。',
        () => this.resetSettings()
      );
    });

    document.getElementById('export-settings').addEventListener('click', () => {
      this.exportSettings();
    });

    document.getElementById('import-settings').addEventListener('click', () => {
      document.getElementById('import-file-input').click();
    });

    document.getElementById('import-file-input').addEventListener('change', (e) => {
      this.importSettings(e.target.files[0]);
    });

    // 保存按钮
    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    // 模态对话框
    document.getElementById('modal-close').addEventListener('click', () => {
      this.hideModal();
    });

    document.getElementById('modal-cancel').addEventListener('click', () => {
      this.hideModal();
    });

    document.getElementById('modal-overlay').addEventListener('click', (e) => {
      if (e.target === e.currentTarget) {
        this.hideModal();
      }
    });

    // 关于页面链接
    document.getElementById('view-changelog').addEventListener('click', (e) => {
      e.preventDefault();
      this.openUrl('https://github.com/your-repo/releases');
    });

    document.getElementById('view-help').addEventListener('click', (e) => {
      e.preventDefault();
      this.openUrl('https://github.com/your-repo/wiki');
    });

    document.getElementById('report-issue').addEventListener('click', (e) => {
      e.preventDefault();
      this.openUrl('https://github.com/your-repo/issues');
    });

    document.getElementById('rate-extension').addEventListener('click', (e) => {
      e.preventDefault();
      this.openUrl('https://chrome.google.com/webstore/detail/your-extension-id');
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            this.saveSettings();
            break;
          case 'r':
            e.preventDefault();
            this.showConfirmDialog(
              '重置设置',
              '确定要重置所有设置到默认值吗？',
              () => this.resetSettings()
            );
            break;
        }
      }
    });
  }

  /**
   * 切换设置区域
   */
  switchSection(section) {
    // 更新导航状态
    document.querySelectorAll('.nav-button').forEach(button => {
      button.classList.remove('active');
    });
    document.querySelector(`[data-section="${section}"]`).classList.add('active');

    // 更新内容区域
    document.querySelectorAll('.settings-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(`${section}-section`).classList.add('active');

    this.currentSection = section;

    // 如果是快捷键页面，渲染快捷键
    if (section === 'shortcuts') {
      this.renderShortcuts();
    }
  }

  /**
   * 渲染所有设置区域
   */
  renderAllSections() {
    Object.keys(this.settingsDefinitions).forEach(section => {
      this.renderSection(section);
    });
  }

  /**
   * 渲染设置区域
   */
  renderSection(section) {
    const container = document.querySelector(`#${section}-section .settings-grid`);
    if (!container) return;

    container.innerHTML = '';

    this.settingsDefinitions[section].forEach(setting => {
      const settingElement = this.createSettingElement(setting);
      container.appendChild(settingElement);
    });
  }

  /**
   * 创建设置项元素
   */
  createSettingElement(setting) {
    const element = document.createElement('div');
    element.className = 'setting-item';

    const header = document.createElement('div');
    header.className = 'setting-header';

    const info = document.createElement('div');
    info.className = 'setting-info';

    const title = document.createElement('div');
    title.className = 'setting-title';
    title.textContent = setting.title;

    const description = document.createElement('div');
    description.className = 'setting-description';
    description.textContent = setting.description;

    info.appendChild(title);
    info.appendChild(description);

    const control = document.createElement('div');
    control.className = 'setting-control';

    // 根据类型创建控件
    switch (setting.type) {
      case 'switch':
        control.appendChild(this.createSwitchControl(setting));
        break;
      case 'select':
        control.appendChild(this.createSelectControl(setting));
        break;
      case 'slider':
        control.appendChild(this.createSliderControl(setting));
        break;
      case 'input':
        control.appendChild(this.createInputControl(setting));
        break;
    }

    header.appendChild(info);
    header.appendChild(control);
    element.appendChild(header);

    return element;
  }

  /**
   * 创建开关控件
   */
  createSwitchControl(setting) {
    const wrapper = document.createElement('label');
    wrapper.className = 'switch';

    const input = document.createElement('input');
    input.type = 'checkbox';
    input.checked = this.settings[setting.key];
    input.addEventListener('change', (e) => {
      this.settings[setting.key] = e.target.checked;
      this.markAsChanged();
    });

    const slider = document.createElement('span');
    slider.className = 'switch-slider';

    wrapper.appendChild(input);
    wrapper.appendChild(slider);

    return wrapper;
  }

  /**
   * 创建选择控件
   */
  createSelectControl(setting) {
    const wrapper = document.createElement('div');
    wrapper.className = 'select-control';

    const select = document.createElement('select');
    select.value = this.settings[setting.key];

    setting.options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.label;
      select.appendChild(optionElement);
    });

    select.addEventListener('change', (e) => {
      this.settings[setting.key] = e.target.value;
      this.markAsChanged();
    });

    wrapper.appendChild(select);
    return wrapper;
  }

  /**
   * 创建滑块控件
   */
  createSliderControl(setting) {
    const wrapper = document.createElement('div');
    wrapper.className = 'slider-control';

    const sliderWrapper = document.createElement('div');
    sliderWrapper.className = 'slider-wrapper';

    const slider = document.createElement('input');
    slider.type = 'range';
    slider.className = 'slider';
    slider.min = setting.min;
    slider.max = setting.max;
    slider.step = setting.step;
    slider.value = this.settings[setting.key];

    const value = document.createElement('span');
    value.className = 'slider-value';
    value.textContent = this.formatSliderValue(this.settings[setting.key], setting.unit);

    slider.addEventListener('input', (e) => {
      const val = parseFloat(e.target.value);
      this.settings[setting.key] = val;
      value.textContent = this.formatSliderValue(val, setting.unit);
      this.markAsChanged();
    });

    sliderWrapper.appendChild(slider);
    sliderWrapper.appendChild(value);
    wrapper.appendChild(sliderWrapper);

    return wrapper;
  }

  /**
   * 创建输入控件
   */
  createInputControl(setting) {
    const wrapper = document.createElement('div');
    wrapper.className = 'input-control';

    const input = document.createElement('input');
    input.type = setting.inputType || 'text';
    input.value = this.settings[setting.key];
    input.placeholder = setting.placeholder || '';

    input.addEventListener('input', (e) => {
      this.settings[setting.key] = e.target.value;
      this.markAsChanged();
    });

    wrapper.appendChild(input);
    return wrapper;
  }

  /**
   * 格式化滑块值
   */
  formatSliderValue(value, unit) {
    if (unit === '') {
      return (value * 100).toFixed(0) + '%';
    }
    return value + (unit || '');
  }

  /**
   * 渲染快捷键
   */
  async renderShortcuts() {
    const container = document.querySelector('#shortcuts-section .shortcuts-container');
    if (!container) return;

    try {
      // 获取快捷键配置
      const shortcuts = await this.getShortcuts();
      
      container.innerHTML = '';

      shortcuts.forEach(shortcut => {
        const element = this.createShortcutElement(shortcut);
        container.appendChild(element);
      });
    } catch (error) {
      console.error('Error rendering shortcuts:', error);
      container.innerHTML = '<p>无法加载快捷键设置</p>';
    }
  }

  /**
   * 创建快捷键元素
   */
  createShortcutElement(shortcut) {
    const element = document.createElement('div');
    element.className = 'shortcut-item';

    const info = document.createElement('div');
    info.className = 'shortcut-info';

    const title = document.createElement('div');
    title.className = 'shortcut-title';
    title.textContent = shortcut.description;

    const description = document.createElement('div');
    description.className = 'shortcut-description';
    description.textContent = shortcut.category;

    info.appendChild(title);
    info.appendChild(description);

    const keys = document.createElement('div');
    keys.className = 'shortcut-keys';

    // 解析快捷键
    const keyParts = this.parseShortcut(shortcut.shortcut);
    keyParts.forEach((part, index) => {
      if (index > 0) {
        const plus = document.createElement('span');
        plus.className = 'shortcut-plus';
        plus.textContent = '+';
        keys.appendChild(plus);
      }

      const key = document.createElement('span');
      key.className = 'shortcut-key';
      key.textContent = part;
      keys.appendChild(key);
    });

    element.appendChild(info);
    element.appendChild(keys);

    return element;
  }

  /**
   * 解析快捷键字符串
   */
  parseShortcut(shortcut) {
    if (!shortcut) return ['未设置'];
    
    return shortcut.split('+').map(part => {
      const keyMap = {
        'Ctrl': 'Ctrl',
        'Alt': 'Alt',
        'Shift': 'Shift',
        'Meta': 'Cmd',
        'ArrowUp': '↑',
        'ArrowDown': '↓',
        'ArrowLeft': '←',
        'ArrowRight': '→',
        'Enter': '⏎',
        'Escape': 'Esc',
        'Space': '空格'
      };
      return keyMap[part] || part.toUpperCase();
    });
  }

  /**
   * 获取快捷键配置
   */
  async getShortcuts() {
    // 模拟快捷键数据
    return [
      {
        id: 'toggle-sidebar',
        description: '切换侧边栏显示',
        category: '侧边栏',
        shortcut: 'Ctrl+Shift+B'
      },
      {
        id: 'focus-search',
        description: '聚焦搜索框',
        category: '搜索',
        shortcut: 'Ctrl+F'
      },
      {
        id: 'next-tab',
        description: '下一个标签页',
        category: '导航',
        shortcut: 'Ctrl+Tab'
      },
      {
        id: 'prev-tab',
        description: '上一个标签页',
        category: '导航',
        shortcut: 'Ctrl+Shift+Tab'
      },
      {
        id: 'new-tab',
        description: '新建标签页',
        category: '标签页',
        shortcut: 'Ctrl+T'
      },
      {
        id: 'close-tab',
        description: '关闭标签页',
        category: '标签页',
        shortcut: 'Ctrl+W'
      }
    ];
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(null);
      this.settings = { ...this.defaultSettings, ...result };
    } catch (error) {
      console.error('Error loading settings:', error);
      this.settings = { ...this.defaultSettings };
    }
  }

  /**
   * 保存设置
   */
  async saveSettings() {
    try {
      await chrome.storage.sync.set(this.settings);
      this.updateStatus('设置已保存', 'success');
      this.markAsSaved();
    } catch (error) {
      console.error('Error saving settings:', error);
      this.updateStatus('保存失败', 'error');
    }
  }

  /**
   * 重置设置
   */
  async resetSettings() {
    try {
      this.settings = { ...this.defaultSettings };
      await chrome.storage.sync.clear();
      await chrome.storage.sync.set(this.settings);
      
      this.renderAllSections();
      this.updateStatus('设置已重置', 'success');
      this.markAsSaved();
      this.hideModal();
    } catch (error) {
      console.error('Error resetting settings:', error);
      this.updateStatus('重置失败', 'error');
    }
  }

  /**
   * 导出设置
   */
  exportSettings() {
    const data = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      settings: this.settings
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sidebar-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.updateStatus('设置已导出', 'success');
  }

  /**
   * 导入设置
   */
  async importSettings(file) {
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      if (!data.settings) {
        throw new Error('无效的设置文件格式');
      }

      // 验证设置
      const validSettings = {};
      Object.keys(this.defaultSettings).forEach(key => {
        if (key in data.settings) {
          validSettings[key] = data.settings[key];
        }
      });

      this.settings = { ...this.defaultSettings, ...validSettings };
      await chrome.storage.sync.set(this.settings);
      
      this.renderAllSections();
      this.updateStatus('设置已导入', 'success');
      this.markAsSaved();
    } catch (error) {
      console.error('Error importing settings:', error);
      this.updateStatus('导入失败：' + error.message, 'error');
    }

    // 清除文件输入
    document.getElementById('import-file-input').value = '';
  }

  /**
   * 标记为已更改
   */
  markAsChanged() {
    const saveButton = document.getElementById('save-settings');
    saveButton.style.background = '#f85149';
    saveButton.style.borderColor = '#f85149';
    this.updateStatus('有未保存的更改', 'warning');
  }

  /**
   * 标记为已保存
   */
  markAsSaved() {
    const saveButton = document.getElementById('save-settings');
    saveButton.style.background = '#0969da';
    saveButton.style.borderColor = '#0969da';
  }

  /**
   * 更新状态
   */
  updateStatus(message, type = 'info') {
    const statusText = document.getElementById('status-text');
    statusText.textContent = message;
    
    // 显示通知
    this.showNotification(message, type);
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const icon = document.getElementById('notification-icon');
    const messageEl = document.getElementById('notification-message');

    // 设置图标
    const icons = {
      success: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.061L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
      </svg>`,
      error: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
        <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
      </svg>`,
      warning: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
      </svg>`
    };

    icon.innerHTML = icons[type] || icons.success;
    icon.className = `notification-icon ${type}`;
    messageEl.textContent = message;

    notification.style.display = 'block';
    setTimeout(() => notification.classList.add('show'), 10);

    // 3秒后自动隐藏
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        notification.style.display = 'none';
      }, 300);
    }, 3000);
  }

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, message, onConfirm) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('modal-message').textContent = message;
    document.getElementById('modal-overlay').style.display = 'flex';

    const confirmButton = document.getElementById('modal-confirm');
    const newConfirmButton = confirmButton.cloneNode(true);
    confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

    newConfirmButton.addEventListener('click', () => {
      onConfirm();
      this.hideModal();
    });
  }

  /**
   * 隐藏模态对话框
   */
  hideModal() {
    document.getElementById('modal-overlay').style.display = 'none';
  }

  /**
   * 打开URL
   */
  openUrl(url) {
    chrome.tabs.create({ url });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});