/**
 * Popup Styles - 弹出窗口样式
 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  width: 320px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #24292f;
  background-color: #ffffff;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 暗黑模式样式 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1e1e1e;
    color: #e1e4e8;
  }
  
  .popup-header {
    background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%) !important;
  }
  
  .popup-title {
    color: #ffffff !important;
  }
  
  .popup-settings-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
  }
  
  .popup-settings-btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
  }
  
  .popup-content {
    background: #1e1e1e !important;
  }
  
  .popup-section {
    border-bottom-color: #30363d !important;
  }
  
  .toggle-text {
    color: #e1e4e8 !important;
  }
  
  .toggle-slider {
    background-color: #484f58 !important;
  }
  
  .toggle-switch input:checked + .toggle-slider {
    background-color: #1a73e8 !important;
  }
  
  .section-title {
    color: #e1e4e8 !important;
  }
  
  .action-btn {
    background: #21262d !important;
    border-color: #30363d !important;
    color: #e1e4e8 !important;
  }
  
  .action-btn:hover {
    background: #30363d !important;
    border-color: #484f58 !important;
  }
  
  .status-label {
    color: #8b949e !important;
  }
  
  .status-value {
    color: #e1e4e8 !important;
  }
  
  .popup-footer {
    background: #161b22 !important;
    border-top-color: #30363d !important;
  }
  
  .shortcut-item {
    color: #8b949e !important;
  }
  
  kbd {
    background: #21262d !important;
    border-color: #30363d !important;
    color: #e1e4e8 !important;
  }
}

/* 弹出窗口容器 */
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 头部样式 */
.popup-header {
  background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
  color: #ffffff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.popup-settings-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: #ffffff;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-settings-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* 内容区域 */
.popup-content {
  flex: 1;
  padding: 0;
  background: #ffffff;
}

.popup-section {
  padding: 16px 20px;
  border-bottom: 1px solid #e1e4e8;
}

.popup-section:last-child {
  border-bottom: none;
}

/* 开关控件 */
.popup-toggle {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
}

.toggle-text {
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
}

.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d0d7de;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
  background-color: #1a73e8;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* 区域标题 */
.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #24292f;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 快速操作按钮 */
.popup-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #ffffff;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  color: #24292f;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-btn:hover {
  background: #f6f8fa;
  border-color: #bbb;
  transform: translateY(-1px);
}

.action-btn svg {
  flex-shrink: 0;
}

/* 状态信息 */
.popup-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.status-label {
  color: #656d76;
}

.status-value {
  font-weight: 500;
  color: #24292f;
}

/* 底部区域 */
.popup-footer {
  background: #f6f8fa;
  border-top: 1px solid #e1e4e8;
  padding: 12px 20px;
}

.popup-shortcuts {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 11px;
  color: #656d76;
}

kbd {
  background: #f6f8fa;
  border: 1px solid #d0d7de;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 10px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  color: #24292f;
  margin: 0 1px;
}

/* 响应式设计 */
@media (max-width: 350px) {
  body {
    width: 280px;
  }
  
  .popup-header {
    padding: 12px 16px;
  }
  
  .popup-section {
    padding: 12px 16px;
  }
  
  .popup-footer {
    padding: 10px 16px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .action-btn {
    border-width: 2px;
  }
  
  .toggle-slider {
    border: 2px solid #24292f;
  }
  
  .toggle-switch input:checked + .toggle-slider {
    border-color: #0969da;
  }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}