<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chrome Vertical Sidebar</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <header class="popup-header">
      <h1 class="popup-title">垂直侧边栏</h1>
      <button class="popup-settings-btn" id="settingsBtn" title="设置">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
          <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
        </svg>
      </button>
    </header>

    <main class="popup-content">
      <div class="popup-section">
        <div class="popup-toggle">
          <label class="toggle-label">
            <span class="toggle-text">启用侧边栏</span>
            <div class="toggle-switch">
              <input type="checkbox" id="enableSidebar" checked>
              <span class="toggle-slider"></span>
            </div>
          </label>
        </div>
      </div>

      <div class="popup-section">
        <h3 class="section-title">快速操作</h3>
        <div class="popup-actions">
          <button class="action-btn" id="toggleSidebarBtn">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M1 2.75A.75.75 0 0 1 1.75 2h12.5a.75.75 0 0 1 0 1.5H1.75A.75.75 0 0 1 1 2.75zm0 5A.75.75 0 0 1 1.75 7h12.5a.75.75 0 0 1 0 1.5H1.75A.75.75 0 0 1 1 7.75zM1.75 12a.75.75 0 0 0 0 1.5h12.5a.75.75 0 0 0 0-1.5H1.75z"/>
            </svg>
            <span>切换侧边栏</span>
          </button>
          
          <button class="action-btn" id="pinSidebarBtn">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M4.456.734a1.75 1.75 0 0 1 2.826.504l.613 1.327a3.081 3.081 0 0 0 2.084 1.707l2.454.584c1.332.317 1.8 1.972.832 2.94L11.06 10l3.72 3.72a.75.75 0 1 1-1.061 1.06L10 11.06l-2.204 2.205c-.968.968-2.623.5-2.94-.832l-.584-2.454a3.081 3.081 0 0 0-1.707-2.084l-1.327-.613a1.75 1.75 0 0 1-.504-2.826L4.456.734zM5.92 1.866a.25.25 0 0 0-.072.404l3.899 3.899a.25.25 0 0 0 .404-.072l.001-.001c.05-.13.12-.25.211-.35.23-.23.556-.353.887-.348.33.005.663.137.884.353.22.22.35.553.348.884-.005.33-.117.656-.347.886-.1.092-.22.162-.35.212l-.001.001a.25.25 0 0 0-.072.404l3.899 3.899a.25.25 0 0 0 .404-.072c.13-.05.25-.12.35-.211.23-.23.353-.556.348-.887-.005-.33-.137-.663-.353-.884-.22-.22-.553-.35-.884-.348-.33.005-.656.117-.886.347-.092.1-.162.22-.212.35z"/>
            </svg>
            <span>固定侧边栏</span>
          </button>
        </div>
      </div>

      <div class="popup-section">
        <h3 class="section-title">状态信息</h3>
        <div class="popup-status">
          <div class="status-item">
            <span class="status-label">当前标签页:</span>
            <span class="status-value" id="currentTabCount">-</span>
          </div>
          <div class="status-item">
            <span class="status-label">收藏夹数量:</span>
            <span class="status-value" id="bookmarkCount">-</span>
          </div>
        </div>
      </div>
    </main>

    <footer class="popup-footer">
      <div class="popup-shortcuts">
        <div class="shortcut-item">
          <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>S</kbd>
          <span>切换侧边栏</span>
        </div>
      </div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>