/**
 * User Flow Tester - 用户流程测试框架
 */

import { Logger } from '../core/logging/Logger';
import { PerformanceMonitor } from '../core/logging/PerformanceMonitor';

export interface UserFlow {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'advanced' | 'edge-case' | 'performance' | 'accessibility';
  priority: 'low' | 'medium' | 'high' | 'critical';
  steps: UserFlowStep[];
  preconditions: string[];
  expectedOutcome: string;
  timeout: number;
  retryAttempts: number;
  tags: string[];
}

export interface UserFlowStep {
  id: string;
  name: string;
  description: string;
  action: UserAction;
  validation: ValidationRule[];
  timeout: number;
  optional: boolean;
  screenshot: boolean;
  metrics: string[];
}

export interface UserAction {
  type: 'navigate' | 'click' | 'hover' | 'scroll' | 'type' | 'key' | 'wait' | 'custom';
  target?: string;
  value?: any;
  options?: Record<string, any>;
  customHandler?: (context: TestContext) => Promise<void>;
}

export interface ValidationRule {
  type: 'element' | 'text' | 'attribute' | 'style' | 'count' | 'performance' | 'custom';
  target?: string;
  expected: any;
  operator?: 'equals' | 'contains' | 'greater' | 'less' | 'exists' | 'not-exists';
  customValidator?: (context: TestContext) => Promise<boolean>;
  message?: string;
}

export interface TestContext {
  flow: UserFlow;
  currentStep: UserFlowStep;
  stepIndex: number;
  startTime: number;
  screenshots: string[];
  metrics: Record<string, any>;
  variables: Record<string, any>;
  errors: string[];
  warnings: string[];
}

export interface FlowTestResult {
  flowId: string;
  flowName: string;
  status: 'passed' | 'failed' | 'skipped' | 'timeout';
  duration: number;
  steps: StepResult[];
  screenshots: string[];
  metrics: Record<string, any>;
  errors: string[];
  warnings: string[];
  timestamp: number;
}

export interface StepResult {
  stepId: string;
  stepName: string;
  status: 'passed' | 'failed' | 'skipped' | 'timeout';
  duration: number;
  validations: ValidationResult[];
  screenshot?: string;
  metrics: Record<string, any>;
  error?: string;
}

export interface ValidationResult {
  type: string;
  target?: string;
  expected: any;
  actual: any;
  passed: boolean;
  message: string;
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  flows: UserFlow[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  parallel: boolean;
  timeout: number;
}

export interface SuiteResult {
  suiteId: string;
  suiteName: string;
  status: 'passed' | 'failed' | 'partial';
  duration: number;
  flowResults: FlowTestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
  timestamp: number;
}

/**
 * User Flow Tester
 */
export class UserFlowTester {
  private static instance: UserFlowTester;
  private logger: Logger;
  private performanceMonitor: PerformanceMonitor;
  private flows: Map<string, UserFlow> = new Map();
  private suites: Map<string, TestSuite> = new Map();
  private results: Map<string, FlowTestResult> = new Map();
  private suiteResults: Map<string, SuiteResult> = new Map();
  private isRunning = false;
  private currentContext?: TestContext;

  private constructor() {
    this.logger = Logger.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.initializeDefaultFlows();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): UserFlowTester {
    if (!UserFlowTester.instance) {
      UserFlowTester.instance = new UserFlowTester();
    }
    return UserFlowTester.instance;
  }

  /**
   * Register user flow
   */
  registerFlow(flow: UserFlow): void {
    this.flows.set(flow.id, flow);
    this.logger.debug('userFlow', `Registered flow: ${flow.name}`);
  }

  /**
   * Register test suite
   */
  registerSuite(suite: TestSuite): void {
    this.suites.set(suite.id, suite);
    this.logger.debug('userFlow', `Registered suite: ${suite.name}`);
  }

  /**
   * Run single flow
   */
  async runFlow(flowId: string): Promise<FlowTestResult> {
    const flow = this.flows.get(flowId);
    if (!flow) {
      throw new Error(`Flow not found: ${flowId}`);
    }

    this.logger.info('userFlow', `Starting flow: ${flow.name}`);

    const context: TestContext = {
      flow,
      currentStep: flow.steps[0],
      stepIndex: 0,
      startTime: Date.now(),
      screenshots: [],
      metrics: {},
      variables: {},
      errors: [],
      warnings: []
    };

    this.currentContext = context;

    const result: FlowTestResult = {
      flowId: flow.id,
      flowName: flow.name,
      status: 'passed',
      duration: 0,
      steps: [],
      screenshots: [],
      metrics: {},
      errors: [],
      warnings: [],
      timestamp: Date.now()
    };

    try {
      // Check preconditions
      await this.checkPreconditions(flow, context);

      // Execute steps
      for (let i = 0; i < flow.steps.length; i++) {
        context.stepIndex = i;
        context.currentStep = flow.steps[i];

        const stepResult = await this.executeStep(flow.steps[i], context);
        result.steps.push(stepResult);

        if (stepResult.status === 'failed' && !flow.steps[i].optional) {
          result.status = 'failed';
          break;
        }

        if (stepResult.status === 'timeout') {
          result.status = 'timeout';
          break;
        }
      }

      result.duration = Date.now() - context.startTime;
      result.screenshots = context.screenshots;
      result.metrics = context.metrics;
      result.errors = context.errors;
      result.warnings = context.warnings;

      this.results.set(flowId, result);

      this.logger.info('userFlow', `Flow completed: ${flow.name}`, {
        status: result.status,
        duration: result.duration,
        steps: result.steps.length
      });

    } catch (error) {
      result.status = 'failed';
      result.duration = Date.now() - context.startTime;
      result.errors.push(error instanceof Error ? error.message : String(error));

      this.logger.error('userFlow', `Flow failed: ${flow.name}`, error);
    } finally {
      this.currentContext = undefined;
    }

    return result;
  }

  /**
   * Run test suite
   */
  async runSuite(suiteId: string): Promise<SuiteResult> {
    const suite = this.suites.get(suiteId);
    if (!suite) {
      throw new Error(`Suite not found: ${suiteId}`);
    }

    this.logger.info('userFlow', `Starting suite: ${suite.name}`);

    const result: SuiteResult = {
      suiteId: suite.id,
      suiteName: suite.name,
      status: 'passed',
      duration: 0,
      flowResults: [],
      summary: {
        total: suite.flows.length,
        passed: 0,
        failed: 0,
        skipped: 0
      },
      timestamp: Date.now()
    };

    const startTime = Date.now();

    try {
      // Setup
      if (suite.setup) {
        await suite.setup();
      }

      // Run flows
      if (suite.parallel) {
        const promises = suite.flows.map(flow => this.runFlow(flow.id));
        result.flowResults = await Promise.all(promises);
      } else {
        for (const flow of suite.flows) {
          const flowResult = await this.runFlow(flow.id);
          result.flowResults.push(flowResult);
        }
      }

      // Calculate summary
      result.flowResults.forEach(flowResult => {
        switch (flowResult.status) {
          case 'passed':
            result.summary.passed++;
            break;
          case 'failed':
          case 'timeout':
            result.summary.failed++;
            break;
          case 'skipped':
            result.summary.skipped++;
            break;
        }
      });

      result.status = result.summary.failed > 0 ? 
        (result.summary.passed > 0 ? 'partial' : 'failed') : 'passed';

      result.duration = Date.now() - startTime;

      // Teardown
      if (suite.teardown) {
        await suite.teardown();
      }

      this.suiteResults.set(suiteId, result);

      this.logger.info('userFlow', `Suite completed: ${suite.name}`, {
        status: result.status,
        duration: result.duration,
        summary: result.summary
      });

    } catch (error) {
      result.status = 'failed';
      result.duration = Date.now() - startTime;

      this.logger.error('userFlow', `Suite failed: ${suite.name}`, error);
    }

    return result;
  }

  /**
   * Get flow result
   */
  getFlowResult(flowId: string): FlowTestResult | null {
    return this.results.get(flowId) || null;
  }

  /**
   * Get suite result
   */
  getSuiteResult(suiteId: string): SuiteResult | null {
    return this.suiteResults.get(suiteId) || null;
  }

  /**
   * Get all flows
   */
  getAllFlows(): UserFlow[] {
    return Array.from(this.flows.values());
  }

  /**
   * Get all suites
   */
  getAllSuites(): TestSuite[] {
    return Array.from(this.suites.values());
  }

  /**
   * Get flows by category
   */
  getFlowsByCategory(category: UserFlow['category']): UserFlow[] {
    return Array.from(this.flows.values()).filter(flow => flow.category === category);
  }

  /**
   * Get flows by tags
   */
  getFlowsByTags(tags: string[]): UserFlow[] {
    return Array.from(this.flows.values()).filter(flow => 
      tags.some(tag => flow.tags.includes(tag))
    );
  }

  /**
   * Check preconditions
   */
  private async checkPreconditions(flow: UserFlow, context: TestContext): Promise<void> {
    for (const precondition of flow.preconditions) {
      // Simple precondition checking - can be extended
      if (precondition.includes('sidebar')) {
        const sidebarExists = document.querySelector('.vertical-sidebar');
        if (!sidebarExists) {
          throw new Error(`Precondition failed: ${precondition}`);
        }
      }
    }
  }

  /**
   * Execute step
   */
  private async executeStep(step: UserFlowStep, context: TestContext): Promise<StepResult> {
    this.logger.debug('userFlow', `Executing step: ${step.name}`);

    const stepResult: StepResult = {
      stepId: step.id,
      stepName: step.name,
      status: 'passed',
      duration: 0,
      validations: [],
      metrics: {},
    };

    const stepStartTime = Date.now();

    try {
      // Execute action
      await this.executeAction(step.action, context);

      // Take screenshot if requested
      if (step.screenshot) {
        const screenshot = await this.takeScreenshot();
        stepResult.screenshot = screenshot;
        context.screenshots.push(screenshot);
      }

      // Collect metrics
      if (step.metrics.length > 0) {
        stepResult.metrics = await this.collectMetrics(step.metrics);
        Object.assign(context.metrics, stepResult.metrics);
      }

      // Run validations
      for (const validation of step.validation) {
        const validationResult = await this.runValidation(validation, context);
        stepResult.validations.push(validationResult);

        if (!validationResult.passed) {
          stepResult.status = 'failed';
          stepResult.error = validationResult.message;
        }
      }

      stepResult.duration = Date.now() - stepStartTime;

    } catch (error) {
      stepResult.status = 'failed';
      stepResult.duration = Date.now() - stepStartTime;
      stepResult.error = error instanceof Error ? error.message : String(error);

      context.errors.push(stepResult.error);
    }

    return stepResult;
  }

  /**
   * Execute action
   */
  private async executeAction(action: UserAction, context: TestContext): Promise<void> {
    switch (action.type) {
      case 'navigate':
        if (action.value) {
          window.location.href = action.value;
          await this.waitForPageLoad();
        }
        break;

      case 'click':
        if (action.target) {
          const element = document.querySelector(action.target);
          if (element) {
            (element as HTMLElement).click();
            await this.wait(100); // Small delay after click
          } else {
            throw new Error(`Click target not found: ${action.target}`);
          }
        }
        break;

      case 'hover':
        if (action.target) {
          const element = document.querySelector(action.target);
          if (element) {
            const event = new MouseEvent('mouseover', { bubbles: true });
            element.dispatchEvent(event);
            await this.wait(100);
          } else {
            throw new Error(`Hover target not found: ${action.target}`);
          }
        }
        break;

      case 'scroll':
        if (action.value) {
          window.scrollTo(0, action.value);
          await this.wait(200);
        }
        break;

      case 'type':
        if (action.target && action.value) {
          const element = document.querySelector(action.target) as HTMLInputElement;
          if (element) {
            element.value = action.value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.wait(100);
          } else {
            throw new Error(`Type target not found: ${action.target}`);
          }
        }
        break;

      case 'key':
        if (action.value) {
          const event = new KeyboardEvent('keydown', { 
            key: action.value,
            bubbles: true 
          });
          document.dispatchEvent(event);
          await this.wait(100);
        }
        break;

      case 'wait':
        if (action.value) {
          await this.wait(action.value);
        }
        break;

      case 'custom':
        if (action.customHandler) {
          await action.customHandler(context);
        }
        break;
    }
  }

  /**
   * Run validation
   */
  private async runValidation(validation: ValidationRule, context: TestContext): Promise<ValidationResult> {
    const result: ValidationResult = {
      type: validation.type,
      target: validation.target,
      expected: validation.expected,
      actual: null,
      passed: false,
      message: validation.message || 'Validation failed'
    };

    try {
      switch (validation.type) {
        case 'element':
          if (validation.target) {
            const element = document.querySelector(validation.target);
            result.actual = element !== null;
            
            switch (validation.operator) {
              case 'exists':
                result.passed = element !== null;
                break;
              case 'not-exists':
                result.passed = element === null;
                break;
              default:
                result.passed = (element !== null) === validation.expected;
            }
          }
          break;

        case 'text':
          if (validation.target) {
            const element = document.querySelector(validation.target);
            result.actual = element?.textContent || '';
            
            switch (validation.operator) {
              case 'contains':
                result.passed = result.actual.includes(validation.expected);
                break;
              case 'equals':
              default:
                result.passed = result.actual === validation.expected;
            }
          }
          break;

        case 'attribute':
          if (validation.target && validation.options?.attribute) {
            const element = document.querySelector(validation.target);
            result.actual = element?.getAttribute(validation.options.attribute);
            result.passed = result.actual === validation.expected;
          }
          break;

        case 'style':
          if (validation.target && validation.options?.property) {
            const element = document.querySelector(validation.target);
            if (element) {
              const computedStyle = window.getComputedStyle(element);
              result.actual = computedStyle.getPropertyValue(validation.options.property);
              result.passed = result.actual === validation.expected;
            }
          }
          break;

        case 'count':
          if (validation.target) {
            const elements = document.querySelectorAll(validation.target);
            result.actual = elements.length;
            
            switch (validation.operator) {
              case 'greater':
                result.passed = result.actual > validation.expected;
                break;
              case 'less':
                result.passed = result.actual < validation.expected;
                break;
              case 'equals':
              default:
                result.passed = result.actual === validation.expected;
            }
          }
          break;

        case 'performance':
          if (validation.options?.metric) {
            const metricValue = context.metrics[validation.options.metric];
            result.actual = metricValue;
            
            switch (validation.operator) {
              case 'less':
                result.passed = metricValue < validation.expected;
                break;
              case 'greater':
                result.passed = metricValue > validation.expected;
                break;
              default:
                result.passed = metricValue === validation.expected;
            }
          }
          break;

        case 'custom':
          if (validation.customValidator) {
            result.passed = await validation.customValidator(context);
            result.actual = result.passed;
          }
          break;
      }

      if (result.passed) {
        result.message = validation.message || 'Validation passed';
      } else {
        result.message = validation.message || 
          `Expected ${validation.expected}, got ${result.actual}`;
      }

    } catch (error) {
      result.passed = false;
      result.message = `Validation error: ${error instanceof Error ? error.message : String(error)}`;
    }

    return result;
  }

  /**
   * Take screenshot
   */
  private async takeScreenshot(): Promise<string> {
    // This is a placeholder for screenshot functionality
    // In a real implementation, this would capture the current page state
    return `screenshot_${Date.now()}.png`;
  }

  /**
   * Collect metrics
   */
  private async collectMetrics(metricNames: string[]): Promise<Record<string, any>> {
    const metrics: Record<string, any> = {};

    for (const metricName of metricNames) {
      switch (metricName) {
        case 'loadTime':
          metrics.loadTime = performance.now();
          break;
        case 'memoryUsage':
          if ('memory' in performance) {
            metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
          }
          break;
        case 'elementCount':
          metrics.elementCount = document.querySelectorAll('*').length;
          break;
        case 'sidebarWidth':
          const sidebar = document.querySelector('.vertical-sidebar') as HTMLElement;
          metrics.sidebarWidth = sidebar ? sidebar.offsetWidth : 0;
          break;
        case 'scrollPosition':
          metrics.scrollPosition = window.scrollY;
          break;
      }
    }

    return metrics;
  }

  /**
   * Wait for page load
   */
  private async waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', () => resolve(), { once: true });
      }
    });
  }

  /**
   * Wait utility
   */
  private async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Initialize default flows
   */
  private initializeDefaultFlows(): void {
    // Basic sidebar functionality flow
    this.registerFlow({
      id: 'basic-sidebar-functionality',
      name: 'Basic Sidebar Functionality',
      description: 'Test basic sidebar show/hide and navigation functionality',
      category: 'basic',
      priority: 'critical',
      preconditions: ['Page is loaded', 'Extension is active'],
      expectedOutcome: 'Sidebar should show/hide correctly and allow navigation',
      timeout: 30000,
      retryAttempts: 2,
      tags: ['sidebar', 'basic', 'navigation'],
      steps: [
        {
          id: 'hover-trigger',
          name: 'Hover to show sidebar',
          description: 'Hover over the left edge to trigger sidebar',
          action: {
            type: 'hover',
            target: 'body',
            options: { x: 0, y: 100 }
          },
          validation: [
            {
              type: 'element',
              target: '.vertical-sidebar',
              expected: true,
              operator: 'exists',
              message: 'Sidebar should appear on hover'
            }
          ],
          timeout: 5000,
          optional: false,
          screenshot: true,
          metrics: ['loadTime', 'sidebarWidth']
        },
        {
          id: 'check-tabs',
          name: 'Check tabs display',
          description: 'Verify that tabs are displayed in sidebar',
          action: {
            type: 'wait',
            value: 1000
          },
          validation: [
            {
              type: 'count',
              target: '.tab-item',
              expected: 1,
              operator: 'greater',
              message: 'At least one tab should be displayed'
            }
          ],
          timeout: 3000,
          optional: false,
          screenshot: false,
          metrics: ['elementCount']
        },
        {
          id: 'click-tab',
          name: 'Click on tab',
          description: 'Click on a tab to switch to it',
          action: {
            type: 'click',
            target: '.tab-item:first-child'
          },
          validation: [
            {
              type: 'custom',
              expected: true,
              customValidator: async (context) => {
                // Check if tab switching worked
                await new Promise(resolve => setTimeout(resolve, 500));
                return true; // Simplified validation
              },
              message: 'Tab should be activated'
            }
          ],
          timeout: 3000,
          optional: false,
          screenshot: true,
          metrics: []
        }
      ]
    });

    // Bookmark functionality flow
    this.registerFlow({
      id: 'bookmark-functionality',
      name: 'Bookmark Functionality',
      description: 'Test bookmark display and interaction',
      category: 'basic',
      priority: 'high',
      preconditions: ['Sidebar is visible', 'User has bookmarks'],
      expectedOutcome: 'Bookmarks should be displayed and clickable',
      timeout: 20000,
      retryAttempts: 1,
      tags: ['bookmarks', 'navigation'],
      steps: [
        {
          id: 'show-bookmarks',
          name: 'Show bookmarks section',
          description: 'Navigate to bookmarks section in sidebar',
          action: {
            type: 'click',
            target: '.bookmarks-tab'
          },
          validation: [
            {
              type: 'element',
              target: '.bookmarks-section',
              expected: true,
              operator: 'exists',
              message: 'Bookmarks section should be visible'
            }
          ],
          timeout: 3000,
          optional: false,
          screenshot: true,
          metrics: []
        },
        {
          id: 'check-bookmark-items',
          name: 'Check bookmark items',
          description: 'Verify bookmark items are displayed',
          action: {
            type: 'wait',
            value: 500
          },
          validation: [
            {
              type: 'count',
              target: '.bookmark-item',
              expected: 0,
              operator: 'greater',
              message: 'Bookmark items should be displayed'
            }
          ],
          timeout: 3000,
          optional: true,
          screenshot: false,
          metrics: ['elementCount']
        }
      ]
    });

    // Performance flow
    this.registerFlow({
      id: 'performance-test',
      name: 'Performance Test',
      description: 'Test sidebar performance with many tabs',
      category: 'performance',
      priority: 'medium',
      preconditions: ['Multiple tabs are open'],
      expectedOutcome: 'Sidebar should perform well with many tabs',
      timeout: 15000,
      retryAttempts: 1,
      tags: ['performance', 'stress-test'],
      steps: [
        {
          id: 'measure-render-time',
          name: 'Measure render time',
          description: 'Measure time to render sidebar with many tabs',
          action: {
            type: 'hover',
            target: 'body',
            options: { x: 0, y: 100 }
          },
          validation: [
            {
              type: 'performance',
              expected: 1000,
              operator: 'less',
              options: { metric: 'loadTime' },
              message: 'Sidebar should render in less than 1 second'
            }
          ],
          timeout: 5000,
          optional: false,
          screenshot: false,
          metrics: ['loadTime', 'memoryUsage', 'elementCount']
        }
      ]
    });

    // Accessibility flow
    this.registerFlow({
      id: 'accessibility-test',
      name: 'Accessibility Test',
      description: 'Test sidebar accessibility features',
      category: 'accessibility',
      priority: 'high',
      preconditions: ['Sidebar is available'],
      expectedOutcome: 'Sidebar should be accessible via keyboard',
      timeout: 10000,
      retryAttempts: 1,
      tags: ['accessibility', 'keyboard'],
      steps: [
        {
          id: 'keyboard-navigation',
          name: 'Keyboard navigation',
          description: 'Test keyboard navigation in sidebar',
          action: {
            type: 'key',
            value: 'Tab'
          },
          validation: [
            {
              type: 'custom',
              expected: true,
              customValidator: async (context) => {
                // Check if focus is properly managed
                const focusedElement = document.activeElement;
                return focusedElement !== null && focusedElement !== document.body;
              },
              message: 'Focus should be properly managed'
            }
          ],
          timeout: 3000,
          optional: false,
          screenshot: false,
          metrics: []
        }
      ]
    });

    // Create basic test suite
    this.registerSuite({
      id: 'basic-functionality-suite',
      name: 'Basic Functionality Test Suite',
      description: 'Test suite covering basic sidebar functionality',
      flows: [
        this.flows.get('basic-sidebar-functionality')!,
        this.flows.get('bookmark-functionality')!
      ],
      parallel: false,
      timeout: 60000
    });

    // Create comprehensive test suite
    this.registerSuite({
      id: 'comprehensive-test-suite',
      name: 'Comprehensive Test Suite',
      description: 'Complete test suite covering all functionality',
      flows: Array.from(this.flows.values()),
      parallel: false,
      timeout: 120000
    });
  }
}

// Export convenience functions
export const userFlowTester = UserFlowTester.getInstance();

export function runUserFlow(flowId: string): Promise<FlowTestResult> {
  return userFlowTester.runFlow(flowId);
}

export function runTestSuite(suiteId: string): Promise<SuiteResult> {
  return userFlowTester.runSuite(suiteId);
}

export function registerUserFlow(flow: UserFlow): void {
  userFlowTester.registerFlow(flow);
}

export function registerTestSuite(suite: TestSuite): void {
  userFlowTester.registerSuite(suite);
}