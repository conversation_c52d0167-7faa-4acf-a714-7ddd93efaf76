/**
 * 跨组件交互集成测试
 * 测试不同组件之间的协作和数据流
 */

import { SidebarManager } from '../../core/SidebarManager'
import { BackgroundService } from '../../background/BackgroundService'
import { TabManager } from '../../core/TabManager'
import { BookmarkManager } from '../../core/BookmarkManager'
import { SettingsManager } from '../../core/SettingsManager'
import { SearchManager } from '../../core/SearchManager'
import { KeyboardManager } from '../../core/KeyboardManager'

interface ComponentTestContext {
  components: {
    sidebar: SidebarManager
    background: BackgroundService
    tabs: TabManager
    bookmarks: BookmarkManager
    settings: SettingsManager
    search: SearchManager
    keyboard: KeyboardManager
  }
  messageQueue: Message[]
  eventLog: ComponentEvent[]
}

interface Message {
  type: string
  source: string
  target: string
  payload: any
  timestamp: number
}

interface ComponentEvent {
  component: string
  event: string
  data: any
  timestamp: number
}

class CrossComponentTestSuite {
  private context: ComponentTestContext
  private testResults: Map<string, TestResult> = new Map()

  constructor() {
    this.setupTestContext()
  }

  private setupTestContext(): void {
    // 初始化所有组件
    this.context = {
      components: {
        sidebar: new SidebarManager(),
        background: new BackgroundService(),
        tabs: new TabManager(),
        bookmarks: new BookmarkManager(),
        settings: new SettingsManager(),
        search: new SearchManager(),
        keyboard: new KeyboardManager()
      },
      messageQueue: [],
      eventLog: []
    }

    // 设置消息拦截
    this.setupMessageInterception()
    
    // 设置事件监听
    this.setupEventLogging()
  }

  private setupMessageInterception(): void {
    // 拦截Chrome消息传递
    const originalSendMessage = chrome.runtime.sendMessage
    chrome.runtime.sendMessage = jest.fn().mockImplementation((message, callback) => {
      this.context.messageQueue.push({
        type: message.type,
        source: 'content',
        target: 'background',
        payload: message.payload,
        timestamp: Date.now()
      })
      
      // 模拟异步响应
      setTimeout(() => {
        if (callback) {
          callback(this.generateMockResponse(message))
        }
      }, 10)
    })
  }

  private setupEventLogging(): void {
    // 为每个组件设置事件监听
    Object.entries(this.context.components).forEach(([name, component]) => {
      if (component.on) {
        // 监听所有可能的事件
        const events = ['stateChange', 'dataUpdate', 'userAction', 'error']
        events.forEach(eventType => {
          component.on(eventType, (data: any) => {
            this.context.eventLog.push({
              component: name,
              event: eventType,
              data,
              timestamp: Date.now()
            })
          })
        })
      }
    })
  }

  private generateMockResponse(message: any): any {
    switch (message.type) {
      case 'GET_TABS':
        return { success: true, data: this.getMockTabs() }
      case 'GET_BOOKMARKS':
        return { success: true, data: this.getMockBookmarks() }
      case 'GET_SETTINGS':
        return { success: true, data: this.getMockSettings() }
      default:
        return { success: true, data: null }
    }
  }

  /**
   * 测试1：侧边栏与背景脚本的通信
   */
  async testSidebarBackgroundCommunication(): Promise<TestResult> {
    const testName = 'SidebarBackgroundCommunication'
    console.log(`开始测试: ${testName}`)

    try {
      const { sidebar, background } = this.context.components

      // 1. 初始化侧边栏
      await sidebar.initialize()

      // 验证初始化消息发送
      const initMessages = this.context.messageQueue.filter(m => 
        m.type === 'SIDEBAR_INITIALIZED'
      )
      expect(initMessages.length).toBe(1)

      // 2. 请求标签页数据
      await sidebar.requestTabsData()

      // 验证数据请求消息
      const tabsMessages = this.context.messageQueue.filter(m => 
        m.type === 'GET_TABS'
      )
      expect(tabsMessages.length).toBe(1)

      // 3. 模拟背景脚本响应
      const mockTabs = this.getMockTabs()
      await sidebar.updateTabs(mockTabs)

      // 验证UI更新
      const tabElements = document.querySelectorAll('.tab-item')
      expect(tabElements.length).toBe(mockTabs.length)

      // 4. 测试双向通信
      await sidebar.switchToTab(1)

      // 验证标签页切换消息
      const switchMessages = this.context.messageQueue.filter(m => 
        m.type === 'SWITCH_TAB' && m.payload.tabId === 1
      )
      expect(switchMessages.length).toBe(1)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试2：设置变更的跨组件同步
   */
  async testSettingsSynchronization(): Promise<TestResult> {
    const testName = 'SettingsSynchronization'
    console.log(`开始测试: ${testName}`)

    try {
      const { sidebar, settings, keyboard } = this.context.components

      // 1. 初始化组件
      await Promise.all([
        sidebar.initialize(),
        settings.initialize(),
        keyboard.initialize()
      ])

      // 2. 更改主题设置
      await settings.updateSettings({ theme: 'dark' })

      // 等待设置同步
      await this.waitForAsync(100)

      // 验证侧边栏主题更新
      const sidebarElement = document.querySelector('.vertical-sidebar')
      expect(sidebarElement?.classList.contains('theme-dark')).toBe(true)

      // 3. 更改快捷键设置
      await settings.updateSettings({
        keyboardShortcuts: {
          toggle: 'Ctrl+Alt+S',
          search: 'Ctrl+Shift+F'
        }
      })

      // 等待快捷键更新
      await this.waitForAsync(100)

      // 验证键盘管理器更新
      const keyboardManager = keyboard as any
      expect(keyboardManager.shortcuts.toggle).toBe('Ctrl+Alt+S')
      expect(keyboardManager.shortcuts.search).toBe('Ctrl+Shift+F')

      // 4. 测试位置设置同步
      await settings.updateSettings({ position: 'right' })
      await this.waitForAsync(100)

      // 验证侧边栏位置更新
      expect(sidebarElement?.classList.contains('position-right')).toBe(true)

      // 5. 验证事件日志
      const settingsEvents = this.context.eventLog.filter(e => 
        e.component === 'settings' && e.event === 'stateChange'
      )
      expect(settingsEvents.length).toBeGreaterThan(0)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试3：搜索功能的跨组件协作
   */
  async testSearchComponentIntegration(): Promise<TestResult> {
    const testName = 'SearchComponentIntegration'
    console.log(`开始测试: ${testName}`)

    try {
      const { sidebar, search, tabs, bookmarks } = this.context.components

      // 1. 初始化组件和数据
      await Promise.all([
        sidebar.initialize(),
        search.initialize(),
        tabs.initialize(),
        bookmarks.initialize()
      ])

      // 加载测试数据
      const mockTabs = this.getMockTabs()
      const mockBookmarks = this.getMockBookmarks()
      
      await tabs.updateTabs(mockTabs)
      await bookmarks.updateBookmarks(mockBookmarks)

      // 2. 执行搜索
      const searchQuery = 'github'
      await search.performSearch(searchQuery)

      // 验证搜索结果
      const searchResults = await search.getResults()
      expect(searchResults.tabs.length).toBeGreaterThan(0)
      expect(searchResults.bookmarks.length).toBeGreaterThan(0)

      // 3. 验证UI过滤
      await sidebar.applySearchFilter(searchResults)

      // 检查过滤后的显示
      const visibleTabs = document.querySelectorAll('.tab-item:not(.hidden)')
      const visibleBookmarks = document.querySelectorAll('.bookmark-item:not(.hidden)')
      
      expect(visibleTabs.length).toBe(searchResults.tabs.length)
      expect(visibleBookmarks.length).toBe(searchResults.bookmarks.length)

      // 4. 测试搜索清除
      await search.clearSearch()
      await sidebar.clearSearchFilter()

      // 验证所有项目重新显示
      const allTabs = document.querySelectorAll('.tab-item')
      const allBookmarks = document.querySelectorAll('.bookmark-item')
      
      allTabs.forEach(tab => {
        expect(tab.classList.contains('hidden')).toBe(false)
      })
      allBookmarks.forEach(bookmark => {
        expect(bookmark.classList.contains('hidden')).toBe(false)
      })

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试4：键盘快捷键的全局协调
   */
  async testKeyboardShortcutCoordination(): Promise<TestResult> {
    const testName = 'KeyboardShortcutCoordination'
    console.log(`开始测试: ${testName}`)

    try {
      const { sidebar, keyboard, search } = this.context.components

      // 1. 初始化组件
      await Promise.all([
        sidebar.initialize(),
        keyboard.initialize(),
        search.initialize()
      ])

      // 2. 测试侧边栏切换快捷键
      this.simulateKeyboardShortcut('KeyS', { ctrlKey: true, shiftKey: true })
      await this.waitForAsync(100)

      // 验证侧边栏状态变化
      const toggleEvents = this.context.eventLog.filter(e => 
        e.component === 'sidebar' && e.event === 'stateChange'
      )
      expect(toggleEvents.length).toBeGreaterThan(0)

      // 3. 测试搜索快捷键
      this.simulateKeyboardShortcut('KeyF', { ctrlKey: true })
      await this.waitForAsync(100)

      // 验证搜索框获得焦点
      const searchBox = document.querySelector('.search-box input') as HTMLInputElement
      expect(document.activeElement).toBe(searchBox)

      // 4. 测试导航快捷键
      this.simulateKeyboardShortcut('ArrowDown')
      await this.waitForAsync(100)

      // 验证焦点移动
      const focusedItem = document.querySelector('.tab-item.focused, .bookmark-item.focused')
      expect(focusedItem).toBeTruthy()

      // 5. 测试快捷键冲突处理
      // 同时按下多个快捷键
      this.simulateKeyboardShortcut('KeyS', { ctrlKey: true })
      this.simulateKeyboardShortcut('KeyF', { ctrlKey: true })
      await this.waitForAsync(100)

      // 验证只有一个操作生效
      const keyboardEvents = this.context.eventLog.filter(e => 
        e.component === 'keyboard'
      )
      expect(keyboardEvents.length).toBeGreaterThan(0)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试5：错误传播和恢复机制
   */
  async testErrorPropagationAndRecovery(): Promise<TestResult> {
    const testName = 'ErrorPropagationAndRecovery'
    console.log(`开始测试: ${testName}`)

    try {
      const { sidebar, background, tabs } = this.context.components

      // 1. 初始化组件
      await sidebar.initialize()

      // 2. 模拟Chrome API错误
      chrome.tabs.query = jest.fn().mockRejectedValue(new Error('API Error'))

      // 3. 尝试获取标签页数据
      try {
        await tabs.getAllTabs()
      } catch (error) {
        // 预期的错误
      }

      // 验证错误事件记录
      const errorEvents = this.context.eventLog.filter(e => 
        e.event === 'error'
      )
      expect(errorEvents.length).toBeGreaterThan(0)

      // 4. 测试错误恢复
      // 恢复API功能
      chrome.tabs.query = jest.fn().mockResolvedValue(this.getMockTabs())

      // 重试操作
      const tabs_data = await tabs.getAllTabs()
      expect(tabs_data).toBeDefined()
      expect(tabs_data.length).toBeGreaterThan(0)

      // 5. 验证UI状态恢复
      await sidebar.updateTabs(tabs_data)
      const tabElements = document.querySelectorAll('.tab-item')
      expect(tabElements.length).toBe(tabs_data.length)

      // 6. 验证错误清除
      const errorIndicators = document.querySelectorAll('.error-indicator')
      expect(errorIndicators.length).toBe(0)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  // 辅助方法
  private getMockTabs(): chrome.tabs.Tab[] {
    return [
      {
        id: 1,
        title: 'GitHub',
        url: 'https://github.com',
        active: true,
        pinned: false,
        index: 0,
        windowId: 1,
        highlighted: true,
        incognito: false,
        selected: true,
        discarded: false,
        autoDiscardable: true,
        groupId: -1
      },
      {
        id: 2,
        title: 'Google',
        url: 'https://google.com',
        active: false,
        pinned: false,
        index: 1,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: -1
      }
    ]
  }

  private getMockBookmarks(): chrome.bookmarks.BookmarkTreeNode[] {
    return [
      {
        id: '1',
        title: 'GitHub',
        url: 'https://github.com'
      },
      {
        id: '2',
        title: 'Development',
        children: [
          {
            id: '3',
            title: 'Stack Overflow',
            url: 'https://stackoverflow.com'
          }
        ]
      }
    ]
  }

  private getMockSettings(): any {
    return {
      theme: 'light',
      position: 'left',
      expandDelay: 500,
      collapseDelay: 500,
      keyboardShortcuts: {
        toggle: 'Ctrl+Shift+S',
        search: 'Ctrl+F'
      }
    }
  }

  private async waitForAsync(duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration))
  }

  private simulateKeyboardShortcut(code: string, modifiers: any = {}): void {
    const event = new KeyboardEvent('keydown', {
      code,
      bubbles: true,
      ...modifiers
    })
    document.dispatchEvent(event)
  }

  /**
   * 运行所有跨组件测试
   */
  async runAllTests(): Promise<TestSummary> {
    console.log('开始运行跨组件集成测试套件...')
    const startTime = Date.now()

    const tests = [
      () => this.testSidebarBackgroundCommunication(),
      () => this.testSettingsSynchronization(),
      () => this.testSearchComponentIntegration(),
      () => this.testKeyboardShortcutCoordination(),
      () => this.testErrorPropagationAndRecovery()
    ]

    const results: TestResult[] = []

    for (const test of tests) {
      try {
        const result = await test()
        results.push(result)
        this.testResults.set(result.name, result)
      } catch (error) {
        results.push({
          name: 'UnknownTest',
          success: false,
          error: error.message,
          duration: 0
        })
      }
    }

    const endTime = Date.now()
    const summary: TestSummary = {
      totalTests: results.length,
      passedTests: results.filter(r => r.success).length,
      failedTests: results.filter(r => !r.success).length,
      totalDuration: endTime - startTime,
      results
    }

    console.log('跨组件集成测试完成:', summary)
    return summary
  }

  /**
   * 获取消息队列分析
   */
  getMessageQueueAnalysis(): MessageAnalysis {
    const messageTypes = new Map<string, number>()
    const messageFlow = new Map<string, number>()

    this.context.messageQueue.forEach(message => {
      // 统计消息类型
      const count = messageTypes.get(message.type) || 0
      messageTypes.set(message.type, count + 1)

      // 统计消息流向
      const flow = `${message.source}->${message.target}`
      const flowCount = messageFlow.get(flow) || 0
      messageFlow.set(flow, flowCount + 1)
    })

    return {
      totalMessages: this.context.messageQueue.length,
      messageTypes: Object.fromEntries(messageTypes),
      messageFlow: Object.fromEntries(messageFlow),
      averageResponseTime: this.calculateAverageResponseTime()
    }
  }

  private calculateAverageResponseTime(): number {
    // 简化的响应时间计算
    return 50 // 毫秒
  }
}

interface TestResult {
  name: string
  success: boolean
  error?: string
  duration: number
}

interface TestSummary {
  totalTests: number
  passedTests: number
  failedTests: number
  totalDuration: number
  results: TestResult[]
}

interface MessageAnalysis {
  totalMessages: number
  messageTypes: Record<string, number>
  messageFlow: Record<string, number>
  averageResponseTime: number
}

export { CrossComponentTestSuite, TestResult, TestSummary, MessageAnalysis }