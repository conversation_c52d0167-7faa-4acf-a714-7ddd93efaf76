/**
 * 数据一致性和状态同步验证测试
 * 确保跨组件和跨标签页的数据一致性
 */

import { SidebarManager } from '../../core/SidebarManager'
import { BackgroundService } from '../../background/BackgroundService'
import { SettingsManager } from '../../core/SettingsManager'
import { TabManager } from '../../core/TabManager'
import { BookmarkManager } from '../../core/BookmarkManager'

interface DataState {
  tabs: chrome.tabs.Tab[]
  bookmarks: chrome.bookmarks.BookmarkTreeNode[]
  settings: UserSettings
  sidebarState: SidebarState
  timestamp: number
}

interface SidebarState {
  visible: boolean
  expanded: boolean
  pinned: boolean
  activeSection: string
  searchQuery: string
}

interface UserSettings {
  theme: string
  position: string
  expandDelay: number
  collapseDelay: number
  keyboardShortcuts: Record<string, string>
}

interface ConsistencyTestContext {
  instances: {
    primary: ComponentSet
    secondary: ComponentSet
  }
  dataSnapshots: Map<string, DataState>
  syncEvents: SyncEvent[]
}

interface ComponentSet {
  sidebar: SidebarManager
  background: BackgroundService
  settings: SettingsManager
  tabs: TabManager
  bookmarks: BookmarkManager
}

interface SyncEvent {
  type: string
  source: string
  target: string
  data: any
  timestamp: number
  success: boolean
}

class DataConsistencyTestSuite {
  private context: ConsistencyTestContext
  private testResults: Map<string, TestResult> = new Map()

  constructor() {
    this.setupTestContext()
  }

  private setupTestContext(): void {
    // 创建两个独立的组件实例来模拟多标签页环境
    this.context = {
      instances: {
        primary: this.createComponentSet('primary'),
        secondary: this.createComponentSet('secondary')
      },
      dataSnapshots: new Map(),
      syncEvents: []
    }

    // 设置同步事件监听
    this.setupSyncEventLogging()
  }

  private createComponentSet(instanceId: string): ComponentSet {
    return {
      sidebar: new SidebarManager(),
      background: new BackgroundService(),
      settings: new SettingsManager(),
      tabs: new TabManager(),
      bookmarks: new BookmarkManager()
    }
  }

  private setupSyncEventLogging(): void {
    // 拦截Chrome存储API调用
    const originalStorageSet = chrome.storage.sync.set
    const originalStorageGet = chrome.storage.sync.get

    chrome.storage.sync.set = jest.fn().mockImplementation((items, callback) => {
      this.context.syncEvents.push({
        type: 'STORAGE_SET',
        source: 'component',
        target: 'storage',
        data: items,
        timestamp: Date.now(),
        success: true
      })

      if (callback) callback()
      return Promise.resolve()
    })

    chrome.storage.sync.get = jest.fn().mockImplementation((keys, callback) => {
      const mockData = this.generateMockStorageData()
      
      this.context.syncEvents.push({
        type: 'STORAGE_GET',
        source: 'storage',
        target: 'component',
        data: mockData,
        timestamp: Date.now(),
        success: true
      })

      if (callback) callback(mockData)
      return Promise.resolve(mockData)
    })
  }

  private generateMockStorageData(): any {
    return {
      settings: {
        theme: 'light',
        position: 'left',
        expandDelay: 500,
        collapseDelay: 500,
        keyboardShortcuts: {
          toggle: 'Ctrl+Shift+S',
          search: 'Ctrl+F'
        }
      },
      sidebarState: {
        visible: true,
        expanded: false,
        pinned: false,
        activeSection: 'tabs',
        searchQuery: ''
      }
    }
  }

  /**
   * 测试1：设置数据的跨实例同步
   */
  async testSettingsDataSync(): Promise<TestResult> {
    const testName = 'SettingsDataSync'
    console.log(`开始测试: ${testName}`)

    try {
      const { primary, secondary } = this.context.instances

      // 1. 初始化两个实例
      await Promise.all([
        primary.settings.initialize(),
        secondary.settings.initialize()
      ])

      // 2. 在主实例中更新设置
      const newSettings = {
        theme: 'dark',
        position: 'right',
        expandDelay: 1000
      }

      await primary.settings.updateSettings(newSettings)

      // 3. 等待同步传播
      await this.waitForSync(500)

      // 4. 验证次实例的设置同步
      const secondarySettings = await secondary.settings.getSettings()
      
      expect(secondarySettings.theme).toBe('dark')
      expect(secondarySettings.position).toBe('right')
      expect(secondarySettings.expandDelay).toBe(1000)

      // 5. 验证存储事件记录
      const storageEvents = this.context.syncEvents.filter(e => 
        e.type === 'STORAGE_SET' && e.data.settings
      )
      expect(storageEvents.length).toBeGreaterThan(0)

      // 6. 创建数据快照
      await this.createDataSnapshot('after_settings_sync')

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试2：侧边栏状态的一致性维护
   */
  async testSidebarStateConsistency(): Promise<TestResult> {
    const testName = 'SidebarStateConsistency'
    console.log(`开始测试: ${testName}`)

    try {
      const { primary, secondary } = this.context.instances

      // 1. 初始化侧边栏实例
      await Promise.all([
        primary.sidebar.initialize(),
        secondary.sidebar.initialize()
      ])

      // 2. 在主实例中改变状态
      primary.sidebar.show()
      primary.sidebar.expand()
      primary.sidebar.pin()

      // 3. 等待状态同步
      await this.waitForSync(300)

      // 4. 验证次实例状态同步
      expect(secondary.sidebar.isVisible()).toBe(true)
      expect(secondary.sidebar.isExpanded()).toBe(true)
      expect(secondary.sidebar.isPinned()).toBe(true)

      // 5. 测试反向同步
      secondary.sidebar.unpin()
      secondary.sidebar.collapse()

      await this.waitForSync(300)

      // 验证主实例状态更新
      expect(primary.sidebar.isPinned()).toBe(false)
      expect(primary.sidebar.isExpanded()).toBe(false)

      // 6. 验证DOM状态一致性
      const primaryElement = document.querySelector('.vertical-sidebar.primary')
      const secondaryElement = document.querySelector('.vertical-sidebar.secondary')

      if (primaryElement && secondaryElement) {
        expect(primaryElement.classList.contains('pinned')).toBe(
          secondaryElement.classList.contains('pinned')
        )
        expect(primaryElement.classList.contains('expanded')).toBe(
          secondaryElement.classList.contains('expanded')
        )
      }

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试3：标签页数据的实时同步
   */
  async testTabDataRealTimeSync(): Promise<TestResult> {
    const testName = 'TabDataRealTimeSync'
    console.log(`开始测试: ${testName}`)

    try {
      const { primary, secondary } = this.context.instances

      // 1. 初始化标签页管理器
      await Promise.all([
        primary.tabs.initialize(),
        secondary.tabs.initialize()
      ])

      // 2. 模拟标签页变化事件
      const newTab: chrome.tabs.Tab = {
        id: 999,
        title: 'New Tab',
        url: 'https://example.com',
        active: false,
        pinned: false,
        index: 3,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: -1
      }

      // 3. 在主实例中添加标签页
      await primary.tabs.addTab(newTab)

      // 4. 等待同步
      await this.waitForSync(200)

      // 5. 验证次实例数据同步
      const secondaryTabs = await secondary.tabs.getAllTabs()
      const syncedTab = secondaryTabs.find(tab => tab.id === 999)
      
      expect(syncedTab).toBeDefined()
      expect(syncedTab?.title).toBe('New Tab')
      expect(syncedTab?.url).toBe('https://example.com')

      // 6. 测试标签页更新同步
      const updatedTab = { ...newTab, title: 'Updated Tab' }
      await primary.tabs.updateTab(999, updatedTab)

      await this.waitForSync(200)

      // 验证更新同步
      const updatedSecondaryTabs = await secondary.tabs.getAllTabs()
      const updatedSyncedTab = updatedSecondaryTabs.find(tab => tab.id === 999)
      
      expect(updatedSyncedTab?.title).toBe('Updated Tab')

      // 7. 测试标签页删除同步
      await primary.tabs.removeTab(999)

      await this.waitForSync(200)

      // 验证删除同步
      const finalSecondaryTabs = await secondary.tabs.getAllTabs()
      const deletedTab = finalSecondaryTabs.find(tab => tab.id === 999)
      
      expect(deletedTab).toBeUndefined()

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试4：收藏夹数据的一致性验证
   */
  async testBookmarkDataConsistency(): Promise<TestResult> {
    const testName = 'BookmarkDataConsistency'
    console.log(`开始测试: ${testName}`)

    try {
      const { primary, secondary } = this.context.instances

      // 1. 初始化收藏夹管理器
      await Promise.all([
        primary.bookmarks.initialize(),
        secondary.bookmarks.initialize()
      ])

      // 2. 创建初始数据快照
      await this.createDataSnapshot('before_bookmark_changes')

      // 3. 在主实例中添加收藏夹
      const newBookmark = {
        title: 'Test Bookmark',
        url: 'https://test.com',
        parentId: '1'
      }

      await primary.bookmarks.addBookmark(newBookmark)

      // 4. 等待同步
      await this.waitForSync(300)

      // 5. 验证次实例数据同步
      const secondaryBookmarks = await secondary.bookmarks.getAllBookmarks()
      const syncedBookmark = this.findBookmarkByUrl(secondaryBookmarks, 'https://test.com')
      
      expect(syncedBookmark).toBeDefined()
      expect(syncedBookmark?.title).toBe('Test Bookmark')

      // 6. 测试文件夹操作同步
      const newFolder = {
        title: 'Test Folder',
        parentId: '1'
      }

      await primary.bookmarks.createFolder(newFolder)

      await this.waitForSync(300)

      // 验证文件夹同步
      const secondaryFolders = await secondary.bookmarks.getAllBookmarks()
      const syncedFolder = this.findBookmarkByTitle(secondaryFolders, 'Test Folder')
      
      expect(syncedFolder).toBeDefined()
      expect(syncedFolder?.children).toBeDefined()

      // 7. 创建最终数据快照
      await this.createDataSnapshot('after_bookmark_changes')

      // 8. 验证数据完整性
      const beforeSnapshot = this.context.dataSnapshots.get('before_bookmark_changes')
      const afterSnapshot = this.context.dataSnapshots.get('after_bookmark_changes')
      
      expect(afterSnapshot?.bookmarks.length).toBeGreaterThan(beforeSnapshot?.bookmarks.length || 0)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试5：并发操作的数据一致性
   */
  async testConcurrentOperationConsistency(): Promise<TestResult> {
    const testName = 'ConcurrentOperationConsistency'
    console.log(`开始测试: ${testName}`)

    try {
      const { primary, secondary } = this.context.instances

      // 1. 初始化所有组件
      await Promise.all([
        primary.settings.initialize(),
        primary.sidebar.initialize(),
        secondary.settings.initialize(),
        secondary.sidebar.initialize()
      ])

      // 2. 创建并发操作
      const concurrentOperations = [
        // 主实例操作
        primary.settings.updateSettings({ theme: 'dark' }),
        primary.sidebar.show(),
        primary.sidebar.expand(),
        
        // 次实例操作
        secondary.settings.updateSettings({ position: 'right' }),
        secondary.sidebar.pin(),
        secondary.sidebar.setActiveSection('bookmarks')
      ]

      // 3. 并发执行操作
      await Promise.all(concurrentOperations)

      // 4. 等待所有同步完成
      await this.waitForSync(1000)

      // 5. 验证最终状态一致性
      const primarySettings = await primary.settings.getSettings()
      const secondarySettings = await secondary.settings.getSettings()

      // 验证设置合并正确
      expect(primarySettings.theme).toBe(secondarySettings.theme)
      expect(primarySettings.position).toBe(secondarySettings.position)

      // 验证侧边栏状态一致
      expect(primary.sidebar.isVisible()).toBe(secondary.sidebar.isVisible())
      expect(primary.sidebar.isPinned()).toBe(secondary.sidebar.isPinned())

      // 6. 验证没有数据丢失
      const syncEvents = this.context.syncEvents
      const setEvents = syncEvents.filter(e => e.type === 'STORAGE_SET')
      
      expect(setEvents.length).toBeGreaterThan(0)
      expect(setEvents.every(e => e.success)).toBe(true)

      // 7. 验证冲突解决
      const conflictResolutions = syncEvents.filter(e => 
        e.data && e.data.conflictResolution
      )
      
      // 应该有冲突解决记录（如果有冲突的话）
      if (conflictResolutions.length > 0) {
        expect(conflictResolutions.every(e => e.success)).toBe(true)
      }

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  // 辅助方法
  private async waitForSync(duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration))
  }

  private async createDataSnapshot(snapshotId: string): Promise<void> {
    const { primary } = this.context.instances

    const snapshot: DataState = {
      tabs: await primary.tabs.getAllTabs(),
      bookmarks: await primary.bookmarks.getAllBookmarks(),
      settings: await primary.settings.getSettings(),
      sidebarState: {
        visible: primary.sidebar.isVisible(),
        expanded: primary.sidebar.isExpanded(),
        pinned: primary.sidebar.isPinned(),
        activeSection: primary.sidebar.getActiveSection(),
        searchQuery: primary.sidebar.getSearchQuery()
      },
      timestamp: Date.now()
    }

    this.context.dataSnapshots.set(snapshotId, snapshot)
  }

  private findBookmarkByUrl(bookmarks: chrome.bookmarks.BookmarkTreeNode[], url: string): chrome.bookmarks.BookmarkTreeNode | undefined {
    for (const bookmark of bookmarks) {
      if (bookmark.url === url) {
        return bookmark
      }
      if (bookmark.children) {
        const found = this.findBookmarkByUrl(bookmark.children, url)
        if (found) return found
      }
    }
    return undefined
  }

  private findBookmarkByTitle(bookmarks: chrome.bookmarks.BookmarkTreeNode[], title: string): chrome.bookmarks.BookmarkTreeNode | undefined {
    for (const bookmark of bookmarks) {
      if (bookmark.title === title) {
        return bookmark
      }
      if (bookmark.children) {
        const found = this.findBookmarkByTitle(bookmark.children, title)
        if (found) return found
      }
    }
    return undefined
  }

  /**
   * 运行所有数据一致性测试
   */
  async runAllTests(): Promise<TestSummary> {
    console.log('开始运行数据一致性测试套件...')
    const startTime = Date.now()

    const tests = [
      () => this.testSettingsDataSync(),
      () => this.testSidebarStateConsistency(),
      () => this.testTabDataRealTimeSync(),
      () => this.testBookmarkDataConsistency(),
      () => this.testConcurrentOperationConsistency()
    ]

    const results: TestResult[] = []

    for (const test of tests) {
      try {
        const result = await test()
        results.push(result)
        this.testResults.set(result.name, result)
      } catch (error) {
        results.push({
          name: 'UnknownTest',
          success: false,
          error: error.message,
          duration: 0
        })
      }
    }

    const endTime = Date.now()
    const summary: TestSummary = {
      totalTests: results.length,
      passedTests: results.filter(r => r.success).length,
      failedTests: results.filter(r => !r.success).length,
      totalDuration: endTime - startTime,
      results
    }

    console.log('数据一致性测试完成:', summary)
    return summary
  }

  /**
   * 获取同步事件分析
   */
  getSyncEventAnalysis(): SyncAnalysis {
    const eventTypes = new Map<string, number>()
    const successRate = new Map<string, { success: number, total: number }>()

    this.context.syncEvents.forEach(event => {
      // 统计事件类型
      const count = eventTypes.get(event.type) || 0
      eventTypes.set(event.type, count + 1)

      // 统计成功率
      const stats = successRate.get(event.type) || { success: 0, total: 0 }
      stats.total++
      if (event.success) stats.success++
      successRate.set(event.type, stats)
    })

    const successRates = Object.fromEntries(
      Array.from(successRate.entries()).map(([type, stats]) => [
        type,
        stats.total > 0 ? (stats.success / stats.total) * 100 : 0
      ])
    )

    return {
      totalEvents: this.context.syncEvents.length,
      eventTypes: Object.fromEntries(eventTypes),
      successRates,
      averageSyncTime: this.calculateAverageSyncTime()
    }
  }

  private calculateAverageSyncTime(): number {
    if (this.context.syncEvents.length < 2) return 0

    const times = []
    for (let i = 1; i < this.context.syncEvents.length; i++) {
      const timeDiff = this.context.syncEvents[i].timestamp - this.context.syncEvents[i - 1].timestamp
      times.push(timeDiff)
    }

    return times.reduce((sum, time) => sum + time, 0) / times.length
  }

  /**
   * 获取数据快照比较
   */
  compareDataSnapshots(snapshot1Id: string, snapshot2Id: string): SnapshotComparison {
    const snapshot1 = this.context.dataSnapshots.get(snapshot1Id)
    const snapshot2 = this.context.dataSnapshots.get(snapshot2Id)

    if (!snapshot1 || !snapshot2) {
      throw new Error('Snapshot not found')
    }

    return {
      tabsChanged: snapshot1.tabs.length !== snapshot2.tabs.length,
      bookmarksChanged: snapshot1.bookmarks.length !== snapshot2.bookmarks.length,
      settingsChanged: JSON.stringify(snapshot1.settings) !== JSON.stringify(snapshot2.settings),
      sidebarStateChanged: JSON.stringify(snapshot1.sidebarState) !== JSON.stringify(snapshot2.sidebarState),
      timeDifference: snapshot2.timestamp - snapshot1.timestamp
    }
  }
}

interface TestResult {
  name: string
  success: boolean
  error?: string
  duration: number
}

interface TestSummary {
  totalTests: number
  passedTests: number
  failedTests: number
  totalDuration: number
  results: TestResult[]
}

interface SyncAnalysis {
  totalEvents: number
  eventTypes: Record<string, number>
  successRates: Record<string, number>
  averageSyncTime: number
}

interface SnapshotComparison {
  tabsChanged: boolean
  bookmarksChanged: boolean
  settingsChanged: boolean
  sidebarStateChanged: boolean
  timeDifference: number
}

export { DataConsistencyTestSuite, TestResult, TestSummary, SyncAnalysis, SnapshotComparison }