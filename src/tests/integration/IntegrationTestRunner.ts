/**
 * 集成测试运行器
 * 统一管理和执行所有集成测试套件
 */

import { UserFlowTestSuite, TestResult as UserFlowTestResult, TestSummary as UserFlowTestSummary } from './UserFlowTests'
import { CrossComponentTestSuite, TestResult as CrossComponentTestResult, TestSummary as CrossComponentTestSummary } from './CrossComponentTests'
import { DataConsistencyTestSuite, TestResult as DataConsistencyTestResult, TestSummary as DataConsistencyTestSummary } from './DataConsistencyTests'
import { TestDataManager, TestDataSet, TestEnvironment } from '../utils/TestDataManager'

interface IntegrationTestConfig {
  suites: {
    userFlow: boolean
    crossComponent: boolean
    dataConsistency: boolean
  }
  environment: {
    dataSetId: string
    cleanup: boolean
    parallel: boolean
  }
  reporting: {
    detailed: boolean
    exportResults: boolean
    outputPath?: string
  }
}

interface TestSuiteResult {
  name: string
  summary: any
  duration: number
  success: boolean
  error?: string
}

interface IntegrationTestReport {
  config: IntegrationTestConfig
  startTime: number
  endTime: number
  totalDuration: number
  suiteResults: TestSuiteResult[]
  overallSummary: {
    totalSuites: number
    passedSuites: number
    failedSuites: number
    totalTests: number
    passedTests: number
    failedTests: number
    successRate: number
  }
  environment: {
    dataSet: TestDataSet
    memoryUsage: number
    cleanupPerformed: boolean
  }
}

class IntegrationTestRunner {
  private dataManager: TestDataManager
  private config: IntegrationTestConfig
  private testEnvironment?: TestEnvironment

  constructor(config: Partial<IntegrationTestConfig> = {}) {
    this.dataManager = new TestDataManager()
    this.config = this.mergeConfig(config)
  }

  private mergeConfig(config: Partial<IntegrationTestConfig>): IntegrationTestConfig {
    return {
      suites: {
        userFlow: config.suites?.userFlow ?? true,
        crossComponent: config.suites?.crossComponent ?? true,
        dataConsistency: config.suites?.dataConsistency ?? true
      },
      environment: {
        dataSetId: config.environment?.dataSetId ?? 'standard',
        cleanup: config.environment?.cleanup ?? true,
        parallel: config.environment?.parallel ?? false
      },
      reporting: {
        detailed: config.reporting?.detailed ?? true,
        exportResults: config.reporting?.exportResults ?? false,
        outputPath: config.reporting?.outputPath
      }
    }
  }

  /**
   * 初始化测试环境
   */
  async initializeTestEnvironment(): Promise<void> {
    console.log('初始化集成测试环境...')

    // 创建或获取数据集
    let dataSet = this.dataManager.getDataSet(this.config.environment.dataSetId)
    if (!dataSet) {
      console.log(`创建标准数据集: ${this.config.environment.dataSetId}`)
      dataSet = this.dataManager.createStandardDataSet(
        this.config.environment.dataSetId,
        'Standard Integration Test Data'
      )
    }

    // 创建测试环境
    const environmentId = `integration-test-${Date.now()}`
    this.testEnvironment = this.dataManager.createTestEnvironment(
      environmentId,
      'Integration Test Environment',
      this.config.environment.dataSetId
    )

    // 应用测试环境
    this.dataManager.applyTestEnvironment(environmentId)

    console.log('测试环境初始化完成')
  }

  /**
   * 运行所有集成测试
   */
  async runAllTests(): Promise<IntegrationTestReport> {
    console.log('开始运行集成测试套件...')
    const startTime = Date.now()

    // 初始化环境
    await this.initializeTestEnvironment()

    const suiteResults: TestSuiteResult[] = []

    try {
      // 根据配置运行测试套件
      if (this.config.environment.parallel) {
        // 并行执行
        const promises = []
        
        if (this.config.suites.userFlow) {
          promises.push(this.runUserFlowTests())
        }
        if (this.config.suites.crossComponent) {
          promises.push(this.runCrossComponentTests())
        }
        if (this.config.suites.dataConsistency) {
          promises.push(this.runDataConsistencyTests())
        }

        const results = await Promise.allSettled(promises)
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            suiteResults.push(result.value)
          } else {
            suiteResults.push({
              name: `Suite${index}`,
              summary: null,
              duration: 0,
              success: false,
              error: result.reason?.message || 'Unknown error'
            })
          }
        })
      } else {
        // 串行执行
        if (this.config.suites.userFlow) {
          suiteResults.push(await this.runUserFlowTests())
        }
        if (this.config.suites.crossComponent) {
          suiteResults.push(await this.runCrossComponentTests())
        }
        if (this.config.suites.dataConsistency) {
          suiteResults.push(await this.runDataConsistencyTests())
        }
      }

      const endTime = Date.now()

      // 生成报告
      const report = this.generateReport(startTime, endTime, suiteResults)

      // 清理环境
      if (this.config.environment.cleanup) {
        await this.cleanup()
      }

      // 导出结果
      if (this.config.reporting.exportResults) {
        await this.exportResults(report)
      }

      console.log('集成测试完成')
      return report

    } catch (error) {
      console.error('集成测试执行失败:', error)
      
      // 确保清理
      if (this.config.environment.cleanup) {
        await this.cleanup()
      }

      throw error
    }
  }

  /**
   * 运行用户流程测试
   */
  private async runUserFlowTests(): Promise<TestSuiteResult> {
    console.log('运行用户流程测试...')
    const startTime = Date.now()

    try {
      const testSuite = new UserFlowTestSuite()
      const summary = await testSuite.runAllTests()
      const endTime = Date.now()

      return {
        name: 'UserFlowTests',
        summary,
        duration: endTime - startTime,
        success: summary.failedTests === 0
      }
    } catch (error) {
      return {
        name: 'UserFlowTests',
        summary: null,
        duration: Date.now() - startTime,
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 运行跨组件测试
   */
  private async runCrossComponentTests(): Promise<TestSuiteResult> {
    console.log('运行跨组件测试...')
    const startTime = Date.now()

    try {
      const testSuite = new CrossComponentTestSuite()
      const summary = await testSuite.runAllTests()
      const endTime = Date.now()

      return {
        name: 'CrossComponentTests',
        summary,
        duration: endTime - startTime,
        success: summary.failedTests === 0
      }
    } catch (error) {
      return {
        name: 'CrossComponentTests',
        summary: null,
        duration: Date.now() - startTime,
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 运行数据一致性测试
   */
  private async runDataConsistencyTests(): Promise<TestSuiteResult> {
    console.log('运行数据一致性测试...')
    const startTime = Date.now()

    try {
      const testSuite = new DataConsistencyTestSuite()
      const summary = await testSuite.runAllTests()
      const endTime = Date.now()

      return {
        name: 'DataConsistencyTests',
        summary,
        duration: endTime - startTime,
        success: summary.failedTests === 0
      }
    } catch (error) {
      return {
        name: 'DataConsistencyTests',
        summary: null,
        duration: Date.now() - startTime,
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 生成测试报告
   */
  private generateReport(
    startTime: number,
    endTime: number,
    suiteResults: TestSuiteResult[]
  ): IntegrationTestReport {
    const totalDuration = endTime - startTime
    const passedSuites = suiteResults.filter(r => r.success).length
    const failedSuites = suiteResults.filter(r => !r.success).length

    // 计算总测试数
    let totalTests = 0
    let passedTests = 0
    let failedTests = 0

    suiteResults.forEach(result => {
      if (result.summary) {
        totalTests += result.summary.totalTests || 0
        passedTests += result.summary.passedTests || 0
        failedTests += result.summary.failedTests || 0
      }
    })

    const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0

    return {
      config: this.config,
      startTime,
      endTime,
      totalDuration,
      suiteResults,
      overallSummary: {
        totalSuites: suiteResults.length,
        passedSuites,
        failedSuites,
        totalTests,
        passedTests,
        failedTests,
        successRate
      },
      environment: {
        dataSet: this.testEnvironment!.dataSet,
        memoryUsage: this.dataManager.getUsageStats().memoryUsage,
        cleanupPerformed: false
      }
    }
  }

  /**
   * 导出测试结果
   */
  private async exportResults(report: IntegrationTestReport): Promise<void> {
    const outputPath = this.config.reporting.outputPath || './test-results'
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `integration-test-report-${timestamp}.json`

    try {
      // 这里应该实际写入文件，但在测试环境中我们只是模拟
      console.log(`导出测试结果到: ${outputPath}/${filename}`)
      console.log('报告内容:', JSON.stringify(report, null, 2))
    } catch (error) {
      console.error('导出测试结果失败:', error)
    }
  }

  /**
   * 清理测试环境
   */
  private async cleanup(): Promise<void> {
    console.log('清理测试环境...')

    try {
      this.dataManager.cleanupAll()
      
      // 更新报告状态
      if (this.testEnvironment) {
        // 标记清理已完成
        console.log('测试环境清理完成')
      }
    } catch (error) {
      console.error('清理测试环境失败:', error)
    }
  }

  /**
   * 运行特定测试套件
   */
  async runSpecificSuite(suiteName: string): Promise<TestSuiteResult> {
    await this.initializeTestEnvironment()

    try {
      switch (suiteName) {
        case 'userFlow':
          return await this.runUserFlowTests()
        case 'crossComponent':
          return await this.runCrossComponentTests()
        case 'dataConsistency':
          return await this.runDataConsistencyTests()
        default:
          throw new Error(`Unknown test suite: ${suiteName}`)
      }
    } finally {
      if (this.config.environment.cleanup) {
        await this.cleanup()
      }
    }
  }

  /**
   * 获取测试配置
   */
  getConfig(): IntegrationTestConfig {
    return { ...this.config }
  }

  /**
   * 更新测试配置
   */
  updateConfig(updates: Partial<IntegrationTestConfig>): void {
    this.config = this.mergeConfig({ ...this.config, ...updates })
  }

  /**
   * 验证测试环境
   */
  async validateEnvironment(): Promise<ValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // 验证数据管理器
      const dataIntegrity = this.dataManager.validateDataIntegrity()
      errors.push(...dataIntegrity.errors)
      warnings.push(...dataIntegrity.warnings)

      // 验证Chrome API模拟
      if (!global.chrome) {
        errors.push('Chrome API not mocked')
      } else {
        if (!global.chrome.tabs) warnings.push('Chrome tabs API not mocked')
        if (!global.chrome.bookmarks) warnings.push('Chrome bookmarks API not mocked')
        if (!global.chrome.storage) warnings.push('Chrome storage API not mocked')
      }

      // 验证DOM环境
      if (typeof document === 'undefined') {
        errors.push('DOM environment not available')
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      }
    } catch (error) {
      return {
        valid: false,
        errors: [`Environment validation failed: ${error.message}`],
        warnings
      }
    }
  }

  /**
   * 生成测试报告摘要
   */
  generateSummaryReport(report: IntegrationTestReport): string {
    const { overallSummary, totalDuration, suiteResults } = report

    let summary = `
=== 集成测试报告摘要 ===

总体结果:
- 测试套件: ${overallSummary.passedSuites}/${overallSummary.totalSuites} 通过
- 测试用例: ${overallSummary.passedTests}/${overallSummary.totalTests} 通过
- 成功率: ${overallSummary.successRate.toFixed(2)}%
- 总耗时: ${totalDuration}ms

套件详情:
`

    suiteResults.forEach(result => {
      const status = result.success ? '✅ 通过' : '❌ 失败'
      summary += `- ${result.name}: ${status} (${result.duration}ms)\n`
      
      if (result.error) {
        summary += `  错误: ${result.error}\n`
      }
      
      if (result.summary) {
        summary += `  测试: ${result.summary.passedTests}/${result.summary.totalTests} 通过\n`
      }
    })

    return summary
  }
}

interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export { 
  IntegrationTestRunner, 
  IntegrationTestConfig, 
  IntegrationTestReport, 
  TestSuiteResult,
  ValidationResult
}