/**
 * 用户流程集成测试
 * 测试用户典型使用场景的完整流程
 */

import { SidebarManager } from '../../core/SidebarManager'
import { BackgroundService } from '../../background/BackgroundService'
import { SettingsManager } from '../../core/SettingsManager'
import { TabManager } from '../../core/TabManager'
import { BookmarkManager } from '../../core/BookmarkManager'

interface TestContext {
  sidebarManager: SidebarManager
  backgroundService: BackgroundService
  settingsManager: SettingsManager
  tabManager: TabManager
  bookmarkManager: BookmarkManager
  mockTabs: chrome.tabs.Tab[]
  mockBookmarks: chrome.bookmarks.BookmarkTreeNode[]
}

class UserFlowTestSuite {
  private context: TestContext
  private testResults: Map<string, TestResult> = new Map()

  constructor() {
    this.setupTestContext()
  }

  private setupTestContext(): void {
    // 初始化测试环境
    this.context = {
      sidebarManager: new SidebarManager(),
      backgroundService: new BackgroundService(),
      settingsManager: new SettingsManager(),
      tabManager: new TabManager(),
      bookmarkManager: new BookmarkManager(),
      mockTabs: this.createMockTabs(),
      mockBookmarks: this.createMockBookmarks()
    }

    // 设置Chrome API模拟
    this.setupChromeMocks()
  }

  private setupChromeMocks(): void {
    // 模拟Chrome Tabs API
    global.chrome = {
      tabs: {
        query: jest.fn().mockResolvedValue(this.context.mockTabs),
        get: jest.fn().mockImplementation((tabId) => 
          Promise.resolve(this.context.mockTabs.find(tab => tab.id === tabId))
        ),
        update: jest.fn().mockResolvedValue({}),
        remove: jest.fn().mockResolvedValue({}),
        create: jest.fn().mockImplementation((createProperties) => 
          Promise.resolve({ id: Date.now(), ...createProperties })
        ),
        onActivated: { addListener: jest.fn() },
        onUpdated: { addListener: jest.fn() },
        onRemoved: { addListener: jest.fn() }
      },
      bookmarks: {
        getTree: jest.fn().mockResolvedValue(this.context.mockBookmarks),
        create: jest.fn().mockResolvedValue({}),
        remove: jest.fn().mockResolvedValue({}),
        update: jest.fn().mockResolvedValue({})
      },
      storage: {
        sync: {
          get: jest.fn().mockResolvedValue({}),
          set: jest.fn().mockResolvedValue({})
        }
      },
      runtime: {
        sendMessage: jest.fn().mockResolvedValue({}),
        onMessage: { addListener: jest.fn() }
      }
    } as any
  }

  private createMockTabs(): chrome.tabs.Tab[] {
    return [
      {
        id: 1,
        title: 'Google',
        url: 'https://www.google.com',
        active: true,
        pinned: false,
        index: 0,
        windowId: 1,
        highlighted: true,
        incognito: false,
        selected: true,
        discarded: false,
        autoDiscardable: true,
        groupId: -1
      },
      {
        id: 2,
        title: 'GitHub',
        url: 'https://github.com',
        active: false,
        pinned: true,
        index: 1,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 1
      },
      {
        id: 3,
        title: 'Stack Overflow',
        url: 'https://stackoverflow.com',
        active: false,
        pinned: false,
        index: 2,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 1
      }
    ]
  }

  private createMockBookmarks(): chrome.bookmarks.BookmarkTreeNode[] {
    return [
      {
        id: '1',
        title: 'Bookmarks Bar',
        children: [
          {
            id: '2',
            title: 'Development',
            children: [
              {
                id: '3',
                title: 'GitHub',
                url: 'https://github.com'
              },
              {
                id: '4',
                title: 'Stack Overflow',
                url: 'https://stackoverflow.com'
              }
            ]
          },
          {
            id: '5',
            title: 'Google',
            url: 'https://www.google.com'
          }
        ]
      }
    ]
  }

  /**
   * 测试场景1：首次使用侧边栏
   */
  async testFirstTimeUserExperience(): Promise<TestResult> {
    const testName = 'FirstTimeUserExperience'
    console.log(`开始测试: ${testName}`)

    try {
      // 1. 初始化侧边栏
      await this.context.sidebarManager.initialize()
      
      // 验证初始状态
      expect(this.context.sidebarManager.isVisible()).toBe(false)
      expect(this.context.sidebarManager.isExpanded()).toBe(false)
      expect(this.context.sidebarManager.isPinned()).toBe(false)

      // 2. 首次显示侧边栏
      this.context.sidebarManager.show()
      await this.waitForAnimation(300)

      // 验证显示状态
      expect(this.context.sidebarManager.isVisible()).toBe(true)
      expect(document.querySelector('.vertical-sidebar')).toBeTruthy()

      // 3. 鼠标悬停展开
      const sidebarElement = document.querySelector('.vertical-sidebar') as HTMLElement
      this.simulateMouseEnter(sidebarElement)
      await this.waitForAnimation(300)

      // 验证展开状态
      expect(this.context.sidebarManager.isExpanded()).toBe(true)
      expect(sidebarElement.classList.contains('expanded')).toBe(true)

      // 4. 加载标签页数据
      await this.context.sidebarManager.updateTabs(this.context.mockTabs)
      
      // 验证标签页显示
      const tabItems = document.querySelectorAll('.tab-item')
      expect(tabItems.length).toBe(3)

      // 5. 加载收藏夹数据
      await this.context.sidebarManager.updateBookmarks(this.context.mockBookmarks)
      
      // 验证收藏夹显示
      const bookmarkItems = document.querySelectorAll('.bookmark-item')
      expect(bookmarkItems.length).toBeGreaterThan(0)

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试场景2：标签页管理工作流
   */
  async testTabManagementWorkflow(): Promise<TestResult> {
    const testName = 'TabManagementWorkflow'
    console.log(`开始测试: ${testName}`)

    try {
      // 1. 确保侧边栏已初始化并展开
      await this.context.sidebarManager.initialize()
      this.context.sidebarManager.show()
      this.context.sidebarManager.expand()
      await this.context.sidebarManager.updateTabs(this.context.mockTabs)

      // 2. 测试标签页切换
      const tabItem = document.querySelector('.tab-item[data-tab-id="2"]') as HTMLElement
      expect(tabItem).toBeTruthy()

      // 模拟点击标签页
      tabItem.click()
      await this.waitForAsync(100)

      // 验证标签页切换调用
      expect(chrome.tabs.update).toHaveBeenCalledWith(2, { active: true })

      // 3. 测试右键菜单
      this.simulateRightClick(tabItem)
      await this.waitForAsync(100)

      // 验证右键菜单显示
      const contextMenu = document.querySelector('.context-menu')
      expect(contextMenu).toBeTruthy()

      // 4. 测试标签页关闭
      const closeMenuItem = document.querySelector('.context-menu-item[data-action="close"]') as HTMLElement
      closeMenuItem.click()
      await this.waitForAsync(100)

      // 验证关闭调用
      expect(chrome.tabs.remove).toHaveBeenCalledWith(2)

      // 5. 测试标签页分组
      const groupMenuItem = document.querySelector('.context-menu-item[data-action="group"]') as HTMLElement
      if (groupMenuItem) {
        groupMenuItem.click()
        await this.waitForAsync(100)

        // 验证分组功能
        const groupDialog = document.querySelector('.group-dialog')
        expect(groupDialog).toBeTruthy()
      }

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试场景3：收藏夹管理工作流
   */
  async testBookmarkManagementWorkflow(): Promise<TestResult> {
    const testName = 'BookmarkManagementWorkflow'
    console.log(`开始测试: ${testName}`)

    try {
      // 1. 初始化收藏夹数据
      await this.context.sidebarManager.initialize()
      this.context.sidebarManager.show()
      this.context.sidebarManager.expand()
      await this.context.sidebarManager.updateBookmarks(this.context.mockBookmarks)

      // 2. 测试文件夹展开/折叠
      const folderItem = document.querySelector('.bookmark-folder[data-folder-id="2"]') as HTMLElement
      expect(folderItem).toBeTruthy()

      // 点击文件夹展开
      const folderToggle = folderItem.querySelector('.folder-toggle') as HTMLElement
      folderToggle.click()
      await this.waitForAsync(100)

      // 验证子项目显示
      const childItems = folderItem.querySelectorAll('.bookmark-item')
      expect(childItems.length).toBeGreaterThan(0)

      // 3. 测试收藏夹点击打开
      const bookmarkItem = document.querySelector('.bookmark-item[data-bookmark-id="3"]') as HTMLElement
      bookmarkItem.click()
      await this.waitForAsync(100)

      // 验证新标签页创建
      expect(chrome.tabs.create).toHaveBeenCalledWith({
        url: 'https://github.com',
        active: false
      })

      // 4. 测试拖拽添加收藏夹
      const dragData = {
        url: 'https://example.com',
        title: 'Example Site'
      }

      this.simulateDragDrop(folderItem, dragData)
      await this.waitForAsync(100)

      // 验证收藏夹创建
      expect(chrome.bookmarks.create).toHaveBeenCalledWith({
        parentId: '2',
        title: 'Example Site',
        url: 'https://example.com'
      })

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试场景4：搜索和过滤工作流
   */
  async testSearchAndFilterWorkflow(): Promise<TestResult> {
    const testName = 'SearchAndFilterWorkflow'
    console.log(`开始测试: ${testName}`)

    try {
      // 1. 初始化数据
      await this.context.sidebarManager.initialize()
      this.context.sidebarManager.show()
      this.context.sidebarManager.expand()
      await this.context.sidebarManager.updateTabs(this.context.mockTabs)
      await this.context.sidebarManager.updateBookmarks(this.context.mockBookmarks)

      // 2. 测试搜索框激活
      const searchBox = document.querySelector('.search-box input') as HTMLInputElement
      expect(searchBox).toBeTruthy()

      // 模拟Ctrl+F快捷键
      this.simulateKeyboardShortcut('KeyF', { ctrlKey: true })
      await this.waitForAsync(100)

      // 验证搜索框获得焦点
      expect(document.activeElement).toBe(searchBox)

      // 3. 测试实时搜索
      this.simulateTyping(searchBox, 'git')
      await this.waitForAsync(300) // 等待防抖

      // 验证搜索结果过滤
      const visibleTabItems = document.querySelectorAll('.tab-item:not(.hidden)')
      const visibleBookmarkItems = document.querySelectorAll('.bookmark-item:not(.hidden)')
      
      expect(visibleTabItems.length).toBe(1) // 只有GitHub标签页匹配
      expect(visibleBookmarkItems.length).toBe(1) // 只有GitHub收藏夹匹配

      // 4. 测试搜索结果高亮
      const highlightedElements = document.querySelectorAll('.search-highlight')
      expect(highlightedElements.length).toBeGreaterThan(0)

      // 5. 测试清空搜索
      this.simulateKeyPress(searchBox, 'Escape')
      await this.waitForAsync(100)

      // 验证搜索清空
      expect(searchBox.value).toBe('')
      const allItems = document.querySelectorAll('.tab-item, .bookmark-item')
      allItems.forEach(item => {
        expect(item.classList.contains('hidden')).toBe(false)
      })

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  /**
   * 测试场景5：设置配置工作流
   */
  async testSettingsConfigurationWorkflow(): Promise<TestResult> {
    const testName = 'SettingsConfigurationWorkflow'
    console.log(`开始测试: ${testName}`)

    try {
      // 1. 初始化设置
      await this.context.settingsManager.initialize()
      const initialSettings = await this.context.settingsManager.getSettings()

      // 2. 测试主题切换
      await this.context.settingsManager.updateSettings({ theme: 'dark' })
      
      // 验证主题应用
      const sidebarElement = document.querySelector('.vertical-sidebar')
      expect(sidebarElement?.classList.contains('theme-dark')).toBe(true)

      // 3. 测试位置切换
      await this.context.settingsManager.updateSettings({ position: 'right' })
      
      // 验证位置变更
      expect(sidebarElement?.classList.contains('position-right')).toBe(true)

      // 4. 测试延迟设置
      await this.context.settingsManager.updateSettings({ 
        expandDelay: 1000,
        collapseDelay: 2000 
      })

      // 验证延迟生效
      const sidebarManager = this.context.sidebarManager as any
      expect(sidebarManager.expandDelay).toBe(1000)
      expect(sidebarManager.collapseDelay).toBe(2000)

      // 5. 测试快捷键配置
      await this.context.settingsManager.updateSettings({
        keyboardShortcuts: {
          toggle: 'Ctrl+Shift+S',
          search: 'Ctrl+K'
        }
      })

      // 验证快捷键注册
      this.simulateKeyboardShortcut('KeyS', { ctrlKey: true, shiftKey: true })
      await this.waitForAsync(100)

      // 验证侧边栏切换
      const toggleCalled = jest.spyOn(this.context.sidebarManager, 'toggle')
      expect(toggleCalled).toHaveBeenCalled()

      return { name: testName, success: true, duration: Date.now() }
    } catch (error) {
      return { name: testName, success: false, error: error.message, duration: Date.now() }
    }
  }

  // 辅助方法
  private async waitForAnimation(duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration))
  }

  private async waitForAsync(duration: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration))
  }

  private simulateMouseEnter(element: HTMLElement): void {
    const event = new MouseEvent('mouseenter', { bubbles: true })
    element.dispatchEvent(event)
  }

  private simulateRightClick(element: HTMLElement): void {
    const event = new MouseEvent('contextmenu', { 
      bubbles: true, 
      button: 2 
    })
    element.dispatchEvent(event)
  }

  private simulateKeyboardShortcut(code: string, modifiers: any = {}): void {
    const event = new KeyboardEvent('keydown', {
      code,
      bubbles: true,
      ...modifiers
    })
    document.dispatchEvent(event)
  }

  private simulateTyping(input: HTMLInputElement, text: string): void {
    input.value = text
    const event = new Event('input', { bubbles: true })
    input.dispatchEvent(event)
  }

  private simulateKeyPress(element: HTMLElement, key: string): void {
    const event = new KeyboardEvent('keydown', {
      key,
      bubbles: true
    })
    element.dispatchEvent(event)
  }

  private simulateDragDrop(target: HTMLElement, data: any): void {
    const dragEvent = new DragEvent('drop', {
      bubbles: true,
      dataTransfer: new DataTransfer()
    })
    
    // 设置拖拽数据
    dragEvent.dataTransfer?.setData('text/plain', JSON.stringify(data))
    target.dispatchEvent(dragEvent)
  }

  /**
   * 运行所有用户流程测试
   */
  async runAllTests(): Promise<TestSummary> {
    console.log('开始运行用户流程测试套件...')
    const startTime = Date.now()

    const tests = [
      () => this.testFirstTimeUserExperience(),
      () => this.testTabManagementWorkflow(),
      () => this.testBookmarkManagementWorkflow(),
      () => this.testSearchAndFilterWorkflow(),
      () => this.testSettingsConfigurationWorkflow()
    ]

    const results: TestResult[] = []

    for (const test of tests) {
      try {
        const result = await test()
        results.push(result)
        this.testResults.set(result.name, result)
      } catch (error) {
        results.push({
          name: 'UnknownTest',
          success: false,
          error: error.message,
          duration: 0
        })
      }
    }

    const endTime = Date.now()
    const summary: TestSummary = {
      totalTests: results.length,
      passedTests: results.filter(r => r.success).length,
      failedTests: results.filter(r => !r.success).length,
      totalDuration: endTime - startTime,
      results
    }

    console.log('用户流程测试完成:', summary)
    return summary
  }
}

interface TestResult {
  name: string
  success: boolean
  error?: string
  duration: number
}

interface TestSummary {
  totalTests: number
  passedTests: number
  failedTests: number
  totalDuration: number
  results: TestResult[]
}

export { UserFlowTestSuite, TestResult, TestSummary }