/**
 * 最终验证执行脚本
 * 提供命令行接口执行完整的最终验证流程
 */

import { FinalVerificationRunner, FinalVerificationConfig } from './verification/FinalVerificationRunner'

interface VerificationOptions {
  skipIntegration?: boolean
  skipFunctional?: boolean
  skipQuality?: boolean
  noExport?: boolean
  outputPath?: string
  failOnCritical?: boolean
  failOnMajor?: boolean
  failOnSecurity?: boolean
  failOnTests?: boolean
  verbose?: boolean
}

class VerificationExecutor {
  private runner: FinalVerificationRunner

  constructor(options: VerificationOptions = {}) {
    const config: Partial<FinalVerificationConfig> = {
      includeIntegrationTests: !options.skipIntegration,
      includeFunctionalVerification: !options.skipFunctional,
      includeCodeQualityAnalysis: !options.skipQuality,
      generateDetailedReport: true,
      exportResults: !options.noExport,
      outputPath: options.outputPath,
      failOnIssues: {
        critical: options.failOnCritical !== false,
        major: options.failOnMajor === true,
        securityCritical: options.failOnSecurity !== false,
        testFailures: options.failOnTests !== false
      }
    }

    this.runner = new FinalVerificationRunner(config)
  }

  /**
   * 执行最终验证
   */
  async execute(): Promise<void> {
    console.log('🎯 Chrome垂直侧边栏扩展 - 最终验证')
    console.log('=' .repeat(80))
    console.log('')

    try {
      const startTime = Date.now()
      
      // 执行验证
      const report = await this.runner.executeFullVerification()
      
      const endTime = Date.now()
      const totalTime = ((endTime - startTime) / 1000).toFixed(1)

      // 显示摘要
      console.log('')
      console.log('📋 验证摘要')
      console.log('-' .repeat(40))
      const summary = this.runner.generateVerificationSummary(report)
      console.log(summary)

      // 显示详细统计
      this.displayDetailedStats(report)

      // 显示发布建议
      this.displayReleaseGuidance(report)

      console.log('')
      console.log(`⏱️  总验证时间: ${totalTime}秒`)
      
      // 最终状态
      if (report.summary.overallStatus === 'passed') {
        console.log('🎉 验证通过! 产品已准备好发布。')
        process.exit(0)
      } else if (report.summary.overallStatus === 'warning') {
        console.log('⚠️  验证完成，但存在需要注意的问题。')
        process.exit(0)
      } else {
        console.log('❌ 验证失败，需要修复问题后重新验证。')
        process.exit(1)
      }

    } catch (error) {
      console.error('')
      console.error('💥 验证执行失败:')
      console.error(`   ${error.message}`)
      console.error('')
      
      if (error.stack) {
        console.error('详细错误信息:')
        console.error(error.stack)
      }
      
      process.exit(1)
    }
  }

  /**
   * 显示详细统计
   */
  private displayDetailedStats(report: any): void {
    console.log('')
    console.log('📊 详细统计')
    console.log('-' .repeat(30))

    // 集成测试统计
    if (report.integrationTests) {
      const integration = report.integrationTests.overallSummary
      console.log(`集成测试:`)
      console.log(`  套件: ${integration.passedSuites}/${integration.totalSuites} 通过`)
      console.log(`  用例: ${integration.passedTests}/${integration.totalTests} 通过`)
      console.log(`  成功率: ${integration.successRate.toFixed(1)}%`)
    }

    // 功能验证统计
    if (report.functionalVerification) {
      const functional = report.functionalVerification.summary
      console.log(`功能验证:`)
      console.log(`  需求: ${functional.passedRequirements}/${functional.totalRequirements} 通过`)
      console.log(`  评分: ${functional.overallScore.toFixed(1)}/100`)
      console.log(`  问题: ${functional.criticalIssues + functional.majorIssues + functional.minorIssues}个`)
    }

    // 代码质量统计
    if (report.codeQuality) {
      const quality = report.codeQuality
      console.log(`代码质量:`)
      console.log(`  评分: ${quality.overall.score}/100 (${quality.overall.grade}级)`)
      console.log(`  状态: ${quality.overall.status}`)
      console.log(`  覆盖率: ${quality.metrics.testCoverage}%`)
      console.log(`  复杂度: ${quality.metrics.cyclomaticComplexity}`)
    }
  }

  /**
   * 显示发布指导
   */
  private displayReleaseGuidance(report: any): void {
    console.log('')
    console.log('🚀 发布指导')
    console.log('-' .repeat(20))

    const readinessLevel = report.summary.readinessLevel
    const criticalIssues = report.criticalIssues.filter((i: any) => i.mustFixBeforeRelease)

    switch (readinessLevel) {
      case 'production-ready':
        console.log('✅ 产品已准备好发布')
        console.log('   建议: 可以立即进行发布流程')
        break

      case 'needs-minor-fixes':
        console.log('⚠️  需要少量修复后可发布')
        console.log(`   需要修复: ${criticalIssues.length}个关键问题`)
        console.log('   预计时间: 1-2个工作日')
        break

      case 'needs-major-fixes':
        console.log('❌ 需要重大修复后才能发布')
        console.log(`   需要修复: ${criticalIssues.length}个关键问题`)
        console.log('   预计时间: 3-5个工作日')
        break

      case 'not-ready':
        console.log('🚫 产品尚未准备好发布')
        console.log('   建议: 完成所有必要的开发和测试工作')
        console.log('   预计时间: 1-2周')
        break
    }

    // 显示优先级最高的问题
    if (criticalIssues.length > 0) {
      console.log('')
      console.log('🔥 优先修复问题:')
      criticalIssues.slice(0, 3).forEach((issue: any, index: number) => {
        console.log(`   ${index + 1}. ${issue.description}`)
        console.log(`      预计修复时间: ${issue.estimatedFixTime}`)
      })
      
      if (criticalIssues.length > 3) {
        console.log(`   ... 还有 ${criticalIssues.length - 3} 个问题`)
      }
    }

    // 显示发布检查清单
    console.log('')
    console.log('📋 发布前检查清单:')
    const checklist = [
      { item: '所有关键问题已修复', status: criticalIssues.length === 0 },
      { item: '集成测试全部通过', status: report.integrationTests?.overallSummary.failedTests === 0 },
      { item: '功能验证全部通过', status: report.functionalVerification?.summary.failedRequirements === 0 },
      { item: '代码质量达标', status: report.codeQuality?.overall.score >= 80 },
      { item: '安全问题已解决', status: !report.criticalIssues.some((i: any) => i.category === 'security' && i.severity === 'critical') }
    ]

    checklist.forEach(check => {
      const status = check.status ? '✅' : '❌'
      console.log(`   ${status} ${check.item}`)
    })
  }

  /**
   * 快速验证模式
   */
  async quickVerification(): Promise<void> {
    console.log('⚡ 快速验证模式')
    console.log('只执行关键验证项目...')
    
    // 重新配置为快速模式
    const quickConfig: Partial<FinalVerificationConfig> = {
      includeIntegrationTests: true,
      includeFunctionalVerification: true,
      includeCodeQualityAnalysis: false, // 跳过代码质量分析
      generateDetailedReport: false,
      exportResults: false
    }

    const quickRunner = new FinalVerificationRunner(quickConfig)
    
    try {
      const report = await quickRunner.executeFullVerification()
      
      console.log('')
      console.log('⚡ 快速验证结果:')
      console.log(`   状态: ${report.summary.overallStatus}`)
      console.log(`   评分: ${report.summary.overallScore.toFixed(1)}/100`)
      console.log(`   关键问题: ${report.criticalIssues.length}个`)
      
      if (report.summary.overallStatus === 'passed') {
        console.log('✅ 快速验证通过')
      } else {
        console.log('❌ 快速验证发现问题，建议执行完整验证')
        process.exit(1)
      }
      
    } catch (error) {
      console.error('❌ 快速验证失败:', error.message)
      process.exit(1)
    }
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(): VerificationOptions & { mode?: string } {
  const args = process.argv.slice(2)
  const options: VerificationOptions & { mode?: string } = {}

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--quick':
        options.mode = 'quick'
        break
      case '--skip-integration':
        options.skipIntegration = true
        break
      case '--skip-functional':
        options.skipFunctional = true
        break
      case '--skip-quality':
        options.skipQuality = true
        break
      case '--no-export':
        options.noExport = true
        break
      case '--output':
        options.outputPath = args[++i]
        break
      case '--fail-on-major':
        options.failOnMajor = true
        break
      case '--no-fail-on-critical':
        options.failOnCritical = false
        break
      case '--no-fail-on-security':
        options.failOnSecurity = false
        break
      case '--no-fail-on-tests':
        options.failOnTests = false
        break
      case '--verbose':
        options.verbose = true
        break
      case '--help':
        showHelp()
        process.exit(0)
        break
    }
  }

  return options
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
Chrome垂直侧边栏扩展 - 最终验证工具

用法:
  npm run verify [选项]

模式:
  --quick                    快速验证模式（跳过代码质量分析）

验证选项:
  --skip-integration         跳过集成测试
  --skip-functional          跳过功能验证
  --skip-quality             跳过代码质量分析

输出选项:
  --no-export                不导出验证报告
  --output <path>            指定报告输出路径

失败条件:
  --fail-on-major            主要问题也导致失败
  --no-fail-on-critical      关键问题不导致失败
  --no-fail-on-security      安全问题不导致失败
  --no-fail-on-tests         测试失败不导致失败

其他选项:
  --verbose                  详细输出
  --help                     显示此帮助信息

示例:
  npm run verify                    # 完整验证
  npm run verify --quick            # 快速验证
  npm run verify --skip-quality     # 跳过代码质量分析
  npm run verify --fail-on-major    # 主要问题也导致失败
`)
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const options = parseArgs()
  
  // 设置详细输出
  if (options.verbose) {
    process.env.VERBOSE = 'true'
  }

  const executor = new VerificationExecutor(options)

  if (options.mode === 'quick') {
    await executor.quickVerification()
  } else {
    await executor.execute()
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('执行失败:', error)
    process.exit(1)
  })
}

export { VerificationExecutor, VerificationOptions }