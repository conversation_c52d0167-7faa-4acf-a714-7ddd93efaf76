/**
 * 集成测试执行脚本
 * 提供命令行接口来运行各种集成测试
 */

import { IntegrationTestRunner, IntegrationTestConfig } from './integration/IntegrationTestRunner'

interface TestOptions {
  suite?: string
  parallel?: boolean
  cleanup?: boolean
  detailed?: boolean
  export?: boolean
  outputPath?: string
  dataSet?: string
}

class TestExecutor {
  private runner: IntegrationTestRunner

  constructor(options: TestOptions = {}) {
    const config: Partial<IntegrationTestConfig> = {
      suites: {
        userFlow: !options.suite || options.suite === 'userFlow' || options.suite === 'all',
        crossComponent: !options.suite || options.suite === 'crossComponent' || options.suite === 'all',
        dataConsistency: !options.suite || options.suite === 'dataConsistency' || options.suite === 'all'
      },
      environment: {
        dataSetId: options.dataSet || 'standard',
        cleanup: options.cleanup !== false,
        parallel: options.parallel === true
      },
      reporting: {
        detailed: options.detailed !== false,
        exportResults: options.export === true,
        outputPath: options.outputPath
      }
    }

    this.runner = new IntegrationTestRunner(config)
  }

  /**
   * 执行测试
   */
  async execute(): Promise<void> {
    console.log('🚀 开始执行Chrome垂直侧边栏集成测试')
    console.log('=' .repeat(50))

    try {
      // 验证测试环境
      console.log('🔍 验证测试环境...')
      const validation = await this.runner.validateEnvironment()
      
      if (!validation.valid) {
        console.error('❌ 测试环境验证失败:')
        validation.errors.forEach(error => console.error(`  - ${error}`))
        return
      }

      if (validation.warnings.length > 0) {
        console.warn('⚠️  测试环境警告:')
        validation.warnings.forEach(warning => console.warn(`  - ${warning}`))
      }

      console.log('✅ 测试环境验证通过')
      console.log('')

      // 运行测试
      const startTime = Date.now()
      const report = await this.runner.runAllTests()
      const endTime = Date.now()

      // 显示结果
      console.log('')
      console.log('📊 测试结果')
      console.log('=' .repeat(50))
      
      const summaryReport = this.runner.generateSummaryReport(report)
      console.log(summaryReport)

      // 详细结果
      if (report.config.reporting.detailed) {
        this.displayDetailedResults(report)
      }

      // 性能统计
      this.displayPerformanceStats(report)

      // 最终状态
      const success = report.overallSummary.failedSuites === 0
      if (success) {
        console.log('🎉 所有测试通过!')
      } else {
        console.log('💥 部分测试失败')
        process.exit(1)
      }

    } catch (error) {
      console.error('💥 测试执行失败:', error.message)
      console.error(error.stack)
      process.exit(1)
    }
  }

  /**
   * 显示详细结果
   */
  private displayDetailedResults(report: any): void {
    console.log('')
    console.log('📋 详细测试结果')
    console.log('-' .repeat(30))

    report.suiteResults.forEach((suiteResult: any) => {
      console.log(`\n📦 ${suiteResult.name}`)
      
      if (suiteResult.summary && suiteResult.summary.results) {
        suiteResult.summary.results.forEach((testResult: any) => {
          const status = testResult.success ? '✅' : '❌'
          console.log(`  ${status} ${testResult.name} (${testResult.duration}ms)`)
          
          if (!testResult.success && testResult.error) {
            console.log(`    错误: ${testResult.error}`)
          }
        })
      }
    })
  }

  /**
   * 显示性能统计
   */
  private displayPerformanceStats(report: any): void {
    console.log('')
    console.log('⚡ 性能统计')
    console.log('-' .repeat(20))
    
    console.log(`总执行时间: ${report.totalDuration}ms`)
    console.log(`内存使用: ${(report.environment.memoryUsage / 1024).toFixed(2)}KB`)
    
    const avgTestTime = report.overallSummary.totalTests > 0 
      ? (report.totalDuration / report.overallSummary.totalTests).toFixed(2)
      : '0'
    console.log(`平均测试时间: ${avgTestTime}ms`)

    // 套件性能
    report.suiteResults.forEach((suite: any) => {
      const testsInSuite = suite.summary?.totalTests || 0
      const avgSuiteTime = testsInSuite > 0 
        ? (suite.duration / testsInSuite).toFixed(2)
        : '0'
      console.log(`${suite.name}: ${suite.duration}ms (平均 ${avgSuiteTime}ms/测试)`)
    })
  }

  /**
   * 运行特定套件
   */
  async runSuite(suiteName: string): Promise<void> {
    console.log(`🎯 运行测试套件: ${suiteName}`)
    
    try {
      const result = await this.runner.runSpecificSuite(suiteName)
      
      console.log('')
      console.log('📊 套件结果')
      console.log('-' .repeat(20))
      
      const status = result.success ? '✅ 通过' : '❌ 失败'
      console.log(`${result.name}: ${status}`)
      console.log(`执行时间: ${result.duration}ms`)
      
      if (result.summary) {
        console.log(`测试用例: ${result.summary.passedTests}/${result.summary.totalTests} 通过`)
      }
      
      if (result.error) {
        console.error(`错误: ${result.error}`)
        process.exit(1)
      }
      
    } catch (error) {
      console.error(`套件执行失败: ${error.message}`)
      process.exit(1)
    }
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(): TestOptions {
  const args = process.argv.slice(2)
  const options: TestOptions = {}

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--suite':
        options.suite = args[++i]
        break
      case '--parallel':
        options.parallel = true
        break
      case '--no-cleanup':
        options.cleanup = false
        break
      case '--no-detailed':
        options.detailed = false
        break
      case '--export':
        options.export = true
        break
      case '--output':
        options.outputPath = args[++i]
        break
      case '--dataset':
        options.dataSet = args[++i]
        break
      case '--help':
        showHelp()
        process.exit(0)
        break
    }
  }

  return options
}

/**
 * 显示帮助信息
 */
function showHelp(): void {
  console.log(`
Chrome垂直侧边栏集成测试

用法:
  npm run test:integration [选项]

选项:
  --suite <name>     运行特定测试套件 (userFlow|crossComponent|dataConsistency|all)
  --parallel         并行运行测试套件
  --no-cleanup       测试后不清理环境
  --no-detailed      不显示详细结果
  --export           导出测试结果
  --output <path>    指定结果输出路径
  --dataset <id>     使用特定数据集
  --help             显示此帮助信息

示例:
  npm run test:integration                    # 运行所有测试
  npm run test:integration --suite userFlow  # 只运行用户流程测试
  npm run test:integration --parallel        # 并行运行所有测试
  npm run test:integration --export          # 运行测试并导出结果
`)
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const options = parseArgs()
  const executor = new TestExecutor(options)

  if (options.suite && options.suite !== 'all') {
    await executor.runSuite(options.suite)
  } else {
    await executor.execute()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('执行失败:', error)
    process.exit(1)
  })
}

export { TestExecutor, TestOptions }