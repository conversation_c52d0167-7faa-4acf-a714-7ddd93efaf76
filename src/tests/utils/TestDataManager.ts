/**
 * 测试数据管理和清理机制
 * 提供测试数据的创建、管理和清理功能
 */

interface TestDataSet {
  id: string
  name: string
  tabs: chrome.tabs.Tab[]
  bookmarks: chrome.bookmarks.BookmarkTreeNode[]
  settings: UserSettings
  sidebarState: SidebarState
  createdAt: number
  usedBy: string[]
}

interface UserSettings {
  theme: 'light' | 'dark' | 'auto'
  position: 'left' | 'right'
  expandDelay: number
  collapseDelay: number
  showBookmarks: boolean
  showTabs: boolean
  defaultPinned: boolean
  keyboardShortcuts: {
    toggle: string
    search: string
  }
}

interface SidebarState {
  visible: boolean
  expanded: boolean
  pinned: boolean
  activeSection: 'tabs' | 'bookmarks' | 'tools'
  searchQuery: string
}

interface TestEnvironment {
  id: string
  name: string
  components: ComponentMocks
  dataSet: TestDataSet
  cleanup: (() => void)[]
}

interface ComponentMocks {
  chrome: any
  dom: {
    elements: Map<string, HTMLElement>
    events: Event[]
  }
  storage: Map<string, any>
  network: {
    requests: NetworkRequest[]
    responses: Map<string, any>
  }
}

interface NetworkRequest {
  url: string
  method: string
  headers: Record<string, string>
  body?: any
  timestamp: number
}

class TestDataManager {
  private dataSets: Map<string, TestDataSet> = new Map()
  private environments: Map<string, TestEnvironment> = new Map()
  private cleanupTasks: (() => void)[] = []

  /**
   * 创建标准测试数据集
   */
  createStandardDataSet(id: string, name: string): TestDataSet {
    const dataSet: TestDataSet = {
      id,
      name,
      tabs: this.createStandardTabs(),
      bookmarks: this.createStandardBookmarks(),
      settings: this.createStandardSettings(),
      sidebarState: this.createStandardSidebarState(),
      createdAt: Date.now(),
      usedBy: []
    }

    this.dataSets.set(id, dataSet)
    return dataSet
  }

  /**
   * 创建自定义测试数据集
   */
  createCustomDataSet(
    id: string,
    name: string,
    options: Partial<TestDataSet>
  ): TestDataSet {
    const dataSet: TestDataSet = {
      id,
      name,
      tabs: options.tabs || this.createStandardTabs(),
      bookmarks: options.bookmarks || this.createStandardBookmarks(),
      settings: options.settings || this.createStandardSettings(),
      sidebarState: options.sidebarState || this.createStandardSidebarState(),
      createdAt: Date.now(),
      usedBy: []
    }

    this.dataSets.set(id, dataSet)
    return dataSet
  }

  /**
   * 创建标准标签页数据
   */
  private createStandardTabs(): chrome.tabs.Tab[] {
    return [
      {
        id: 1,
        title: 'Google',
        url: 'https://www.google.com',
        favIconUrl: 'https://www.google.com/favicon.ico',
        active: true,
        pinned: false,
        index: 0,
        windowId: 1,
        highlighted: true,
        incognito: false,
        selected: true,
        discarded: false,
        autoDiscardable: true,
        groupId: -1
      },
      {
        id: 2,
        title: 'GitHub - Where the world builds software',
        url: 'https://github.com',
        favIconUrl: 'https://github.com/favicon.ico',
        active: false,
        pinned: true,
        index: 1,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 1
      },
      {
        id: 3,
        title: 'Stack Overflow - Where Developers Learn',
        url: 'https://stackoverflow.com',
        favIconUrl: 'https://stackoverflow.com/favicon.ico',
        active: false,
        pinned: false,
        index: 2,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 1
      },
      {
        id: 4,
        title: 'MDN Web Docs',
        url: 'https://developer.mozilla.org',
        favIconUrl: 'https://developer.mozilla.org/favicon.ico',
        active: false,
        pinned: false,
        index: 3,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 2
      },
      {
        id: 5,
        title: 'TypeScript Documentation',
        url: 'https://www.typescriptlang.org/docs',
        favIconUrl: 'https://www.typescriptlang.org/favicon.ico',
        active: false,
        pinned: false,
        index: 4,
        windowId: 1,
        highlighted: false,
        incognito: false,
        selected: false,
        discarded: false,
        autoDiscardable: true,
        groupId: 2
      }
    ]
  }

  /**
   * 创建标准收藏夹数据
   */
  private createStandardBookmarks(): chrome.bookmarks.BookmarkTreeNode[] {
    return [
      {
        id: '1',
        title: 'Bookmarks Bar',
        dateAdded: Date.now() - 86400000,
        children: [
          {
            id: '2',
            title: 'Development',
            dateAdded: Date.now() - 86400000,
            parentId: '1',
            children: [
              {
                id: '3',
                title: 'GitHub',
                url: 'https://github.com',
                dateAdded: Date.now() - 86400000,
                parentId: '2'
              },
              {
                id: '4',
                title: 'Stack Overflow',
                url: 'https://stackoverflow.com',
                dateAdded: Date.now() - 86400000,
                parentId: '2'
              },
              {
                id: '5',
                title: 'MDN Web Docs',
                url: 'https://developer.mozilla.org',
                dateAdded: Date.now() - 86400000,
                parentId: '2'
              }
            ]
          },
          {
            id: '6',
            title: 'Tools',
            dateAdded: Date.now() - 86400000,
            parentId: '1',
            children: [
              {
                id: '7',
                title: 'Chrome DevTools',
                url: 'chrome://devtools',
                dateAdded: Date.now() - 86400000,
                parentId: '6'
              },
              {
                id: '8',
                title: 'Chrome Extensions',
                url: 'chrome://extensions',
                dateAdded: Date.now() - 86400000,
                parentId: '6'
              }
            ]
          },
          {
            id: '9',
            title: 'Google',
            url: 'https://www.google.com',
            dateAdded: Date.now() - 86400000,
            parentId: '1'
          }
        ]
      }
    ]
  }

  /**
   * 创建标准设置数据
   */
  private createStandardSettings(): UserSettings {
    return {
      theme: 'light',
      position: 'left',
      expandDelay: 500,
      collapseDelay: 500,
      showBookmarks: true,
      showTabs: true,
      defaultPinned: false,
      keyboardShortcuts: {
        toggle: 'Ctrl+Shift+S',
        search: 'Ctrl+F'
      }
    }
  }

  /**
   * 创建标准侧边栏状态
   */
  private createStandardSidebarState(): SidebarState {
    return {
      visible: false,
      expanded: false,
      pinned: false,
      activeSection: 'tabs',
      searchQuery: ''
    }
  }

  /**
   * 创建测试环境
   */
  createTestEnvironment(id: string, name: string, dataSetId: string): TestEnvironment {
    const dataSet = this.dataSets.get(dataSetId)
    if (!dataSet) {
      throw new Error(`Data set ${dataSetId} not found`)
    }

    const environment: TestEnvironment = {
      id,
      name,
      components: this.createComponentMocks(dataSet),
      dataSet,
      cleanup: []
    }

    // 记录数据集使用
    dataSet.usedBy.push(id)

    this.environments.set(id, environment)
    return environment
  }

  /**
   * 创建组件模拟对象
   */
  private createComponentMocks(dataSet: TestDataSet): ComponentMocks {
    const mocks: ComponentMocks = {
      chrome: this.createChromeMocks(dataSet),
      dom: {
        elements: new Map(),
        events: []
      },
      storage: new Map(),
      network: {
        requests: [],
        responses: new Map()
      }
    }

    return mocks
  }

  /**
   * 创建Chrome API模拟
   */
  private createChromeMocks(dataSet: TestDataSet): any {
    const chromeMocks = {
      tabs: {
        query: jest.fn().mockResolvedValue(dataSet.tabs),
        get: jest.fn().mockImplementation((tabId: number) => 
          Promise.resolve(dataSet.tabs.find(tab => tab.id === tabId))
        ),
        update: jest.fn().mockResolvedValue({}),
        remove: jest.fn().mockResolvedValue({}),
        create: jest.fn().mockImplementation((createProperties: any) => 
          Promise.resolve({ 
            id: Date.now(), 
            ...createProperties,
            active: true,
            pinned: false,
            index: dataSet.tabs.length,
            windowId: 1,
            highlighted: true,
            incognito: false,
            selected: true,
            discarded: false,
            autoDiscardable: true,
            groupId: -1
          })
        ),
        onActivated: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onUpdated: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onRemoved: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onCreated: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        }
      },
      bookmarks: {
        getTree: jest.fn().mockResolvedValue(dataSet.bookmarks),
        get: jest.fn().mockImplementation((id: string) => 
          Promise.resolve([this.findBookmarkById(dataSet.bookmarks, id)].filter(Boolean))
        ),
        create: jest.fn().mockImplementation((bookmark: any) => 
          Promise.resolve({ 
            id: Date.now().toString(), 
            ...bookmark,
            dateAdded: Date.now()
          })
        ),
        update: jest.fn().mockResolvedValue({}),
        remove: jest.fn().mockResolvedValue({}),
        move: jest.fn().mockResolvedValue({}),
        onCreated: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onRemoved: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onChanged: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        onMoved: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        }
      },
      storage: {
        sync: {
          get: jest.fn().mockImplementation((keys: string | string[] | null) => {
            const result: any = {}
            if (keys === null) {
              result.settings = dataSet.settings
              result.sidebarState = dataSet.sidebarState
            } else if (typeof keys === 'string') {
              if (keys === 'settings') result.settings = dataSet.settings
              if (keys === 'sidebarState') result.sidebarState = dataSet.sidebarState
            } else if (Array.isArray(keys)) {
              keys.forEach(key => {
                if (key === 'settings') result.settings = dataSet.settings
                if (key === 'sidebarState') result.sidebarState = dataSet.sidebarState
              })
            }
            return Promise.resolve(result)
          }),
          set: jest.fn().mockResolvedValue({}),
          remove: jest.fn().mockResolvedValue({}),
          clear: jest.fn().mockResolvedValue({})
        },
        local: {
          get: jest.fn().mockResolvedValue({}),
          set: jest.fn().mockResolvedValue({}),
          remove: jest.fn().mockResolvedValue({}),
          clear: jest.fn().mockResolvedValue({})
        },
        onChanged: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        }
      },
      runtime: {
        sendMessage: jest.fn().mockResolvedValue({ success: true }),
        onMessage: { 
          addListener: jest.fn(),
          removeListener: jest.fn()
        },
        getURL: jest.fn().mockImplementation((path: string) => `chrome-extension://test/${path}`),
        id: 'test-extension-id'
      },
      action: {
        setBadgeText: jest.fn().mockResolvedValue({}),
        setBadgeBackgroundColor: jest.fn().mockResolvedValue({}),
        setIcon: jest.fn().mockResolvedValue({})
      }
    }

    return chromeMocks
  }

  /**
   * 根据ID查找收藏夹
   */
  private findBookmarkById(bookmarks: chrome.bookmarks.BookmarkTreeNode[], id: string): chrome.bookmarks.BookmarkTreeNode | undefined {
    for (const bookmark of bookmarks) {
      if (bookmark.id === id) {
        return bookmark
      }
      if (bookmark.children) {
        const found = this.findBookmarkById(bookmark.children, id)
        if (found) return found
      }
    }
    return undefined
  }

  /**
   * 应用测试环境
   */
  applyTestEnvironment(environmentId: string): void {
    const environment = this.environments.get(environmentId)
    if (!environment) {
      throw new Error(`Environment ${environmentId} not found`)
    }

    // 应用Chrome API模拟
    global.chrome = environment.components.chrome

    // 设置DOM清理
    const originalCreateElement = document.createElement
    document.createElement = jest.fn().mockImplementation((tagName: string) => {
      const element = originalCreateElement.call(document, tagName)
      environment.components.dom.elements.set(element.id || `element-${Date.now()}`, element)
      return element
    })

    // 添加清理任务
    environment.cleanup.push(() => {
      document.createElement = originalCreateElement
      environment.components.dom.elements.clear()
      environment.components.dom.events = []
    })

    this.cleanupTasks.push(...environment.cleanup)
  }

  /**
   * 获取数据集
   */
  getDataSet(id: string): TestDataSet | undefined {
    return this.dataSets.get(id)
  }

  /**
   * 获取测试环境
   */
  getTestEnvironment(id: string): TestEnvironment | undefined {
    return this.environments.get(id)
  }

  /**
   * 更新数据集
   */
  updateDataSet(id: string, updates: Partial<TestDataSet>): void {
    const dataSet = this.dataSets.get(id)
    if (!dataSet) {
      throw new Error(`Data set ${id} not found`)
    }

    Object.assign(dataSet, updates)
  }

  /**
   * 克隆数据集
   */
  cloneDataSet(sourceId: string, newId: string, newName: string): TestDataSet {
    const source = this.dataSets.get(sourceId)
    if (!source) {
      throw new Error(`Source data set ${sourceId} not found`)
    }

    const cloned: TestDataSet = {
      id: newId,
      name: newName,
      tabs: JSON.parse(JSON.stringify(source.tabs)),
      bookmarks: JSON.parse(JSON.stringify(source.bookmarks)),
      settings: JSON.parse(JSON.stringify(source.settings)),
      sidebarState: JSON.parse(JSON.stringify(source.sidebarState)),
      createdAt: Date.now(),
      usedBy: []
    }

    this.dataSets.set(newId, cloned)
    return cloned
  }

  /**
   * 清理单个数据集
   */
  cleanupDataSet(id: string): void {
    const dataSet = this.dataSets.get(id)
    if (dataSet) {
      // 清理使用该数据集的环境
      dataSet.usedBy.forEach(envId => {
        this.cleanupEnvironment(envId)
      })
      
      this.dataSets.delete(id)
    }
  }

  /**
   * 清理单个环境
   */
  cleanupEnvironment(id: string): void {
    const environment = this.environments.get(id)
    if (environment) {
      // 执行清理任务
      environment.cleanup.forEach(cleanup => {
        try {
          cleanup()
        } catch (error) {
          console.warn(`Cleanup task failed for environment ${id}:`, error)
        }
      })

      // 从数据集的使用记录中移除
      const dataSet = environment.dataSet
      const index = dataSet.usedBy.indexOf(id)
      if (index > -1) {
        dataSet.usedBy.splice(index, 1)
      }

      this.environments.delete(id)
    }
  }

  /**
   * 清理所有测试数据
   */
  cleanupAll(): void {
    // 执行所有清理任务
    this.cleanupTasks.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.warn('Cleanup task failed:', error)
      }
    })

    // 清理环境
    this.environments.forEach((_, id) => {
      this.cleanupEnvironment(id)
    })

    // 清理数据集
    this.dataSets.clear()
    this.environments.clear()
    this.cleanupTasks = []

    // 恢复全局对象
    if (global.chrome) {
      delete global.chrome
    }
  }

  /**
   * 获取使用统计
   */
  getUsageStats(): UsageStats {
    const totalDataSets = this.dataSets.size
    const totalEnvironments = this.environments.size
    const activeEnvironments = Array.from(this.environments.values()).filter(env => 
      env.cleanup.length > 0
    ).length

    const dataSetUsage = new Map<string, number>()
    this.dataSets.forEach(dataSet => {
      dataSetUsage.set(dataSet.id, dataSet.usedBy.length)
    })

    return {
      totalDataSets,
      totalEnvironments,
      activeEnvironments,
      dataSetUsage: Object.fromEntries(dataSetUsage),
      memoryUsage: this.estimateMemoryUsage()
    }
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(): number {
    let totalSize = 0

    this.dataSets.forEach(dataSet => {
      totalSize += JSON.stringify(dataSet).length
    })

    this.environments.forEach(environment => {
      totalSize += JSON.stringify(environment.dataSet).length
      totalSize += environment.components.dom.elements.size * 1000 // 估算DOM元素大小
    })

    return totalSize
  }

  /**
   * 验证数据完整性
   */
  validateDataIntegrity(): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证数据集
    this.dataSets.forEach((dataSet, id) => {
      if (!dataSet.tabs || dataSet.tabs.length === 0) {
        warnings.push(`Data set ${id} has no tabs`)
      }

      if (!dataSet.bookmarks || dataSet.bookmarks.length === 0) {
        warnings.push(`Data set ${id} has no bookmarks`)
      }

      if (!dataSet.settings) {
        errors.push(`Data set ${id} has no settings`)
      }

      // 验证标签页ID唯一性
      const tabIds = new Set()
      dataSet.tabs.forEach(tab => {
        if (tabIds.has(tab.id)) {
          errors.push(`Duplicate tab ID ${tab.id} in data set ${id}`)
        }
        tabIds.add(tab.id)
      })
    })

    // 验证环境
    this.environments.forEach((environment, id) => {
      if (!this.dataSets.has(environment.dataSet.id)) {
        errors.push(`Environment ${id} references non-existent data set ${environment.dataSet.id}`)
      }
    })

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }
}

interface UsageStats {
  totalDataSets: number
  totalEnvironments: number
  activeEnvironments: number
  dataSetUsage: Record<string, number>
  memoryUsage: number
}

interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export { 
  TestDataManager, 
  TestDataSet, 
  TestEnvironment, 
  UserSettings, 
  SidebarState,
  UsageStats,
  ValidationResult
}