/**
 * 最终验证运行器
 * 统一执行所有验证流程并生成最终报告
 */

import { FunctionalVerificationSuite, VerificationReport } from './FunctionalVerificationSuite'
import { CodeQualityAnalyzer, QualityReport } from './CodeQualityAnalyzer'
import { IntegrationTestRunner, IntegrationTestReport } from '../integration/IntegrationTestRunner'

interface FinalVerificationConfig {
  includeIntegrationTests: boolean
  includeFunctionalVerification: boolean
  includeCodeQualityAnalysis: boolean
  generateDetailedReport: boolean
  exportResults: boolean
  outputPath?: string
  failOnIssues: {
    critical: boolean
    major: boolean
    securityCritical: boolean
    testFailures: boolean
  }
}

interface FinalVerificationReport {
  summary: {
    overallStatus: 'passed' | 'failed' | 'warning'
    overallScore: number
    readinessLevel: 'production-ready' | 'needs-minor-fixes' | 'needs-major-fixes' | 'not-ready'
    timestamp: number
    duration: number
  }
  integrationTests?: IntegrationTestReport
  functionalVerification?: VerificationReport
  codeQuality?: QualityReport
  criticalIssues: CriticalIssue[]
  recommendations: FinalRecommendation[]
  releaseNotes: ReleaseNote[]
  nextSteps: NextStep[]
}

interface CriticalIssue {
  category: 'functionality' | 'security' | 'performance' | 'compatibility'
  severity: 'critical' | 'major' | 'minor'
  description: string
  impact: string
  location: string
  mustFixBeforeRelease: boolean
  estimatedFixTime: string
}

interface FinalRecommendation {
  priority: 'immediate' | 'before-release' | 'post-release' | 'future'
  category: 'bug-fix' | 'enhancement' | 'optimization' | 'maintenance'
  title: string
  description: string
  benefits: string[]
  risks: string[]
  effort: string
}

interface ReleaseNote {
  type: 'feature' | 'improvement' | 'bugfix' | 'security' | 'breaking-change'
  title: string
  description: string
  userImpact: string
  technicalDetails?: string
}

interface NextStep {
  phase: 'immediate' | 'pre-release' | 'post-release' | 'maintenance'
  action: string
  owner: string
  deadline: string
  dependencies: string[]
}

class FinalVerificationRunner {
  private config: FinalVerificationConfig
  private integrationRunner: IntegrationTestRunner
  private functionalSuite: FunctionalVerificationSuite
  private qualityAnalyzer: CodeQualityAnalyzer

  constructor(config: Partial<FinalVerificationConfig> = {}) {
    this.config = this.mergeConfig(config)
    this.integrationRunner = new IntegrationTestRunner()
    this.functionalSuite = new FunctionalVerificationSuite()
    this.qualityAnalyzer = new CodeQualityAnalyzer()
  }

  private mergeConfig(config: Partial<FinalVerificationConfig>): FinalVerificationConfig {
    return {
      includeIntegrationTests: config.includeIntegrationTests ?? true,
      includeFunctionalVerification: config.includeFunctionalVerification ?? true,
      includeCodeQualityAnalysis: config.includeCodeQualityAnalysis ?? true,
      generateDetailedReport: config.generateDetailedReport ?? true,
      exportResults: config.exportResults ?? true,
      outputPath: config.outputPath,
      failOnIssues: {
        critical: config.failOnIssues?.critical ?? true,
        major: config.failOnIssues?.major ?? false,
        securityCritical: config.failOnIssues?.securityCritical ?? true,
        testFailures: config.failOnIssues?.testFailures ?? true
      }
    }
  }

  /**
   * 执行完整的最终验证
   */
  async executeFullVerification(): Promise<FinalVerificationReport> {
    console.log('🚀 开始执行最终验证流程...')
    console.log('=' .repeat(60))
    
    const startTime = Date.now()
    let integrationReport: IntegrationTestReport | undefined
    let functionalReport: VerificationReport | undefined
    let qualityReport: QualityReport | undefined

    try {
      // 1. 执行集成测试
      if (this.config.includeIntegrationTests) {
        console.log('📋 执行集成测试...')
        integrationReport = await this.integrationRunner.runAllTests()
        this.logIntegrationResults(integrationReport)
      }

      // 2. 执行功能验证
      if (this.config.includeFunctionalVerification) {
        console.log('\n🔍 执行功能验证...')
        functionalReport = await this.functionalSuite.executeFullVerification()
        this.logFunctionalResults(functionalReport)
      }

      // 3. 执行代码质量分析
      if (this.config.includeCodeQualityAnalysis) {
        console.log('\n📊 执行代码质量分析...')
        qualityReport = await this.qualityAnalyzer.analyzeCodeQuality()
        this.logQualityResults(qualityReport)
      }

      // 4. 生成最终报告
      const finalReport = this.generateFinalReport(
        startTime,
        integrationReport,
        functionalReport,
        qualityReport
      )

      // 5. 输出结果
      this.displayFinalResults(finalReport)

      // 6. 导出报告
      if (this.config.exportResults) {
        await this.exportFinalReport(finalReport)
      }

      // 7. 检查是否应该失败
      this.checkFailureConditions(finalReport)

      console.log('\n✅ 最终验证完成')
      return finalReport

    } catch (error) {
      console.error('\n💥 最终验证执行失败:', error.message)
      throw error
    }
  }

  /**
   * 记录集成测试结果
   */
  private logIntegrationResults(report: IntegrationTestReport): void {
    const { overallSummary } = report
    const status = overallSummary.failedSuites === 0 ? '✅' : '❌'
    
    console.log(`  ${status} 集成测试: ${overallSummary.passedSuites}/${overallSummary.totalSuites} 套件通过`)
    console.log(`     测试用例: ${overallSummary.passedTests}/${overallSummary.totalTests} 通过`)
    console.log(`     成功率: ${overallSummary.successRate.toFixed(1)}%`)
    console.log(`     耗时: ${report.totalDuration}ms`)
  }

  /**
   * 记录功能验证结果
   */
  private logFunctionalResults(report: VerificationReport): void {
    const { summary } = report
    const status = summary.failedRequirements === 0 ? '✅' : '❌'
    
    console.log(`  ${status} 功能验证: ${summary.passedRequirements}/${summary.totalRequirements} 需求通过`)
    console.log(`     总体评分: ${summary.overallScore.toFixed(1)}/100`)
    console.log(`     关键问题: ${summary.criticalIssues}个`)
    console.log(`     主要问题: ${summary.majorIssues}个`)
  }

  /**
   * 记录代码质量结果
   */
  private logQualityResults(report: QualityReport): void {
    const { overall, issues } = report
    const status = overall.status === 'excellent' || overall.status === 'good' ? '✅' : '❌'
    
    console.log(`  ${status} 代码质量: ${overall.score}/100 (${overall.grade}级)`)
    console.log(`     状态: ${overall.status}`)
    console.log(`     代码问题: ${issues.code.length}个`)
    console.log(`     安全问题: ${issues.security.length}个`)
    console.log(`     性能问题: ${issues.performance.length}个`)
  }

  /**
   * 生成最终报告
   */
  private generateFinalReport(
    startTime: number,
    integrationReport?: IntegrationTestReport,
    functionalReport?: VerificationReport,
    qualityReport?: QualityReport
  ): FinalVerificationReport {
    const endTime = Date.now()
    const duration = endTime - startTime

    // 计算总体状态和评分
    const { overallStatus, overallScore, readinessLevel } = this.calculateOverallStatus(
      integrationReport,
      functionalReport,
      qualityReport
    )

    // 收集关键问题
    const criticalIssues = this.collectCriticalIssues(
      integrationReport,
      functionalReport,
      qualityReport
    )

    // 生成建议
    const recommendations = this.generateFinalRecommendations(
      integrationReport,
      functionalReport,
      qualityReport,
      criticalIssues
    )

    // 生成发布说明
    const releaseNotes = this.generateReleaseNotes(
      integrationReport,
      functionalReport,
      qualityReport
    )

    // 生成后续步骤
    const nextSteps = this.generateNextSteps(criticalIssues, recommendations)

    return {
      summary: {
        overallStatus,
        overallScore,
        readinessLevel,
        timestamp: endTime,
        duration
      },
      integrationTests: integrationReport,
      functionalVerification: functionalReport,
      codeQuality: qualityReport,
      criticalIssues,
      recommendations,
      releaseNotes,
      nextSteps
    }
  }

  /**
   * 计算总体状态
   */
  private calculateOverallStatus(
    integrationReport?: IntegrationTestReport,
    functionalReport?: VerificationReport,
    qualityReport?: QualityReport
  ): { overallStatus: 'passed' | 'failed' | 'warning', overallScore: number, readinessLevel: string } {
    let totalScore = 0
    let scoreCount = 0
    let hasFailures = false
    let hasWarnings = false

    // 集成测试评分
    if (integrationReport) {
      const integrationScore = integrationReport.overallSummary.successRate
      totalScore += integrationScore
      scoreCount++
      
      if (integrationReport.overallSummary.failedTests > 0) {
        hasFailures = true
      }
    }

    // 功能验证评分
    if (functionalReport) {
      const functionalScore = functionalReport.summary.overallScore
      totalScore += functionalScore
      scoreCount++
      
      if (functionalReport.summary.failedRequirements > 0) {
        hasFailures = true
      }
      
      if (functionalReport.summary.criticalIssues > 0) {
        hasFailures = true
      } else if (functionalReport.summary.majorIssues > 0) {
        hasWarnings = true
      }
    }

    // 代码质量评分
    if (qualityReport) {
      totalScore += qualityReport.overall.score
      scoreCount++
      
      if (qualityReport.overall.status === 'critical' || qualityReport.overall.status === 'poor') {
        hasFailures = true
      } else if (qualityReport.overall.status === 'fair') {
        hasWarnings = true
      }
    }

    const overallScore = scoreCount > 0 ? totalScore / scoreCount : 0

    // 确定总体状态
    let overallStatus: 'passed' | 'failed' | 'warning'
    if (hasFailures) {
      overallStatus = 'failed'
    } else if (hasWarnings) {
      overallStatus = 'warning'
    } else {
      overallStatus = 'passed'
    }

    // 确定就绪级别
    let readinessLevel: string
    if (overallScore >= 90 && overallStatus === 'passed') {
      readinessLevel = 'production-ready'
    } else if (overallScore >= 80 && overallStatus !== 'failed') {
      readinessLevel = 'needs-minor-fixes'
    } else if (overallScore >= 60) {
      readinessLevel = 'needs-major-fixes'
    } else {
      readinessLevel = 'not-ready'
    }

    return { overallStatus, overallScore, readinessLevel }
  }

  /**
   * 收集关键问题
   */
  private collectCriticalIssues(
    integrationReport?: IntegrationTestReport,
    functionalReport?: VerificationReport,
    qualityReport?: QualityReport
  ): CriticalIssue[] {
    const issues: CriticalIssue[] = []

    // 从集成测试收集问题
    if (integrationReport) {
      integrationReport.suiteResults.forEach(suite => {
        if (!suite.success && suite.error) {
          issues.push({
            category: 'functionality',
            severity: 'critical',
            description: `集成测试套件失败: ${suite.name}`,
            impact: '核心功能可能无法正常工作',
            location: suite.name,
            mustFixBeforeRelease: true,
            estimatedFixTime: '1-2天'
          })
        }
      })
    }

    // 从功能验证收集问题
    if (functionalReport) {
      functionalReport.requirementResults.forEach((result, reqId) => {
        if (!result.passed && result.issues) {
          result.issues.forEach(issue => {
            if (issue.severity === 'critical') {
              issues.push({
                category: 'functionality',
                severity: 'critical',
                description: `需求验证失败: ${reqId} - ${issue.description}`,
                impact: issue.description,
                location: issue.location,
                mustFixBeforeRelease: true,
                estimatedFixTime: '0.5-1天'
              })
            }
          })
        }
      })
    }

    // 从代码质量收集问题
    if (qualityReport) {
      qualityReport.issues.security.forEach(issue => {
        if (issue.severity === 'critical') {
          issues.push({
            category: 'security',
            severity: 'critical',
            description: issue.description,
            impact: issue.impact,
            location: issue.location,
            mustFixBeforeRelease: true,
            estimatedFixTime: '0.5-1天'
          })
        }
      })

      qualityReport.issues.performance.forEach(issue => {
        if (issue.priority === 'high') {
          issues.push({
            category: 'performance',
            severity: 'major',
            description: issue.description,
            impact: issue.impact,
            location: issue.location,
            mustFixBeforeRelease: false,
            estimatedFixTime: '1-2天'
          })
        }
      })
    }

    return issues
  }

  /**
   * 生成最终建议
   */
  private generateFinalRecommendations(
    integrationReport?: IntegrationTestReport,
    functionalReport?: VerificationReport,
    qualityReport?: QualityReport,
    criticalIssues: CriticalIssue[] = []
  ): FinalRecommendation[] {
    const recommendations: FinalRecommendation[] = []

    // 基于关键问题的建议
    const mustFixIssues = criticalIssues.filter(i => i.mustFixBeforeRelease)
    if (mustFixIssues.length > 0) {
      recommendations.push({
        priority: 'immediate',
        category: 'bug-fix',
        title: '修复发布阻塞问题',
        description: `修复${mustFixIssues.length}个必须在发布前解决的关键问题`,
        benefits: ['确保产品质量', '避免用户体验问题', '降低安全风险'],
        risks: ['延迟发布时间'],
        effort: '1-3天'
      })
    }

    // 基于测试结果的建议
    if (integrationReport && integrationReport.overallSummary.successRate < 100) {
      recommendations.push({
        priority: 'before-release',
        category: 'bug-fix',
        title: '完善测试覆盖',
        description: '修复失败的测试用例，确保所有功能正常工作',
        benefits: ['提高产品稳定性', '增强用户信心'],
        risks: ['可能发现更多问题'],
        effort: '2-4天'
      })
    }

    // 基于代码质量的建议
    if (qualityReport && qualityReport.overall.score < 80) {
      recommendations.push({
        priority: 'post-release',
        category: 'optimization',
        title: '提升代码质量',
        description: '重构低质量代码，提高可维护性',
        benefits: ['降低维护成本', '提高开发效率', '减少bug数量'],
        risks: ['可能引入新的问题'],
        effort: '1-2周'
      })
    }

    // 性能优化建议
    if (qualityReport && qualityReport.issues.performance.length > 0) {
      recommendations.push({
        priority: 'post-release',
        category: 'optimization',
        title: '性能优化',
        description: '优化识别出的性能瓶颈',
        benefits: ['提升用户体验', '降低资源消耗'],
        risks: ['优化过程可能影响稳定性'],
        effort: '3-5天'
      })
    }

    return recommendations
  }

  /**
   * 生成发布说明
   */
  private generateReleaseNotes(
    integrationReport?: IntegrationTestReport,
    functionalReport?: VerificationReport,
    qualityReport?: QualityReport
  ): ReleaseNote[] {
    const notes: ReleaseNote[] = []

    // 主要功能
    notes.push({
      type: 'feature',
      title: 'Chrome垂直侧边栏扩展',
      description: '提供类似Microsoft Edge的垂直侧边栏功能',
      userImpact: '用户可以在Chrome中使用垂直侧边栏管理标签页和收藏夹',
      technicalDetails: '基于Chrome Extension Manifest V3开发'
    })

    // 核心功能
    notes.push({
      type: 'feature',
      title: '智能展开/收起机制',
      description: '鼠标悬停自动展开，离开后自动收起',
      userImpact: '提供流畅的交互体验，不影响页面浏览'
    })

    notes.push({
      type: 'feature',
      title: '标签页管理',
      description: '在侧边栏中显示和管理当前窗口的所有标签页',
      userImpact: '方便用户在多个标签页间快速切换'
    })

    notes.push({
      type: 'feature',
      title: '收藏夹集成',
      description: '在侧边栏中显示和管理Chrome收藏夹',
      userImpact: '快速访问常用网站和收藏夹'
    })

    // 如果有质量问题修复
    if (qualityReport && qualityReport.issues.security.length > 0) {
      notes.push({
        type: 'security',
        title: '安全性增强',
        description: '修复了多个安全相关问题',
        userImpact: '提高扩展的安全性，保护用户数据'
      })
    }

    return notes
  }

  /**
   * 生成后续步骤
   */
  private generateNextSteps(
    criticalIssues: CriticalIssue[],
    recommendations: FinalRecommendation[]
  ): NextStep[] {
    const steps: NextStep[] = []

    // 立即需要处理的问题
    const immediateIssues = criticalIssues.filter(i => i.mustFixBeforeRelease)
    if (immediateIssues.length > 0) {
      steps.push({
        phase: 'immediate',
        action: '修复发布阻塞问题',
        owner: '开发团队',
        deadline: '1-2个工作日',
        dependencies: []
      })
    }

    // 发布前的步骤
    const preReleaseRecommendations = recommendations.filter(r => r.priority === 'before-release')
    if (preReleaseRecommendations.length > 0) {
      steps.push({
        phase: 'pre-release',
        action: '完成发布前优化',
        owner: '开发团队',
        deadline: '1周',
        dependencies: ['修复发布阻塞问题']
      })
    }

    // 发布步骤
    steps.push({
      phase: 'pre-release',
      action: '准备发布包',
      owner: '发布团队',
      deadline: '发布前1天',
      dependencies: ['完成所有必要修复']
    })

    steps.push({
      phase: 'pre-release',
      action: 'Chrome Web Store发布',
      owner: '发布团队',
      deadline: '发布日',
      dependencies: ['准备发布包']
    })

    // 发布后的步骤
    const postReleaseRecommendations = recommendations.filter(r => r.priority === 'post-release')
    if (postReleaseRecommendations.length > 0) {
      steps.push({
        phase: 'post-release',
        action: '实施后续优化',
        owner: '开发团队',
        deadline: '发布后2周',
        dependencies: ['成功发布']
      })
    }

    return steps
  }

  /**
   * 显示最终结果
   */
  private displayFinalResults(report: FinalVerificationReport): void {
    console.log('\n' + '=' .repeat(60))
    console.log('📊 最终验证报告')
    console.log('=' .repeat(60))

    const { summary } = report
    const statusIcon = summary.overallStatus === 'passed' ? '✅' : 
                      summary.overallStatus === 'warning' ? '⚠️' : '❌'

    console.log(`\n总体状态: ${statusIcon} ${summary.overallStatus.toUpperCase()}`)
    console.log(`总体评分: ${summary.overallScore.toFixed(1)}/100`)
    console.log(`就绪级别: ${summary.readinessLevel}`)
    console.log(`验证耗时: ${summary.duration}ms`)

    // 关键问题
    if (report.criticalIssues.length > 0) {
      console.log(`\n🚨 关键问题 (${report.criticalIssues.length}个):`)
      report.criticalIssues.forEach((issue, index) => {
        const mustFix = issue.mustFixBeforeRelease ? '🔴' : '🟡'
        console.log(`  ${mustFix} ${index + 1}. ${issue.description}`)
        console.log(`     影响: ${issue.impact}`)
        console.log(`     位置: ${issue.location}`)
        console.log(`     预计修复时间: ${issue.estimatedFixTime}`)
      })
    }

    // 建议
    if (report.recommendations.length > 0) {
      console.log(`\n💡 建议 (${report.recommendations.length}个):`)
      report.recommendations.forEach((rec, index) => {
        const priorityIcon = rec.priority === 'immediate' ? '🔴' :
                           rec.priority === 'before-release' ? '🟡' : '🟢'
        console.log(`  ${priorityIcon} ${index + 1}. ${rec.title}`)
        console.log(`     优先级: ${rec.priority}`)
        console.log(`     预计工作量: ${rec.effort}`)
      })
    }

    // 后续步骤
    if (report.nextSteps.length > 0) {
      console.log(`\n📋 后续步骤:`)
      report.nextSteps.forEach((step, index) => {
        console.log(`  ${index + 1}. [${step.phase}] ${step.action}`)
        console.log(`     负责人: ${step.owner}`)
        console.log(`     截止时间: ${step.deadline}`)
      })
    }

    // 发布就绪性评估
    console.log('\n🎯 发布就绪性评估:')
    if (summary.readinessLevel === 'production-ready') {
      console.log('  ✅ 产品已准备好发布')
    } else if (summary.readinessLevel === 'needs-minor-fixes') {
      console.log('  ⚠️  需要修复少量问题后可发布')
    } else if (summary.readinessLevel === 'needs-major-fixes') {
      console.log('  ❌ 需要修复重大问题后才能发布')
    } else {
      console.log('  🚫 产品尚未准备好发布')
    }
  }

  /**
   * 导出最终报告
   */
  private async exportFinalReport(report: FinalVerificationReport): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `final-verification-report-${timestamp}.json`
    const outputPath = this.config.outputPath || './verification-results'

    try {
      console.log(`\n📄 导出最终报告到: ${outputPath}/${filename}`)
      
      // 在实际环境中，这里会写入文件
      // 现在只是模拟输出
      const reportJson = JSON.stringify(report, null, 2)
      console.log('报告已生成，大小:', (reportJson.length / 1024).toFixed(2), 'KB')
      
    } catch (error) {
      console.error('导出报告失败:', error.message)
    }
  }

  /**
   * 检查失败条件
   */
  private checkFailureConditions(report: FinalVerificationReport): void {
    const { config } = this
    const { criticalIssues, summary } = report
    let shouldFail = false
    const reasons: string[] = []

    // 检查关键问题
    if (config.failOnIssues.critical) {
      const criticalCount = criticalIssues.filter(i => i.severity === 'critical').length
      if (criticalCount > 0) {
        shouldFail = true
        reasons.push(`${criticalCount}个关键问题`)
      }
    }

    // 检查主要问题
    if (config.failOnIssues.major) {
      const majorCount = criticalIssues.filter(i => i.severity === 'major').length
      if (majorCount > 0) {
        shouldFail = true
        reasons.push(`${majorCount}个主要问题`)
      }
    }

    // 检查安全关键问题
    if (config.failOnIssues.securityCritical) {
      const securityCriticalCount = criticalIssues.filter(i => 
        i.category === 'security' && i.severity === 'critical'
      ).length
      if (securityCriticalCount > 0) {
        shouldFail = true
        reasons.push(`${securityCriticalCount}个关键安全问题`)
      }
    }

    // 检查测试失败
    if (config.failOnIssues.testFailures && summary.overallStatus === 'failed') {
      shouldFail = true
      reasons.push('测试失败')
    }

    if (shouldFail) {
      const reasonText = reasons.join(', ')
      throw new Error(`验证失败: ${reasonText}`)
    }
  }

  /**
   * 生成验证摘要
   */
  generateVerificationSummary(report: FinalVerificationReport): string {
    const { summary, criticalIssues, recommendations } = report

    return `
Chrome垂直侧边栏扩展 - 最终验证摘要

状态: ${summary.overallStatus.toUpperCase()}
评分: ${summary.overallScore.toFixed(1)}/100
就绪级别: ${summary.readinessLevel}

关键问题: ${criticalIssues.length}个
- 必须修复: ${criticalIssues.filter(i => i.mustFixBeforeRelease).length}个
- 建议修复: ${criticalIssues.filter(i => !i.mustFixBeforeRelease).length}个

建议: ${recommendations.length}个
- 立即处理: ${recommendations.filter(r => r.priority === 'immediate').length}个
- 发布前处理: ${recommendations.filter(r => r.priority === 'before-release').length}个
- 发布后处理: ${recommendations.filter(r => r.priority === 'post-release').length}个

验证时间: ${new Date(summary.timestamp).toLocaleString()}
耗时: ${(summary.duration / 1000).toFixed(1)}秒
    `.trim()
  }
}

export { 
  FinalVerificationRunner, 
  FinalVerificationConfig, 
  FinalVerificationReport,
  CriticalIssue,
  FinalRecommendation,
  ReleaseNote,
  NextStep
}