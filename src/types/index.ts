// 全局类型定义文件

// 基础类型
export interface TabInfo {
  id: number;
  title: string;
  url: string;
  favIconUrl?: string;
  active: boolean;
  windowId: number;
  groupId?: number;
  index: number;
  pinned: boolean;
  discarded?: boolean;
  autoDiscardable?: boolean;
  status?: 'loading' | 'complete';
  lastAccessed?: number;
}

export interface BookmarkNode {
  id: string;
  title: string;
  url?: string;
  children?: BookmarkNode[];
  dateAdded?: number;
  dateGroupModified?: number;
  parentId?: string;
  index?: number;
}

export interface TabGroup {
  id: number;
  title: string;
  color: string;
  collapsed: boolean;
  windowId: number;
}

export interface UserSettings {
  enabled: boolean;
  position: 'left' | 'right';
  showTabs: boolean;
  showBookmarks: boolean;
  defaultPinned: boolean;
  expandDelay: number;
  collapseDelay: number;
  keyboardShortcuts: {
    toggle: string;
    search: string;
  };
  showRecentTabs: boolean;
  maxRecentTabs: number;
  enableSearch: boolean;
  searchDelay: number;
  showSearchHistory: boolean;
  maxSearchHistory: number;
  debugMode: boolean;
  enableAnimations: boolean;
  enableSounds: boolean;
  autoUpdate: boolean;
  telemetry: boolean;
}

// 消息类型
export interface Message {
  type: string;
  payload?: any;
  timestamp: number;
}

export interface MessageResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// 内容脚本接口
export interface ContentInterface {
  sidebarElement: HTMLElement | null;
  isExpanded: boolean;
  isPinned: boolean;
  isEnabled: boolean;
  settings: UserSettings | null;
  tabs: TabInfo[];
  tabGroups: TabGroup[];
  updateInterval: number | null;
  expandTimeout: number | null;
  collapseTimeout: number | null;
}

// 事件监听器信息
export interface EventListenerInfo {
  element: HTMLElement;
  event: string;
  handler: EventListener;
  options?: boolean | AddEventListenerOptions;
}

// 搜索结果
export interface SearchResult {
  total: number;
  bookmarks: number;
  tabs: number;
  folders: number;
  avgScore: number;
  items: SearchItem[];
}

export interface SearchItem {
  id: string;
  title: string;
  url?: string;
  type: 'tab' | 'bookmark' | 'folder';
  score: number;
  favicon?: string;
  parentId?: string;
}

// 扩展状态
export interface ExtensionState {
  enabled: boolean;
  version: string;
  lastUpdated: number;
  settings: UserSettings;
}

// 性能指标
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  cpuUsage: number;
}

// 错误信息
export interface ErrorInfo {
  code: string;
  message: string;
  stack?: string;
  context?: string;
  timestamp: number;
}

// 调试信息
export interface DebugInfo {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: any;
  timestamp: number;
  source: string;
}

// 侧边栏配置
export const SIDEBAR_CONFIG = {
  dimensions: {
    collapsedWidth: 60,   // 收起状态宽度
    expandedWidth: 300    // 展开状态宽度
  },
  animations: {
    expandDuration: 300,  // 展开动画持续时间(ms)
    collapseDuration: 300, // 收起动画持续时间(ms)
    easing: 'cubic-bezier(0.4, 0.0, 0.2, 1)' // 缓动函数
  }
};

// 默认设置
export const DEFAULT_SETTINGS: UserSettings = {
  enabled: true,
  position: 'left',
  showTabs: true,
  showBookmarks: true,
  defaultPinned: false,
  expandDelay: 100,
  collapseDelay: 100,
  keyboardShortcuts: {
    toggle: 'Alt+T',
    search: 'Alt+S'
  },
  showRecentTabs: true,
  maxRecentTabs: 10,
  enableSearch: true,
  searchDelay: 200,
  showSearchHistory: true,
  maxSearchHistory: 50,
  debugMode: false,
  enableAnimations: true,
  enableSounds: false,
  autoUpdate: true,
  telemetry: false,
  sidebarWidth: {
    collapsed: 60,   // 收起状态：60px，只显示图标
    expanded: 300    // 展开状态：300px，显示完整内容
  }
};