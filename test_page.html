<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试侧边栏持续性</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .refresh-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .refresh-btn:hover {
            background: #1557b0;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .content {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #1a73e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 侧边栏持续性测试页面</h1>
        
        <div class="status info">
            <strong>测试目标：</strong> 验证页面刷新时侧边栏不会消失，而是立即恢复到之前的状态
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤：</h3>
            <ol>
                <li>确保侧边栏扩展已启用</li>
                <li>侧边栏应该立即显示（折叠状态）</li>
                <li>点击侧边栏切换按钮展开侧边栏</li>
                <li>点击下方的"刷新页面"按钮</li>
                <li>观察页面刷新过程中侧边栏是否保持可见</li>
                <li>验证刷新后侧边栏状态是否正确恢复</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🎯 预期结果：</h3>
            <ul>
                <li>页面刷新时侧边栏不会消失</li>
                <li>侧边栏状态（展开/折叠）得到正确恢复</li>
                <li>没有明显的闪烁或重新渲染效果</li>
                <li>页面布局保持连续性</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 测试操作：</h3>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新页面</button>
            <button class="refresh-btn" onclick="location.reload(true)">🔄 强制刷新</button>
            <button class="refresh-btn" onclick="testNavigation()">🔀 模拟导航</button>
        </div>
        
        <div class="content">
            <h3>📝 当前页面信息：</h3>
            <p><strong>页面加载时间：</strong> <span id="loadTime"></span></p>
            <p><strong>用户代理：</strong> <span id="userAgent"></span></p>
            <p><strong>页面URL：</strong> <span id="pageUrl"></span></p>
            <p><strong>localStorage支持：</strong> <span id="storageSupport"></span></p>
        </div>
        
        <div class="content">
            <h3>🗂️ 缓存状态检查：</h3>
            <p><strong>侧边栏设置缓存：</strong> <span id="settingsCache"></span></p>
            <p><strong>侧边栏状态缓存：</strong> <span id="stateCache"></span></p>
        </div>
    </div>
    
    <script>
        // 页面加载完成后显示信息
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loadTime').textContent = new Date().toLocaleString();
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('storageSupport').textContent = typeof(Storage) !== "undefined" ? "✅ 支持" : "❌ 不支持";
            
            // 检查缓存状态
            checkCacheStatus();
        });
        
        function checkCacheStatus() {
            const settingsCache = localStorage.getItem('edgeSidebar_settings');
            const stateCache = localStorage.getItem('edgeSidebar_state');
            
            document.getElementById('settingsCache').innerHTML = settingsCache 
                ? `✅ 已缓存 (${JSON.parse(settingsCache).timestamp ? new Date(JSON.parse(settingsCache).timestamp).toLocaleString() : '时间未知'})`
                : '❌ 未缓存';
                
            document.getElementById('stateCache').innerHTML = stateCache 
                ? `✅ 已缓存 (${JSON.parse(stateCache).timestamp ? new Date(JSON.parse(stateCache).timestamp).toLocaleString() : '时间未知'}) - 状态: ${JSON.parse(stateCache).expanded ? '展开' : '折叠'}`
                : '❌ 未缓存';
        }
        
        function testNavigation() {
            // 模拟页面导航
            const currentUrl = window.location.href;
            const newUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'test=' + Date.now();
            window.location.href = newUrl;
        }
        
        // 每秒更新缓存状态检查
        setInterval(checkCacheStatus, 1000);
    </script>
    
    <!-- 引入侧边栏样式 -->
    <link rel="stylesheet" href="edge-sidebar.css">
    
    <!-- 引入侧边栏脚本 -->
    <script src="edge-sidebar.js"></script>
</body>
</html>