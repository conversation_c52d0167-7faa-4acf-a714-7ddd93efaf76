<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 侧边栏刷新持续性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        h1 {
            color: #fff;
            margin-bottom: 30px;
            text-align: center;
            font-size: 2.5em;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 12px;
            background: rgba(255,255,255,0.05);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
            text-align: center;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #a5d6a7;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.5);
            color: #90caf9;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(238, 90, 36, 0.6);
        }
        
        .test-btn.secondary {
            background: linear-gradient(135deg, #4ecdc4, #45b7b8);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }
        
        .test-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .info-label {
            font-weight: 600;
            color: #b0bec5;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #fff;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85em;
            word-break: break-all;
        }
        
        .cache-status {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .cache-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .cache-indicator.good {
            background: #4caf50;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }
        
        .cache-indicator.bad {
            background: #f44336;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.5);
        }

        ol, ul {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        code {
            background: rgba(0,0,0,0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 侧边栏刷新持续性测试</h1>
        
        <div class="status success">
            <strong>✨ 新功能:</strong> 页面刷新时侧边栏将无缝保持显示，无闪烁无消失！
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤：</h3>
            <ol>
                <li>确保侧边栏扩展已启用</li>
                <li>观察侧边栏是否立即显示（无延迟）</li>
                <li>点击侧边栏切换按钮改变展开状态</li>
                <li>点击下方的测试按钮执行刷新</li>
                <li>观察页面刷新过程中侧边栏是否始终可见</li>
                <li>验证刷新后侧边栏状态是否正确恢复</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🎯 预期改进效果：</h3>
            <ul>
                <li><strong>无缝持续性:</strong> 页面刷新时侧边栏不会消失</li>
                <li><strong>状态恢复:</strong> 侧边栏状态（展开/折叠）完全保持</li>
                <li><strong>零闪烁:</strong> 没有任何视觉故障或重新渲染</li>
                <li><strong>布局稳定:</strong> 页面布局保持完全连续性</li>
                <li><strong>即时显示:</strong> 页面加载时侧边栏立即出现</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 执行测试：</h3>
            <div class="test-buttons">
                <button class="test-btn" onclick="location.reload()">
                    🔄 标准刷新
                </button>
                <button class="test-btn" onclick="location.reload(true)">
                    💥 强制刷新
                </button>
                <button class="test-btn secondary" onclick="testHardNavigation()">
                    🔀 硬导航测试
                </button>
                <button class="test-btn secondary" onclick="clearCacheAndReload()">
                    🧹 清理缓存刷新
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 页面信息：</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">页面加载时间</div>
                    <div class="info-value" id="loadTime"></div>
                </div>
                <div class="info-item">
                    <div class="info-label">页面URL</div>
                    <div class="info-value" id="pageUrl"></div>
                </div>
                <div class="info-item">
                    <div class="info-label">浏览器</div>
                    <div class="info-value" id="userAgent"></div>
                </div>
                <div class="info-item">
                    <div class="info-label">localStorage 支持</div>
                    <div class="info-value" id="storageSupport"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>💾 侧边栏缓存状态：</h3>
            <div class="cache-status">
                <div id="stateCache"></div>
                <div id="settingsCache"></div>
                <div id="lastUpdate"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 实时监控：</h3>
            <div class="cache-status">
                <div id="currentSidebarState">检测中...</div>
                <div id="sidebarElement">检测中...</div>
                <div id="placeholderElement">检测中...</div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后显示信息
        document.addEventListener('DOMContentLoaded', function() {
            updatePageInfo();
            updateCacheStatus();
            updateSidebarStatus();
            
            // 每秒更新状态
            setInterval(() => {
                updateCacheStatus();
                updateSidebarStatus();
            }, 1000);
        });
        
        function updatePageInfo() {
            document.getElementById('loadTime').textContent = new Date().toLocaleString();
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 80) + '...';
            document.getElementById('storageSupport').textContent = typeof(Storage) !== "undefined" ? "✅ 支持" : "❌ 不支持";
        }
        
        function updateCacheStatus() {
            // 状态缓存
            const stateCache = localStorage.getItem('edgeSidebar_state');
            const stateElement = document.getElementById('stateCache');
            if (stateCache) {
                try {
                    const state = JSON.parse(stateCache);
                    const time = state.timestamp ? new Date(state.timestamp).toLocaleString() : '时间未知';
                    const expanded = state.expanded ? '展开' : '收起';
                    stateElement.innerHTML = `<span class="cache-indicator good"></span><strong>侧边栏状态:</strong> ✅ 已缓存 (${time}) - ${expanded}`;
                } catch (e) {
                    stateElement.innerHTML = `<span class="cache-indicator bad"></span><strong>侧边栏状态:</strong> ❌ 缓存损坏`;
                }
            } else {
                stateElement.innerHTML = `<span class="cache-indicator bad"></span><strong>侧边栏状态:</strong> ❌ 未缓存`;
            }
            
            // 设置缓存
            const settingsCache = localStorage.getItem('edgeSidebar_settings');
            const settingsElement = document.getElementById('settingsCache');
            if (settingsCache) {
                try {
                    const settings = JSON.parse(settingsCache);
                    const time = settings.timestamp ? new Date(settings.timestamp).toLocaleString() : '时间未知';
                    settingsElement.innerHTML = `<span class="cache-indicator good"></span><strong>设置缓存:</strong> ✅ 已缓存 (${time})`;
                } catch (e) {
                    settingsElement.innerHTML = `<span class="cache-indicator bad"></span><strong>设置缓存:</strong> ❌ 缓存损坏`;
                }
            } else {
                settingsElement.innerHTML = `<span class="cache-indicator bad"></span><strong>设置缓存:</strong> ❌ 未缓存`;
            }
            
            // 最后更新时间
            const lastUpdate = localStorage.getItem('edgeSidebar_lastUpdate');
            const updateElement = document.getElementById('lastUpdate');
            if (lastUpdate) {
                try {
                    const update = JSON.parse(lastUpdate);
                    const time = update.timestamp ? new Date(update.timestamp).toLocaleString() : '时间未知';
                    updateElement.innerHTML = `<span class="cache-indicator good"></span><strong>最后更新:</strong> ${time}`;
                } catch (e) {
                    updateElement.innerHTML = `<span class="cache-indicator bad"></span><strong>最后更新:</strong> 数据损坏`;
                }
            } else {
                updateElement.innerHTML = `<span class="cache-indicator bad"></span><strong>最后更新:</strong> 无记录`;
            }
        }
        
        function updateSidebarStatus() {
            // 检查当前侧边栏状态
            const sidebarElement = document.getElementById('edge-vertical-sidebar');
            const placeholderElement = document.getElementById('edge-vertical-sidebar-placeholder');
            const manager = window.__edgeSidebarManager || window.edgeSidebarManager;
            
            // 当前侧边栏状态
            const currentStateElement = document.getElementById('currentSidebarState');
            if (manager) {
                const expanded = manager.isExpanded ? '展开' : '收起';
                currentStateElement.innerHTML = `<span class="cache-indicator good"></span><strong>当前状态:</strong> ${expanded} (管理器已加载)`;
            } else {
                currentStateElement.innerHTML = `<span class="cache-indicator bad"></span><strong>当前状态:</strong> 管理器未加载`;
            }
            
            // 侧边栏元素
            const sidebarElementStatus = document.getElementById('sidebarElement');
            if (sidebarElement) {
                const classes = sidebarElement.className;
                sidebarElementStatus.innerHTML = `<span class="cache-indicator good"></span><strong>侧边栏元素:</strong> ✅ 存在 (${classes})`;
            } else {
                sidebarElementStatus.innerHTML = `<span class="cache-indicator bad"></span><strong>侧边栏元素:</strong> ❌ 不存在`;
            }
            
            // 占位符元素
            const placeholderStatus = document.getElementById('placeholderElement');
            if (placeholderElement) {
                const width = placeholderElement.style.width;
                placeholderStatus.innerHTML = `<span class="cache-indicator good"></span><strong>早期占位符:</strong> ✅ 存在 (宽度: ${width})`;
            } else {
                placeholderStatus.innerHTML = `<span class="cache-indicator bad"></span><strong>早期占位符:</strong> ❌ 已清理或不存在`;
            }
        }
        
        function testHardNavigation() {
            // 模拟硬导航 - 改变URL
            const currentUrl = window.location.href;
            const newUrl = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'test=' + Date.now();
            window.location.href = newUrl;
        }
        
        function clearCacheAndReload() {
            // 清理缓存后刷新
            localStorage.removeItem('edgeSidebar_state');
            localStorage.removeItem('edgeSidebar_settings');
            localStorage.removeItem('edgeSidebar_lastUpdate');
            alert('缓存已清理，即将刷新页面');
            setTimeout(() => location.reload(), 500);
        }
    </script>
    
    <!-- 引入侧边栏样式和脚本 -->
    <link rel="stylesheet" href="edge-sidebar.css">
    <script src="edge-sidebar.js"></script>
</body>
</html>