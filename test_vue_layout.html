<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js SPA 布局测试</title>
    <style>
        /* 模拟写作猫的布局结构 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            overflow: hidden;
        }
        
        .el-container.fullscreen {
            position: absolute !important;
            top: 0;
            left: 0;
            width: 1200px;
            height: 100vh;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            height: 60px;
            background: #409eff;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background: white;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .status-info {
            background: #e1f5fe;
            border: 1px solid #81d4fa;
            color: #0277bd;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .test-result {
            background: #f3e5f5;
            border: 1px solid #ce93d8;
            color: #7b1fa2;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <!-- 模拟Element UI的fullscreen容器 -->
    <section class="el-container fullscreen is-vertical">
        <header class="header">
            🧪 Vue.js SPA 绝对定位布局测试
        </header>
        
        <main class="main-content">
            <div class="status-info">
                <strong>测试场景：</strong> 模拟写作猫等使用Element UI的Vue.js应用布局
            </div>
            
            <div class="card">
                <h3>📋 布局特征：</h3>
                <ul>
                    <li><code>.el-container.fullscreen</code> 容器使用绝对定位</li>
                    <li>容器宽度固定为 1200px</li>
                    <li>body 设置了 overflow: hidden</li>
                    <li>主要内容在绝对定位容器内部</li>
                </ul>
            </div>
            
            <div class="card">
                <h3>🎯 预期修复效果：</h3>
                <ul>
                    <li>侧边栏脚本检测到 Vue.js SPA 绝对定位布局</li>
                    <li>margin 应用到 <code>.el-container.fullscreen</code> 而不是 body</li>
                    <li>整个容器向右偏移，为侧边栏让出空间</li>
                    <li>布局响应流畅，无视觉故障</li>
                </ul>
            </div>
            
            <div class="test-result">
                <h3>🔍 实际效果检查：</h3>
                <p id="layoutType">布局类型检测中...</p>
                <p id="marginTarget">Margin应用目标检测中...</p>
                <p id="containerMargin">容器margin值检测中...</p>
            </div>
            
            <div class="card">
                <h3>📊 页面信息：</h3>
                <p><strong>页面URL：</strong> <span id="pageUrl"></span></p>
                <p><strong>容器定位：</strong> <span id="containerPosition"></span></p>
                <p><strong>容器宽度：</strong> <span id="containerWidth"></span></p>
                <p><strong>Body overflow：</strong> <span id="bodyOverflow"></span></p>
            </div>
        </main>
    </section>
    
    <script>
        // 页面加载完成后显示信息
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.el-container.fullscreen');
            
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('containerPosition').textContent = window.getComputedStyle(container).position;
            document.getElementById('containerWidth').textContent = window.getComputedStyle(container).width;
            document.getElementById('bodyOverflow').textContent = window.getComputedStyle(document.body).overflow;
            
            // 检查布局检测功能（如果侧边栏脚本已加载）
            function checkLayoutDetection() {
                if (typeof window.edgeSidebarManager !== 'undefined' && window.edgeSidebarManager.detectPageLayoutType) {
                    const layoutInfo = window.edgeSidebarManager.detectPageLayoutType();
                    document.getElementById('layoutType').innerHTML = `<strong>✅ 检测到：</strong> ${layoutInfo.description}`;
                    document.getElementById('marginTarget').innerHTML = `<strong>🎯 目标元素：</strong> ${layoutInfo.targetElement.tagName}${layoutInfo.targetElement.className ? '.' + layoutInfo.targetElement.className : ''}`;
                    
                    const marginLeft = window.getComputedStyle(layoutInfo.targetElement).marginLeft;
                    document.getElementById('containerMargin').innerHTML = `<strong>📏 Margin值：</strong> ${marginLeft}`;
                } else {
                    document.getElementById('layoutType').innerHTML = '<strong>⏳</strong> 等待侧边栏脚本加载...';
                }
            }
            
            // 定期检查
            checkLayoutDetection();
            setInterval(checkLayoutDetection, 1000);
        });
    </script>
    
    <!-- 引入侧边栏样式和脚本 -->
    <link rel="stylesheet" href="edge-sidebar.css">
    <script src="edge-sidebar.js"></script>
</body>
</html>