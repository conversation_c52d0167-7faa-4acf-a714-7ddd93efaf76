/**
 * Background Service 单元测试
 */

import { BackgroundServiceImpl } from '../../src/background/services/BackgroundService';
import { TabManager } from '../../src/background/services/TabManager';
import { BookmarkManager } from '../../src/background/services/BookmarkManager';
import { SettingsManager } from '../../src/background/services/SettingsManager';

// Mock 依赖
jest.mock('../../src/background/services/TabManager');
jest.mock('../../src/background/services/BookmarkManager');
jest.mock('../../src/background/services/SettingsManager');
jest.mock('../../src/background/services/MessageRouter');

describe('BackgroundService', () => {
  let backgroundService: BackgroundServiceImpl;
  let mockTabManager: jest.Mocked<TabManager>;
  let mockBookmarkManager: jest.Mocked<BookmarkManager>;
  let mockSettingsManager: jest.Mocked<SettingsManager>;

  beforeEach(() => {
    // 重置所有 mocks
    jest.clearAllMocks();
    
    backgroundService = new BackgroundServiceImpl();
    
    // 获取 mock 实例
    mockTabManager = (TabManager as jest.MockedClass<typeof TabManager>).mock.instances[0] as jest.Mocked<TabManager>;
    mockBookmarkManager = (BookmarkManager as jest.MockedClass<typeof BookmarkManager>).mock.instances[0] as jest.Mocked<BookmarkManager>;
    mockSettingsManager = (SettingsManager as jest.MockedClass<typeof SettingsManager>).mock.instances[0] as jest.Mocked<SettingsManager>;
    
    // 设置默认 mock 行为
    mockTabManager.initialize = jest.fn().mockResolvedValue(undefined);
    mockBookmarkManager.initialize = jest.fn().mockResolvedValue(undefined);
    mockSettingsManager.initialize = jest.fn().mockResolvedValue(undefined);
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await backgroundService.initialize();
      
      expect(backgroundService.isInitialized()).toBe(true);
      expect(mockTabManager.initialize).toHaveBeenCalled();
      expect(mockBookmarkManager.initialize).toHaveBeenCalled();
      expect(mockSettingsManager.initialize).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      const error = new Error('Initialization failed');
      mockTabManager.initialize.mockRejectedValue(error);
      
      await expect(backgroundService.initialize()).rejects.toThrow('Initialization failed');
      expect(backgroundService.isInitialized()).toBe(false);
    });
  });

  describe('tab management', () => {
    beforeEach(async () => {
      await backgroundService.initialize();
    });

    it('should get all tabs', async () => {
      const mockTabs = [createMockTab({ id: 1, title: 'Tab 1' })];
      mockTabManager.getCurrentWindowTabs.mockResolvedValue(mockTabs);
      
      const tabs = await backgroundService.getAllTabs();
      
      expect(tabs).toEqual(mockTabs);
      expect(mockTabManager.getCurrentWindowTabs).toHaveBeenCalled();
    });

    it('should switch to tab', async () => {
      const tabId = 123;
      mockTabManager.activateTab.mockResolvedValue(undefined);
      
      await backgroundService.switchToTab(tabId);
      
      expect(mockTabManager.activateTab).toHaveBeenCalledWith(tabId);
    });

    it('should close tab', async () => {
      const tabId = 123;
      mockTabManager.closeTab.mockResolvedValue(undefined);
      
      await backgroundService.closeTab(tabId);
      
      expect(mockTabManager.closeTab).toHaveBeenCalledWith(tabId);
    });

    it('should create tab group', async () => {
      const name = 'Test Group';
      const color = 'blue';
      const tabIds = [1, 2, 3];
      const mockGroup = { id: 1, title: name, color, collapsed: false, tabIds, windowId: 1 };
      
      mockTabManager.createTabGroup.mockResolvedValue(mockGroup);
      
      const group = await backgroundService.createTabGroup(name, color, tabIds);
      
      expect(group).toEqual(mockGroup);
      expect(mockTabManager.createTabGroup).toHaveBeenCalledWith(name, color, tabIds);
    });
  });

  describe('bookmark management', () => {
    beforeEach(async () => {
      await backgroundService.initialize();
    });

    it('should get bookmarks', async () => {
      const mockBookmarks = [createMockBookmark({ id: '1', title: 'Bookmark 1' })];
      mockBookmarkManager.getBookmarkTree.mockResolvedValue(mockBookmarks);
      
      const bookmarks = await backgroundService.getBookmarks();
      
      expect(bookmarks).toEqual(mockBookmarks);
      expect(mockBookmarkManager.getBookmarkTree).toHaveBeenCalled();
    });

    it('should add bookmark', async () => {
      const url = 'https://example.com';
      const title = 'Example';
      const folderId = 'folder1';
      const mockBookmark = createMockBookmark({ url, title, parentId: folderId });
      
      mockBookmarkManager.createBookmark.mockResolvedValue(mockBookmark);
      
      const bookmark = await backgroundService.addBookmark(url, title, folderId);
      
      expect(bookmark).toEqual(mockBookmark);
      expect(mockBookmarkManager.createBookmark).toHaveBeenCalledWith({ url, title, parentId: folderId });
    });
  });

  describe('settings management', () => {
    beforeEach(async () => {
      await backgroundService.initialize();
    });

    it('should get settings', async () => {
      const mockSettings = { position: 'left' as const, theme: 'light' as const };
      mockSettingsManager.load.mockResolvedValue(mockSettings as any);
      
      const settings = await backgroundService.getSettings();
      
      expect(settings).toEqual(mockSettings);
      expect(mockSettingsManager.load).toHaveBeenCalled();
    });

    it('should update settings', async () => {
      const updates = { position: 'right' as const };
      mockSettingsManager.save.mockResolvedValue(undefined);
      
      await backgroundService.updateSettings(updates);
      
      expect(mockSettingsManager.save).toHaveBeenCalledWith(updates);
    });

    it('should reset settings', async () => {
      mockSettingsManager.reset.mockResolvedValue(undefined);
      
      await backgroundService.resetSettings();
      
      expect(mockSettingsManager.reset).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should throw error when not initialized', async () => {
      await expect(backgroundService.getAllTabs()).rejects.toThrow('BackgroundService not initialized');
    });

    it('should handle tab manager errors', async () => {
      await backgroundService.initialize();
      
      const error = new Error('Tab manager error');
      mockTabManager.getCurrentWindowTabs.mockRejectedValue(error);
      
      await expect(backgroundService.getAllTabs()).rejects.toThrow('Tab manager error');
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', async () => {
      await backgroundService.initialize();
      
      mockTabManager.destroy = jest.fn();
      mockBookmarkManager.destroy = jest.fn();
      mockSettingsManager.destroy = jest.fn();
      
      backgroundService.destroy();
      
      expect(backgroundService.isInitialized()).toBe(false);
      expect(mockTabManager.destroy).toHaveBeenCalled();
      expect(mockBookmarkManager.destroy).toHaveBeenCalled();
      expect(mockSettingsManager.destroy).toHaveBeenCalled();
    });
  });
});