/**
 * Bookmark Manager 单元测试
 */

import { BookmarkManager } from '../../src/background/services/BookmarkManager';
import { BookmarkNode } from '../../types/index';

describe('BookmarkManager', () => {
  let bookmarkManager: BookmarkManager;
  let mockBookmarks: chrome.bookmarks.BookmarkTreeNode[];

  beforeEach(() => {
    bookmarkManager = new BookmarkManager();
    
    // Mock bookmark tree structure
    mockBookmarks = [
      {
        id: '0',
        title: '',
        children: [
          {
            id: '1',
            title: '书签栏',
            children: [
              {
                id: '2',
                title: 'Google',
                url: 'https://google.com',
                dateAdded: 1234567890000,
                parentId: '1',
                index: 0
              },
              {
                id: '3',
                title: '工作',
                children: [
                  {
                    id: '4',
                    title: 'GitHub',
                    url: 'https://github.com',
                    dateAdded: 1234567890001,
                    parentId: '3',
                    index: 0
                  }
                ],
                dateAdded: 1234567890002,
                parentId: '1',
                index: 1
              }
            ],
            dateAdded: 1234567890003,
            parentId: '0',
            index: 0
          }
        ]
      }
    ];

    // Mock Chrome APIs
    (chrome.bookmarks.getTree as jest.Mock).mockResolvedValue(mockBookmarks);
    (chrome.bookmarks.search as jest.Mock).mockResolvedValue([]);
    (chrome.bookmarks.create as jest.Mock).mockResolvedValue(mockBookmarks[0].children![0].children![0]);
    (chrome.bookmarks.update as jest.Mock).mockResolvedValue(undefined);
    (chrome.bookmarks.remove as jest.Mock).mockResolvedValue(undefined);
    (chrome.bookmarks.removeTree as jest.Mock).mockResolvedValue(undefined);
    (chrome.bookmarks.move as jest.Mock).mockResolvedValue(undefined);
    (chrome.bookmarks.get as jest.Mock).mockImplementation((id: string) => {
      const findBookmark = (nodes: chrome.bookmarks.BookmarkTreeNode[]): chrome.bookmarks.BookmarkTreeNode | null => {
        for (const node of nodes) {
          if (node.id === id) return node;
          if (node.children) {
            const found = findBookmark(node.children);
            if (found) return found;
          }
        }
        return null;
      };
      
      const bookmark = findBookmark(mockBookmarks);
      return bookmark ? Promise.resolve([bookmark]) : Promise.resolve([]);
    });
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await bookmarkManager.initialize();
      expect(bookmarkManager).toBeDefined();
    });

    it('should setup event listeners during initialization', async () => {
      const onCreatedSpy = jest.spyOn(chrome.bookmarks.onCreated, 'addListener');
      const onRemovedSpy = jest.spyOn(chrome.bookmarks.onRemoved, 'addListener');
      const onChangedSpy = jest.spyOn(chrome.bookmarks.onChanged, 'addListener');
      
      await bookmarkManager.initialize();
      
      expect(onCreatedSpy).toHaveBeenCalled();
      expect(onRemovedSpy).toHaveBeenCalled();
      expect(onChangedSpy).toHaveBeenCalled();
    });

    it('should preload bookmarks during initialization', async () => {
      await bookmarkManager.initialize();
      
      expect(chrome.bookmarks.getTree).toHaveBeenCalled();
    });
  });

  describe('bookmark operations', () => {
    beforeEach(async () => {
      await bookmarkManager.initialize();
    });

    it('should get bookmark tree', async () => {
      const bookmarks = await bookmarkManager.getBookmarkTree();
      
      expect(bookmarks).toHaveLength(1);
      expect(bookmarks[0].title).toBe('书签栏');
      expect(bookmarks[0].children).toHaveLength(2);
    });

    it('should use cache for subsequent calls', async () => {
      // First call
      await bookmarkManager.getBookmarkTree();
      
      // Second call should use cache
      await bookmarkManager.getBookmarkTree();
      
      // getTree should only be called once during initialization
      expect(chrome.bookmarks.getTree).toHaveBeenCalledTimes(1);
    });

    it('should search bookmarks', async () => {
      const mockSearchResults = [
        {
          id: '2',
          title: 'Google',
          url: 'https://google.com',
          dateAdded: 1234567890000,
          parentId: '1',
          index: 0
        }
      ];
      
      (chrome.bookmarks.search as jest.Mock).mockResolvedValue(mockSearchResults);
      
      const results = await bookmarkManager.searchBookmarks('Google');
      
      expect(chrome.bookmarks.search).toHaveBeenCalledWith('Google');
      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('Google');
    });

    it('should return empty array for empty search query', async () => {
      const results = await bookmarkManager.searchBookmarks('');
      
      expect(results).toHaveLength(0);
      expect(chrome.bookmarks.search).not.toHaveBeenCalled();
    });

    it('should create bookmark', async () => {
      const newBookmark = {
        title: 'New Bookmark',
        url: 'https://example.com',
        parentId: '1'
      };
      
      const createdBookmark = await bookmarkManager.createBookmark(newBookmark);
      
      expect(chrome.bookmarks.create).toHaveBeenCalledWith({
        title: 'New Bookmark',
        url: 'https://example.com',
        parentId: '1'
      });
      expect(createdBookmark.title).toBe('Google'); // Mock returns first bookmark
    });

    it('should create bookmark with index', async () => {
      const newBookmark = {
        title: 'New Bookmark',
        url: 'https://example.com',
        parentId: '1',
        index: 2
      };
      
      await bookmarkManager.createBookmark(newBookmark);
      
      expect(chrome.bookmarks.create).toHaveBeenCalledWith({
        title: 'New Bookmark',
        url: 'https://example.com',
        parentId: '1',
        index: 2
      });
    });

    it('should update bookmark', async () => {
      const updates = {
        title: 'Updated Title',
        url: 'https://updated.com'
      };
      
      await bookmarkManager.updateBookmark('2', updates);
      
      expect(chrome.bookmarks.update).toHaveBeenCalledWith('2', {
        title: 'Updated Title',
        url: 'https://updated.com'
      });
    });

    it('should delete bookmark', async () => {
      await bookmarkManager.deleteBookmark('2');
      
      expect(chrome.bookmarks.remove).toHaveBeenCalledWith('2');
    });

    it('should delete bookmark folder recursively', async () => {
      // Mock folder bookmark
      (chrome.bookmarks.get as jest.Mock).mockResolvedValue([{
        id: '3',
        title: '工作',
        children: []
      }]);
      
      await bookmarkManager.deleteBookmark('3');
      
      expect(chrome.bookmarks.removeTree).toHaveBeenCalledWith('3');
    });

    it('should move bookmark', async () => {
      await bookmarkManager.moveBookmark('2', '3', 1);
      
      expect(chrome.bookmarks.move).toHaveBeenCalledWith('2', {
        parentId: '3',
        index: 1
      });
    });

    it('should create bookmark folder', async () => {
      const mockFolder = {
        id: '5',
        title: 'New Folder',
        parentId: '1'
      };
      
      (chrome.bookmarks.create as jest.Mock).mockResolvedValue(mockFolder);
      
      const folder = await bookmarkManager.createBookmarkFolder('New Folder', '1');
      
      expect(chrome.bookmarks.create).toHaveBeenCalledWith({
        title: 'New Folder',
        parentId: '1'
      });
      expect(folder.title).toBe('New Folder');
    });

    it('should get bookmark path', async () => {
      const path = await bookmarkManager.getBookmarkPath('4');
      
      expect(path).toHaveLength(1);
      expect(path[0].title).toBe('GitHub');
    });
  });

  describe('event handling', () => {
    beforeEach(async () => {
      await bookmarkManager.initialize();
    });

    it('should register bookmark change callback', () => {
      const callback = jest.fn();
      
      bookmarkManager.onBookmarksChanged(callback);
      
      // Simulate bookmark creation
      const onCreatedCallback = (chrome.bookmarks.onCreated.addListener as jest.Mock).mock.calls[0][0];
      onCreatedCallback('5', { id: '5', title: 'New Bookmark' });
      
      // Should eventually call the callback (async)
      setTimeout(() => {
        expect(callback).toHaveBeenCalled();
      }, 0);
    });

    it('should remove bookmark change callback', () => {
      const callback = jest.fn();
      
      bookmarkManager.onBookmarksChanged(callback);
      bookmarkManager.removeBookmarksChangedListener(callback);
      
      expect(bookmarkManager['bookmarkChangeCallbacks']).not.toContain(callback);
    });
  });

  describe('caching', () => {
    beforeEach(async () => {
      await bookmarkManager.initialize();
    });

    it('should invalidate cache after creating bookmark', async () => {
      // First call to populate cache
      await bookmarkManager.getBookmarkTree();
      
      // Create bookmark (should invalidate cache)
      await bookmarkManager.createBookmark({
        title: 'Test',
        url: 'https://test.com'
      });
      
      // Next call should reload from API
      await bookmarkManager.getBookmarkTree();
      
      expect(chrome.bookmarks.getTree).toHaveBeenCalledTimes(2);
    });

    it('should invalidate cache after updating bookmark', async () => {
      await bookmarkManager.getBookmarkTree();
      await bookmarkManager.updateBookmark('2', { title: 'Updated' });
      await bookmarkManager.getBookmarkTree();
      
      expect(chrome.bookmarks.getTree).toHaveBeenCalledTimes(2);
    });

    it('should invalidate cache after deleting bookmark', async () => {
      await bookmarkManager.getBookmarkTree();
      await bookmarkManager.deleteBookmark('2');
      await bookmarkManager.getBookmarkTree();
      
      expect(chrome.bookmarks.getTree).toHaveBeenCalledTimes(2);
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await bookmarkManager.initialize();
    });

    it('should handle bookmark creation errors', async () => {
      (chrome.bookmarks.create as jest.Mock).mockRejectedValue(new Error('Creation failed'));
      
      await expect(bookmarkManager.createBookmark({
        title: 'Test',
        url: 'https://test.com'
      })).rejects.toThrow('Creation failed');
    });

    it('should handle bookmark search errors', async () => {
      (chrome.bookmarks.search as jest.Mock).mockRejectedValue(new Error('Search failed'));
      
      await expect(bookmarkManager.searchBookmarks('test')).rejects.toThrow('Search failed');
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', async () => {
      await bookmarkManager.initialize();
      
      bookmarkManager.destroy();
      
      expect(bookmarkManager['bookmarkChangeCallbacks']).toHaveLength(0);
      expect(bookmarkManager['bookmarkCache']).toHaveLength(0);
    });
  });
});