/**
 * Settings Manager 单元测试
 */

import { SettingsManager } from '../../src/background/services/SettingsManager';
import { UserSettings, DEFAULT_SETTINGS } from '../../types/index';

describe('SettingsManager', () => {
  let settingsManager: SettingsManager;
  let mockStorageData: any = {};

  beforeEach(() => {
    settingsManager = new SettingsManager();
    mockStorageData = {};

    // Mock Chrome storage APIs
    (chrome.storage.sync.get as jest.Mock).mockImplementation((key) => {
      return Promise.resolve(mockStorageData);
    });

    (chrome.storage.sync.set as jest.Mock).mockImplementation((data) => {
      Object.assign(mockStorageData, data);
      return Promise.resolve();
    });

    (chrome.storage.local.get as jest.Mock).mockImplementation((key) => {
      return Promise.resolve(mockStorageData);
    });

    (chrome.storage.local.set as jest.Mock).mockImplementation((data) => {
      Object.assign(mockStorageData, data);
      return Promise.resolve();
    });

    (chrome.storage.onChanged.addListener as jest.Mock).mockImplementation(() => {});
  });

  describe('initialization', () => {
    it('should initialize with default settings when no stored settings exist', async () => {
      await settingsManager.initialize();
      
      const settings = await settingsManager.load();
      expect(settings).toEqual(DEFAULT_SETTINGS);
    });

    it('should load existing settings from storage', async () => {
      const existingSettings = {
        ...DEFAULT_SETTINGS,
        position: 'right' as const,
        theme: 'dark' as const
      };
      mockStorageData.userSettings = existingSettings;

      await settingsManager.initialize();
      
      const settings = await settingsManager.load();
      expect(settings.position).toBe('right');
      expect(settings.theme).toBe('dark');
    });

    it('should use default settings when stored settings are invalid', async () => {
      mockStorageData.userSettings = { invalid: 'data' };

      await settingsManager.initialize();
      
      const settings = await settingsManager.load();
      expect(settings).toEqual(DEFAULT_SETTINGS);
    });

    it('should setup storage listener', async () => {
      await settingsManager.initialize();
      
      expect(chrome.storage.onChanged.addListener).toHaveBeenCalled();
    });
  });

  describe('settings operations', () => {
    beforeEach(async () => {
      await settingsManager.initialize();
    });

    it('should save partial settings', async () => {
      const updates = {
        position: 'right' as const,
        theme: 'dark' as const
      };

      await settingsManager.save(updates);
      
      const settings = await settingsManager.load();
      expect(settings.position).toBe('right');
      expect(settings.theme).toBe('dark');
      expect(settings.expandDelay).toBe(DEFAULT_SETTINGS.expandDelay); // Should keep default
    });

    it('should validate settings before saving', async () => {
      const invalidUpdates = {
        position: 'invalid' as any,
        expandDelay: 5000 // Out of range
      };

      await settingsManager.save(invalidUpdates);
      
      const settings = await settingsManager.load();
      expect(settings.position).toBe(DEFAULT_SETTINGS.position); // Should keep default
      expect(settings.expandDelay).toBe(DEFAULT_SETTINGS.expandDelay); // Should keep default
    });

    it('should reset settings to default', async () => {
      // First change some settings
      await settingsManager.save({
        position: 'right',
        theme: 'dark'
      });

      // Then reset
      await settingsManager.reset();
      
      const settings = await settingsManager.load();
      expect(settings).toEqual(DEFAULT_SETTINGS);
    });

    it('should get specific setting', async () => {
      await settingsManager.save({ position: 'right' });
      
      const position = settingsManager.getSetting('position');
      expect(position).toBe('right');
    });

    it('should set specific setting', async () => {
      await settingsManager.setSetting('theme', 'dark');
      
      const settings = await settingsManager.load();
      expect(settings.theme).toBe('dark');
    });
  });

  describe('import/export', () => {
    beforeEach(async () => {
      await settingsManager.initialize();
    });

    it('should export settings', async () => {
      await settingsManager.save({
        position: 'right',
        theme: 'dark'
      });

      const exportData = await settingsManager.export();
      const parsed = JSON.parse(exportData);
      
      expect(parsed.version).toBeDefined();
      expect(parsed.timestamp).toBeDefined();
      expect(parsed.settings.position).toBe('right');
      expect(parsed.settings.theme).toBe('dark');
    });

    it('should import valid settings', async () => {
      const importData = {
        version: '1.0.0',
        timestamp: Date.now(),
        settings: {
          ...DEFAULT_SETTINGS,
          position: 'right' as const,
          theme: 'dark' as const
        }
      };

      await settingsManager.import(JSON.stringify(importData));
      
      const settings = await settingsManager.load();
      expect(settings.position).toBe('right');
      expect(settings.theme).toBe('dark');
    });

    it('should reject invalid import data', async () => {
      const invalidData = '{"invalid": "data"}';
      
      await expect(settingsManager.import(invalidData)).rejects.toThrow('Invalid import data format');
    });

    it('should handle malformed JSON in import', async () => {
      const malformedData = '{"invalid": json}';
      
      await expect(settingsManager.import(malformedData)).rejects.toThrow();
    });
  });

  describe('validation', () => {
    beforeEach(async () => {
      await settingsManager.initialize();
    });

    it('should validate correct settings', () => {
      const validSettings = {
        ...DEFAULT_SETTINGS,
        position: 'right' as const
      };

      expect(settingsManager.validate(validSettings)).toBe(true);
    });

    it('should reject settings with invalid position', () => {
      const invalidSettings = {
        ...DEFAULT_SETTINGS,
        position: 'invalid' as any
      };

      expect(settingsManager.validate(invalidSettings)).toBe(false);
    });

    it('should reject settings with invalid theme', () => {
      const invalidSettings = {
        ...DEFAULT_SETTINGS,
        theme: 'invalid' as any
      };

      expect(settingsManager.validate(invalidSettings)).toBe(false);
    });

    it('should reject settings with out-of-range delays', () => {
      const invalidSettings = {
        ...DEFAULT_SETTINGS,
        expandDelay: 50 // Too low
      };

      expect(settingsManager.validate(invalidSettings)).toBe(false);
    });

    it('should reject settings with invalid sidebar width', () => {
      const invalidSettings = {
        ...DEFAULT_SETTINGS,
        sidebarWidth: {
          collapsed: 20, // Too low
          expanded: 300
        }
      };

      expect(settingsManager.validate(invalidSettings)).toBe(false);
    });

    it('should reject settings missing required fields', () => {
      const incompleteSettings = {
        position: 'left'
        // Missing other required fields
      };

      expect(settingsManager.validate(incompleteSettings)).toBe(false);
    });

    it('should reject non-object settings', () => {
      expect(settingsManager.validate(null)).toBe(false);
      expect(settingsManager.validate('string')).toBe(false);
      expect(settingsManager.validate(123)).toBe(false);
    });
  });

  describe('event handling', () => {
    beforeEach(async () => {
      await settingsManager.initialize();
    });

    it('should register settings change callback', async () => {
      const callback = jest.fn();
      
      settingsManager.onSettingsChanged(callback);
      await settingsManager.save({ position: 'right' });
      
      expect(callback).toHaveBeenCalled();
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        position: 'right'
      }));
    });

    it('should remove settings change callback', async () => {
      const callback = jest.fn();
      
      settingsManager.onSettingsChanged(callback);
      settingsManager.removeSettingsChangedListener(callback);
      
      await settingsManager.save({ position: 'right' });
      
      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle callback errors gracefully', async () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error');
      });
      const normalCallback = jest.fn();
      
      settingsManager.onSettingsChanged(errorCallback);
      settingsManager.onSettingsChanged(normalCallback);
      
      await settingsManager.save({ position: 'right' });
      
      // Normal callback should still be called despite error in first callback
      expect(normalCallback).toHaveBeenCalled();
    });
  });

  describe('storage fallback', () => {
    beforeEach(async () => {
      await settingsManager.initialize();
    });

    it('should fallback to local storage when sync storage fails', async () => {
      (chrome.storage.sync.set as jest.Mock).mockRejectedValue(new Error('Sync storage failed'));
      
      await settingsManager.save({ position: 'right' });
      
      expect(chrome.storage.local.set).toHaveBeenCalled();
    });

    it('should throw error when both storage methods fail', async () => {
      (chrome.storage.sync.set as jest.Mock).mockRejectedValue(new Error('Sync storage failed'));
      (chrome.storage.local.set as jest.Mock).mockRejectedValue(new Error('Local storage failed'));
      
      await expect(settingsManager.save({ position: 'right' })).rejects.toThrow('Local storage failed');
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', async () => {
      await settingsManager.initialize();
      
      const callback = jest.fn();
      settingsManager.onSettingsChanged(callback);
      
      settingsManager.destroy();
      
      expect(settingsManager['settingsChangeCallbacks']).toHaveLength(0);
      expect(settingsManager['initialized']).toBe(false);
    });
  });
});