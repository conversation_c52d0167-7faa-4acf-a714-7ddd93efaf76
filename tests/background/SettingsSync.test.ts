/**
 * Settings Sync 单元测试
 */

import { SettingsSync } from '../../src/background/services/SettingsSync';
import { DEFAULT_SETTINGS } from '../../types/index';

describe('SettingsSync', () => {
  let settingsSync: SettingsSync;
  let mockTabs: chrome.tabs.Tab[];

  beforeEach(() => {
    settingsSync = new SettingsSync({
      syncInterval: 1000, // 1秒用于测试
      maxRetries: 2,
      retryDelay: 100
    });

    mockTabs = [
      {
        id: 1,
        url: 'https://example.com',
        title: 'Example',
        active: true,
        windowId: 1,
        index: 0,
        pinned: false
      } as chrome.tabs.Tab,
      {
        id: 2,
        url: 'https://google.com',
        title: 'Google',
        active: false,
        windowId: 1,
        index: 1,
        pinned: false
      } as chrome.tabs.Tab
    ];

    // Mock Chrome APIs
    (chrome.tabs.query as jest.Mock).mockResolvedValue(mockTabs);
    (chrome.tabs.sendMessage as jest.Mock).mockResolvedValue(undefined);
    (chrome.storage.sync.get as jest.Mock).mockResolvedValue({
      userSettings: DEFAULT_SETTINGS
    });
    (chrome.storage.onChanged.addListener as jest.Mock).mockImplementation(() => {});
    (chrome.tabs.onActivated.addListener as jest.Mock).mockImplementation(() => {});

    // Mock timers
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    settingsSync.destroy();
  });

  describe('initialization', () => {
    it('should initialize successfully', () => {
      settingsSync.initialize();
      
      expect(chrome.storage.onChanged.addListener).toHaveBeenCalled();
      expect(chrome.tabs.onActivated.addListener).toHaveBeenCalled();
    });

    it('should start periodic sync', () => {
      settingsSync.initialize();
      
      // Fast-forward time to trigger periodic sync
      jest.advanceTimersByTime(1000);
      
      expect(chrome.storage.sync.get).toHaveBeenCalled();
    });
  });

  describe('sync operations', () => {
    beforeEach(() => {
      settingsSync.initialize();
    });

    it('should sync settings to all tabs', async () => {
      const testSettings = {
        ...DEFAULT_SETTINGS,
        position: 'right' as const
      };

      await settingsSync.syncToAllTabs(testSettings);

      expect(chrome.tabs.query).toHaveBeenCalled();
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(2);
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(1, {
        type: 'SETTINGS_UPDATED',
        payload: testSettings,
        timestamp: expect.any(Number)
      });
    });

    it('should skip chrome:// URLs when syncing', async () => {
      const chromeTab = {
        id: 3,
        url: 'chrome://settings/',
        title: 'Settings',
        active: false,
        windowId: 1,
        index: 2,
        pinned: false
      } as chrome.tabs.Tab;

      (chrome.tabs.query as jest.Mock).mockResolvedValue([...mockTabs, chromeTab]);

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      // Should only send messages to non-chrome tabs
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(2);
      expect(chrome.tabs.sendMessage).not.toHaveBeenCalledWith(3, expect.anything());
    });

    it('should retry failed sync attempts', async () => {
      (chrome.tabs.sendMessage as jest.Mock)
        .mockRejectedValueOnce(new Error('Connection failed'))
        .mockResolvedValueOnce(undefined);

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      // Should retry once for the failed tab
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(3); // 2 initial + 1 retry
    });

    it('should give up after max retries', async () => {
      (chrome.tabs.sendMessage as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      // Should try initial + 2 retries = 3 times per tab
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(6); // 2 tabs * 3 attempts
    });

    it('should handle sync in progress', async () => {
      const syncPromise1 = settingsSync.syncToAllTabs(DEFAULT_SETTINGS);
      const syncPromise2 = settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      await Promise.all([syncPromise1, syncPromise2]);

      // Second sync should be skipped
      expect(chrome.tabs.sendMessage).toHaveBeenCalledTimes(2); // Only first sync
    });
  });

  describe('conflict resolution', () => {
    beforeEach(() => {
      settingsSync.initialize();
    });

    it('should use custom conflict resolver', async () => {
      const localSettings = { ...DEFAULT_SETTINGS, position: 'left' as const };
      const remoteSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };
      const resolvedSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };

      const resolver = jest.fn().mockReturnValue(resolvedSettings);
      settingsSync.addConflictResolver(resolver);

      const result = await settingsSync.resolveConflict(localSettings, remoteSettings);

      expect(resolver).toHaveBeenCalledWith(localSettings, remoteSettings);
      expect(result).toEqual(resolvedSettings);
    });

    it('should use default resolution when no custom resolver', async () => {
      const localSettings = { ...DEFAULT_SETTINGS, position: 'left' as const };
      const remoteSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };

      const result = await settingsSync.resolveConflict(localSettings, remoteSettings);

      // Should use local settings as default
      expect(result).toEqual(localSettings);
    });

    it('should handle resolver errors gracefully', async () => {
      const localSettings = { ...DEFAULT_SETTINGS, position: 'left' as const };
      const remoteSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };

      const errorResolver = jest.fn().mockImplementation(() => {
        throw new Error('Resolver error');
      });
      settingsSync.addConflictResolver(errorResolver);

      const result = await settingsSync.resolveConflict(localSettings, remoteSettings);

      // Should fallback to local settings
      expect(result).toEqual(localSettings);
    });
  });

  describe('event handling', () => {
    beforeEach(() => {
      settingsSync.initialize();
    });

    it('should register sync callback', async () => {
      const callback = jest.fn();
      settingsSync.onSync(callback);

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      expect(callback).toHaveBeenCalledWith(DEFAULT_SETTINGS);
    });

    it('should remove sync callback', async () => {
      const callback = jest.fn();
      settingsSync.onSync(callback);
      settingsSync.removeSyncCallback(callback);

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle callback errors gracefully', async () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error');
      });
      const normalCallback = jest.fn();

      settingsSync.onSync(errorCallback);
      settingsSync.onSync(normalCallback);

      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);

      // Normal callback should still be called
      expect(normalCallback).toHaveBeenCalled();
    });

    it('should sync on storage change', () => {
      const storageListener = (chrome.storage.onChanged.addListener as jest.Mock).mock.calls[0][0];
      const syncSpy = jest.spyOn(settingsSync, 'syncToAllTabs').mockResolvedValue();

      const changes = {
        userSettings: {
          newValue: { ...DEFAULT_SETTINGS, position: 'right' as const }
        }
      };

      storageListener(changes, 'sync');

      expect(syncSpy).toHaveBeenCalledWith(changes.userSettings.newValue);
    });

    it('should sync on tab activation', async () => {
      const tabListener = (chrome.tabs.onActivated.addListener as jest.Mock).mock.calls[0][0];
      
      await tabListener({ tabId: 1, windowId: 1 });

      expect(chrome.storage.sync.get).toHaveBeenCalled();
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(1, expect.objectContaining({
        type: 'SETTINGS_UPDATED'
      }));
    });
  });

  describe('sync status', () => {
    beforeEach(() => {
      settingsSync.initialize();
    });

    it('should return sync status', () => {
      const status = settingsSync.getSyncStatus();

      expect(status).toHaveProperty('inProgress');
      expect(status).toHaveProperty('lastSync');
      expect(status).toHaveProperty('nextSync');
      expect(typeof status.inProgress).toBe('boolean');
      expect(typeof status.lastSync).toBe('number');
      expect(typeof status.nextSync).toBe('number');
    });

    it('should update sync status after sync', async () => {
      const statusBefore = settingsSync.getSyncStatus();
      
      await settingsSync.syncToAllTabs(DEFAULT_SETTINGS);
      
      const statusAfter = settingsSync.getSyncStatus();

      expect(statusAfter.lastSync).toBeGreaterThan(statusBefore.lastSync);
    });
  });

  describe('force sync', () => {
    beforeEach(() => {
      settingsSync.initialize();
    });

    it('should force immediate sync', async () => {
      const syncSpy = jest.spyOn(settingsSync, 'syncToAllTabs');

      await settingsSync.forcSync(DEFAULT_SETTINGS);

      expect(syncSpy).toHaveBeenCalledWith(DEFAULT_SETTINGS);
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', () => {
      settingsSync.initialize();
      
      const callback = jest.fn();
      const resolver = jest.fn();
      settingsSync.onSync(callback);
      settingsSync.addConflictResolver(resolver);

      settingsSync.destroy();

      expect(settingsSync['syncCallbacks']).toHaveLength(0);
      expect(settingsSync['conflictResolvers']).toHaveLength(0);
      expect(settingsSync['syncInProgress']).toBe(false);
    });

    it('should stop periodic sync on destroy', () => {
      settingsSync.initialize();
      
      const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
      
      settingsSync.destroy();

      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });
});