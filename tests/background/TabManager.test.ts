/**
 * Tab Manager 单元测试
 */

import { TabManager } from '../../src/background/services/TabManager';
import { TabInfo, TabGroup } from '../../types/index';

describe('TabManager', () => {
  let tabManager: TabManager;
  let mockTabs: chrome.tabs.Tab[];

  beforeEach(() => {
    tabManager = new TabManager();
    
    // Mock Chrome tabs
    mockTabs = [
      {
        id: 1,
        title: 'Tab 1',
        url: 'https://example.com',
        active: true,
        pinned: false,
        index: 0,
        windowId: 1,
        groupId: -1
      } as chrome.tabs.Tab,
      {
        id: 2,
        title: 'Tab 2',
        url: 'https://google.com',
        active: false,
        pinned: true,
        index: 1,
        windowId: 1,
        groupId: -1
      } as chrome.tabs.Tab
    ];

    // Mock Chrome APIs
    (chrome.tabs.query as jest.Mock).mockResolvedValue(mockTabs);
    (chrome.tabs.get as jest.Mock).mockImplementation((tabId: number) => {
      const tab = mockTabs.find(t => t.id === tabId);
      return tab ? Promise.resolve(tab) : Promise.reject(new Error('No tab with id'));
    });
    (chrome.tabs.update as jest.Mock).mockResolvedValue(undefined);
    (chrome.tabs.remove as jest.Mock).mockResolvedValue(undefined);
    (chrome.tabs.move as jest.Mock).mockResolvedValue(undefined);
    (chrome.tabs.create as jest.Mock).mockResolvedValue(mockTabs[0]);
    (chrome.storage.local.get as jest.Mock).mockResolvedValue({});
    (chrome.storage.local.set as jest.Mock).mockResolvedValue(undefined);
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await tabManager.initialize();
      expect(tabManager).toBeDefined();
    });

    it('should setup event listeners during initialization', async () => {
      const onCreatedSpy = jest.spyOn(chrome.tabs.onCreated, 'addListener');
      const onUpdatedSpy = jest.spyOn(chrome.tabs.onUpdated, 'addListener');
      const onRemovedSpy = jest.spyOn(chrome.tabs.onRemoved, 'addListener');
      
      await tabManager.initialize();
      
      expect(onCreatedSpy).toHaveBeenCalled();
      expect(onUpdatedSpy).toHaveBeenCalled();
      expect(onRemovedSpy).toHaveBeenCalled();
    });
  });

  describe('tab operations', () => {
    beforeEach(async () => {
      await tabManager.initialize();
    });

    it('should get current window tabs', async () => {
      const tabs = await tabManager.getCurrentWindowTabs();
      
      expect(tabs).toHaveLength(2);
      expect(tabs[0]).toMatchObject({
        id: 1,
        title: 'Tab 1',
        url: 'https://example.com',
        active: true,
        pinned: false
      });
    });

    it('should get tab by id', async () => {
      const tab = await tabManager.getTabById(1);
      
      expect(tab).toMatchObject({
        id: 1,
        title: 'Tab 1',
        url: 'https://example.com'
      });
    });

    it('should return null for non-existent tab', async () => {
      const tab = await tabManager.getTabById(999);
      expect(tab).toBeNull();
    });

    it('should activate tab', async () => {
      await tabManager.activateTab(2);
      
      expect(chrome.tabs.update).toHaveBeenCalledWith(2, { active: true });
    });

    it('should close tab', async () => {
      await tabManager.closeTab(1);
      
      expect(chrome.tabs.remove).toHaveBeenCalledWith(1);
    });

    it('should pin tab', async () => {
      await tabManager.pinTab(1, true);
      
      expect(chrome.tabs.update).toHaveBeenCalledWith(1, { pinned: true });
    });

    it('should move tab', async () => {
      await tabManager.moveTab(1, 2);
      
      expect(chrome.tabs.move).toHaveBeenCalledWith(1, { index: 2 });
    });

    it('should duplicate tab', async () => {
      const duplicatedTab = await tabManager.duplicateTab(1);
      
      expect(chrome.tabs.create).toHaveBeenCalledWith({
        url: 'https://example.com',
        windowId: 1,
        index: 1,
        active: false
      });
      expect(duplicatedTab.id).toBe(1);
    });
  });

  describe('tab groups', () => {
    beforeEach(async () => {
      await tabManager.initialize();
    });

    it('should get tab groups when chrome.tabGroups is available', async () => {
      const mockGroups = [
        {
          id: 1,
          title: 'Group 1',
          color: 'blue',
          collapsed: false,
          windowId: 1
        }
      ];
      
      (chrome.tabGroups as any) = {
        query: jest.fn().mockResolvedValue(mockGroups)
      };
      
      const groups = await tabManager.getTabGroups();
      
      expect(groups).toHaveLength(1);
      expect(groups[0]).toMatchObject({
        id: 1,
        title: 'Group 1',
        color: 'blue',
        collapsed: false
      });
    });

    it('should create tab group with native API', async () => {
      const mockGroupId = 1;
      const mockGroup = {
        id: mockGroupId,
        title: 'Test Group',
        color: 'blue',
        collapsed: false,
        windowId: 1
      };
      
      (chrome.tabGroups as any) = {
        update: jest.fn().mockResolvedValue(undefined),
        get: jest.fn().mockResolvedValue(mockGroup)
      };
      (chrome.tabs.group as jest.Mock).mockResolvedValue(mockGroupId);
      
      const group = await tabManager.createTabGroup('Test Group', 'blue', [1, 2]);
      
      expect(chrome.tabs.group).toHaveBeenCalledWith({ tabIds: [1, 2] });
      expect(chrome.tabGroups.update).toHaveBeenCalledWith(mockGroupId, {
        title: 'Test Group',
        color: 'blue'
      });
      expect(group.title).toBe('Test Group');
    });

    it('should create custom tab group when native API not available', async () => {
      delete (chrome as any).tabGroups;
      
      const group = await tabManager.createTabGroup('Custom Group', 'red', [1, 2]);
      
      expect(group.title).toBe('Custom Group');
      expect(group.color).toBe('red');
      expect(group.tabIds).toEqual([1, 2]);
      expect(chrome.storage.local.set).toHaveBeenCalled();
    });
  });

  describe('event handling', () => {
    beforeEach(async () => {
      await tabManager.initialize();
    });

    it('should register tab change callback', () => {
      const callback = jest.fn();
      
      tabManager.onTabsChanged(callback);
      
      // Simulate tab change
      const onCreatedCallback = (chrome.tabs.onCreated.addListener as jest.Mock).mock.calls[0][0];
      onCreatedCallback(mockTabs[0]);
      
      // Should eventually call the callback (async)
      setTimeout(() => {
        expect(callback).toHaveBeenCalled();
      }, 0);
    });

    it('should remove tab change callback', () => {
      const callback = jest.fn();
      
      tabManager.onTabsChanged(callback);
      tabManager.removeTabsChangedListener(callback);
      
      // Callback should be removed
      expect(tabManager['tabChangeCallbacks']).not.toContain(callback);
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await tabManager.initialize();
    });

    it('should handle tab activation errors', async () => {
      (chrome.tabs.update as jest.Mock).mockRejectedValue(new Error('Tab not found'));
      
      await expect(tabManager.activateTab(999)).rejects.toThrow('Tab not found');
    });

    it('should handle tab closing errors', async () => {
      (chrome.tabs.remove as jest.Mock).mockRejectedValue(new Error('Cannot close tab'));
      
      await expect(tabManager.closeTab(999)).rejects.toThrow('Cannot close tab');
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', async () => {
      await tabManager.initialize();
      
      tabManager.destroy();
      
      expect(tabManager['tabChangeCallbacks']).toHaveLength(0);
      expect(tabManager['tabGroups'].size).toBe(0);
    });
  });
});