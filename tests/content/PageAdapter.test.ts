/**
 * Page Adapter 单元测试
 */

import { PageAdapter } from '../../src/content/utils/PageAdapter';

// Mock DOM environment
const mockDocument = {
  body: {
    style: {},
    appendChild: jest.fn(),
    removeChild: jest.fn()
  },
  documentElement: {
    style: {}
  },
  createElement: jest.fn(() => ({
    style: {},
    setAttribute: jest.fn(),
    getAttribute: jest.fn(),
    removeAttribute: jest.fn()
  })),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
  contentType: 'text/html'
};

const mockWindow = {
  location: {
    href: 'https://example.com'
  },
  top: null as any,
  getComputedStyle: jest.fn(() => ({
    position: 'static',
    left: '0px',
    right: '0px',
    width: '100%',
    zIndex: '1',
    getPropertyValue: jest.fn(() => 'auto')
  })),
  dispatchEvent: jest.fn()
};

// Set up global mocks
(global as any).document = mockDocument;
(global as any).window = mockWindow;
mockWindow.top = mockWindow;

describe('PageAdapter', () => {
  let pageAdapter: PageAdapter;

  beforeEach(() => {
    pageAdapter = new PageAdapter();
    jest.clearAllMocks();
  });

  describe('canInject', () => {
    it('should return true for valid pages', () => {
      expect(pageAdapter.canInject()).toBe(true);
    });

    it('should return false for chrome:// URLs', () => {
      mockWindow.location.href = 'chrome://settings/';
      
      expect(pageAdapter.canInject()).toBe(false);
    });

    it('should return false for chrome-extension:// URLs', () => {
      mockWindow.location.href = 'chrome-extension://abc123/popup.html';
      
      expect(pageAdapter.canInject()).toBe(false);
    });

    it('should return false for about: URLs', () => {
      mockWindow.location.href = 'about:blank';
      
      expect(pageAdapter.canInject()).toBe(false);
    });

    it('should return false for file:// URLs', () => {
      mockWindow.location.href = 'file:///path/to/file.html';
      
      expect(pageAdapter.canInject()).toBe(false);
    });

    it('should return false when DOM is not ready', () => {
      (global as any).document.body = null;
      
      expect(pageAdapter.canInject()).toBe(false);
      
      // Restore
      (global as any).document.body = mockDocument.body;
    });

    it('should return false for iframes', () => {
      const mockTop = {};
      mockWindow.top = mockTop;
      
      expect(pageAdapter.canInject()).toBe(false);
      
      // Restore
      mockWindow.top = mockWindow;
    });

    it('should return false for non-HTML content', () => {
      mockDocument.contentType = 'application/pdf';
      
      expect(pageAdapter.canInject()).toBe(false);
      
      // Restore
      mockDocument.contentType = 'text/html';
    });

    it('should return false for PDF pages', () => {
      mockDocument.querySelector = jest.fn((selector) => {
        if (selector === 'embed[type="application/pdf"]') {
          return { type: 'application/pdf' };
        }
        return null;
      });
      
      expect(pageAdapter.canInject()).toBe(false);
      
      // Restore
      mockDocument.querySelector = jest.fn();
    });
  });

  describe('adjustPageLayout', () => {
    beforeEach(() => {
      // Reset styles
      mockDocument.body.style = {};
      mockDocument.documentElement.style = {};
    });

    it('should adjust layout for left position', () => {
      pageAdapter.adjustPageLayout(300, 'left');
      
      expect(mockDocument.body.style.marginLeft).toBe('300px');
      expect(mockDocument.documentElement.style.marginLeft).toBe('300px');
      expect(mockDocument.body.style.transition).toBeDefined();
    });

    it('should adjust layout for right position', () => {
      pageAdapter.adjustPageLayout(300, 'right');
      
      expect(mockDocument.body.style.marginRight).toBe('300px');
      expect(mockDocument.documentElement.style.marginRight).toBe('300px');
    });

    it('should trigger resize event', () => {
      pageAdapter.adjustPageLayout(300, 'left');
      
      expect(mockWindow.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({ type: 'resize' })
      );
    });

    it('should handle errors gracefully', () => {
      // Mock error in style setting
      Object.defineProperty(mockDocument.body, 'style', {
        get: () => {
          throw new Error('Style access error');
        }
      });
      
      expect(() => pageAdapter.adjustPageLayout(300, 'left')).not.toThrow();
    });
  });

  describe('restorePageLayout', () => {
    it('should restore original layout', () => {
      // First adjust layout
      pageAdapter.adjustPageLayout(300, 'left');
      
      // Then restore
      pageAdapter.restorePageLayout();
      
      expect(mockWindow.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({ type: 'resize' })
      );
    });

    it('should restore elements with data attributes', () => {
      const mockElement = {
        style: { width: '200px', marginLeft: '300px' },
        getAttribute: jest.fn((attr) => {
          if (attr === 'data-original-width') return '100%';
          if (attr === 'data-original-left') return '0px';
          return null;
        }),
        removeAttribute: jest.fn()
      };

      mockDocument.querySelectorAll = jest.fn((selector) => {
        if (selector === '[data-original-width]') return [mockElement];
        if (selector === '[data-original-left], [data-original-right]') return [mockElement];
        return [];
      });

      pageAdapter.restorePageLayout();

      expect(mockElement.removeAttribute).toHaveBeenCalledWith('data-original-width');
    });
  });

  describe('detectConflicts', () => {
    it('should detect no conflicts on clean page', () => {
      const conflicts = pageAdapter.detectConflicts();
      
      expect(Array.isArray(conflicts)).toBe(true);
    });

    it('should detect fixed element conflicts', () => {
      const mockFixedElement = {
        style: { position: 'fixed', left: '10px' }
      };

      mockWindow.getComputedStyle = jest.fn(() => ({
        position: 'fixed',
        left: '10px',
        right: 'auto',
        width: '100%',
        zIndex: '1',
        getPropertyValue: jest.fn(() => 'auto')
      }));

      mockDocument.querySelectorAll = jest.fn(() => [mockFixedElement]);

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('left-positioned fixed elements'))).toBe(true);
    });

    it('should detect full-width elements', () => {
      const mockFullWidthElement = {
        style: { width: '100vw' }
      };

      mockWindow.getComputedStyle = jest.fn(() => ({
        position: 'static',
        left: '0px',
        right: '0px',
        width: '100vw',
        zIndex: '1',
        getPropertyValue: jest.fn(() => 'auto')
      }));

      mockDocument.querySelectorAll = jest.fn((selector) => {
        if (selector === 'div, section, header, footer, nav') {
          return [mockFullWidthElement];
        }
        return [];
      });

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('full-width elements'))).toBe(true);
    });

    it('should detect high z-index elements', () => {
      const mockHighZElement = {
        style: { zIndex: '9999999999' }
      };

      mockWindow.getComputedStyle = jest.fn(() => ({
        position: 'static',
        left: '0px',
        right: '0px',
        width: '100%',
        zIndex: '9999999999',
        getPropertyValue: jest.fn(() => 'auto')
      }));

      mockDocument.querySelectorAll = jest.fn(() => [mockHighZElement]);

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('high z-index elements'))).toBe(true);
    });

    it('should detect Bootstrap framework', () => {
      mockDocument.querySelector = jest.fn((selector) => {
        if (selector === '.container-fluid, .container') {
          return { className: 'container' };
        }
        return null;
      });

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('Bootstrap framework'))).toBe(true);
    });

    it('should detect Tailwind CSS framework', () => {
      mockDocument.querySelector = jest.fn((selector) => {
        if (selector === '[class*="w-full"], [class*="w-screen"]') {
          return { className: 'w-full' };
        }
        return null;
      });

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('Tailwind CSS framework'))).toBe(true);
    });

    it('should detect custom scrollbar', () => {
      mockWindow.getComputedStyle = jest.fn(() => ({
        position: 'static',
        left: '0px',
        right: '0px',
        width: '100%',
        zIndex: '1',
        getPropertyValue: jest.fn((prop) => {
          if (prop === 'scrollbar-width') return 'thin';
          return 'auto';
        })
      }));

      const conflicts = pageAdapter.detectConflicts();
      
      expect(conflicts.some(c => c.includes('Custom scrollbar'))).toBe(true);
    });
  });

  describe('handleConflicts', () => {
    it('should handle empty conflicts array', () => {
      expect(() => pageAdapter.handleConflicts([])).not.toThrow();
    });

    it('should handle fixed element conflicts', () => {
      const conflicts = ['Found 2 left-positioned fixed elements'];
      
      expect(() => pageAdapter.handleConflicts(conflicts)).not.toThrow();
    });

    it('should handle full-width element conflicts', () => {
      const conflicts = ['Found 3 full-width elements'];
      
      expect(() => pageAdapter.handleConflicts(conflicts)).not.toThrow();
    });

    it('should handle z-index conflicts', () => {
      const conflicts = ['Found 1 high z-index elements'];
      
      expect(() => pageAdapter.handleConflicts(conflicts)).not.toThrow();
    });

    it('should handle scrollbar conflicts', () => {
      const conflicts = ['Custom scrollbar detected'];
      
      expect(() => pageAdapter.handleConflicts(conflicts)).not.toThrow();
    });

    it('should handle multiple conflicts', () => {
      const conflicts = [
        'Found 2 left-positioned fixed elements',
        'Found 3 full-width elements',
        'Custom scrollbar detected'
      ];
      
      expect(() => pageAdapter.handleConflicts(conflicts)).not.toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle DOM access errors', () => {
      // Mock DOM access error
      Object.defineProperty(global, 'document', {
        get: () => {
          throw new Error('DOM access error');
        }
      });
      
      expect(pageAdapter.canInject()).toBe(false);
      
      // Restore
      (global as any).document = mockDocument;
    });

    it('should handle style computation errors', () => {
      mockWindow.getComputedStyle = jest.fn(() => {
        throw new Error('Style computation error');
      });
      
      const conflicts = pageAdapter.detectConflicts();
      expect(conflicts.some(c => c.includes('Error during conflict detection'))).toBe(true);
    });
  });
});