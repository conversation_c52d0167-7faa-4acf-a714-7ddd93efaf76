/**
 * Sidebar Container 单元测试
 */

import { SidebarContainer } from '../../src/content/sidebar/components/SidebarContainer';
import { DEFAULT_SETTINGS } from '../../types/index';

// Mock child components
const mockHeader = {
  element: document.createElement('div'),
  render: jest.fn(),
  destroy: jest.fn(),
  update: jest.fn(),
  focusSearch: jest.fn(),
  onSearch: jest.fn()
};

const mockContent = {
  element: document.createElement('div'),
  render: jest.fn(),
  destroy: jest.fn(),
  update: jest.fn(),
  updateTabs: jest.fn(),
  updateBookmarks: jest.fn(),
  updateSettings: jest.fn(),
  search: jest.fn()
};

const mockFooter = {
  element: document.createElement('div'),
  render: jest.fn(),
  destroy: jest.fn(),
  update: jest.fn(),
  showPinButton: jest.fn(),
  showUnpinButton: jest.fn(),
  onPinClick: jest.fn(),
  onUnpinClick: jest.fn()
};

// Mock constructors
jest.mock('../../src/content/sidebar/components/SidebarHeader', () => ({
  SidebarHeader: jest.fn(() => mockHeader)
}));

jest.mock('../../src/content/sidebar/components/SidebarContent', () => ({
  SidebarContent: jest.fn(() => mockContent)
}));

jest.mock('../../src/content/sidebar/components/SidebarFooter', () => ({
  SidebarFooter: jest.fn(() => mockFooter)
}));

// Mock DOM
const mockDocument = {
  createElement: jest.fn((tag) => {
    const element = {
      tagName: tag.toUpperCase(),
      className: '',
      id: '',
      style: {},
      innerHTML: '',
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        contains: jest.fn(() => false),
        toggle: jest.fn()
      },
      contains: jest.fn(() => true),
      parentNode: null
    };
    return element;
  }),
  body: {
    appendChild: jest.fn(),
    contains: jest.fn(() => false)
  }
};

(global as any).document = mockDocument;

// Mock window.matchMedia
(global as any).window = {
  matchMedia: jest.fn(() => ({
    matches: false,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  }))
};

describe('SidebarContainer', () => {
  let sidebarContainer: SidebarContainer;
  let options: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    options = {
      position: 'left' as const,
      theme: 'light' as const,
      collapsedWidth: 60,
      expandedWidth: 300,
      showTabs: true,
      showBookmarks: true
    };

    sidebarContainer = new SidebarContainer(options);
  });

  afterEach(() => {
    sidebarContainer.destroy();
  });

  describe('initialization', () => {
    it('should create container element', () => {
      expect(mockDocument.createElement).toHaveBeenCalledWith('div');
      expect(sidebarContainer.element.className).toBe('vertical-sidebar');
      expect(sidebarContainer.element.id).toBe('chrome-vertical-sidebar');
    });

    it('should create child components', () => {
      const SidebarHeader = require('../../src/content/sidebar/components/SidebarHeader').SidebarHeader;
      const SidebarContent = require('../../src/content/sidebar/components/SidebarContent').SidebarContent;
      const SidebarFooter = require('../../src/content/sidebar/components/SidebarFooter').SidebarFooter;

      expect(SidebarHeader).toHaveBeenCalled();
      expect(SidebarContent).toHaveBeenCalledWith({
        showTabs: true,
        showBookmarks: true
      });
      expect(SidebarFooter).toHaveBeenCalled();
    });

    it('should setup event listeners', () => {
      expect(sidebarContainer.element.addEventListener).toHaveBeenCalledWith('mouseenter', expect.any(Function));
      expect(sidebarContainer.element.addEventListener).toHaveBeenCalledWith('mouseleave', expect.any(Function));
    });
  });

  describe('rendering', () => {
    it('should render all child components', () => {
      sidebarContainer.render();

      expect(sidebarContainer.element.appendChild).toHaveBeenCalledWith(mockHeader.element);
      expect(sidebarContainer.element.appendChild).toHaveBeenCalledWith(mockContent.element);
      expect(sidebarContainer.element.appendChild).toHaveBeenCalledWith(mockFooter.element);

      expect(mockHeader.render).toHaveBeenCalled();
      expect(mockContent.render).toHaveBeenCalled();
      expect(mockFooter.render).toHaveBeenCalled();
    });

    it('should apply styles', () => {
      sidebarContainer.render();

      expect(sidebarContainer.element.style.position).toBe('fixed');
      expect(sidebarContainer.element.style.top).toBe('0');
      expect(sidebarContainer.element.style.height).toBe('100vh');
      expect(sidebarContainer.element.style.width).toBe('60px');
    });

    it('should apply position styles', () => {
      sidebarContainer.render();

      expect(sidebarContainer.element.style.left).toBe('0');
      expect(sidebarContainer.element.style.right).toBe('auto');
      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('position-left');
    });

    it('should apply right position styles', () => {
      options.position = 'right';
      sidebarContainer = new SidebarContainer(options);
      sidebarContainer.render();

      expect(sidebarContainer.element.style.left).toBe('auto');
      expect(sidebarContainer.element.style.right).toBe('0');
      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('position-right');
    });

    it('should apply theme styles', () => {
      sidebarContainer.render();

      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('theme-light');
    });

    it('should handle auto theme', () => {
      options.theme = 'auto';
      sidebarContainer = new SidebarContainer(options);
      sidebarContainer.render();

      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('theme-auto');
    });
  });

  describe('visibility operations', () => {
    it('should show sidebar', () => {
      sidebarContainer.show();

      expect(sidebarContainer.element.style.display).toBe('flex');
      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('visible');
    });

    it('should hide sidebar', () => {
      sidebarContainer.hide();

      expect(sidebarContainer.element.style.display).toBe('none');
      expect(sidebarContainer.element.classList.remove).toHaveBeenCalledWith('visible', 'expanded', 'pinned');
    });
  });

  describe('expand/collapse operations', () => {
    it('should expand sidebar', () => {
      const expandCallback = jest.fn();
      sidebarContainer.onExpand(expandCallback);

      sidebarContainer.expand();

      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('expanded');
      expect(sidebarContainer.element.style.width).toBe('300px');
      expect(expandCallback).toHaveBeenCalled();
    });

    it('should collapse sidebar', () => {
      const collapseCallback = jest.fn();
      sidebarContainer.onCollapse(collapseCallback);

      sidebarContainer.collapse();

      expect(sidebarContainer.element.classList.remove).toHaveBeenCalledWith('expanded');
      expect(sidebarContainer.element.style.width).toBe('60px');
      expect(collapseCallback).toHaveBeenCalled();
    });
  });

  describe('pin/unpin operations', () => {
    it('should pin sidebar', () => {
      const pinCallback = jest.fn();
      sidebarContainer.onPin(pinCallback);

      sidebarContainer.pin();

      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('pinned');
      expect(mockFooter.showUnpinButton).toHaveBeenCalled();
      expect(pinCallback).toHaveBeenCalled();
    });

    it('should unpin sidebar', () => {
      const unpinCallback = jest.fn();
      sidebarContainer.onUnpin(unpinCallback);

      sidebarContainer.unpin();

      expect(sidebarContainer.element.classList.remove).toHaveBeenCalledWith('pinned');
      expect(mockFooter.showPinButton).toHaveBeenCalled();
      expect(unpinCallback).toHaveBeenCalled();
    });
  });

  describe('data updates', () => {
    it('should update tabs', () => {
      const tabs = [createMockTab({ id: 1, title: 'Tab 1' })];

      sidebarContainer.updateTabs(tabs);

      expect(mockContent.updateTabs).toHaveBeenCalledWith(tabs);
    });

    it('should update bookmarks', () => {
      const bookmarks = [createMockBookmark({ id: '1', title: 'Bookmark 1' })];

      sidebarContainer.updateBookmarks(bookmarks);

      expect(mockContent.updateBookmarks).toHaveBeenCalledWith(bookmarks);
    });

    it('should update settings', () => {
      const newSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };

      sidebarContainer.updateSettings(newSettings);

      expect(mockContent.updateSettings).toHaveBeenCalledWith(newSettings);
    });
  });

  describe('mouse hover behavior', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should expand on mouse enter after delay', () => {
      const mouseEnterHandler = sidebarContainer.element.addEventListener.mock.calls
        .find(call => call[0] === 'mouseenter')[1];

      mouseEnterHandler();

      // Should not expand immediately
      expect(sidebarContainer.element.classList.add).not.toHaveBeenCalledWith('expanded');

      // Should expand after delay
      jest.advanceTimersByTime(300);
      expect(sidebarContainer.element.classList.add).toHaveBeenCalledWith('expanded');
    });

    it('should collapse on mouse leave after delay', () => {
      // First expand
      sidebarContainer.element.classList.contains = jest.fn((className) => className === 'expanded');

      const mouseLeaveHandler = sidebarContainer.element.addEventListener.mock.calls
        .find(call => call[0] === 'mouseleave')[1];

      mouseLeaveHandler();

      // Should not collapse immediately
      expect(sidebarContainer.element.classList.remove).not.toHaveBeenCalledWith('expanded');

      // Should collapse after delay
      jest.advanceTimersByTime(500);
      expect(sidebarContainer.element.classList.remove).toHaveBeenCalledWith('expanded');
    });

    it('should not expand when pinned', () => {
      sidebarContainer.element.classList.contains = jest.fn((className) => className === 'pinned');

      const mouseEnterHandler = sidebarContainer.element.addEventListener.mock.calls
        .find(call => call[0] === 'mouseenter')[1];

      mouseEnterHandler();
      jest.advanceTimersByTime(300);

      expect(sidebarContainer.element.classList.add).not.toHaveBeenCalledWith('expanded');
    });

    it('should not collapse when pinned', () => {
      sidebarContainer.element.classList.contains = jest.fn((className) => 
        className === 'pinned' || className === 'expanded'
      );

      const mouseLeaveHandler = sidebarContainer.element.addEventListener.mock.calls
        .find(call => call[0] === 'mouseleave')[1];

      mouseLeaveHandler();
      jest.advanceTimersByTime(500);

      expect(sidebarContainer.element.classList.remove).not.toHaveBeenCalledWith('expanded');
    });
  });

  describe('search functionality', () => {
    it('should focus search', () => {
      sidebarContainer.focusSearch();

      expect(mockHeader.focusSearch).toHaveBeenCalled();
    });

    it('should handle search events', () => {
      // Simulate search callback setup
      const searchCallback = mockHeader.onSearch.mock.calls[0][0];
      
      searchCallback('test query');

      expect(mockContent.search).toHaveBeenCalledWith('test query');
    });
  });

  describe('resize and scroll handling', () => {
    it('should handle resize', () => {
      sidebarContainer.handleResize();

      // Should not throw error
      expect(true).toBe(true);
    });

    it('should handle scroll', () => {
      sidebarContainer.handleScroll();

      expect(sidebarContainer.element.style.top).toBe('0px');
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', () => {
      sidebarContainer.destroy();

      expect(mockHeader.destroy).toHaveBeenCalled();
      expect(mockContent.destroy).toHaveBeenCalled();
      expect(mockFooter.destroy).toHaveBeenCalled();
    });

    it('should clear timers on destroy', () => {
      jest.useFakeTimers();
      
      // Trigger mouse enter to start timer
      const mouseEnterHandler = sidebarContainer.element.addEventListener.mock.calls
        .find(call => call[0] === 'mouseenter')[1];
      mouseEnterHandler();

      sidebarContainer.destroy();

      // Timer should be cleared
      jest.advanceTimersByTime(300);
      expect(sidebarContainer.element.classList.add).not.toHaveBeenCalledWith('expanded');

      jest.useRealTimers();
    });

    it('should clear callbacks on destroy', () => {
      const expandCallback = jest.fn();
      sidebarContainer.onExpand(expandCallback);

      sidebarContainer.destroy();

      // Callbacks should be cleared
      expect(sidebarContainer['expandCallbacks']).toHaveLength(0);
    });
  });
});