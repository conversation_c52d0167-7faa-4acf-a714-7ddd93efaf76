/**
 * Sidebar Manager 单元测试
 */

import { SidebarManagerImpl } from '../../src/content/sidebar/SidebarManager';
import { DEFAULT_SETTINGS, DEFAULT_SIDEBAR_STATE } from '../../types/index';

// Mock dependencies
jest.mock('../../src/content/sidebar/components/SidebarContainer');
jest.mock('../../src/content/utils/PageAdapter');
jest.mock('../../src/content/utils/StyleManager');
jest.mock('../../src/content/utils/EventManager');
jest.mock('../../src/content/utils/AnimationManager');

const mockSidebarContainer = {
  show: jest.fn(),
  hide: jest.fn(),
  expand: jest.fn(),
  collapse: jest.fn(),
  pin: jest.fn(),
  unpin: jest.fn(),
  updateTabs: jest.fn(),
  updateBookmarks: jest.fn(),
  updateSettings: jest.fn(),
  focusSearch: jest.fn(),
  handleResize: jest.fn(),
  handleScroll: jest.fn(),
  onExpand: jest.fn(),
  onCollapse: jest.fn(),
  onPin: jest.fn(),
  onUnpin: jest.fn(),
  destroy: jest.fn()
};

const mockPageAdapter = {
  canInject: jest.fn(() => true),
  detectConflicts: jest.fn(() => []),
  handleConflicts: jest.fn(),
  adjustPageLayout: jest.fn(),
  restorePageLayout: jest.fn()
};

const mockStyleManager = {
  injectStyles: jest.fn(),
  removeStyles: jest.fn(),
  updateTheme: jest.fn(),
  updatePosition: jest.fn(),
  updateDimensions: jest.fn()
};

const mockEventManager = {
  addEventListener: jest.fn(),
  removeAllListeners: jest.fn()
};

const mockAnimationManager = {
  initialize: jest.fn(),
  expandSidebar: jest.fn(),
  collapseSidebar: jest.fn(),
  destroy: jest.fn()
};

// Mock Chrome APIs
(chrome.runtime.sendMessage as jest.Mock).mockResolvedValue({
  success: true,
  data: DEFAULT_SETTINGS
});

(chrome.runtime.onMessage.addListener as jest.Mock).mockImplementation(() => {});

describe('SidebarManager', () => {
  let sidebarManager: SidebarManagerImpl;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock constructors
    require('../../src/content/sidebar/components/SidebarContainer').SidebarContainer = jest.fn(() => mockSidebarContainer);
    require('../../src/content/utils/PageAdapter').PageAdapter = jest.fn(() => mockPageAdapter);
    require('../../src/content/utils/StyleManager').StyleManager = jest.fn(() => mockStyleManager);
    require('../../src/content/utils/EventManager').EventManager = jest.fn(() => mockEventManager);
    require('../../src/content/utils/AnimationManager').AnimationManager = jest.fn(() => mockAnimationManager);

    sidebarManager = new SidebarManagerImpl();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await sidebarManager.initialize();
      
      expect(sidebarManager.isInitialized()).toBe(true);
      expect(mockPageAdapter.canInject).toHaveBeenCalled();
      expect(mockStyleManager.injectStyles).toHaveBeenCalled();
      expect(mockAnimationManager.initialize).toHaveBeenCalled();
    });

    it('should handle page injection failure', async () => {
      mockPageAdapter.canInject.mockReturnValue(false);
      
      await expect(sidebarManager.initialize()).rejects.toThrow('Cannot inject sidebar on this page');
      expect(sidebarManager.isInitialized()).toBe(false);
    });

    it('should handle page conflicts', async () => {
      const conflicts = ['Found 2 left-positioned fixed elements'];
      mockPageAdapter.detectConflicts.mockReturnValue(conflicts);
      
      await sidebarManager.initialize();
      
      expect(mockPageAdapter.handleConflicts).toHaveBeenCalledWith(conflicts);
    });

    it('should show and pin sidebar if defaultPinned is true', async () => {
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue({
        success: true,
        data: { ...DEFAULT_SETTINGS, defaultPinned: true }
      });
      
      await sidebarManager.initialize();
      
      expect(mockSidebarContainer.show).toHaveBeenCalled();
      expect(mockSidebarContainer.pin).toHaveBeenCalled();
    });
  });

  describe('sidebar operations', () => {
    beforeEach(async () => {
      await sidebarManager.initialize();
    });

    it('should show sidebar', () => {
      sidebarManager.show();
      
      expect(mockSidebarContainer.show).toHaveBeenCalled();
      expect(sidebarManager.getState().visible).toBe(true);
    });

    it('should hide sidebar', () => {
      sidebarManager.hide();
      
      expect(mockSidebarContainer.hide).toHaveBeenCalled();
      expect(sidebarManager.getState().visible).toBe(false);
      expect(sidebarManager.getState().expanded).toBe(false);
    });

    it('should toggle sidebar visibility', () => {
      // Initially visible
      sidebarManager.show();
      
      // Toggle should hide
      sidebarManager.toggle();
      expect(mockSidebarContainer.hide).toHaveBeenCalled();
      
      // Toggle should show
      sidebarManager.toggle();
      expect(mockSidebarContainer.show).toHaveBeenCalled();
    });

    it('should expand sidebar', () => {
      sidebarManager.show();
      sidebarManager.expand();
      
      expect(mockAnimationManager.expandSidebar).toHaveBeenCalled();
      expect(mockSidebarContainer.expand).toHaveBeenCalled();
      expect(sidebarManager.getState().expanded).toBe(true);
    });

    it('should collapse sidebar', () => {
      sidebarManager.show();
      sidebarManager.expand();
      sidebarManager.collapse();
      
      expect(mockAnimationManager.collapseSidebar).toHaveBeenCalled();
      expect(mockSidebarContainer.collapse).toHaveBeenCalled();
      expect(sidebarManager.getState().expanded).toBe(false);
    });

    it('should pin sidebar', () => {
      sidebarManager.show();
      sidebarManager.pin();
      
      expect(mockPageAdapter.adjustPageLayout).toHaveBeenCalled();
      expect(mockSidebarContainer.pin).toHaveBeenCalled();
      expect(sidebarManager.getState().pinned).toBe(true);
    });

    it('should unpin sidebar', () => {
      sidebarManager.show();
      sidebarManager.pin();
      sidebarManager.unpin();
      
      expect(mockPageAdapter.restorePageLayout).toHaveBeenCalled();
      expect(mockSidebarContainer.unpin).toHaveBeenCalled();
      expect(sidebarManager.getState().pinned).toBe(false);
    });

    it('should not operate when not visible', () => {
      sidebarManager.hide();
      sidebarManager.expand();
      
      expect(mockSidebarContainer.expand).not.toHaveBeenCalled();
    });
  });

  describe('data updates', () => {
    beforeEach(async () => {
      await sidebarManager.initialize();
    });

    it('should update tabs', () => {
      const tabs = [createMockTab({ id: 1, title: 'Tab 1' })];
      
      sidebarManager.updateTabs(tabs);
      
      expect(mockSidebarContainer.updateTabs).toHaveBeenCalledWith(tabs);
    });

    it('should update bookmarks', () => {
      const bookmarks = [createMockBookmark({ id: '1', title: 'Bookmark 1' })];
      
      sidebarManager.updateBookmarks(bookmarks);
      
      expect(mockSidebarContainer.updateBookmarks).toHaveBeenCalledWith(bookmarks);
    });

    it('should update settings', () => {
      const newSettings = { ...DEFAULT_SETTINGS, position: 'right' as const };
      
      sidebarManager.updateSettings(newSettings);
      
      expect(mockStyleManager.updateTheme).toHaveBeenCalledWith(newSettings.theme);
      expect(mockStyleManager.updatePosition).toHaveBeenCalledWith(newSettings.position);
      expect(mockStyleManager.updateDimensions).toHaveBeenCalledWith(
        newSettings.sidebarWidth.collapsed,
        newSettings.sidebarWidth.expanded
      );
      expect(mockSidebarContainer.updateSettings).toHaveBeenCalledWith(newSettings);
    });

    it('should readjust layout when pinned and settings updated', () => {
      const newSettings = { ...DEFAULT_SETTINGS, sidebarWidth: { collapsed: 80, expanded: 350 } };
      
      // Pin sidebar first
      sidebarManager.show();
      sidebarManager.pin();
      
      // Update settings
      sidebarManager.updateSettings(newSettings);
      
      expect(mockPageAdapter.adjustPageLayout).toHaveBeenCalledWith(80, 'left');
    });
  });

  describe('state management', () => {
    beforeEach(async () => {
      await sidebarManager.initialize();
    });

    it('should get current state', () => {
      const state = sidebarManager.getState();
      
      expect(state).toEqual(expect.objectContaining({
        visible: expect.any(Boolean),
        expanded: expect.any(Boolean),
        pinned: expect.any(Boolean)
      }));
    });

    it('should set state', () => {
      sidebarManager.setState({
        visible: true,
        expanded: true,
        pinned: true
      });
      
      const state = sidebarManager.getState();
      expect(state.visible).toBe(true);
      expect(state.expanded).toBe(true);
      expect(state.pinned).toBe(true);
    });

    it('should trigger operations when state changes', () => {
      sidebarManager.setState({ visible: true });
      expect(mockSidebarContainer.show).toHaveBeenCalled();
      
      sidebarManager.setState({ expanded: true });
      expect(mockSidebarContainer.expand).toHaveBeenCalled();
      
      sidebarManager.setState({ pinned: true });
      expect(mockSidebarContainer.pin).toHaveBeenCalled();
    });
  });

  describe('message handling', () => {
    beforeEach(async () => {
      await sidebarManager.initialize();
    });

    it('should handle toggle command', () => {
      const messageListener = (chrome.runtime.onMessage.addListener as jest.Mock).mock.calls[0][0];
      const sendResponse = jest.fn();
      
      messageListener({ type: 'TOGGLE_SIDEBAR_COMMAND' }, {}, sendResponse);
      
      expect(sendResponse).toHaveBeenCalledWith({ success: true });
    });

    it('should handle pin command', () => {
      const messageListener = (chrome.runtime.onMessage.addListener as jest.Mock).mock.calls[0][0];
      const sendResponse = jest.fn();
      
      messageListener({ type: 'PIN_SIDEBAR_COMMAND' }, {}, sendResponse);
      
      expect(sendResponse).toHaveBeenCalledWith({ success: true });
    });

    it('should handle settings update', () => {
      const messageListener = (chrome.runtime.onMessage.addListener as jest.Mock).mock.calls[0][0];
      const sendResponse = jest.fn();
      const newSettings = { ...DEFAULT_SETTINGS, theme: 'dark' as const };
      
      messageListener({ 
        type: 'SETTINGS_UPDATED', 
        payload: newSettings 
      }, {}, sendResponse);
      
      expect(mockSidebarContainer.updateSettings).toHaveBeenCalledWith(newSettings);
      expect(sendResponse).toHaveBeenCalledWith({ success: true });
    });

    it('should handle unknown message type', () => {
      const messageListener = (chrome.runtime.onMessage.addListener as jest.Mock).mock.calls[0][0];
      const sendResponse = jest.fn();
      
      messageListener({ type: 'UNKNOWN_TYPE' }, {}, sendResponse);
      
      expect(sendResponse).toHaveBeenCalledWith({ 
        success: false, 
        error: 'Unknown message type: UNKNOWN_TYPE' 
      });
    });
  });

  describe('keyboard shortcuts', () => {
    beforeEach(async () => {
      await sidebarManager.initialize();
    });

    it('should handle toggle shortcut', () => {
      const keydownListener = mockEventManager.addEventListener.mock.calls
        .find(call => call[1] === 'keydown')[2];
      
      const event = new KeyboardEvent('keydown', {
        key: 'S',
        ctrlKey: true,
        shiftKey: true
      });
      
      keydownListener(event);
      
      // Should toggle sidebar
      expect(mockSidebarContainer.show).toHaveBeenCalled();
    });

    it('should handle search shortcut when expanded', () => {
      sidebarManager.expand();
      
      const keydownListener = mockEventManager.addEventListener.mock.calls
        .find(call => call[1] === 'keydown')[2];
      
      const event = new KeyboardEvent('keydown', {
        key: 'f',
        ctrlKey: true
      });
      
      keydownListener(event);
      
      expect(mockSidebarContainer.focusSearch).toHaveBeenCalled();
    });

    it('should handle escape key when expanded and not pinned', () => {
      sidebarManager.expand();
      
      const keydownListener = mockEventManager.addEventListener.mock.calls
        .find(call => call[1] === 'keydown')[2];
      
      const event = new KeyboardEvent('keydown', {
        key: 'Escape'
      });
      
      keydownListener(event);
      
      expect(mockSidebarContainer.collapse).toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should destroy properly', async () => {
      await sidebarManager.initialize();
      
      sidebarManager.destroy();
      
      expect(mockSidebarContainer.destroy).toHaveBeenCalled();
      expect(mockStyleManager.removeStyles).toHaveBeenCalled();
      expect(mockEventManager.removeAllListeners).toHaveBeenCalled();
      expect(mockAnimationManager.destroy).toHaveBeenCalled();
      expect(sidebarManager.isInitialized()).toBe(false);
    });

    it('should restore page layout when pinned', async () => {
      await sidebarManager.initialize();
      sidebarManager.pin();
      
      sidebarManager.destroy();
      
      expect(mockPageAdapter.restorePageLayout).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle initialization errors', async () => {
      mockStyleManager.injectStyles.mockImplementation(() => {
        throw new Error('Style injection failed');
      });
      
      await expect(sidebarManager.initialize()).rejects.toThrow('Style injection failed');
    });

    it('should handle operation errors gracefully', async () => {
      await sidebarManager.initialize();
      
      mockSidebarContainer.show.mockImplementation(() => {
        throw new Error('Show failed');
      });
      
      expect(() => sidebarManager.show()).not.toThrow();
    });
  });
});