/**
 * Search Interaction Integration Tests - 搜索交互集成测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SearchManager } from '../../src/content/sidebar/components/SearchManager.js';
import { SearchBox } from '../../src/content/sidebar/components/SearchBox.js';
import { SearchFilter } from '../../src/content/sidebar/components/SearchFilter.js';
import { SearchResults } from '../../src/content/sidebar/components/SearchResults.js';
import { SearchSuggestions } from '../../src/content/sidebar/components/SearchSuggestions.js';
import { SearchKeyboardHandler } from '../../src/content/utils/SearchKeyboardHandler.js';
import { BookmarkNode, TabInfo } from '../../src/types/index.js';

// Mock Chrome APIs
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
};

// @ts-ignore
global.chrome = mockChrome;

describe('Search Interaction Integration', () => {
  let container: HTMLElement;
  let mockBookmarks: BookmarkNode[];
  let mockTabs: TabInfo[];

  beforeEach(() => {
    // 创建测试容器
    container = document.createElement('div');
    document.body.appendChild(container);

    // 创建模拟数据
    mockBookmarks = [
      {
        id: '1',
        title: 'Google Search',
        url: 'https://www.google.com',
        parentId: '0',
        index: 0,
        dateAdded: Date.now()
      },
      {
        id: '2',
        title: 'GitHub Repository',
        url: 'https://github.com/user/repo',
        parentId: '0',
        index: 1,
        dateAdded: Date.now()
      },
      {
        id: '3',
        title: 'Development Tools',
        parentId: '0',
        index: 2,
        dateAdded: Date.now(),
        children: [
          {
            id: '4',
            title: 'Visual Studio Code',
            url: 'https://code.visualstudio.com',
            parentId: '3',
            index: 0,
            dateAdded: Date.now()
          }
        ]
      }
    ];

    mockTabs = [
      {
        id: 1,
        title: 'Google Chrome',
        url: 'https://www.google.com/chrome',
        active: true,
        pinned: false,
        windowId: 1,
        index: 0,
        favIconUrl: 'https://www.google.com/favicon.ico'
      },
      {
        id: 2,
        title: 'Stack Overflow',
        url: 'https://stackoverflow.com',
        active: false,
        pinned: false,
        windowId: 1,
        index: 1,
        favIconUrl: 'https://stackoverflow.com/favicon.ico'
      }
    ];

    // 重置 mock
    vi.clearAllMocks();
  });

  afterEach(() => {
    // 清理DOM
    document.body.removeChild(container);
  });

  describe('SearchManager Integration', () => {
    let searchManager: SearchManager;

    beforeEach(() => {
      searchManager = new SearchManager({
        showFilters: true,
        showStats: true,
        groupResults: true
      });
      container.appendChild(searchManager.element);
      
      searchManager.setBookmarks(mockBookmarks);
      searchManager.setTabs(mockTabs);
    });

    afterEach(() => {
      searchManager.destroy();
    });

    it('should perform complete search workflow', async () => {
      const searchCallback = vi.fn();
      const itemSelectCallback = vi.fn();
      
      searchManager.onSearch(searchCallback);
      searchManager.onItemSelect(itemSelectCallback);

      // 执行搜索
      searchManager.search('Google');

      // 等待搜索完成
      await new Promise(resolve => setTimeout(resolve, 50));

      // 验证搜索回调被调用
      expect(searchCallback).toHaveBeenCalled();
      
      // 验证搜索结果
      const results = searchManager.getCurrentResults();
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].item.title).toContain('Google');

      // 模拟点击搜索结果
      const resultElements = container.querySelectorAll('.search-result-item');
      expect(resultElements.length).toBeGreaterThan(0);
      
      (resultElements[0] as HTMLElement).click();
      
      // 验证项目选择回调被调用
      expect(itemSelectCallback).toHaveBeenCalled();
    });

    it('should handle filter interactions', async () => {
      // 执行搜索
      searchManager.search('a');
      await new Promise(resolve => setTimeout(resolve, 50));

      // 获取过滤器元素
      const filterElements = container.querySelectorAll('.search-filter-item');
      expect(filterElements.length).toBeGreaterThan(0);

      // 点击收藏夹过滤器
      const bookmarkFilter = Array.from(filterElements).find(el => 
        el.textContent?.includes('收藏夹')
      ) as HTMLElement;
      
      if (bookmarkFilter) {
        bookmarkFilter.click();
        
        // 验证过滤器被激活
        expect(bookmarkFilter.style.backgroundColor).toBe('rgb(9, 105, 218)');
        
        // 验证结果被过滤
        const visibleResults = container.querySelectorAll('.search-result-item:not([style*="display: none"])');
        expect(visibleResults.length).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle keyboard navigation', async () => {
      // 执行搜索
      searchManager.search('Google');
      await new Promise(resolve => setTimeout(resolve, 50));

      // 聚焦搜索框
      searchManager.focus();

      // 模拟键盘导航
      const searchInput = container.querySelector('.search-input') as HTMLInputElement;
      expect(searchInput).toBeTruthy();

      // 模拟按下向下箭头键
      const downEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      searchInput.dispatchEvent(downEvent);

      // 验证焦点移动到结果列表
      const focusedElement = document.activeElement;
      expect(focusedElement?.classList.contains('search-result-item')).toBe(true);
    });

    it('should clear search properly', () => {
      // 执行搜索
      searchManager.search('Google');
      expect(searchManager.getCurrentQuery()).toBe('Google');

      // 清除搜索
      searchManager.clear();
      expect(searchManager.getCurrentQuery()).toBe('');
      expect(searchManager.getCurrentResults()).toHaveLength(0);

      // 验证UI被清除
      const resultElements = container.querySelectorAll('.search-result-item');
      expect(resultElements.length).toBe(0);
    });

    it('should handle empty search results', async () => {
      const emptyResultsCallback = vi.fn();
      searchManager.onEmptyResults(emptyResultsCallback);

      // 搜索不存在的内容
      searchManager.search('xyz123nonexistent');
      await new Promise(resolve => setTimeout(resolve, 50));

      // 验证空结果回调被调用
      expect(emptyResultsCallback).toHaveBeenCalledWith('xyz123nonexistent');

      // 验证显示空状态
      const emptyState = container.querySelector('.search-results-empty');
      expect(emptyState).toBeTruthy();
      expect(emptyState?.textContent).toContain('未找到相关结果');
    });
  });

  describe('SearchBox Component', () => {
    let searchBox: SearchBox;

    beforeEach(() => {
      searchBox = new SearchBox({
        placeholder: '测试搜索框',
        showSearchHistory: true
      });
      container.appendChild(searchBox.element);
    });

    afterEach(() => {
      searchBox.destroy();
    });

    it('should handle input and debouncing', async () => {
      const searchCallback = vi.fn();
      searchBox.onSearch(searchCallback);

      const input = container.querySelector('.search-input') as HTMLInputElement;
      
      // 模拟输入
      input.value = 'test';
      input.dispatchEvent(new Event('input'));

      // 验证搜索回调还没被调用（防抖）
      expect(searchCallback).not.toHaveBeenCalled();

      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350));

      // 验证搜索回调被调用
      expect(searchCallback).toHaveBeenCalledWith('test');
    });

    it('should show and hide search history', () => {
      // 添加搜索历史
      searchBox.addToHistory('test search');
      searchBox.addToHistory('another search');

      const input = container.querySelector('.search-input') as HTMLInputElement;
      
      // 聚焦输入框
      input.focus();

      // 验证历史记录显示
      const historyDropdown = container.querySelector('.search-history-dropdown');
      expect(historyDropdown?.style.display).toBe('block');

      // 验证历史记录项
      const historyItems = container.querySelectorAll('.search-history-item');
      expect(historyItems.length).toBe(2);

      // 失焦输入框
      input.blur();
      
      // 等待失焦处理
      setTimeout(() => {
        expect(historyDropdown?.style.display).toBe('none');
      }, 200);
    });

    it('should handle clear button', () => {
      const clearCallback = vi.fn();
      searchBox.onClear(clearCallback);

      // 设置搜索值
      searchBox.setValue('test');
      
      // 验证清除按钮显示
      const clearButton = container.querySelector('.search-clear') as HTMLElement;
      expect(clearButton.style.display).toBe('block');

      // 点击清除按钮
      clearButton.click();

      // 验证清除回调被调用
      expect(clearCallback).toHaveBeenCalled();
      expect(searchBox.getValue()).toBe('');
    });
  });

  describe('SearchSuggestions Component', () => {
    let searchSuggestions: SearchSuggestions;

    beforeEach(() => {
      searchSuggestions = new SearchSuggestions({
        maxSuggestions: 5,
        showIcons: true
      });
      container.appendChild(searchSuggestions.element);
    });

    afterEach(() => {
      searchSuggestions.destroy();
    });

    it('should display and interact with suggestions', () => {
      const selectCallback = vi.fn();
      searchSuggestions.onSelect(selectCallback);

      // 设置建议
      const suggestions = [
        { text: 'Google', type: 'suggestion' as const },
        { text: 'GitHub', type: 'history' as const },
        { text: 'Gmail', type: 'completion' as const }
      ];
      
      searchSuggestions.setSuggestions(suggestions);
      searchSuggestions.show();

      // 验证建议显示
      expect(searchSuggestions.isVisible()).toBe(true);
      const suggestionItems = container.querySelectorAll('.search-suggestion-item');
      expect(suggestionItems.length).toBe(3);

      // 点击第一个建议
      (suggestionItems[0] as HTMLElement).click();

      // 验证选择回调被调用
      expect(selectCallback).toHaveBeenCalledWith(suggestions[0]);
    });

    it('should handle keyboard navigation', () => {
      const suggestions = [
        { text: 'Google', type: 'suggestion' as const },
        { text: 'GitHub', type: 'history' as const }
      ];
      
      searchSuggestions.setSuggestions(suggestions);
      searchSuggestions.show();

      // 选择下一个
      searchSuggestions.selectNext();
      expect(searchSuggestions.getSelectedSuggestion()).toEqual(suggestions[0]);

      // 再选择下一个
      searchSuggestions.selectNext();
      expect(searchSuggestions.getSelectedSuggestion()).toEqual(suggestions[1]);

      // 选择上一个
      searchSuggestions.selectPrevious();
      expect(searchSuggestions.getSelectedSuggestion()).toEqual(suggestions[0]);
    });
  });

  describe('SearchKeyboardHandler', () => {
    let keyboardHandler: SearchKeyboardHandler;

    beforeEach(() => {
      keyboardHandler = new SearchKeyboardHandler({
        enableGlobalShortcuts: true
      });
    });

    afterEach(() => {
      keyboardHandler.destroy();
    });

    it('should register and execute shortcuts', () => {
      const actionCallback = vi.fn();
      keyboardHandler.onAction('test.action', actionCallback);

      // 注册快捷键
      keyboardHandler.registerShortcut({
        key: 't',
        ctrlKey: true,
        action: 'test.action',
        description: 'Test action'
      });

      // 模拟快捷键事件
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      const handled = keyboardHandler.handleSearchBoxKeydown(event);
      expect(handled).toBe(true);
      expect(actionCallback).toHaveBeenCalled();
    });

    it('should provide shortcut help', () => {
      const shortcuts = keyboardHandler.getAllShortcuts();
      expect(shortcuts.length).toBeGreaterThan(0);

      const help = keyboardHandler.getShortcutHelp();
      expect(help.length).toBeGreaterThan(0);
      expect(help[0]).toHaveProperty('category');
      expect(help[0]).toHaveProperty('shortcuts');
    });

    it('should format shortcuts correctly', () => {
      const shortcut = {
        key: 'f',
        ctrlKey: true,
        action: 'test',
        description: 'Test'
      };

      const formatted = SearchKeyboardHandler.formatShortcut(shortcut);
      expect(formatted).toContain('Ctrl');
      expect(formatted).toContain('F');
    });
  });

  describe('End-to-End Search Flow', () => {
    let searchManager: SearchManager;
    let keyboardHandler: SearchKeyboardHandler;

    beforeEach(() => {
      searchManager = new SearchManager();
      keyboardHandler = new SearchKeyboardHandler();
      
      container.appendChild(searchManager.element);
      
      searchManager.setBookmarks(mockBookmarks);
      searchManager.setTabs(mockTabs);

      // 设置键盘处理器回调
      keyboardHandler.onAction('search.focus', () => {
        searchManager.focus();
      });

      keyboardHandler.onAction('search.clear', () => {
        searchManager.clear();
      });
    });

    afterEach(() => {
      searchManager.destroy();
      keyboardHandler.destroy();
    });

    it('should complete full search interaction flow', async () => {
      // 1. 使用快捷键聚焦搜索框
      const focusEvent = new KeyboardEvent('keydown', {
        key: 'f',
        ctrlKey: true
      });
      
      keyboardHandler.handleSearchBoxKeydown(focusEvent);
      
      // 验证搜索框获得焦点
      const searchInput = container.querySelector('.search-input') as HTMLInputElement;
      expect(document.activeElement).toBe(searchInput);

      // 2. 输入搜索查询
      searchInput.value = 'Google';
      searchInput.dispatchEvent(new Event('input'));

      // 等待搜索完成
      await new Promise(resolve => setTimeout(resolve, 350));

      // 3. 验证搜索结果显示
      const results = searchManager.getCurrentResults();
      expect(results.length).toBeGreaterThan(0);

      const resultElements = container.querySelectorAll('.search-result-item');
      expect(resultElements.length).toBeGreaterThan(0);

      // 4. 使用键盘导航到结果
      const downEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      keyboardHandler.handleSearchResultsKeydown(downEvent);

      // 5. 选择结果
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      keyboardHandler.handleSearchResultsKeydown(enterEvent);

      // 验证Chrome API被调用
      expect(mockChrome.runtime.sendMessage).toHaveBeenCalled();

      // 6. 清除搜索
      const clearEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      keyboardHandler.handleSearchBoxKeydown(clearEvent);

      expect(searchManager.getCurrentQuery()).toBe('');
    });

    it('should handle filter interactions with keyboard', async () => {
      // 执行搜索
      searchManager.search('test');
      await new Promise(resolve => setTimeout(resolve, 50));

      // 使用快捷键激活收藏夹过滤器
      keyboardHandler.onAction('filter.bookmarks', () => {
        const filterElement = container.querySelector('[data-filter-id="bookmarks"]') as HTMLElement;
        if (filterElement) {
          filterElement.click();
        }
      });

      const filterEvent = new KeyboardEvent('keydown', {
        key: '1',
        altKey: true
      });

      keyboardHandler.handleSearchBoxKeydown(filterEvent);

      // 验证过滤器被激活
      const bookmarkFilter = container.querySelector('[data-filter-id="bookmarks"]') as HTMLElement;
      expect(bookmarkFilter?.style.backgroundColor).toBe('rgb(9, 105, 218)');
    });

    it('should export search results', async () => {
      // 执行搜索
      searchManager.search('Google');
      await new Promise(resolve => setTimeout(resolve, 50));

      // 导出JSON格式
      const jsonResults = searchManager.exportResults('json');
      const parsedResults = JSON.parse(jsonResults);
      expect(Array.isArray(parsedResults)).toBe(true);
      expect(parsedResults.length).toBeGreaterThan(0);
      expect(parsedResults[0]).toHaveProperty('title');
      expect(parsedResults[0]).toHaveProperty('url');
      expect(parsedResults[0]).toHaveProperty('type');
      expect(parsedResults[0]).toHaveProperty('score');

      // 导出CSV格式
      const csvResults = searchManager.exportResults('csv');
      expect(csvResults).toContain('Title,URL,Type,Score');
      expect(csvResults.split('\n').length).toBeGreaterThan(1);
    });

    it('should provide performance metrics', async () => {
      // 执行搜索
      searchManager.search('Google');
      await new Promise(resolve => setTimeout(resolve, 50));

      const metrics = searchManager.getPerformanceMetrics();
      expect(metrics).toHaveProperty('totalItems');
      expect(metrics).toHaveProperty('searchTime');
      expect(metrics).toHaveProperty('resultsCount');
      expect(metrics).toHaveProperty('averageScore');
      
      expect(metrics.totalItems).toBe(mockBookmarks.length + mockTabs.length);
      expect(metrics.resultsCount).toBeGreaterThanOrEqual(0);
      expect(metrics.averageScore).toBeGreaterThanOrEqual(0);
    });
  });
});