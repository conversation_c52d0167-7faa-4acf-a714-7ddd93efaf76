/**
 * Sidebar Navigation Integration Tests - 侧边栏导航集成测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SidebarNavigationManager, NavigableElement } from '../../src/content/utils/SidebarNavigationManager.js';
import { FocusManager, FocusTrap } from '../../src/content/utils/FocusManager.js';
import { AccessibilityHelper } from '../../src/content/utils/AccessibilityHelper.js';

describe('Sidebar Navigation Integration', () => {
  let container: HTMLElement;
  let navigationManager: SidebarNavigationManager;
  let focusManager: FocusManager;
  let accessibilityHelper: AccessibilityHelper;

  beforeEach(() => {
    // 创建测试容器
    container = document.createElement('div');
    container.className = 'sidebar-container';
    document.body.appendChild(container);

    // 创建管理器实例
    navigationManager = new SidebarNavigationManager({
      enableArrowKeys: true,
      enableTabNavigation: true,
      enableHomeEndKeys: true,
      enablePageUpDown: true,
      enableTypeAhead: true,
      wrapAround: true
    });

    focusManager = new FocusManager();
    accessibilityHelper = new AccessibilityHelper({
      enableAriaLive: true,
      enableFocusAnnouncements: true
    });
  });

  afterEach(() => {
    // 清理
    navigationManager.destroy();
    focusManager.clearFocusStack();
    accessibilityHelper.destroy();
    document.body.removeChild(container);
  });

  describe('SidebarNavigationManager', () => {
    let testElements: HTMLElement[];

    beforeEach(() => {
      // 创建测试元素
      testElements = [];
      
      for (let i = 0; i < 5; i++) {
        const element = document.createElement('div');
        element.id = `test-element-${i}`;
        element.textContent = `Test Element ${i}`;
        element.setAttribute('tabindex', '-1');
        container.appendChild(element);
        testElements.push(element);

        // 注册为可导航元素
        const navigableElement: NavigableElement = {
          element,
          id: element.id,
          type: i === 0 ? 'search' : i < 3 ? 'bookmark' : 'folder',
          level: 0,
          focusable: true,
          visible: true
        };

        navigationManager.registerElement(navigableElement);
      }
    });

    it('should register and manage navigable elements', () => {
      const allElements = navigationManager.getAllElements();
      expect(allElements).toHaveLength(5);

      const focusableElements = navigationManager.getFocusableElements();
      expect(focusableElements).toHaveLength(5);
    });

    it('should set initial focus to first element', () => {
      const focusedElement = navigationManager.getFocusedElement();
      expect(focusedElement).toBeTruthy();
      expect(focusedElement?.id).toBe('test-element-0');
      expect(document.activeElement).toBe(testElements[0]);
    });

    it('should navigate with arrow keys', () => {
      // 初始焦点在第一个元素
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-0');

      // 向下移动
      navigationManager.moveNext();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-1');
      expect(document.activeElement).toBe(testElements[1]);

      // 向上移动
      navigationManager.movePrevious();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-0');
      expect(document.activeElement).toBe(testElements[0]);
    });

    it('should handle Home and End keys', () => {
      // 移动到中间元素
      navigationManager.setFocus('test-element-2');
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-2');

      // Home键移动到第一个元素
      navigationManager.focusFirstElement();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-0');

      // End键移动到最后一个元素
      navigationManager.focusLastElement();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-4');
    });

    it('should handle wrap around navigation', () => {
      // 移动到最后一个元素
      navigationManager.focusLastElement();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-4');

      // 继续向下应该回到第一个元素
      navigationManager.moveNext();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-0');

      // 向上应该回到最后一个元素
      navigationManager.movePrevious();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-4');
    });

    it('should handle type-ahead search', () => {
      // 搜索以"Test Element 3"开头的元素
      navigationManager.typeAheadSearch('t');
      navigationManager.typeAheadSearch('e');
      navigationManager.typeAheadSearch('s');
      navigationManager.typeAheadSearch('t');
      navigationManager.typeAheadSearch(' ');
      navigationManager.typeAheadSearch('e');
      navigationManager.typeAheadSearch('l');
      navigationManager.typeAheadSearch('e');
      navigationManager.typeAheadSearch('m');
      navigationManager.typeAheadSearch('e');
      navigationManager.typeAheadSearch('n');
      navigationManager.typeAheadSearch('t');
      navigationManager.typeAheadSearch(' ');
      navigationManager.typeAheadSearch('3');

      // 应该找到第4个元素（索引3）
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-3');
    });

    it('should handle page up and page down', () => {
      // 移动到第一个元素
      navigationManager.focusFirstElement();
      
      // Page Down
      navigationManager.pageDown();
      const afterPageDown = navigationManager.getFocusedElement();
      expect(afterPageDown?.id).not.toBe('test-element-0');

      // Page Up
      navigationManager.pageUp();
      const afterPageUp = navigationManager.getFocusedElement();
      expect(afterPageUp?.id).toBe('test-element-0');
    });

    it('should update element properties', () => {
      // 禁用第二个元素
      navigationManager.updateElement('test-element-1', {
        focusable: false,
        visible: false
      });

      // 从第一个元素向下移动应该跳过第二个元素
      navigationManager.setFocus('test-element-0');
      navigationManager.moveNext();
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-2');
    });

    it('should handle element removal', () => {
      // 移动焦点到第二个元素
      navigationManager.setFocus('test-element-1');
      expect(navigationManager.getFocusedElement()?.id).toBe('test-element-1');

      // 移除第二个元素
      navigationManager.unregisterElement('test-element-1');

      // 焦点应该移动到下一个可用元素
      const focusedAfterRemoval = navigationManager.getFocusedElement();
      expect(focusedAfterRemoval?.id).not.toBe('test-element-1');
      expect(focusedAfterRemoval).toBeTruthy();
    });

    it('should handle keyboard events', () => {
      const navigationCallback = vi.fn();
      navigationManager.onNavigation(navigationCallback);

      // 模拟键盘事件
      const downEvent = new KeyboardEvent('keydown', {
        key: 'ArrowDown',
        bubbles: true
      });

      testElements[0].dispatchEvent(downEvent);

      // 验证导航回调被调用
      expect(navigationCallback).toHaveBeenCalled();
    });
  });

  describe('FocusManager', () => {
    let focusableElements: HTMLElement[];

    beforeEach(() => {
      // 创建可聚焦元素
      focusableElements = [];
      
      ['button', 'input', 'a'].forEach((tagName, index) => {
        const element = document.createElement(tagName);
        element.id = `focusable-${index}`;
        
        if (tagName === 'button') {
          element.textContent = `Button ${index}`;
        } else if (tagName === 'input') {
          (element as HTMLInputElement).type = 'text';
          (element as HTMLInputElement).placeholder = `Input ${index}`;
        } else if (tagName === 'a') {
          (element as HTMLAnchorElement).href = '#';
          element.textContent = `Link ${index}`;
        }
        
        container.appendChild(element);
        focusableElements.push(element);
      });
    });

    it('should identify focusable elements', () => {
      const focusable = focusManager.getFocusableElements(container);
      expect(focusable).toHaveLength(3);
      expect(focusable.every(el => focusManager.isElementFocusable(el))).toBe(true);
    });

    it('should get first and last focusable elements', () => {
      const first = focusManager.getFirstFocusableElement(container);
      const last = focusManager.getLastFocusableElement(container);

      expect(first).toBe(focusableElements[0]);
      expect(last).toBe(focusableElements[2]);
    });

    it('should find next and previous focusable elements', () => {
      const current = focusableElements[1];
      
      const next = focusManager.findNextFocusableElement(current, container);
      const previous = focusManager.findPreviousFocusableElement(current, container);

      expect(next).toBe(focusableElements[2]);
      expect(previous).toBe(focusableElements[0]);
    });

    it('should save and restore focus', () => {
      // 设置焦点到第二个元素
      focusableElements[1].focus();
      expect(document.activeElement).toBe(focusableElements[1]);

      // 保存焦点
      focusManager.saveFocus();

      // 改变焦点
      focusableElements[2].focus();
      expect(document.activeElement).toBe(focusableElements[2]);

      // 恢复焦点
      const restored = focusManager.restoreFocus();
      expect(restored).toBe(true);
      expect(document.activeElement).toBe(focusableElements[1]);
    });

    it('should set initial focus', () => {
      const success = focusManager.setInitialFocus(container, focusableElements[1]);
      expect(success).toBe(true);
      expect(document.activeElement).toBe(focusableElements[1]);
    });
  });

  describe('FocusTrap', () => {
    let focusTrap: FocusTrap;
    let trapContainer: HTMLElement;

    beforeEach(() => {
      // 创建焦点陷阱容器
      trapContainer = document.createElement('div');
      trapContainer.className = 'focus-trap-container';
      
      // 添加可聚焦元素
      const button1 = document.createElement('button');
      button1.textContent = 'Button 1';
      trapContainer.appendChild(button1);

      const input = document.createElement('input');
      input.type = 'text';
      input.placeholder = 'Input';
      trapContainer.appendChild(input);

      const button2 = document.createElement('button');
      button2.textContent = 'Button 2';
      trapContainer.appendChild(button2);

      container.appendChild(trapContainer);

      focusTrap = focusManager.createFocusTrap(trapContainer, {
        initialFocus: button1,
        escapeDeactivates: true
      });
    });

    afterEach(() => {
      if (focusTrap.isActive()) {
        focusTrap.deactivate();
      }
    });

    it('should activate and deactivate focus trap', () => {
      expect(focusTrap.isActive()).toBe(false);

      focusTrap.activate();
      expect(focusTrap.isActive()).toBe(true);

      focusTrap.deactivate();
      expect(focusTrap.isActive()).toBe(false);
    });

    it('should set initial focus when activated', () => {
      const button1 = trapContainer.querySelector('button') as HTMLElement;
      
      focusTrap.activate();
      expect(document.activeElement).toBe(button1);
    });

    it('should trap focus within container', () => {
      focusTrap.activate();

      const buttons = trapContainer.querySelectorAll('button');
      const lastButton = buttons[buttons.length - 1] as HTMLElement;

      // 移动焦点到最后一个按钮
      lastButton.focus();
      expect(document.activeElement).toBe(lastButton);

      // 模拟Tab键（应该回到第一个元素）
      const tabEvent = new KeyboardEvent('keydown', {
        key: 'Tab',
        bubbles: true
      });

      document.dispatchEvent(tabEvent);

      // 焦点应该回到第一个按钮
      setTimeout(() => {
        expect(document.activeElement).toBe(buttons[0]);
      }, 0);
    });

    it('should deactivate on Escape key', () => {
      focusTrap.activate();
      expect(focusTrap.isActive()).toBe(true);

      // 模拟Escape键
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        bubbles: true
      });

      document.dispatchEvent(escapeEvent);
      expect(focusTrap.isActive()).toBe(false);
    });
  });

  describe('AccessibilityHelper', () => {
    it('should announce messages', () => {
      const spy = vi.spyOn(accessibilityHelper, 'announce');
      
      accessibilityHelper.announce('Test message', 'polite');
      expect(spy).toHaveBeenCalledWith('Test message', 'polite');
    });

    it('should announce focus changes', () => {
      const spy = vi.spyOn(accessibilityHelper, 'announce');
      
      accessibilityHelper.announceFocus('bookmark', 'Test Bookmark', 1);
      expect(spy).toHaveBeenCalledWith('收藏夹 Test Bookmark, 层级 1', 'polite');
    });

    it('should announce navigation actions', () => {
      const spy = vi.spyOn(accessibilityHelper, 'announce');
      
      accessibilityHelper.announceNavigation('next', 'tab', 'Google Chrome');
      expect(spy).toHaveBeenCalledWith('移动到下一个 标签页 Google Chrome', 'polite');
    });

    it('should announce search results', () => {
      const spy = vi.spyOn(accessibilityHelper, 'announce');
      
      accessibilityHelper.announceSearchResults(5, 'google');
      expect(spy).toHaveBeenCalledWith('找到 5 个与"google"相关的结果', 'polite');

      accessibilityHelper.announceSearchResults(0, 'xyz');
      expect(spy).toHaveBeenCalledWith('没有找到与"xyz"相关的结果', 'polite');
    });

    it('should set ARIA attributes', () => {
      const element = document.createElement('div');
      
      accessibilityHelper.setAriaAttributes(element, {
        'label': 'Test Label',
        'expanded': 'false',
        'role': 'button'
      });

      expect(element.getAttribute('aria-label')).toBe('Test Label');
      expect(element.getAttribute('aria-expanded')).toBe('false');
      expect(element.getAttribute('role')).toBe('button');
    });

    it('should set element descriptions', () => {
      const element = document.createElement('button');
      element.textContent = 'Test Button';
      container.appendChild(element);

      accessibilityHelper.setElementDescription(element, 'This is a test button');

      const describedBy = element.getAttribute('aria-describedby');
      expect(describedBy).toBeTruthy();

      const descElement = document.getElementById(describedBy!);
      expect(descElement).toBeTruthy();
      expect(descElement?.textContent).toBe('This is a test button');
    });

    it('should create shortcut hints', () => {
      const element = document.createElement('button');
      container.appendChild(element);

      accessibilityHelper.createShortcutHint(element, ['Ctrl+S', 'Cmd+S']);

      const describedBy = element.getAttribute('aria-describedby');
      expect(describedBy).toBeTruthy();
    });

    it('should set loading and error states', () => {
      const element = document.createElement('div');

      // 设置加载状态
      accessibilityHelper.setLoadingState(element, true, '正在加载...');
      expect(element.getAttribute('aria-busy')).toBe('true');

      // 清除加载状态
      accessibilityHelper.setLoadingState(element, false, '加载完成');
      expect(element.hasAttribute('aria-busy')).toBe(false);

      // 设置错误状态
      accessibilityHelper.setErrorState(element, true, '加载失败');
      expect(element.getAttribute('aria-invalid')).toBe('true');

      // 清除错误状态
      accessibilityHelper.setErrorState(element, false);
      expect(element.hasAttribute('aria-invalid')).toBe(false);
    });

    it('should handle expanded state changes', () => {
      const element = document.createElement('button');
      element.textContent = 'Test Folder';
      container.appendChild(element);

      const spy = vi.spyOn(accessibilityHelper, 'announce');

      accessibilityHelper.setExpandedState(element, true);
      expect(element.getAttribute('aria-expanded')).toBe('true');
      expect(spy).toHaveBeenCalledWith('Test Folder 已展开', 'polite');

      accessibilityHelper.setExpandedState(element, false);
      expect(element.getAttribute('aria-expanded')).toBe('false');
      expect(spy).toHaveBeenCalledWith('Test Folder 已折叠', 'polite');
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete navigation workflow', () => {
      // 创建复杂的导航结构
      const searchBox = document.createElement('input');
      searchBox.id = 'search-box';
      searchBox.type = 'text';
      searchBox.placeholder = 'Search...';
      container.appendChild(searchBox);

      const bookmarkList = document.createElement('div');
      bookmarkList.id = 'bookmark-list';
      container.appendChild(bookmarkList);

      // 创建书签项目
      for (let i = 0; i < 3; i++) {
        const bookmark = document.createElement('div');
        bookmark.id = `bookmark-${i}`;
        bookmark.textContent = `Bookmark ${i}`;
        bookmark.setAttribute('tabindex', '-1');
        bookmarkList.appendChild(bookmark);
      }

      // 注册导航元素
      navigationManager.registerElement({
        element: searchBox,
        id: 'search-box',
        type: 'search',
        level: 0,
        focusable: true,
        visible: true
      });

      for (let i = 0; i < 3; i++) {
        const bookmark = document.getElementById(`bookmark-${i}`) as HTMLElement;
        navigationManager.registerElement({
          element: bookmark,
          id: `bookmark-${i}`,
          type: 'bookmark',
          level: 1,
          focusable: true,
          visible: true
        });
      }

      // 测试导航流程
      expect(navigationManager.getFocusedElement()?.id).toBe('search-box');

      navigationManager.moveNext();
      expect(navigationManager.getFocusedElement()?.id).toBe('bookmark-0');

      navigationManager.moveNext();
      expect(navigationManager.getFocusedElement()?.id).toBe('bookmark-1');

      navigationManager.focusFirstElement();
      expect(navigationManager.getFocusedElement()?.id).toBe('search-box');

      navigationManager.focusLastElement();
      expect(navigationManager.getFocusedElement()?.id).toBe('bookmark-2');
    });

    it('should integrate with accessibility announcements', () => {
      const announceSpy = vi.spyOn(accessibilityHelper, 'announce');
      const navigationCallback = vi.fn((elementId: string, action: string) => {
        const element = navigationManager.getAllElements().find(el => el.id === elementId);
        if (element) {
          accessibilityHelper.announceNavigation(action, element.type, element.element.textContent || '');
        }
      });

      navigationManager.onNavigation(navigationCallback);

      // 创建测试元素
      const element = document.createElement('div');
      element.textContent = 'Test Item';
      container.appendChild(element);

      navigationManager.registerElement({
        element,
        id: 'test-item',
        type: 'bookmark',
        level: 0,
        focusable: true,
        visible: true
      });

      // 触发导航
      navigationManager.moveNext();

      expect(navigationCallback).toHaveBeenCalled();
      expect(announceSpy).toHaveBeenCalled();
    });

    it('should handle focus trap with navigation', () => {
      // 创建模态对话框
      const modal = document.createElement('div');
      modal.className = 'modal';
      
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Close';
      modal.appendChild(closeButton);

      const input = document.createElement('input');
      input.type = 'text';
      modal.appendChild(input);

      const saveButton = document.createElement('button');
      saveButton.textContent = 'Save';
      modal.appendChild(saveButton);

      container.appendChild(modal);

      // 创建焦点陷阱
      const focusTrap = focusManager.createFocusTrap(modal, {
        initialFocus: closeButton,
        escapeDeactivates: true
      });

      focusTrap.activate();

      // 验证焦点被困在模态框内
      expect(document.activeElement).toBe(closeButton);

      // 模拟Tab导航
      saveButton.focus();
      expect(document.activeElement).toBe(saveButton);

      // 模拟Escape关闭
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        bubbles: true
      });

      document.dispatchEvent(escapeEvent);
      expect(focusTrap.isActive()).toBe(false);
    });
  });
});