/**
 * Jest测试环境设置
 */

// Mock Chrome APIs
const mockChrome = {
  runtime: {
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
    onInstalled: {
      addListener: jest.fn(),
    },
    onStartup: {
      addListener: jest.fn(),
    },
    sendMessage: jest.fn(),
    getManifest: jest.fn(() => ({ version: '1.0.0' })),
    lastError: null,
  },
  tabs: {
    query: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    create: jest.fn(),
    onUpdated: {
      addListener: jest.fn(),
    },
    onRemoved: {
      addListener: jest.fn(),
    },
  },
  bookmarks: {
    getTree: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    onCreated: {
      addListener: jest.fn(),
    },
    onChanged: {
      addListener: jest.fn(),
    },
    onRemoved: {
      addListener: jest.fn(),
    },
  },
  storage: {
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
    },
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
    },
    onChanged: {
      addListener: jest.fn(),
    },
  },
  commands: {
    onCommand: {
      addListener: jest.fn(),
    },
  },
};

// 设置全局Chrome对象
(global as any).chrome = mockChrome;

// Mock DOM APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
(global as any).ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
(global as any).IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 设置测试环境变量
process.env.NODE_ENV = 'test';

// 全局测试工具函数
(global as any).createMockTab = (overrides = {}) => ({
  id: 1,
  title: 'Test Tab',
  url: 'https://example.com',
  active: false,
  pinned: false,
  index: 0,
  windowId: 1,
  ...overrides,
});

(global as any).createMockBookmark = (overrides = {}) => ({
  id: '1',
  title: 'Test Bookmark',
  url: 'https://example.com',
  dateAdded: Date.now(),
  ...overrides,
});

// 清理函数
afterEach(() => {
  jest.clearAllMocks();
});