/**
 * Global Keyboard Manager Unit Tests - 全局键盘管理器单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GlobalKeyboardManager, GlobalShortcut } from '../../src/content/utils/GlobalKeyboardManager.js';

describe('GlobalKeyboardManager', () => {
  let keyboardManager: GlobalKeyboardManager;

  beforeEach(() => {
    keyboardManager = new GlobalKeyboardManager({
      enableGlobalShortcuts: false, // 禁用全局监听以避免测试干扰
      debugMode: false
    });
  });

  afterEach(() => {
    keyboardManager.destroy();
  });

  describe('Shortcut Registration', () => {
    it('should register a shortcut', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      expect(id).toBe('ctrl+t');

      const allShortcuts = keyboardManager.getAllShortcuts();
      const registeredShortcut = allShortcuts.find(s => s.id === id);
      expect(registeredShortcut).toBeDefined();
      expect(registeredShortcut?.key).toBe('t');
      expect(registeredShortcut?.ctrlKey).toBe(true);
    });

    it('should unregister a shortcut', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      expect(keyboardManager.getAllShortcuts()).toHaveLength(19); // 18 default + 1 new

      const removed = keyboardManager.unregisterShortcut(id);
      expect(removed).toBe(true);
      expect(keyboardManager.getAllShortcuts()).toHaveLength(18); // back to default
    });

    it('should update a shortcut', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      
      const updated = keyboardManager.updateShortcut(id, {
        description: 'Updated description',
        enabled: false
      });

      expect(updated).toBe(true);

      const updatedShortcut = keyboardManager.getAllShortcuts().find(s => s.id === id);
      expect(updatedShortcut?.description).toBe('Updated description');
      expect(updatedShortcut?.enabled).toBe(false);
    });

    it('should toggle shortcut enabled state', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      
      // Toggle to disabled
      keyboardManager.toggleShortcut(id, false);
      let toggledShortcut = keyboardManager.getAllShortcuts().find(s => s.id === id);
      expect(toggledShortcut?.enabled).toBe(false);

      // Toggle to enabled
      keyboardManager.toggleShortcut(id, true);
      toggledShortcut = keyboardManager.getAllShortcuts().find(s => s.id === id);
      expect(toggledShortcut?.enabled).toBe(true);

      // Toggle without parameter (should flip current state)
      keyboardManager.toggleShortcut(id);
      toggledShortcut = keyboardManager.getAllShortcuts().find(s => s.id === id);
      expect(toggledShortcut?.enabled).toBe(false);
    });
  });

  describe('Action Handlers', () => {
    it('should add and execute action handlers', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      
      keyboardManager.onAction(id, handler);

      // Simulate keyboard event
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      const handled = keyboardManager.handleKeyboardEvent(event);
      expect(handled).toBe(true);
      expect(handler).toHaveBeenCalled();
    });

    it('should remove action handlers', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      
      keyboardManager.onAction(id, handler);
      keyboardManager.offAction(id, handler);

      // Simulate keyboard event
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      const handled = keyboardManager.handleKeyboardEvent(event);
      expect(handled).toBe(true); // Still handled but no action executed
      expect(handler).not.toHaveBeenCalled();
    });

    it('should handle multiple action handlers', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler1 = vi.fn();
      const handler2 = vi.fn();
      
      keyboardManager.onAction(id, handler1);
      keyboardManager.onAction(id, handler2);

      // Simulate keyboard event
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      keyboardManager.handleKeyboardEvent(event);
      expect(handler1).toHaveBeenCalled();
      expect(handler2).toHaveBeenCalled();
    });
  });

  describe('Context Management', () => {
    it('should manage context stack', () => {
      expect(keyboardManager.getCurrentContext()).toBeUndefined();

      keyboardManager.pushContext('search');
      expect(keyboardManager.getCurrentContext()).toBe('search');

      keyboardManager.pushContext('navigation');
      expect(keyboardManager.getCurrentContext()).toBe('navigation');

      const popped = keyboardManager.popContext();
      expect(popped).toBe('navigation');
      expect(keyboardManager.getCurrentContext()).toBe('search');

      keyboardManager.clearContextStack();
      expect(keyboardManager.getCurrentContext()).toBeUndefined();
    });

    it('should prioritize context-relevant shortcuts', () => {
      // Register two shortcuts with same keys but different categories
      const searchShortcut: Omit<GlobalShortcut, 'id'> = {
        key: 'f',
        ctrlKey: true,
        description: 'Search shortcut',
        category: 'search',
        enabled: true,
        global: false
      };

      const navShortcut: Omit<GlobalShortcut, 'id'> = {
        key: 'f',
        ctrlKey: true,
        description: 'Navigation shortcut',
        category: 'navigation',
        enabled: true,
        global: false
      };

      const searchId = keyboardManager.registerShortcut(searchShortcut);
      const navId = keyboardManager.registerShortcut(navShortcut);

      const searchHandler = vi.fn();
      const navHandler = vi.fn();

      keyboardManager.onAction(searchId, searchHandler);
      keyboardManager.onAction(navId, navHandler);

      // Set search context
      keyboardManager.pushContext('search');

      // Simulate keyboard event
      const event = new KeyboardEvent('keydown', {
        key: 'f',
        ctrlKey: true
      });

      keyboardManager.handleKeyboardEvent(event);

      // Search handler should be called (context priority)
      expect(searchHandler).toHaveBeenCalled();
      expect(navHandler).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle keyboard events correctly', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 'Enter',
        description: 'Enter shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      keyboardManager.onAction(id, handler);

      // Test matching event
      const matchingEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      const handled = keyboardManager.handleKeyboardEvent(matchingEvent);
      expect(handled).toBe(true);
      expect(handler).toHaveBeenCalled();

      // Test non-matching event
      handler.mockClear();
      const nonMatchingEvent = new KeyboardEvent('keydown', { key: 'Escape' });
      const notHandled = keyboardManager.handleKeyboardEvent(nonMatchingEvent);
      expect(notHandled).toBe(false);
      expect(handler).not.toHaveBeenCalled();
    });

    it('should not handle disabled shortcuts', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: false, // Disabled
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      keyboardManager.onAction(id, handler);

      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      const handled = keyboardManager.handleKeyboardEvent(event);
      expect(handled).toBe(false);
      expect(handler).not.toHaveBeenCalled();
    });

    it('should handle complex modifier combinations', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 'k',
        ctrlKey: true,
        shiftKey: true,
        altKey: true,
        description: 'Complex shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      keyboardManager.onAction(id, handler);

      // Test exact match
      const exactEvent = new KeyboardEvent('keydown', {
        key: 'k',
        ctrlKey: true,
        shiftKey: true,
        altKey: true
      });

      const handled = keyboardManager.handleKeyboardEvent(exactEvent);
      expect(handled).toBe(true);
      expect(handler).toHaveBeenCalled();

      // Test partial match (should not trigger)
      handler.mockClear();
      const partialEvent = new KeyboardEvent('keydown', {
        key: 'k',
        ctrlKey: true,
        shiftKey: true
        // Missing altKey
      });

      const notHandled = keyboardManager.handleKeyboardEvent(partialEvent);
      expect(notHandled).toBe(false);
      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('Shortcut Queries', () => {
    beforeEach(() => {
      // Add some test shortcuts
      keyboardManager.registerShortcut({
        key: 't1',
        description: 'Test 1',
        category: 'test',
        enabled: true,
        global: false
      });

      keyboardManager.registerShortcut({
        key: 't2',
        description: 'Test 2',
        category: 'test',
        enabled: false,
        global: true
      });

      keyboardManager.registerShortcut({
        key: 't3',
        description: 'Test 3',
        category: 'other',
        enabled: true,
        global: false
      });
    });

    it('should get shortcuts by category', () => {
      const testShortcuts = keyboardManager.getShortcutsByCategory('test');
      expect(testShortcuts).toHaveLength(2);
      expect(testShortcuts.every(s => s.category === 'test')).toBe(true);
    });

    it('should get enabled shortcuts', () => {
      const enabledShortcuts = keyboardManager.getEnabledShortcuts();
      const enabledCount = enabledShortcuts.length;
      
      // Should include default shortcuts + enabled test shortcuts
      expect(enabledCount).toBeGreaterThan(0);
      expect(enabledShortcuts.every(s => s.enabled)).toBe(true);
    });

    it('should check for conflicts', () => {
      // Register conflicting shortcuts
      keyboardManager.registerShortcut({
        key: 'x',
        ctrlKey: true,
        description: 'Conflict 1',
        category: 'test',
        enabled: true,
        global: false
      });

      keyboardManager.registerShortcut({
        key: 'x',
        ctrlKey: true,
        description: 'Conflict 2',
        category: 'test',
        enabled: true,
        global: false
      });

      const conflicts = keyboardManager.checkConflicts();
      expect(conflicts.length).toBeGreaterThan(0);
      
      const hasConflict = conflicts.some(conflict => 
        conflict.shortcut1.key === 'x' && conflict.shortcut2.key === 'x'
      );
      expect(hasConflict).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    it('should export configuration', () => {
      const config = keyboardManager.exportConfig();
      expect(config).toBeTruthy();
      
      const parsed = JSON.parse(config);
      expect(parsed).toHaveProperty('shortcuts');
      expect(parsed).toHaveProperty('options');
      expect(Array.isArray(parsed.shortcuts)).toBe(true);
    });

    it('should import configuration', () => {
      const originalCount = keyboardManager.getAllShortcuts().length;
      
      // Create test config
      const testConfig = {
        shortcuts: [
          {
            id: 'test-import',
            key: 'i',
            ctrlKey: true,
            description: 'Imported shortcut',
            category: 'test',
            enabled: true,
            global: false
          }
        ],
        options: {
          enableGlobalShortcuts: true
        }
      };

      const success = keyboardManager.importConfig(JSON.stringify(testConfig));
      expect(success).toBe(true);

      const newCount = keyboardManager.getAllShortcuts().length;
      expect(newCount).toBeGreaterThan(originalCount);

      const importedShortcut = keyboardManager.getAllShortcuts().find(s => s.id === 'test-import');
      expect(importedShortcut).toBeDefined();
    });

    it('should handle invalid configuration import', () => {
      const invalidConfig = '{ invalid json }';
      const success = keyboardManager.importConfig(invalidConfig);
      expect(success).toBe(false);
    });
  });

  describe('Manager State', () => {
    it('should enable and disable manager', () => {
      expect(keyboardManager.isEnabled()).toBe(true);

      keyboardManager.disable();
      expect(keyboardManager.isEnabled()).toBe(false);

      // Disabled manager should not handle events
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      const id = keyboardManager.registerShortcut(shortcut);
      const handler = vi.fn();
      keyboardManager.onAction(id, handler);

      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true
      });

      const handled = keyboardManager.handleKeyboardEvent(event);
      expect(handled).toBe(false);
      expect(handler).not.toHaveBeenCalled();

      keyboardManager.enable();
      expect(keyboardManager.isEnabled()).toBe(true);
    });

    it('should destroy manager properly', () => {
      const shortcut: Omit<GlobalShortcut, 'id'> = {
        key: 't',
        ctrlKey: true,
        description: 'Test shortcut',
        category: 'test',
        enabled: true,
        global: false
      };

      keyboardManager.registerShortcut(shortcut);
      keyboardManager.pushContext('test');

      expect(keyboardManager.getAllShortcuts().length).toBeGreaterThan(0);
      expect(keyboardManager.getCurrentContext()).toBe('test');

      keyboardManager.destroy();

      expect(keyboardManager.getAllShortcuts()).toHaveLength(0);
      expect(keyboardManager.getCurrentContext()).toBeUndefined();
      expect(keyboardManager.isEnabled()).toBe(false);
    });
  });

  describe('Shortcut Formatting', () => {
    it('should format shortcuts correctly', () => {
      const shortcut: GlobalShortcut = {
        id: 'test',
        key: 'f',
        ctrlKey: true,
        shiftKey: true,
        description: 'Test',
        category: 'test',
        enabled: true,
        global: false
      };

      const formatted = GlobalKeyboardManager.formatShortcut(shortcut);
      expect(formatted).toContain('Ctrl');
      expect(formatted).toContain('Shift');
      expect(formatted).toContain('F');
    });

    it('should handle special keys', () => {
      const shortcuts: GlobalShortcut[] = [
        {
          id: 'enter',
          key: 'Enter',
          description: 'Enter',
          category: 'test',
          enabled: true,
          global: false
        },
        {
          id: 'escape',
          key: 'Escape',
          description: 'Escape',
          category: 'test',
          enabled: true,
          global: false
        },
        {
          id: 'space',
          key: ' ',
          description: 'Space',
          category: 'test',
          enabled: true,
          global: false
        }
      ];

      shortcuts.forEach(shortcut => {
        const formatted = GlobalKeyboardManager.formatShortcut(shortcut);
        expect(formatted).toBeTruthy();
        expect(formatted.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Default Shortcuts', () => {
    it('should have default shortcuts registered', () => {
      const allShortcuts = keyboardManager.getAllShortcuts();
      expect(allShortcuts.length).toBeGreaterThan(0);

      // Check for some expected default shortcuts
      const searchShortcut = allShortcuts.find(s => s.key === 'f' && s.ctrlKey && s.category === 'search');
      expect(searchShortcut).toBeDefined();

      const sidebarShortcut = allShortcuts.find(s => s.key === 'b' && s.ctrlKey && s.shiftKey && s.category === 'sidebar');
      expect(sidebarShortcut).toBeDefined();
    });

    it('should have shortcuts in different categories', () => {
      const allShortcuts = keyboardManager.getAllShortcuts();
      const categories = new Set(allShortcuts.map(s => s.category));
      
      expect(categories.has('search')).toBe(true);
      expect(categories.has('sidebar')).toBe(true);
      expect(categories.has('navigation')).toBe(true);
      expect(categories.has('tabs')).toBe(true);
      expect(categories.has('bookmarks')).toBe(true);
      expect(categories.has('general')).toBe(true);
    });
  });
});