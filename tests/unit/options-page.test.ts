/**
 * Options Page Unit Tests - 设置页面单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock Chrome APIs
const mockChrome = {
  storage: {
    sync: {
      get: vi.fn(),
      set: vi.fn(),
      clear: vi.fn()
    }
  },
  tabs: {
    create: vi.fn()
  }
};

// @ts-ignore
global.chrome = mockChrome;

describe('Options Page', () => {
  let container: HTMLElement;
  let optionsManager: any;

  beforeEach(() => {
    // 创建测试容器
    container = document.createElement('div');
    container.innerHTML = `
      <div class="options-container">
        <nav class="options-nav">
          <ul class="nav-list">
            <li class="nav-item">
              <button class="nav-button active" data-section="general">常规设置</button>
            </li>
            <li class="nav-item">
              <button class="nav-button" data-section="appearance">外观设置</button>
            </li>
          </ul>
        </nav>
        <div class="options-content">
          <section id="general-section" class="settings-section active">
            <div class="settings-grid"></div>
          </section>
          <section id="appearance-section" class="settings-section">
            <div class="settings-grid"></div>
          </section>
        </div>
        <div id="status-text">设置已加载</div>
        <button id="save-settings">保存设置</button>
        <button id="reset-settings">重置设置</button>
        <button id="export-settings">导出设置</button>
        <button id="import-settings">导入设置</button>
        <input type="file" id="import-file-input" style="display: none;">
        <div id="modal-overlay" style="display: none;">
          <div class="modal-dialog">
            <div class="modal-header">
              <h3 id="modal-title">确认操作</h3>
              <button id="modal-close">×</button>
            </div>
            <div class="modal-body">
              <p id="modal-message">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
              <button id="modal-cancel">取消</button>
              <button id="modal-confirm">确认</button>
            </div>
          </div>
        </div>
        <div id="notification" style="display: none;">
          <div class="notification-content">
            <div id="notification-icon"></div>
            <span id="notification-message">操作成功</span>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(container);

    // 重置 mock
    vi.clearAllMocks();
    mockChrome.storage.sync.get.mockResolvedValue({});
    mockChrome.storage.sync.set.mockResolvedValue(undefined);
    mockChrome.storage.sync.clear.mockResolvedValue(undefined);
  });

  afterEach(() => {
    document.body.removeChild(container);
    if (optionsManager) {
      optionsManager = null;
    }
  });

  describe('OptionsManager Class', () => {
    beforeEach(async () => {
      // 动态导入OptionsManager类（模拟）
      const OptionsManager = class {
        constructor() {
          this.currentSection = 'general';
          this.settings = {};
          this.defaultSettings = this.getDefaultSettings();
          this.settingsDefinitions = this.getSettingsDefinitions();
        }

        getDefaultSettings() {
          return {
            enabled: true,
            autoShow: true,
            showOnHover: true,
            hoverDelay: 300,
            theme: 'auto',
            width: 300,
            opacity: 0.95
          };
        }

        getSettingsDefinitions() {
          return {
            general: [
              {
                key: 'enabled',
                type: 'switch',
                title: '启用侧边栏',
                description: '启用或禁用垂直侧边栏功能'
              },
              {
                key: 'hoverDelay',
                type: 'slider',
                title: '悬停延迟',
                description: '鼠标悬停多长时间后显示侧边栏（毫秒）',
                min: 0,
                max: 1000,
                step: 50,
                unit: 'ms'
              }
            ],
            appearance: [
              {
                key: 'theme',
                type: 'select',
                title: '主题',
                description: '选择侧边栏的外观主题',
                options: [
                  { value: 'auto', label: '跟随系统' },
                  { value: 'light', label: '浅色主题' },
                  { value: 'dark', label: '深色主题' }
                ]
              }
            ]
          };
        }

        async loadSettings() {
          const result = await chrome.storage.sync.get(null);
          this.settings = { ...this.defaultSettings, ...result };
        }

        async saveSettings() {
          await chrome.storage.sync.set(this.settings);
        }

        switchSection(section) {
          this.currentSection = section;
          
          // 更新导航状态
          document.querySelectorAll('.nav-button').forEach(button => {
            button.classList.remove('active');
          });
          document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

          // 更新内容区域
          document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.remove('active');
          });
          document.getElementById(`${section}-section`)?.classList.add('active');
        }

        createSettingElement(setting) {
          const element = document.createElement('div');
          element.className = 'setting-item';
          element.innerHTML = `
            <div class="setting-header">
              <div class="setting-info">
                <div class="setting-title">${setting.title}</div>
                <div class="setting-description">${setting.description}</div>
              </div>
              <div class="setting-control">
                ${this.createControlHTML(setting)}
              </div>
            </div>
          `;
          return element;
        }

        createControlHTML(setting) {
          switch (setting.type) {
            case 'switch':
              return `
                <label class="switch">
                  <input type="checkbox" ${this.settings[setting.key] ? 'checked' : ''}>
                  <span class="switch-slider"></span>
                </label>
              `;
            case 'select':
              const options = setting.options.map(opt => 
                `<option value="${opt.value}" ${this.settings[setting.key] === opt.value ? 'selected' : ''}>${opt.label}</option>`
              ).join('');
              return `
                <div class="select-control">
                  <select>${options}</select>
                </div>
              `;
            case 'slider':
              return `
                <div class="slider-control">
                  <div class="slider-wrapper">
                    <input type="range" class="slider" min="${setting.min}" max="${setting.max}" step="${setting.step}" value="${this.settings[setting.key]}">
                    <span class="slider-value">${this.settings[setting.key]}${setting.unit}</span>
                  </div>
                </div>
              `;
            default:
              return '';
          }
        }

        renderSection(section) {
          const container = document.querySelector(`#${section}-section .settings-grid`);
          if (!container) return;

          container.innerHTML = '';
          this.settingsDefinitions[section].forEach(setting => {
            const settingElement = this.createSettingElement(setting);
            container.appendChild(settingElement);
          });
        }

        showNotification(message, type = 'success') {
          const notification = document.getElementById('notification');
          const messageEl = document.getElementById('notification-message');
          
          if (notification && messageEl) {
            messageEl.textContent = message;
            notification.style.display = 'block';
            notification.classList.add('show');
          }
        }

        showConfirmDialog(title, message, onConfirm) {
          const modal = document.getElementById('modal-overlay');
          const titleEl = document.getElementById('modal-title');
          const messageEl = document.getElementById('modal-message');
          
          if (modal && titleEl && messageEl) {
            titleEl.textContent = title;
            messageEl.textContent = message;
            modal.style.display = 'flex';
          }
        }

        hideModal() {
          const modal = document.getElementById('modal-overlay');
          if (modal) {
            modal.style.display = 'none';
          }
        }
      };

      optionsManager = new OptionsManager();
      await optionsManager.loadSettings();
    });

    it('should initialize with default settings', () => {
      expect(optionsManager.settings).toEqual(expect.objectContaining({
        enabled: true,
        autoShow: true,
        showOnHover: true,
        hoverDelay: 300,
        theme: 'auto',
        width: 300,
        opacity: 0.95
      }));
    });

    it('should load settings from storage', async () => {
      const mockSettings = { enabled: false, theme: 'dark' };
      mockChrome.storage.sync.get.mockResolvedValue(mockSettings);

      await optionsManager.loadSettings();

      expect(optionsManager.settings.enabled).toBe(false);
      expect(optionsManager.settings.theme).toBe('dark');
      expect(optionsManager.settings.autoShow).toBe(true); // 默认值保留
    });

    it('should save settings to storage', async () => {
      optionsManager.settings.enabled = false;
      optionsManager.settings.theme = 'dark';

      await optionsManager.saveSettings();

      expect(mockChrome.storage.sync.set).toHaveBeenCalledWith(
        expect.objectContaining({
          enabled: false,
          theme: 'dark'
        })
      );
    });

    it('should switch between sections', () => {
      // 初始状态
      expect(optionsManager.currentSection).toBe('general');
      expect(document.querySelector('[data-section="general"]')?.classList.contains('active')).toBe(true);
      expect(document.getElementById('general-section')?.classList.contains('active')).toBe(true);

      // 切换到外观设置
      optionsManager.switchSection('appearance');

      expect(optionsManager.currentSection).toBe('appearance');
      expect(document.querySelector('[data-section="appearance"]')?.classList.contains('active')).toBe(true);
      expect(document.getElementById('appearance-section')?.classList.contains('active')).toBe(true);
      expect(document.querySelector('[data-section="general"]')?.classList.contains('active')).toBe(false);
      expect(document.getElementById('general-section')?.classList.contains('active')).toBe(false);
    });

    it('should create setting elements correctly', () => {
      const switchSetting = {
        key: 'enabled',
        type: 'switch',
        title: '启用侧边栏',
        description: '启用或禁用垂直侧边栏功能'
      };

      const element = optionsManager.createSettingElement(switchSetting);

      expect(element.className).toBe('setting-item');
      expect(element.querySelector('.setting-title')?.textContent).toBe('启用侧边栏');
      expect(element.querySelector('.setting-description')?.textContent).toBe('启用或禁用垂直侧边栏功能');
      expect(element.querySelector('.switch')).toBeTruthy();
      expect(element.querySelector('input[type="checkbox"]')).toBeTruthy();
    });

    it('should render sections with settings', () => {
      optionsManager.renderSection('general');

      const container = document.querySelector('#general-section .settings-grid');
      expect(container?.children.length).toBe(2); // 两个设置项

      const firstSetting = container?.children[0];
      expect(firstSetting?.querySelector('.setting-title')?.textContent).toBe('启用侧边栏');
    });

    it('should show notifications', () => {
      optionsManager.showNotification('测试消息', 'success');

      const notification = document.getElementById('notification');
      const message = document.getElementById('notification-message');

      expect(notification?.style.display).toBe('block');
      expect(notification?.classList.contains('show')).toBe(true);
      expect(message?.textContent).toBe('测试消息');
    });

    it('should show and hide modal dialogs', () => {
      const modal = document.getElementById('modal-overlay');
      
      // 显示模态框
      optionsManager.showConfirmDialog('测试标题', '测试消息', () => {});

      expect(modal?.style.display).toBe('flex');
      expect(document.getElementById('modal-title')?.textContent).toBe('测试标题');
      expect(document.getElementById('modal-message')?.textContent).toBe('测试消息');

      // 隐藏模态框
      optionsManager.hideModal();

      expect(modal?.style.display).toBe('none');
    });
  });

  describe('UI Interactions', () => {
    beforeEach(async () => {
      // 简化的OptionsManager用于UI测试
      optionsManager = {
        switchSection: vi.fn(),
        saveSettings: vi.fn(),
        showConfirmDialog: vi.fn(),
        exportSettings: vi.fn()
      };

      // 设置事件监听器
      document.querySelectorAll('.nav-button').forEach(button => {
        button.addEventListener('click', (e) => {
          const section = (e.currentTarget as HTMLElement).dataset.section;
          optionsManager.switchSection(section);
        });
      });

      document.getElementById('save-settings')?.addEventListener('click', () => {
        optionsManager.saveSettings();
      });

      document.getElementById('reset-settings')?.addEventListener('click', () => {
        optionsManager.showConfirmDialog('重置设置', '确定要重置吗？', () => {});
      });

      document.getElementById('export-settings')?.addEventListener('click', () => {
        optionsManager.exportSettings();
      });
    });

    it('should handle navigation button clicks', () => {
      const appearanceButton = document.querySelector('[data-section="appearance"]') as HTMLElement;
      
      appearanceButton.click();

      expect(optionsManager.switchSection).toHaveBeenCalledWith('appearance');
    });

    it('should handle save button click', () => {
      const saveButton = document.getElementById('save-settings') as HTMLElement;
      
      saveButton.click();

      expect(optionsManager.saveSettings).toHaveBeenCalled();
    });

    it('should handle reset button click', () => {
      const resetButton = document.getElementById('reset-settings') as HTMLElement;
      
      resetButton.click();

      expect(optionsManager.showConfirmDialog).toHaveBeenCalledWith(
        '重置设置',
        '确定要重置吗？',
        expect.any(Function)
      );
    });

    it('should handle export button click', () => {
      const exportButton = document.getElementById('export-settings') as HTMLElement;
      
      exportButton.click();

      expect(optionsManager.exportSettings).toHaveBeenCalled();
    });

    it('should handle modal close button', () => {
      const modal = document.getElementById('modal-overlay') as HTMLElement;
      const closeButton = document.getElementById('modal-close') as HTMLElement;

      // 显示模态框
      modal.style.display = 'flex';

      // 点击关闭按钮
      closeButton.addEventListener('click', () => {
        modal.style.display = 'none';
      });
      closeButton.click();

      expect(modal.style.display).toBe('none');
    });
  });

  describe('Settings Controls', () => {
    it('should create switch control correctly', () => {
      const switchHTML = `
        <label class="switch">
          <input type="checkbox" checked>
          <span class="switch-slider"></span>
        </label>
      `;

      container.innerHTML = switchHTML;
      const switchElement = container.querySelector('.switch');
      const input = switchElement?.querySelector('input[type="checkbox"]') as HTMLInputElement;

      expect(switchElement).toBeTruthy();
      expect(input?.checked).toBe(true);

      // 测试切换
      input.checked = false;
      input.dispatchEvent(new Event('change'));

      expect(input.checked).toBe(false);
    });

    it('should create select control correctly', () => {
      const selectHTML = `
        <div class="select-control">
          <select>
            <option value="auto">跟随系统</option>
            <option value="light" selected>浅色主题</option>
            <option value="dark">深色主题</option>
          </select>
        </div>
      `;

      container.innerHTML = selectHTML;
      const select = container.querySelector('select') as HTMLSelectElement;

      expect(select).toBeTruthy();
      expect(select.value).toBe('light');

      // 测试选择变化
      select.value = 'dark';
      select.dispatchEvent(new Event('change'));

      expect(select.value).toBe('dark');
    });

    it('should create slider control correctly', () => {
      const sliderHTML = `
        <div class="slider-control">
          <div class="slider-wrapper">
            <input type="range" class="slider" min="0" max="1000" step="50" value="300">
            <span class="slider-value">300ms</span>
          </div>
        </div>
      `;

      container.innerHTML = sliderHTML;
      const slider = container.querySelector('.slider') as HTMLInputElement;
      const valueDisplay = container.querySelector('.slider-value') as HTMLElement;

      expect(slider).toBeTruthy();
      expect(slider.value).toBe('300');
      expect(valueDisplay.textContent).toBe('300ms');

      // 测试滑块变化
      slider.value = '500';
      slider.dispatchEvent(new Event('input'));

      expect(slider.value).toBe('500');
    });
  });

  describe('Responsive Design', () => {
    it('should handle mobile layout', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480
      });

      // 触发resize事件
      window.dispatchEvent(new Event('resize'));

      // 检查是否应用了移动样式
      const nav = container.querySelector('.options-nav');
      expect(nav).toBeTruthy();
    });

    it('should handle tablet layout', () => {
      // 模拟平板设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      });

      window.dispatchEvent(new Event('resize'));

      const nav = container.querySelector('.options-nav');
      expect(nav).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      const navButtons = container.querySelectorAll('.nav-button');
      
      navButtons.forEach(button => {
        expect(button.getAttribute('role')).toBeTruthy();
      });
    });

    it('should support keyboard navigation', () => {
      const firstNavButton = container.querySelector('.nav-button') as HTMLElement;
      
      // 模拟Tab键导航
      firstNavButton.focus();
      expect(document.activeElement).toBe(firstNavButton);

      // 模拟Enter键激活
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      firstNavButton.dispatchEvent(enterEvent);
    });

    it('should have proper focus management', () => {
      const modal = document.getElementById('modal-overlay') as HTMLElement;
      const firstButton = modal.querySelector('button') as HTMLElement;

      // 显示模态框时应该聚焦到第一个按钮
      modal.style.display = 'flex';
      
      // 模拟焦点管理
      if (firstButton) {
        firstButton.focus();
        expect(document.activeElement).toBe(firstButton);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      mockChrome.storage.sync.get.mockRejectedValue(new Error('Storage error'));

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      try {
        await optionsManager?.loadSettings();
      } catch (error) {
        // 应该捕获错误
      }

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle invalid import files', () => {
      const invalidFile = new File(['invalid json'], 'settings.json', { type: 'application/json' });
      
      // 模拟文件读取
      const fileReader = {
        readAsText: vi.fn(),
        result: 'invalid json',
        onload: null as any,
        onerror: null as any
      };

      // 测试无效JSON处理
      try {
        JSON.parse(fileReader.result);
      } catch (error) {
        expect(error).toBeInstanceOf(SyntaxError);
      }
    });
  });
});