/**
 * Search Engine Unit Tests - 搜索引擎单元测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { SearchEngine } from '../../src/content/utils/SearchEngine.js';
import { BookmarkNode, TabInfo } from '../../src/types/index.js';

describe('SearchEngine', () => {
  let searchEngine: SearchEngine;
  let mockBookmarks: BookmarkNode[];
  let mockTabs: TabInfo[];

  beforeEach(() => {
    searchEngine = new SearchEngine();

    mockBookmarks = [
      {
        id: '1',
        title: 'Google Search',
        url: 'https://www.google.com',
        parentId: '0',
        index: 0,
        dateAdded: Date.now()
      },
      {
        id: '2',
        title: 'GitHub Repository',
        url: 'https://github.com/user/repo',
        parentId: '0',
        index: 1,
        dateAdded: Date.now()
      },
      {
        id: '3',
        title: 'Development Tools',
        parentId: '0',
        index: 2,
        dateAdded: Date.now(),
        children: [
          {
            id: '4',
            title: 'Visual Studio Code',
            url: 'https://code.visualstudio.com',
            parentId: '3',
            index: 0,
            dateAdded: Date.now()
          }
        ]
      }
    ];

    mockTabs = [
      {
        id: 1,
        title: 'Google Chrome',
        url: 'https://www.google.com/chrome',
        active: true,
        pinned: false,
        windowId: 1,
        index: 0,
        favIconUrl: 'https://www.google.com/favicon.ico'
      },
      {
        id: 2,
        title: 'Stack Overflow',
        url: 'https://stackoverflow.com',
        active: false,
        pinned: false,
        windowId: 1,
        index: 1,
        favIconUrl: 'https://stackoverflow.com/favicon.ico'
      }
    ];
  });

  describe('searchBookmarks', () => {
    it('should return empty array for empty query', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, '');
      expect(results).toEqual([]);
    });

    it('should find exact title matches', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'Google');
      expect(results).toHaveLength(1);
      expect(results[0].item.title).toBe('Google Search');
      expect(results[0].type).toBe('bookmark');
    });

    it('should find partial title matches', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'git');
      expect(results).toHaveLength(1);
      expect(results[0].item.title).toBe('GitHub Repository');
    });

    it('should find URL matches', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'github.com');
      expect(results).toHaveLength(1);
      expect(results[0].item.url).toContain('github.com');
    });

    it('should search recursively in folders', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'Visual Studio');
      expect(results).toHaveLength(1);
      expect(results[0].item.title).toBe('Visual Studio Code');
    });

    it('should identify folder types correctly', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'Development');
      expect(results).toHaveLength(1);
      expect(results[0].type).toBe('folder');
    });

    it('should return results sorted by score', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'google');
      expect(results[0].score).toBeGreaterThanOrEqual(results[results.length - 1]?.score || 0);
    });

    it('should include match information', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'Google');
      expect(results[0].matches).toBeDefined();
      expect(results[0].matches.length).toBeGreaterThan(0);
      expect(results[0].matches[0].field).toBe('title');
      expect(results[0].matches[0].indices).toBeDefined();
    });

    it('should respect maxResults option', () => {
      const limitedEngine = new SearchEngine({ maxResults: 1 });
      const results = limitedEngine.searchBookmarks(mockBookmarks, 'a');
      expect(results.length).toBeLessThanOrEqual(1);
    });

    it('should handle case insensitive search', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'GOOGLE');
      expect(results).toHaveLength(1);
      expect(results[0].item.title).toBe('Google Search');
    });
  });

  describe('searchTabs', () => {
    it('should return empty array for empty query', () => {
      const results = searchEngine.searchTabs(mockTabs, '');
      expect(results).toEqual([]);
    });

    it('should find tab by title', () => {
      const results = searchEngine.searchTabs(mockTabs, 'Chrome');
      expect(results).toHaveLength(1);
      expect(results[0].item.title).toBe('Google Chrome');
      expect(results[0].type).toBe('tab');
    });

    it('should find tab by URL', () => {
      const results = searchEngine.searchTabs(mockTabs, 'stackoverflow');
      expect(results).toHaveLength(1);
      expect(results[0].item.url).toContain('stackoverflow.com');
    });

    it('should return results sorted by score', () => {
      const results = searchEngine.searchTabs(mockTabs, 'google');
      expect(results[0].score).toBeGreaterThanOrEqual(results[results.length - 1]?.score || 0);
    });
  });

  describe('highlightMatches', () => {
    it('should highlight single match', () => {
      const matches = [{
        field: 'title',
        indices: [[0, 5]] as [number, number][],
        value: 'Google Search'
      }];
      
      const highlighted = searchEngine.highlightMatches('Google Search', matches);
      expect(highlighted).toBe('<mark class="search-highlight">Google</mark> Search');
    });

    it('should highlight multiple matches', () => {
      const matches = [{
        field: 'title',
        indices: [[0, 5], [7, 12]] as [number, number][],
        value: 'Google Search'
      }];
      
      const highlighted = searchEngine.highlightMatches('Google Search', matches);
      expect(highlighted).toBe('<mark class="search-highlight">Google</mark> <mark class="search-highlight">Search</mark>');
    });

    it('should handle overlapping matches', () => {
      const matches = [{
        field: 'title',
        indices: [[0, 3], [2, 5]] as [number, number][],
        value: 'Google'
      }];
      
      const highlighted = searchEngine.highlightMatches('Google', matches);
      expect(highlighted).toBe('<mark class="search-highlight">Google</mark>');
    });

    it('should use custom CSS class', () => {
      const matches = [{
        field: 'title',
        indices: [[0, 5]] as [number, number][],
        value: 'Google Search'
      }];
      
      const highlighted = searchEngine.highlightMatches('Google Search', matches, 'custom-highlight');
      expect(highlighted).toBe('<mark class="custom-highlight">Google</mark> Search');
    });

    it('should return original text if no matches', () => {
      const highlighted = searchEngine.highlightMatches('Google Search', []);
      expect(highlighted).toBe('Google Search');
    });
  });

  describe('createSuggestions', () => {
    it('should create suggestions from titles', () => {
      const suggestions = searchEngine.createSuggestions('goo', [...mockBookmarks, ...mockTabs]);
      expect(suggestions).toContain('google');
    });

    it('should create suggestions from URLs', () => {
      const suggestions = searchEngine.createSuggestions('git', mockBookmarks);
      expect(suggestions).toContain('github.com');
    });

    it('should limit number of suggestions', () => {
      const suggestions = searchEngine.createSuggestions('a', [...mockBookmarks, ...mockTabs]);
      expect(suggestions.length).toBeLessThanOrEqual(5);
    });

    it('should return unique suggestions', () => {
      const duplicateBookmarks = [...mockBookmarks, ...mockBookmarks];
      const suggestions = searchEngine.createSuggestions('google', duplicateBookmarks);
      const uniqueSuggestions = [...new Set(suggestions)];
      expect(suggestions.length).toBe(uniqueSuggestions.length);
    });
  });

  describe('getSearchStats', () => {
    it('should calculate correct statistics', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'a');
      const stats = searchEngine.getSearchStats(results);
      
      expect(stats.total).toBe(results.length);
      expect(stats.bookmarks).toBeGreaterThanOrEqual(0);
      expect(stats.folders).toBeGreaterThanOrEqual(0);
      expect(stats.tabs).toBe(0);
      expect(stats.avgScore).toBeGreaterThanOrEqual(0);
      expect(stats.avgScore).toBeLessThanOrEqual(1);
    });

    it('should handle empty results', () => {
      const stats = searchEngine.getSearchStats([]);
      expect(stats.total).toBe(0);
      expect(stats.avgScore).toBe(0);
    });
  });

  describe('search options', () => {
    it('should respect threshold option', () => {
      const strictEngine = new SearchEngine({ threshold: 0.8 });
      const results = strictEngine.searchBookmarks(mockBookmarks, 'xyz');
      expect(results.length).toBe(0);
    });

    it('should respect minMatchCharLength option', () => {
      const engine = new SearchEngine({ minMatchCharLength: 3 });
      const results = engine.searchBookmarks(mockBookmarks, 'go');
      expect(results.length).toBe(0);
    });

    it('should respect searchFields option', () => {
      const titleOnlyEngine = new SearchEngine({ searchFields: ['title'] });
      const results = titleOnlyEngine.searchBookmarks(mockBookmarks, 'github.com');
      expect(results.length).toBe(0); // Should not find URL matches
    });
  });

  describe('fuzzy matching', () => {
    it('should find fuzzy matches', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'gogle');
      expect(results.length).toBeGreaterThan(0);
    });

    it('should score exact matches higher than fuzzy matches', () => {
      const exactResults = searchEngine.searchBookmarks(mockBookmarks, 'Google');
      const fuzzyResults = searchEngine.searchBookmarks(mockBookmarks, 'Gogle');
      
      if (exactResults.length > 0 && fuzzyResults.length > 0) {
        expect(exactResults[0].score).toBeGreaterThan(fuzzyResults[0].score);
      }
    });
  });

  describe('performance', () => {
    it('should handle large datasets efficiently', () => {
      // Create a large dataset
      const largeBookmarks: BookmarkNode[] = [];
      for (let i = 0; i < 1000; i++) {
        largeBookmarks.push({
          id: i.toString(),
          title: `Bookmark ${i}`,
          url: `https://example${i}.com`,
          parentId: '0',
          index: i,
          dateAdded: Date.now()
        });
      }

      const startTime = performance.now();
      const results = searchEngine.searchBookmarks(largeBookmarks, 'Bookmark');
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
      expect(results.length).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    it('should handle empty bookmark array', () => {
      const results = searchEngine.searchBookmarks([], 'test');
      expect(results).toEqual([]);
    });

    it('should handle bookmarks without URLs', () => {
      const bookmarksWithoutUrls: BookmarkNode[] = [{
        id: '1',
        title: 'Folder',
        parentId: '0',
        index: 0,
        dateAdded: Date.now(),
        children: []
      }];

      const results = searchEngine.searchBookmarks(bookmarksWithoutUrls, 'Folder');
      expect(results).toHaveLength(1);
    });

    it('should handle special characters in query', () => {
      const results = searchEngine.searchBookmarks(mockBookmarks, 'Google & Search');
      expect(results.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle very long queries', () => {
      const longQuery = 'a'.repeat(1000);
      const results = searchEngine.searchBookmarks(mockBookmarks, longQuery);
      expect(results.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle Unicode characters', () => {
      const unicodeBookmarks: BookmarkNode[] = [{
        id: '1',
        title: '测试书签',
        url: 'https://example.com',
        parentId: '0',
        index: 0,
        dateAdded: Date.now()
      }];

      const results = searchEngine.searchBookmarks(unicodeBookmarks, '测试');
      expect(results).toHaveLength(1);
    });
  });
});