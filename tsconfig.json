{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "resolveJsonModule": true, "types": ["chrome"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}